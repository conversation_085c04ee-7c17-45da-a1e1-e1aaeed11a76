package com.asmtunis.procaisseinventory.core.ktor

import android.content.Context
import android.util.Log
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.data.ApiResponse
// Firebase logging removed
import com.asmtunis.procaisseinventory.core.data.ResponseMacroFormat
import com.asmtunis.procaisseinventory.core.data.CustomArticlePaginationResponse
import com.asmtunis.procaisseinventory.core.data.CustomArticleCodeBarPaginationResponse
import com.asmtunis.procaisseinventory.core.data.CustomBonCommandePaginationResponse
import com.asmtunis.procaisseinventory.core.data.toPaginationResponseArticle
import com.asmtunis.procaisseinventory.core.data.toPaginationResponseArticleCodeBare
import com.asmtunis.procaisseinventory.core.data.toPaginationResponseBonCommande
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.network_errors.domaine.NetworkError
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.client.network.sockets.SocketTimeoutException
import io.ktor.client.plugins.ClientRequestException
import io.ktor.client.plugins.RedirectResponseException
import io.ktor.client.plugins.ServerResponseException
import io.ktor.client.request.delete
import io.ktor.client.request.forms.MultiPartFormDataContent
import io.ktor.client.request.forms.formData
import io.ktor.client.request.header
import io.ktor.client.request.parameter
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.request.url
import io.ktor.http.isSuccess
import io.ktor.utils.io.InternalAPI
import kotlinx.coroutines.ensureActive
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.serialization.SerializationException
import kotlinx.serialization.json.Json
import java.io.IOException
import java.net.ConnectException
import java.net.UnknownHostException
import kotlin.coroutines.coroutineContext


@OptIn(InternalAPI::class)
suspend inline fun <reified T> executePostApiCall(
    client: HttpClient,
    baseUrl: String = Urls.BASE_URL,
    endpoint: String,
    queryParams: Map<String, Any> = mapOf(),
    headers: Map<String, String> = emptyMap(),
    formsData: Map<String, String> = emptyMap(),
    baseConfig: String = "",
    customError: String? = null
): Flow<DataResult<T>> = flow {
    //  try {
    emit(DataResult.Loading())
    val response = client.post {


        if(Globals.USER_ID.isNotEmpty())
            header("user", Globals.USER_ID)


        if(headers.isNotEmpty())
            headers.forEach { (key, value) -> header(key, value) }

        url("$baseUrl$endpoint")

        // Debug logging for URL construction
        veryLongLog("API_REQUEST_URL", "Full URL: $baseUrl$endpoint")
        veryLongLog("API_REQUEST_DETAILS", "BaseURL: $baseUrl, Endpoint: $endpoint")

        if(formsData.isNotEmpty()) {
            setBody(
                MultiPartFormDataContent(
                    formData {
                        formsData.forEach { (key, value) -> append(key, value) }
                    }
                )
            )
        }



        if(queryParams.isNotEmpty()) {
            queryParams.forEach { (key, value) -> parameter(key, value) }
        }

        /*   url {
               queryParams.forEach { (key, value) -> parameters.append(key, value) }
           }*/

        if(baseConfig.isNotEmpty()) {
            body = baseConfig
        }

    }

    if (response.status.isSuccess()) {
        val result = parseResponseWithMultipleStrategies<T>(response, endpoint, baseConfig, queryParams)
        emit(DataResult.Success(result))
    } else {
        // Firebase logging disabled to prevent OutOfMemoryError
        // Log API error to Firebase
        /*
        try {
            val context = Globals.getApplicationContext()
            if (context != null) {
                FirebaseLogger.getInstance(context).logApiError(
                    endpoint = endpoint,
                    errorMessage = "${response.status.value}: ${response.status.description}",
                    baseConfig = baseConfig,
                    userId = Globals.USER_ID.takeIf { it.isNotEmpty() }
                )
            }
        } catch (e: Exception) {
            // Don't let logging errors crash the app
        }
        */
        emit(DataResult.Error(message = "${response.status.value}: ${response.status.description}"))
    }
//    } catch (e: Exception) {
//        coroutineContext.ensureActive() // to check if the coroutine is still active
//        val errorMessage = handleExeptions(e = e, customError = customError)
//        emit(DataResult.Error(errorMessage))
//    }
}.catch { exception ->
    when (exception) {
        is ServerErrorException -> {
            // Handle server error messages specifically
            emit(DataResult.Error(exception.message ?: "Server Error"))
        }
        else -> {
            // Handle other exceptions
            emit(DataResult.Error(exception.message ?: "Error"))
        }
    }
}

@OptIn(InternalAPI::class)
suspend inline fun <reified T> executeDeleteApiCall(
    client: HttpClient,
    baseUrl: String = Urls.BASE_URL,
    endpoint: String,
    queryParams: Map<String, Any> = mapOf(),
    headers: Map<String, String> = emptyMap(),
    formsData: Map<String, String> = emptyMap(),
    baseConfig: String = "",
    customError: String? = null
): Flow<DataResult<T>> = flow {
    try {
        emit(DataResult.Loading())
        val response = client.delete {


            if(Globals.USER_ID.isNotEmpty())
                header("user", Globals.USER_ID)


            if(headers.isNotEmpty())
                headers.forEach { (key, value) -> header(key, value) }

            url("$baseUrl$endpoint")

            // Debug logging for URL construction
            veryLongLog("API_REQUEST_URL", "Full URL: $baseUrl$endpoint")
            veryLongLog("API_REQUEST_DETAILS", "BaseURL: $baseUrl, Endpoint: $endpoint")

            if(formsData.isNotEmpty())
                setBody(
                    MultiPartFormDataContent(
                        formData {
                            formsData.forEach { (key, value) -> append(key, value) }
                        }
                    )
                )


            if(queryParams.isNotEmpty())
                queryParams.forEach { (key, value) -> parameter(key, value) }

            /*   url {
                   queryParams.forEach { (key, value) -> parameters.append(key, value) }
               }*/

            if(baseConfig.isNotEmpty())
                body = baseConfig
        }

        if (response.status.isSuccess()) {
            emit(DataResult.Success(response.body<T>()))
        } else {
            emit(DataResult.Error(message = "${response.status.value}: ${response.status.description}"))
        }
    } catch (e: Exception) {
        coroutineContext.ensureActive() // to check if the coroutine is still active
        val errorMessage = handleExeptions(e = e, customError = customError)
        emit(DataResult.Error(errorMessage))
    }
}



//TODO make customError: String of type uitext (for custom localisation)
fun handleExeptions(e: Exception, customError: String?): String = when (e) {

    is ConnectException -> "Échec de connexion. Veuillez réessayer plus tard."
    is ClientRequestException -> "HTTP Error: ${e.response.status}" //4xx client errors
    is ServerResponseException -> "Erreur serveur: ${e.response.status}" // 5xx
    is RedirectResponseException -> "${e.response.status}: Not following redirection to ${ e.response.headers["Location"]}" //3xx redirects
    is SocketTimeoutException -> "Time out: " + (e.cause?.localizedMessage ?: e.localizedMessage ?: "Erreur inconnue")
    is UnknownHostException -> "Unknown Host Exception: " + (e.cause?.localizedMessage ?: e.localizedMessage ?: "Erreur inconnue")
    is IOException -> "IO Exception: " + (e.cause?.localizedMessage ?: e.localizedMessage ?: "Erreur inconnue")
    is SerializationException -> "Serialization Exception: " + (e.cause?.localizedMessage ?: e.localizedMessage ?: "Erreur inconnue")
    else -> customError ?: e.cause?.localizedMessage?: e.localizedMessage?: "Erreur inconnue"
}
/*
suspend fun <T> apiCall(customError: String? = null, apiCall: suspend () -> T): Flow<DataResult<T>> = flow {
    try {
        emit(DataResult.Loading())
        val result = apiCall()
        emit(DataResult.Success(result))
    }catch (e: Exception) {
        emit(DataResult.Error(customError?: e.localizedMessage ?: e.message?: "Erreur inconnue"))
    }

    catch (e: ClientRequestException) {
        emit(DataResult.Error(customError?: "HTTP Error: ${e.response.status}"))
    } catch (e: ConnectException) {
        emit(DataResult.Error(customError?: "Échec de connexion. Veuillez réessayer plus tard."))
    }
}*/


fun insertNetworkError(
    proCaisseLocalDb: ProCaisseLocalDb,
    url: String,
    extraInfo: String = "",
    errorMessage: String?
) {
    val networkError = NetworkError(
        url = url,
        extraInfo = extraInfo,
        errorMessage = errorMessage
    )
    proCaisseLocalDb.networkErrors.insert(item = networkError)
}


fun veryLongLog(tag: String, message: String) {
    val maxLength = 4000 // Maximum length of each part

    if (message.length <= maxLength) {
        Log.d(tag, message)
    } else {
        var startIndex = 0
        var endIndex = maxLength

        while (startIndex < message.length) {
            // Ensure endIndex does not exceed the message length
            if (endIndex > message.length) {
                endIndex = message.length
            }

            val part = message.substring(startIndex, endIndex)
            Log.d(tag, part)

            startIndex = endIndex
            endIndex += maxLength
        }
    }



}

fun extractCurrentPage(input: String): Int? {
    val regex = """Current page:\s*(\d+)""".toRegex()
    val matchResult = regex.find(input)
    return matchResult?.groups?.get(1)?.value?.toInt()
}


fun maskUrls(input: String?, customError: String): String {
    // Regex to match URLs
    val urlRegex = """(https?://[^\s]+)""".toRegex()

    // Replace each URL with asterisks of the same length
    return urlRegex.replace(input?: customError) { matchResult ->
        "*".repeat(matchResult.value.length)
    }
}

/**
 * Helper function to log API success to Firebase (backward compatibility)
 * DISABLED: Firebase logging removed to prevent OutOfMemoryError
 */
fun logApiSuccess(
    endpoint: String,
    responseFormat: String,
    strategyUsed: String,
    responseSize: Int,
    baseConfig: String
) {
    // Firebase logging removed - function kept for backward compatibility
}

/**
 * Helper function to log API success to Firebase with response data
 * DISABLED: Firebase logging disabled to prevent OutOfMemoryError
 */
fun logApiSuccess(
    endpoint: String,
    responseFormat: String,
    strategyUsed: String,
    responseSize: Int,
    baseConfig: String,
    responseData: String?
) {
    // Firebase logging disabled to prevent OutOfMemoryError
    /*
    try {
        val context = Globals.getApplicationContext()
        if (context != null) {
            FirebaseLogger.getInstance(context).logApiResponse(
                endpoint = endpoint,
                responseFormat = responseFormat,
                strategyUsed = strategyUsed,
                responseSize = responseSize,
                baseConfig = baseConfig,
                userId = Globals.USER_ID.takeIf { it.isNotEmpty() },
                responseData = responseData
            )
        }
    } catch (e: Exception) {
        // Don't let logging errors crash the app
    }
    */
}

/**
 * Helper function to log API error to Firebase
 * DISABLED: Firebase logging disabled to prevent OutOfMemoryError
 */
fun logApiError(
    endpoint: String,
    errorMessage: String,
    responseText: String,
    baseConfig: String
) {
    // Firebase logging disabled to prevent OutOfMemoryError
    /*
    try {
        val context = Globals.getApplicationContext()
        if (context != null) {
            FirebaseLogger.getInstance(context).logApiError(
                endpoint = endpoint,
                errorMessage = errorMessage,
                responseText = responseText,
                baseConfig = baseConfig,
                userId = Globals.USER_ID.takeIf { it.isNotEmpty() }
            )
        }
    } catch (e: Exception) {
        // Don't let logging errors crash the app
    }
    */
}

/**
 * Parse response with multiple strategies to handle different API response formats
 */
suspend inline fun <reified T> parseResponseWithMultipleStrategies(
    response: HttpResponse,
    endpoint: String,
    baseConfig: String = "",
    queryParams: Map<String, Any> = mapOf()
): T {
    val responseText = response.bodyAsText()


    // Check for server error messages that are not JSON
    if (isServerErrorMessage(responseText)) {
        val errorMessage = extractServerErrorMessage(responseText, endpoint)
        logApiError(endpoint, "Server returned error message instead of JSON", responseText, baseConfig)
        throw ServerErrorException("Server Error: $errorMessage")
    }

    // Strategy 1: Try to parse as direct type T
    try {
        val directResult = response.body<T>()
        Log.d("API_RESPONSE_SUCCESS", "Endpoint: $endpoint, Strategy: Direct parsing")

        // Log success to Firebase
        logApiSuccess(endpoint, "DIRECT_TYPE", "Strategy1_DirectParsing", responseText.length, baseConfig, responseText)

        // Firebase logging disabled to prevent OutOfMemoryError
        // Create detailed Firebase log for important endpoints
        /*
        if (endpoint.contains("Session") || endpoint.contains("closeSession") || endpoint.contains("login") || endpoint.contains("auth")) {
            try {
                val context = Globals.getApplicationContext()
                if (context != null) {
                    FirebaseLogger.getInstance(context).logDetailedApiCall(
                        endpoint = endpoint,
                        responseData = responseText
                    )
                    // Also create a searchable issue for critical endpoints
                    FirebaseLogger.getInstance(context).createSearchableLogEntry(
                        endpoint = endpoint,
                        responseData = responseText,
                        isError = false
                    )
                }
            } catch (e: Exception) {
                // Don't let logging errors crash the app
            }
        }
        */

        return directResult
    } catch (e: Exception) {
        veryLongLog("API_RESPONSE_STRATEGY1_FAILED", "Endpoint: $endpoint, Error: ${e.message}")
    }

    // Strategy 2: Try to parse as ApiResponse<T> wrapper (ApiResponser trait format)
    try {
        val wrappedResult = response.body<ApiResponse<T>>()
        if (wrappedResult.data != null) {
            Log.d("API_RESPONSE_SUCCESS", "Endpoint: $endpoint, Strategy: ApiResponse wrapper parsing")
            logApiSuccess(endpoint, "API_RESPONSE_WRAPPER", "Strategy2_ApiResponseWrapper", responseText.length, baseConfig, responseText)
            return wrappedResult.data
        }
    } catch (e: Exception) {
        veryLongLog("API_RESPONSE_STRATEGY2_FAILED", "Endpoint: $endpoint, Error: ${e.message}")
    }

    // Strategy 3: Try to parse as Response Macro format {"status": "success", "data": [...]}
    try {
        val json = Json {
            ignoreUnknownKeys = true
            explicitNulls = false
            isLenient = true
            coerceInputValues = true
        }
        val macroResponse = json.decodeFromString<ResponseMacroFormat<T>>(responseText)
        if (macroResponse.status == "success" && macroResponse.data != null) {
            Log.d("API_RESPONSE_SUCCESS", "Endpoint: $endpoint, Strategy: Response macro parsing")
            logApiSuccess(endpoint, "RESPONSE_MACRO", "Strategy3_ResponseMacro", responseText.length, baseConfig, responseText)
            return macroResponse.data!!
        }
    } catch (e: Exception) {
        veryLongLog("API_RESPONSE_STRATEGY3_FAILED", "Endpoint: $endpoint, Error: ${e.message}")
    }

    // Strategy 4: Try to parse as Custom Pagination format {"records": [...], "count": N}
    if (T::class.java.simpleName.contains("Pagination")) {
        try {
            val json = Json {
                ignoreUnknownKeys = true
                explicitNulls = false
                isLenient = true
                coerceInputValues = true
            }
            val convertedResult = when (T::class.java.simpleName) {
                "PaginationResponseArticle" -> {
                    val customPaginationResult = json.decodeFromString<CustomArticlePaginationResponse>(responseText)
                    customPaginationResult.toPaginationResponseArticle() as T
                }
                "PaginationResponseArticleCodeBare" -> {
                    val customPaginationResult = json.decodeFromString<CustomArticleCodeBarPaginationResponse>(responseText)
                    customPaginationResult.toPaginationResponseArticleCodeBare() as T
                }
                "PaginationResponseBonCommande" -> {
                    val customPaginationResult = json.decodeFromString<CustomBonCommandePaginationResponse>(responseText)
                    val currentPage = queryParams["page"]?.toString()?.toIntOrNull() ?: 1
                    val perPage = queryParams["limit"]?.toString()?.toIntOrNull() ?: 300
                    customPaginationResult.toPaginationResponseBonCommande(currentPage, perPage) as T
                }
                else -> throw SerializationException("Unknown pagination type")
            }
            Log.d("API_RESPONSE_SUCCESS", "Endpoint: $endpoint, Strategy: Custom pagination parsing")
            return convertedResult
        } catch (e: Exception) {
            veryLongLog("API_RESPONSE_STRATEGY4_FAILED", "Endpoint: $endpoint, Error: ${e.message}")
        }
    }

    // Strategy 5: Handle empty array case
    if (responseText.trim() == "[]") {
        Log.d("API_RESPONSE_SUCCESS", "Endpoint: $endpoint, Strategy: Empty array handling")
        // For pagination responses, return empty pagination object
        if (T::class.java.simpleName.contains("Pagination")) {
            return createEmptyPaginationResponse<T>()
        }
        // For list responses, return empty list
        return emptyList<Any>() as T
    }

    // Strategy 6: Try manual JSON parsing with lenient settings
    try {
        val json = Json {
            ignoreUnknownKeys = true
            explicitNulls = false
            isLenient = true
            coerceInputValues = true
        }
        val manualResult = json.decodeFromString<T>(responseText)
        Log.d("API_RESPONSE_SUCCESS", "Endpoint: $endpoint, Strategy: Manual JSON parsing")
        return manualResult
    } catch (e: Exception) {
        veryLongLog("API_RESPONSE_STRATEGY6_FAILED", "Endpoint: $endpoint, Error: ${e.message}")
    }

    // If all strategies fail, log error and throw exception
    logApiError(endpoint, "All parsing strategies failed", responseText, baseConfig)
    throw SerializationException("Failed to parse response for endpoint: $endpoint. Response: $responseText")
}

/**
 * Create empty pagination response for handling empty arrays
 */
inline fun <reified T> createEmptyPaginationResponse(): T {
    return when (T::class.java.simpleName) {
        "PaginationResponseArticle" -> {
            com.asmtunis.procaisseinventory.articles.data.article.domaine.PaginationResponseArticle(
                currentPage = 1,
                data = emptyList(),
                total = "0"
            ) as T
        }
        "PaginationResponseArticleCodeBare" -> {
            com.asmtunis.procaisseinventory.articles.data.article.domaine.PaginationResponseArticleCodeBare(
                currentPage = 1,
                data = emptyList(),
                total = "0"
            ) as T
        }
        "PaginationResponseBonCommande" -> {
            com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.PaginationResponseBonCommande(
                currentPage = 1,
                data = emptyList(),
                total = "0"
            ) as T
        }
        else -> emptyList<Any>() as T
    }
}

/**
 * Custom exception for server errors that return error messages instead of JSON
 */
class ServerErrorException(message: String) : Exception(message)

/**
 * Check if the response text is a server error message instead of JSON
 */
fun isServerErrorMessage(responseText: String): Boolean {
    val trimmedText = responseText.trim()

    // Check for common server error patterns
    return when {
        // PHP error messages
        trimmedText.startsWith("Trying to get property") -> true
        trimmedText.startsWith("Call to a member function") -> true
        trimmedText.startsWith("Undefined property") -> true
        trimmedText.startsWith("Undefined variable") -> true
        trimmedText.startsWith("Fatal error") -> true
        trimmedText.startsWith("Parse error") -> true
        trimmedText.startsWith("Warning:") -> true
        trimmedText.startsWith("Notice:") -> true

        // SQL error messages
        trimmedText.contains("SQL syntax") -> true
        trimmedText.contains("Table") && trimmedText.contains("doesn't exist") -> true
        trimmedText.contains("Column") && trimmedText.contains("not found") -> true

        // Generic error patterns
        trimmedText.startsWith("Error:") -> true
        trimmedText.startsWith("Exception:") -> true

        // Check if it's clearly not JSON (doesn't start with { or [)
        trimmedText.isNotEmpty() &&
                !trimmedText.startsWith("{") &&
                !trimmedText.startsWith("[") &&
                !trimmedText.startsWith("\"") &&
                trimmedText.length < 200 && // Short error messages
                !trimmedText.contains("\"") -> true // No quotes suggest it's not JSON

        else -> false
    }
}

/**
 * Extract meaningful error message from server response
 */
fun extractServerErrorMessage(responseText: String, endpoint: String): String {
    val trimmedText = responseText.trim()

    return when {
        trimmedText.contains("ART_TVA") -> {
            "Article data is incomplete or missing. Please ensure all articles have valid TVA information before syncing."
        }
        trimmedText.startsWith("Trying to get property") -> {
            val property = trimmedText.substringAfter("'").substringBefore("'")
            "Missing required data: $property. Please check your data and try again."
        }
        trimmedText.contains("SQL") -> {
            "Database error occurred. Please contact support if this persists."
        }
        trimmedText.length > 100 -> {
            "Server error: ${trimmedText.take(100)}..."
        }
        else -> trimmedText.ifEmpty { "Unknown server error occurred" }
    }
}