<?xml version="1.0" encoding="utf-8"?>

<com.google.android.material.appbar.AppBarLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:card_view="http://schemas.android.com/tools"
    xmlns:table="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent"
    android:tag="modal-appbar"
    android:theme="@style/AppTheme.AppBarOverlay">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/expanded_toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:elevation="4dp"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">


                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center">


                    <FrameLayout
                        android:id="@+id/frame_container"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_below="@id/toolbar">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical">


                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_above="@+id/relativeLayout"
                                android:layout_alignParentStart="true"
                                android:layout_weight="1"
                                android:orientation="vertical">


                                <androidx.cardview.widget.CardView

                                    android:layout_width="match_parent"
                                    android:layout_height="385dp"
                                    android:layout_margin="5dp"
                                    android:elevation="5dp">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:background="@color/material_white"

                                        android:orientation="vertical">

                                        <LinearLayout
                                            android:id="@+id/product_drawer"
                                            android:layout_width="match_parent"
                                            android:layout_height="48dp"
                                            android:background="?android:attr/selectableItemBackground"
                                            android:orientation="horizontal"

                                            >

                                            <androidx.appcompat.widget.AppCompatTextView
                                                android:layout_width="0dp"
                                                android:layout_height="match_parent"
                                                android:layout_weight="1"
                                                android:fontFamily="sans-serif-medium"
                                                android:gravity="center_vertical"
                                                android:paddingLeft="16dp"
                                                android:text="@string/product_title"
                                                android:textColor="?attr/colorAccent"
                                                android:textSize="16sp" />

                                            <View
                                                android:layout_width="1dp"
                                                android:layout_height="match_parent"
                                                android:background="#eee" />


                                            <com.mikepenz.iconics.view.IconicsButton
                                                android:id="@+id/product_drawer_button"
                                                android:layout_width="64dp"
                                                android:layout_height="match_parent"
                                                android:layout_gravity="end"
                                                android:background="?android:attr/selectableItemBackground"
                                                android:clickable="false"
                                                android:gravity="end"
                                                android:padding="12dp"
                                                android:text="{faw-chevron-down}"
                                                android:textColor="?attr/colorAccent"
                                                android:textSize="25dp"
                                                android:tint="?attr/colorAccent" />


                                        </LinearLayout>


                                        <net.cachapa.expandablelayout.ExpandableLayout
                                            android:id="@+id/product_expandable_layout"
                                            android:layout_width="match_parent"
                                            android:layout_height="match_parent"
                                            table:el_duration="200"
                                            table:el_expanded="true">

                                            <LinearLayout
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:orientation="vertical">

                                                <View
                                                    android:layout_width="match_parent"
                                                    android:layout_height="5dp"
                                                    android:background="@drawable/toolbar_dropshadow" />


                                                <com.asmtunis.procaissemobility.ui.components.SortableLigneInvTableView
                                                    android:id="@+id/invtableView"
                                                    android:layout_width="match_parent"
                                                    android:layout_height="wrap_content"
                                                    card_view:tableView_headerColor="@color/material_teal700"
                                                    card_view:tableView_headerElevation="10"
                                                    table:tableView_columnCount="6" />

                                            </LinearLayout>
                                        </net.cachapa.expandablelayout.ExpandableLayout>

                                    </LinearLayout>
                                </androidx.cardview.widget.CardView>
                            </LinearLayout>


                        </LinearLayout>

                    </FrameLayout>
                </RelativeLayout>


            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">


                <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center">


                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@id/toolbar"
                        card_view:ignore="NotSibling">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">


                            <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_above="@+id/relativeLayout"
                                android:layout_alignParentStart="true"
                                android:layout_weight="1"
                                android:orientation="vertical">


                                <androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
                                    xmlns:custom="http://schemas.android.com/tools"
                                    xmlns:table="http://schemas.android.com/apk/res-auto"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_margin="5dp"
                                    android:elevation="5dp"
                                    android:visibility="gone">

                                    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                                        xmlns:custom="http://schemas.android.com/tools"
                                        xmlns:table="http://schemas.android.com/apk/res-auto"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:background="@color/material_white"

                                        android:orientation="vertical">

                                        <LinearLayout
                                            android:id="@+id/payment_drawer"
                                            android:layout_width="match_parent"
                                            android:layout_height="48dp"
                                            android:background="?android:attr/selectableItemBackground"
                                            android:orientation="horizontal"

                                            >

                                            <TextView
                                                android:layout_width="0dp"
                                                android:layout_height="match_parent"
                                                android:layout_weight="1"
                                                android:fontFamily="sans-serif-medium"
                                                android:gravity="center_vertical"
                                                android:paddingLeft="16dp"
                                                android:text="@string/payment_label"
                                                android:textColor="?attr/colorAccent"
                                                android:textSize="16sp" />

                                            <View
                                                android:layout_width="1dp"
                                                android:layout_height="48dp"
                                                android:background="#eee" />


                                            <com.mikepenz.iconics.view.IconicsButton
                                                android:id="@+id/payment_drawer_button"
                                                android:layout_width="64dp"
                                                android:layout_height="wrap_content"
                                                android:background="?android:attr/selectableItemBackground"
                                                android:clickable="false"
                                                android:padding="12dp"
                                                android:text="{faw-chevron-down}"
                                                android:textColor="?attr/colorAccent"
                                                android:textSize="25dp"
                                                android:tint="?attr/colorAccent" />


                                        </LinearLayout>


                                        <net.cachapa.expandablelayout.ExpandableLayout
                                            android:id="@+id/payment_expandable_layout"
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            table:el_duration="200"
                                            table:el_expanded="false">

                                            <LinearLayout
                                                android:layout_width="match_parent"
                                                android:layout_height="wrap_content"
                                                android:orientation="vertical">
                                                />

                                                <View
                                                    android:layout_width="match_parent"
                                                    android:layout_height="5dp"
                                                    android:background="@drawable/toolbar_dropshadow" />

                                                <ScrollView
                                                    android:layout_width="match_parent"
                                                    android:layout_height="wrap_content">

                                                    <LinearLayout
                                                        android:layout_width="match_parent"
                                                        android:layout_height="wrap_content"
                                                        android:orientation="vertical">

                                                        <androidx.cardview.widget.CardView
                                                            xmlns:android="http://schemas.android.com/apk/res/android"
                                                            xmlns:table="http://schemas.android.com/apk/res-auto"
                                                            xmlns:card_view="http://schemas.android.com/tools"
                                                            android:id="@+id/cash_view"
                                                            android:layout_width="match_parent"
                                                            android:layout_height="wrap_content"
                                                            android:layout_margin="5dp"
                                                            android:background="@color/transparent"
                                                            android:orientation="vertical"
                                                            card_view:cardCornerRadius="5dp"
                                                            card_view:cardElevation="5dp"
                                                            card_view:cardUseCompatPadding="true"
                                                            card_view:elevation="5dp">

                                                            <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                                                                xmlns:custom="http://schemas.android.com/tools"
                                                                xmlns:table="http://schemas.android.com/apk/res-auto"
                                                                android:layout_width="match_parent"
                                                                android:layout_height="wrap_content"
                                                                android:background="@color/material_white"
                                                                android:orientation="vertical">

                                                                <LinearLayout
                                                                    android:layout_width="match_parent"
                                                                    android:layout_height="48dp"
                                                                    android:orientation="horizontal">

                                                                    <TextView
                                                                        android:layout_width="0dp"
                                                                        android:layout_height="match_parent"
                                                                        android:layout_weight="1"
                                                                        android:fontFamily="sans-serif-medium"
                                                                        android:gravity="center_vertical"
                                                                        android:paddingLeft="16dp"
                                                                        android:text="@string/cash_title"
                                                                        android:textColor="?attr/colorAccent"
                                                                        android:textSize="16sp" />

                                                                </LinearLayout>

                                                                <View
                                                                    android:layout_width="match_parent"
                                                                    android:layout_height="1dp"
                                                                    android:background="#eee" />

                                                                <RelativeLayout
                                                                    android:layout_width="fill_parent"
                                                                    android:layout_height="wrap_content">

                                                                    <LinearLayout
                                                                        android:layout_width="match_parent"
                                                                        android:layout_height="wrap_content">


                                                                        <EditText

                                                                            android:id="@+id/total_payment_value"
                                                                            android:layout_width="match_parent"
                                                                            android:layout_height="wrap_content"
                                                                            android:layout_below="@+id/title0"
                                                                            android:background="@android:color/transparent"
                                                                            android:clickable="false"
                                                                            android:enabled="false"
                                                                            android:focusable="false"
                                                                            android:fontFamily="sans-serif-medium"
                                                                            android:gravity="center"
                                                                            android:hint="@string/empty_field"
                                                                            android:imeOptions="actionDone"
                                                                            android:inputType="numberDecimal"
                                                                            android:nextFocusUp="@+id/title"
                                                                            android:selectAllOnFocus="true"
                                                                            android:textColor="@color/md_black"
                                                                            android:textSize="26sp" />


                                                                    </LinearLayout>


                                                                </RelativeLayout>
                                                            </LinearLayout>
                                                        </androidx.cardview.widget.CardView>

                                                        <androidx.cardview.widget.CardView
                                                            android:id="@+id/check_view"

                                                            android:layout_width="match_parent"
                                                            android:layout_height="wrap_content"
                                                            android:layout_margin="5dp"
                                                            android:background="@color/transparent"
                                                            android:minHeight="350dp"
                                                            android:orientation="vertical"
                                                            card_view:cardCornerRadius="5dp"
                                                            card_view:cardElevation="5dp"
                                                            card_view:cardUseCompatPadding="true"
                                                            card_view:elevation="5dp">


                                                            <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                                                                xmlns:custom="http://schemas.android.com/tools"
                                                                xmlns:table="http://schemas.android.com/apk/res-auto"
                                                                android:layout_width="match_parent"
                                                                android:layout_height="wrap_content"
                                                                android:background="@color/material_white"

                                                                android:orientation="vertical">

                                                                <LinearLayout
                                                                    android:layout_width="match_parent"
                                                                    android:layout_height="48dp"
                                                                    android:orientation="horizontal">

                                                                    <TextView
                                                                        android:layout_width="0dp"
                                                                        android:layout_height="match_parent"
                                                                        android:layout_weight="1"
                                                                        android:fontFamily="sans-serif-medium"
                                                                        android:gravity="center_vertical"
                                                                        android:paddingLeft="16dp"
                                                                        android:text="@string/check_title"
                                                                        android:textColor="?attr/colorAccent"
                                                                        android:textSize="16sp" />


                                                                </LinearLayout>

                                                                <View
                                                                    android:layout_width="match_parent"
                                                                    android:layout_height="1dp"
                                                                    android:background="#eee" />

                                                                <LinearLayout
                                                                    android:layout_width="fill_parent"
                                                                    android:layout_height="wrap_content"

                                                                    android:orientation="vertical">

                                                                    <com.asmtunis.procaissemobility.ui.components.SortableBankCheckLineTableView
                                                                        android:id="@+id/bankDataTableView"
                                                                        android:layout_width="match_parent"
                                                                        android:layout_height="250dp"
                                                                        custom:tableView_headerColor="@color/material_teal700"
                                                                        custom:tableView_headerElevation="10"
                                                                        table:tableView_columnCount="4" />
                                                                </LinearLayout>


                                                            </LinearLayout>
                                                        </androidx.cardview.widget.CardView>

                                                        <androidx.cardview.widget.CardView
                                                            android:id="@+id/ticket_resto_view"

                                                            android:layout_width="match_parent"
                                                            android:layout_height="wrap_content"
                                                            android:layout_margin="5dp"
                                                            android:background="@color/transparent"
                                                            android:orientation="vertical"
                                                            card_view:cardCornerRadius="5dp"
                                                            card_view:cardElevation="5dp"
                                                            card_view:cardUseCompatPadding="true"
                                                            card_view:elevation="5dp">

                                                            <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                                                                xmlns:custom="http://schemas.android.com/tools"
                                                                xmlns:table="http://schemas.android.com/apk/res-auto"
                                                                android:layout_width="match_parent"
                                                                android:layout_height="wrap_content"
                                                                android:background="@color/material_white"

                                                                android:orientation="vertical">

                                                                <LinearLayout
                                                                    android:layout_width="match_parent"
                                                                    android:layout_height="48dp"
                                                                    android:orientation="horizontal">

                                                                    <TextView
                                                                        android:layout_width="0dp"
                                                                        android:layout_height="match_parent"
                                                                        android:layout_weight="1"
                                                                        android:fontFamily="sans-serif-medium"
                                                                        android:gravity="center_vertical"
                                                                        android:paddingLeft="16dp"
                                                                        android:text="@string/resto_tickets_title"
                                                                        android:textColor="?attr/colorAccent"
                                                                        android:textSize="16sp" />

                                                                </LinearLayout>

                                                                <View
                                                                    android:layout_width="match_parent"
                                                                    android:layout_height="1dp"
                                                                    android:background="#eee" />


                                                                <com.asmtunis.procaissemobility.ui.components.SortableRestoTicketTableView
                                                                    android:id="@+id/restoTicketDataTableView"
                                                                    android:layout_width="match_parent"
                                                                    android:layout_height="250dp"
                                                                    custom:tableView_headerColor="@color/material_teal700"
                                                                    custom:tableView_headerElevation="10"
                                                                    table:tableView_columnCount="3" />


                                                            </LinearLayout>
                                                        </androidx.cardview.widget.CardView>
                                                    </LinearLayout>
                                                </ScrollView>


                                            </LinearLayout>

                                        </net.cachapa.expandablelayout.ExpandableLayout>

                                    </LinearLayout>
                                </androidx.cardview.widget.CardView>
                            </LinearLayout>


                        </LinearLayout>

                    </FrameLayout>
                </RelativeLayout>


            </LinearLayout>


        </LinearLayout>

    </ScrollView>
</com.google.android.material.appbar.AppBarLayout>
