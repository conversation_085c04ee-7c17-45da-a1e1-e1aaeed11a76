<androidx.appcompat.widget.LinearLayoutCompat
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context="com.asmtunis.procaissemobility.ui.fragments.MapFragment">

    <include
        android:id="@+id/emptyView"
        layout="@layout/empty_view"
        android:visibility="gone" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/state_grid"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#fff"
        android:horizontalSpacing="1dp"
        android:numColumns="auto_fit"
        android:stretchMode="columnWidth"
        android:verticalSpacing="1dp"
        android:scrollbars="none"
        android:orientation="horizontal"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        >

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/client_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="top"
            android:gravity="top"
            android:visibility="gone"
            android:orientation="vertical">

            <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
                android:id="@+id/swipeRefreshLayout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone">


                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />


            </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

        </androidx.appcompat.widget.LinearLayoutCompat>

        <FrameLayout
            android:id="@+id/mapFrame"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <com.mapbox.mapboxsdk.maps.MapView
                android:id="@+id/mapView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:mapbox_cameraTargetLat="36.806389"
                app:mapbox_cameraTargetLng="10.181667"
                app:mapbox_cameraZoom="10"
                app:mapbox_uiAttribution="false"
                app:mapbox_uiLogo="false" />
        </FrameLayout>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|end"
            android:layout_marginEnd="@dimen/fab_margin"
            android:layout_marginBottom="5dp"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            app:layout_anchorGravity="bottom|right|end"
            app:layout_dodgeInsetEdges="bottom">

            <androidx.appcompat.widget.AppCompatImageButton
                android:id="@+id/alert_button"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginBottom="@dimen/fab_margin"
                android:background="@drawable/roundedbutton"
                android:backgroundTint="@color/material_red700"
                android:backgroundTintMode="src_over"
                android:src="@drawable/ic_alert_circle"
                android:visibility="gone" />

            <androidx.appcompat.widget.AppCompatImageButton
                android:id="@+id/list_button"
                android:layout_width="56dp"
                android:layout_height="56dp"
                android:layout_marginBottom="@dimen/fab_margin"
                android:background="@drawable/roundedbutton"
                android:backgroundTint="@color/white"
                android:src="@drawable/ic__list"
                android:visibility="gone" />

        </androidx.appcompat.widget.LinearLayoutCompat>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/no_routes"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:background="@color/transparent_grey"
            android:gravity="center"
            android:visibility="gone">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Il n'y a pas d'ordre de mission pour aujourd'hui"
                android:textColor="@color/white"
                android:textSize="15sp" />

        </androidx.appcompat.widget.LinearLayoutCompat>
    </FrameLayout>

</androidx.appcompat.widget.LinearLayoutCompat>