<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.appbar.AppBarLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:card_view="http://schemas.android.com/tools"
    xmlns:table="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:tag="modal-appbar"
    android:theme="@style/AppTheme.AppBarOverlay">

    <androidx.appcompat.widget.Toolbar
        android:layout_marginTop="20dp"
        android:id="@+id/expanded_toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:elevation="4dp"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" >

    </androidx.appcompat.widget.Toolbar>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:orientation="horizontal"
                android:padding="5dp">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center"
                    android:layout_marginEnd="20dp"
                    android:src="@drawable/tax"
                    app:tint="@color/Colororange" />

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/printTaxSwitcher"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="@string/print_tax"
                    android:textColor="#666"
                    android:textSize="18dp" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#e0e0e0" />



            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:orientation="horizontal"
                android:padding="5dp">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center"
                    android:layout_marginEnd="20dp"
                    android:src="@drawable/synchronisation"
                    app:tint="@color/Colororange" />

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/AutoSyncSwitcher"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="@string/auto_sync"
                    android:textColor="#666"
                    android:textSize="18dp" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#e0e0e0" />

            <LinearLayout
                android:id="@+id/updateDatabase"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:orientation="horizontal"
                android:padding="5dp">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center"
                    android:layout_marginEnd="20dp"
                    android:src="@drawable/download_icon"
                    app:tint="@color/material_brown50" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:text="@string/update_db_title"
                    android:textColor="#666"
                    android:textSize="18dp" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#e0e0e0" />

            <LinearLayout
                android:id="@+id/ConfigBase"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:orientation="horizontal"
                android:padding="5dp">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center"
                    android:layout_marginEnd="20dp"
                    android:src="@drawable/settings"
                    app:tint="@color/material_brown600" />


                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:text="@string/update_config_base"
                    android:textColor="#666"
                    android:textSize="18dp" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#e0e0e0" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:orientation="horizontal"
                android:padding="5dp">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center"
                    android:layout_marginEnd="20dp"
                    android:src="@drawable/session"
                    app:tint="@color/material_brown600" />

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/autoClotSwitcher"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:enabled="false"
                    android:gravity="center_vertical"
                    android:text="@string/auto_clot"
                    android:textColor="#666"
                    android:textSize="18dp" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#e0e0e0" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:orientation="horizontal"
                android:padding="5dp">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center"
                    android:layout_marginEnd="20dp"
                    android:src="@drawable/barcode"
                    app:tint="@color/material_brown600" />

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/ean13_check_digit"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="@string/ean_13_check_digit"
                    android:textColor="#666"
                    android:textSize="18dp" />

            </LinearLayout>








            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:orientation="horizontal"
                android:padding="5dp">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center"
                    android:layout_marginEnd="20dp"
                    android:src="@drawable/ic_baseline_a4"
                    app:tint="@color/material_brown600" />

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/a4_print"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="@string/a4_print"
                    android:textColor="#666"
                    android:textSize="18dp" />

            </LinearLayout>

            <TextView
                android:id="@+id/textView4"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="  Taille du papier d'impression"
                android:textColor="#666"
                android:textStyle="bold" />

            <RadioGroup
                android:id="@+id/printerwidth_groupradio"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="5dp"
                android:text="Largeur du papier d'impression">

                <!-- In RadioGroup create the 1 Radio Button-->
                <!-- like this we will add some more Radio Button-->
                <RadioButton
                    android:id="@+id/radio_48"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:minHeight="48dp"
                    android:text="   48mm"
                    android:textColor="#666"
                    android:textSize="20sp" />

                <RadioButton
                    android:id="@+id/radia_38"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:minHeight="48dp"
                    android:text="   38mm"
                    android:textColor="#666"
                    android:textSize="20sp" />

            </RadioGroup>







            <TextView
                android:id="@+id/choose_printer_type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="  Type Imprimante"
                android:textColor="#666"
                android:textStyle="bold" />

            <RadioGroup
                android:id="@+id/type_impriment_radio_groupe"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="5dp"
                android:text="   Type Imprimante">

                <!-- In RadioGroup create the 1 Radio Button-->
                <!-- like this we will add some more Radio Button-->
                <RadioButton
                    android:id="@+id/esc"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:minHeight="48dp"
                    android:text="   ESC/POS"
                    android:textColor="#666"
                    android:textSize="20sp" />

                <RadioButton
                    android:id="@+id/tsc"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:minHeight="48dp"
                    android:text="   TSC"
                    android:textColor="#666"
                    android:textSize="20sp" />

            </RadioGroup>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:orientation="horizontal"
                android:padding="5dp">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center"
                    android:layout_marginEnd="20dp"
                    android:src="@drawable/ic_update"
                    app:tint="@color/material_brown600" />

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/check_update"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="@string/check_update"
                    android:textColor="#666"
                    android:textSize="18dp" />

            </LinearLayout>
        </LinearLayout>



    </ScrollView>
</com.google.android.material.appbar.AppBarLayout>
