<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission
        android:name="android.permission.BLUETOOTH_SCAN"
        tools:targetApi="s" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <!--  <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />-->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <!-- <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" /> -->
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION"
        tools:node="remove"/>
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission
        android:name="com.symbol.emdk.permission.EMDK"
        android:required="false" />
    <uses-permission
        android:name="android.permission.CAMERA"
        android:required="false" />

    <uses-feature
        android:name="android.hardware.camera"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="true" />

    <application
        android:name="com.asmtunis.procaissemobility.App"
        android:allowBackup="false"
        android:fullBackupContent="false"
        android:fullBackupOnly="false"
        android:hardwareAccelerated="true"
        android:hasFragileUserData="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_main_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:replace="android:label,         android:allowBackup">
        <activity
            android:name="com.asmtunis.procaissemobility.ui.activities.DemoActivity"
            android:exported="false" />
        <activity
            android:name="com.asmtunis.procaissemobility.ui.activities.VCAutreActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name="com.asmtunis.procaissemobility.ui.activities.VCAddPrixActivity"
            android:exported="false" />
        <activity
            android:name="com.asmtunis.procaissemobility.ui.activities.VCAddPromoActivity"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.asmtunis.procaissemobility.ui.activities.MyCameraActivity"
            android:exported="false" />
        <activity
            android:name="com.asmtunis.procaissemobility.ui.activities.VCNewProductActivity"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.asmtunis.procaissemobility.ui.activities.InvPatrimoineActivity"
            android:exported="false" />


        <uses-library
            android:name="com.symbol.emdk"
            android:required="false" />

        <activity android:name="com.asmtunis.procaissemobility.ui.activities.BonRetourActivity" />
        <activity android:name="com.asmtunis.procaissemobility.ui.activities.BonCommandeActivity" />
        <activity
            android:name="com.asmtunis.procaissemobility.ui.activities.LoginActivity"
            android:screenOrientation="portrait"
            android:theme="@style/FullscreenTheme" />
        <activity
            android:name="com.asmtunis.procaissemobility.ui.activities.MainActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.asmtunis.procaissemobility.ui.activities.AddClientActivity"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.asmtunis.procaissemobility.ui.activities.AddDNVisiteActivity"
            android:theme="@style/AppTheme" />

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="@string/google_api_key" /> <!-- AIzaSyBHmCR9WQIFMUBO8TmQaJ_H4FN3Dg5yEIw -->
        <activity
            android:name="com.asmtunis.procaissemobility.ui.activities.TicketActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.asmtunis.procaissemobility.ui.activities.CommandeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.asmtunis.procaissemobility.ui.activities.Ticket2PaymentActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.asmtunis.procaissemobility.ui.activities.PaymentActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.asmtunis.procaissemobility.ui.activities.ReclamationActivity"
            android:screenOrientation="portrait" />

        <receiver
            android:name="com.example.barecodereader.barcode.readers.PM80Reader$ScanResultReceiver"
            android:enabled="true"
            android:exported="true"
            android:permission="device.sdk.sample.scanner.permission.SCANNER_RESULT_RECEIVER"
            android:priority="0">
            <intent-filter>
                <action android:name="device.common.USERMSG" />
                <action android:name="device.scanner.EVENT" />
            </intent-filter>
        </receiver>

        <activity
            android:name="com.asmtunis.procaissemobility.ui.activities.SplashscreenActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/FullscreenTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.asmtunis.procaissemobility.ui.activities.DemandeLicenceActivity"
            android:screenOrientation="portrait"
            android:theme="@style/FullscreenTheme" />
        <activity
            android:name="com.asmtunis.procaissemobility.ui.activities.SettingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.asmtunis.procaissemobility.ui.activities.DeviceListActivity"
            android:label="@string/select_device"
            android:theme="@style/Theme.AppCompat.Light.Dialog.Alert" />

        <receiver
            android:name="com.asmtunis.procaissemobility.recivers.NetworkChangeReceiver"
            android:exported="true"
            android:label="NetworkChangeReceiver">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
                <action android:name="android.net.wifi.WIFI_STATE_CHANGED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.asmtunis.procaissemobility.recivers.ConnectivityReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
                <action android:name="android.net.wifi.WIFI_STATE_CHANGED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.asmtunis.procaissemobility.recivers.AlarmReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name=".Alarm" />
            </intent-filter>
        </receiver>

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            tools:node="remove" />

        <service
            android:name="com.asmtunis.procaissemobility.services.LocationUpdatesService"
            android:enabled="true"
            android:foregroundServiceType="location" />


        <provider
            android:authorities="com.asmtunis.procaissemobility.fileprovider"
            android:name="androidx.core.content.FileProvider"
            android:exported="false"
            android:grantUriPermissions="true">

            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths"/>
        </provider>
    </application>

</manifest>