package com.asmtunis.procaissemobility.services;

import android.app.ActivityManager;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.location.Location;
import android.os.Binder;
import android.os.Build;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.IBinder;
import android.os.Looper;
import android.util.Log;
import android.widget.Toast;

import androidx.core.app.NotificationCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.Database;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Traking;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.datamanager.TrakingDataManager;
import com.asmtunis.procaissemobility.helper.Globals;
import com.asmtunis.procaissemobility.helper.utils.DateUtils;
import com.asmtunis.procaissemobility.helper.utils.LocationUtils;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.listener.MapObserverListener;
import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.LocationCallback;
import com.google.android.gms.location.LocationRequest;
import com.google.android.gms.location.LocationResult;
import com.google.android.gms.location.LocationServices;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;

public class LocationUpdatesService extends Service {

    private static final String PACKAGE_NAME =
            "com.google.android.gms.location.sample.locationupdatesforegroundservice";

    private static final String TAG = "resPOINT";

    /**
     * The name of the channel for notifications.
     */
    private static final String CHANNEL_ID = "channel_01";

    public static final String ACTION_BROADCAST = PACKAGE_NAME + ".broadcast";
    private static final int NOTIFICATION_ID = 12345678;
    public static final String EXTRA_LOCATION = PACKAGE_NAME + ".location";
    private static final String EXTRA_STARTED_FROM_NOTIFICATION = PACKAGE_NAME +
            ".started_from_notification";

    private final IBinder mBinder = new LocalBinder();

    /**
     * The desired interval for location updates. Inexact. Updates may be more or less frequent.
     */
    private static final long UPDATE_INTERVAL_IN_MILLISECONDS = Double.valueOf(PrefUtils.getStatSecondeM()).longValue();

    /**
     * The desired distance for location updates. Inexact. Updates may be more or less frequent.
     */
    private static final long UPDATE_DISTANCE_IN_METERS = Double.valueOf(PrefUtils.getStatMetrageM()).longValue();

    /**
     * The fastest rate for active location updates. Updates will never be more frequent
     * than this value.
     */
    private static final long FASTEST_UPDATE_INTERVAL_IN_MILLISECONDS =
            UPDATE_INTERVAL_IN_MILLISECONDS / 2;

    /**
     * Used to check whether the bound activity has really gone away and not unbound as part of an
     * orientation change. We create a foreground service notification only if the former takes
     * place.
     */
    private boolean mChangingConfiguration = false;

    private NotificationManager mNotificationManager;

    /**
     * Contains parameters used by {@link com.google.android.gms.location.FusedLocationProviderApi}.
     */
    private LocationRequest mLocationRequest;

    /**
     * Provides access to the Fused Location Provider API.
     */
    private FusedLocationProviderClient mFusedLocationClient;

    /**
     * Callback for changes in location.
     */
    private LocationCallback mLocationCallback;

    private Handler mServiceHandler;

    Double latitude, longitude;

    private static List<Traking> trakingList;

    /**
     * The current location.
     */
    private Location mLocation;

    private MapObserverListener mapObserverListener;

    private static ObservableEmitter<Object> emitter;

    public static Observable posObservable = Observable.create(emitter1 -> {
        LocationUpdatesService.emitter = emitter1;
    });

    @SuppressWarnings("deprecation")
    public LocationUpdatesService() {
    }

    @SuppressWarnings("deprecation")
    @Override
    public void onCreate() {
        mFusedLocationClient = LocationServices.getFusedLocationProviderClient(this);
            mLocationCallback = new LocationCallback() {
                @Override
                public void onLocationResult(LocationResult locationResult) {
                    super.onLocationResult(locationResult);
                    if(emitter != null) {
                        emitter.onNext(locationResult.getLastLocation());
                    }
                    onNewLocation(locationResult.getLastLocation());
                }
            };

        createLocationRequest();
        getLastLocation();

        HandlerThread handlerThread = new HandlerThread(TAG);
        handlerThread.start();
        mServiceHandler = new Handler(handlerThread.getLooper());
        mNotificationManager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);

        // Android O requires a Notification Channel.
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            CharSequence name = getString(R.string.app_name);
            // Create the channel for the notification
            NotificationChannel mChannel =
                    new NotificationChannel(CHANNEL_ID, name, NotificationManager.IMPORTANCE_DEFAULT);
            // Set the Notification Channel for the Notification Manager.
            mNotificationManager.createNotificationChannel(mChannel);
        }
    }



    @SuppressWarnings("deprecation")
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.i(TAG, "Service started");
        boolean startedFromNotification = intent.getBooleanExtra(EXTRA_STARTED_FROM_NOTIFICATION,
                false);

        // We got here because the user decided to remove location updates from the notification.
        /*if (startedFromNotification) {
            removeLocationUpdates();
            stopSelf();
        }

         */
        // Tells the system to not try to recreate the service after it has been killed.
        return START_NOT_STICKY;
    }


    @SuppressWarnings("deprecation")
    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        mChangingConfiguration = true;
    }


    @SuppressWarnings("deprecation")
    @Override
    public IBinder onBind(Intent intent) {
        // Called when a client (MainActivity in case of this sample) comes to the foreground
        // and binds with this service. The service should cease to be a foreground service
        // when that happens.
        Log.i(TAG, "in onBind()");
        startForeground(NOTIFICATION_ID, getNotification());
        mChangingConfiguration = false;
        // Register Firestore when service will restart
        return mBinder;
    }


    @SuppressWarnings("deprecation")
    @Override
    public void onRebind(Intent intent) {
        // Called when a client (MainActivity in case of this sample) returns to the foreground
        // and binds once again with this service. The service should cease to be a foreground
        // service when that happens.
        Log.i(TAG, "in onRebind()");
        startForeground(NOTIFICATION_ID, getNotification());
        mChangingConfiguration = false;

        // Register Firestore when service will restart

        super.onRebind(intent);
    }


    @SuppressWarnings("deprecation")
    @Override
    public boolean onUnbind(Intent intent) {
        Log.i(TAG, "Last client unbound from service");

        // Called when the last client (MainActivity in case of this sample) unbinds from this
        // service. If this method is called due to a configuration change in MainActivity, we
        // do nothing. Otherwise, we make this service a foreground service.
        if (!mChangingConfiguration && LocationUtils.requestingLocationUpdates(this)) {
            Log.d(TAG, "Starting foreground service");
            /*
            // TODO(developer). If targeting O, use the following code.
            if (Build.VERSION.SDK_INT == Build.VERSION_CODES.O) {
                mNotificationManager.startServiceInForeground(new Intent(this,
                        LocationUpdatesService.class), NOTIFICATION_ID, getNotification());
            } else {
                startForeground(NOTIFICATION_ID, getNotification());
            }
             */
            startForeground(NOTIFICATION_ID, getNotification());

        }
        return true; // Ensures onRebind() is called when a client re-binds.
    }



    @SuppressWarnings("deprecation")
    @Override
    public void onDestroy() {
        stopForeground(true);
        mChangingConfiguration = false;
        mServiceHandler.removeCallbacksAndMessages(null);
    }

    /**
     * Makes a request for location updates. Note that in this sample we merely log the
     * {@link SecurityException}.
     */
    public void requestLocationUpdates(MapObserverListener mapObserverListener) {
        Log.i(TAG, "Requesting location updates");
        LocationUtils.setRequestingLocationUpdates(this, true);
        startService(new Intent(getApplicationContext(), LocationUpdatesService.class));
        try {
            mFusedLocationClient.requestLocationUpdates(mLocationRequest,
                    mLocationCallback, Looper.myLooper());
        } catch (SecurityException unlikely) {
            LocationUtils.setRequestingLocationUpdates(this, false);
            Log.d(TAG, "Lost location permission. Could not request updates. " + unlikely);
        }
        if(mapObserverListener != null) {
            this.mapObserverListener = mapObserverListener;
            this.mapObserverListener.onObservableCreated();
        }
    }

    /**
     * Removes location updates. Note that in this sample we merely log the
     * {@link SecurityException}.
     */
    public void removeLocationUpdates() {
        Log.i(TAG, "Removing location updates");
        Toast.makeText(this,  "Removing location updates", Toast.LENGTH_SHORT).show();
        try {
            mFusedLocationClient.removeLocationUpdates(mLocationCallback);
            LocationUtils.setRequestingLocationUpdates(this, false);
            mNotificationManager.cancelAll();
            stopSelf();
        } catch (SecurityException unlikely) {
            LocationUtils.setRequestingLocationUpdates(this, true);
            Log.d(TAG, "Lost location permission. Could not remove updates. " + unlikely);
        }
    }

    /**
     * Returns the {@link NotificationCompat} used as part of the foreground service.
     */
    private Notification getNotification() {
        Intent intent = new Intent(this, LocationUpdatesService.class);

        CharSequence text = LocationUtils.getLocationText(mLocation);
        // Extra to help us figure out if we arrived in onStartCommand via the notification or not.
        intent.putExtra(EXTRA_STARTED_FROM_NOTIFICATION, true);

        NotificationCompat.Builder builder = new NotificationCompat.Builder(this)
                .setContentTitle(getString(R.string.get_location_info))
                .setOngoing(true)
                .setPriority(Notification.PRIORITY_HIGH)
                .setSmallIcon(R.mipmap.ic_app_icon)
                .setTicker(text)
                .setWhen(System.currentTimeMillis());

        // Set the Channel ID for Android O.
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            builder.setChannelId(CHANNEL_ID); // Channel ID
        }
        return builder.build();
    }

    private void getLastLocation() {
        try {
            mFusedLocationClient.getLastLocation()
                    .addOnCompleteListener(task -> {
                        if (task.isSuccessful() && task.getResult() != null) {
                            mLocation = task.getResult();
                        } else {
                            Log.w(TAG, "Failed to get location.");
                        }
                    });
        } catch (SecurityException unlikely) {
            Log.d(TAG, "Lost location permission." + unlikely);
        }
    }

    private void onNewLocation(Location location) {
        Log.d(TAG, "New location: " + location);

        mLocation = location;

        // Notify anyone listening for broadcasts about the new location.
        Intent intent = new Intent(ACTION_BROADCAST);
        intent.putExtra(EXTRA_LOCATION, location);
        LocalBroadcastManager.getInstance(getApplicationContext()).sendBroadcast(intent);
        latitude = location.getLatitude();
        longitude = location.getLongitude();
        //mNotificationManager.notify(NOTIFICATION_ID, getNotification());

        // Update notification content if running as a foreground service.
        if (serviceIsRunningInForeground(this)) {
            //mNotificationManager.notify(NOTIFICATION_ID, getNotification());
            // Getting location when notification was call.

            // Here using to call Save to serverMethod

            SavetoServer("From foreground service ");
        }
    }

    /**
     * Sets the location request parameters.
     */
    private void createLocationRequest() {
        mLocationRequest = new LocationRequest();
        mLocationRequest.setInterval(UPDATE_INTERVAL_IN_MILLISECONDS);
        //mLocationRequest.setFastestInterval(FASTEST_UPDATE_INTERVAL_IN_MILLISECONDS);
        mLocationRequest.setPriority(LocationRequest.PRIORITY_HIGH_ACCURACY);
        mLocationRequest.setSmallestDisplacement(UPDATE_DISTANCE_IN_METERS);
    }

    /**
     * Class used for the client Binder.  Since this service runs in the same process as its
     * clients, we don't need to deal with IPC.
     */
    public class LocalBinder extends Binder {
        public LocationUpdatesService getService() {
            return LocationUpdatesService.this;
        }
    }

    /**
     * Returns true if this is a foreground service.
     *
     * @param context The {@link Context}.
     */
    public boolean serviceIsRunningInForeground(Context context) {
        ActivityManager manager = (ActivityManager) context.getSystemService(
                Context.ACTIVITY_SERVICE);
        for (ActivityManager.RunningServiceInfo service : manager.getRunningServices(
                Integer.MAX_VALUE)) {
            if (getClass().getName().equals(service.service.getClassName())) {
                if (service.foreground) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Save a value in realtime database when user in background
     * */

    private void saveTrakToLocal(Traking traking) {
        Database.getInstance(this).trakingDAO()
                .insertOne(traking);
    }

    private void SavetoServer(String msg){
        trakingList = new ArrayList<>();
        String ordreCode = App.database.trakingAlarmDAO().getCodeOrdreMissionActif();
        //Toast.makeText(this, ordreCode, Toast.LENGTH_SHORT).show();
        if (ordreCode != null) {
            Traking traking = new Traking(
                    StringUtils.createIDTrack(this),
                    latitude,
                    longitude,
                    null,
                    DateUtils.dateToStr(DateUtils.strToDate(new Date().toString(), "EEEE, dd MMMM yyyy HH:mm"), Globals.DATE_PATTERN),
                    App.prefUtils.getCaisseStationId(),
                    App.prefUtils.getUserId(),
                    App.prefUtils.getSessionCaisseId(),
                    "",
                    ordreCode
            );
            trakingList.add(traking);
            // saveTrakToLocal(traking);

            TrakingDataManager.getInstance().addTraking(
                    new GenericObject(new PrefUtils(this).getBaseConfig(), trakingList),
                    new RemoteCallback<Boolean>(this, false
                    ) {
                        @Override
                        public void onSuccess(Boolean response) {

                        }

                        @Override
                        public void onUnauthorized() {

                        }

                        @Override
                        public void onFailed(Throwable throwable) {
                            saveTrakToLocal(traking);
                        }
                    });
        }

    }
}
