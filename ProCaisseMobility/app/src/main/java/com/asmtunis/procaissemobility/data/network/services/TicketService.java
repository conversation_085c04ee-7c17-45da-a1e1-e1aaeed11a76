package com.asmtunis.procaissemobility.data.network.services;


import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Ticket;
import com.asmtunis.procaissemobility.data.models.TicketUpdate;
import com.asmtunis.procaissemobility.data.models.TicketWithLines;
import com.asmtunis.procaissemobility.data.models.TicketWithLinesAndPayments;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.Headers;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * Created by Achraf on 29/09/2017.
 */
public interface TicketService {

    @POST("getTicketsByCaisseId")
    Call<List<Ticket>> getTicketsByCaisseId(@Body GenericObject genericObject, @Query("ddm") String ddm, @Query("exercice") String exercice,
                                            @Query("archive") Boolean archive, @Query("zone") Boolean zone);

    @POST("getMaxNumTicket")
    Call<Integer> getMaxNumTicket(@Body GenericObject genericObject, @Query("id_exerc") String exercice, @Query("id_carnet") String carnet);

    @Headers("User-Agent: android-api-client")
    @POST("addTicket")
    Call<String> addTicket(@Body Ticket ticket);

    @Headers("User-Agent: android-api-client")
    @POST("addBatchTicketWithLignesTicketAndPayment")
    Call<List<TicketUpdate>> addBatchTicketWithLignesTicketAndPayment(@Body GenericObject ticketWithLinesAndPayments, @Query("auto_facture") boolean autoFacture);

}
