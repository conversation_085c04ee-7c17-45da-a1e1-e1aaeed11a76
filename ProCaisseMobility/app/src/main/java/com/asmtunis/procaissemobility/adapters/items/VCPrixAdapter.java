package com.asmtunis.procaissemobility.adapters.items;

import android.content.Context;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.Toolbar;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelStoreOwner;
import androidx.recyclerview.widget.RecyclerView;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.data.models.VCPrix;
import com.asmtunis.procaissemobility.data.viewModels.ArticleViewModel;
import com.asmtunis.procaissemobility.data.viewModels.VCListConcurrentViewModel;
import com.asmtunis.procaissemobility.data.viewModels.VCTypeCommunicationViewModel;
import com.asmtunis.procaissemobility.helper.utils.DateUtils;
import com.asmtunis.procaissemobility.listener.ItemCallback;
import com.asmtunis.procaissemobility.listener.MenuItemsAction;
import com.mikepenz.fastadapter.items.AbstractItem;

import java.util.List;

public class VCPrixAdapter extends AbstractItem<VCPrixAdapter,VCPrixAdapter.VCPrixViewHolder> {

    private Context context;
    public VCPrix vcPrix;
    protected ItemCallback itemCallback;
    MenuItemsAction menuItemsAction;
    ArticleViewModel articleViewModel ;
    VCTypeCommunicationViewModel vcTypeCommunicationViewModel;
    VCListConcurrentViewModel vcListConcurrentViewModel;

    public VCPrixAdapter(Context context, VCPrix vcPrix, ViewModelStoreOwner viewModelStoreOwner, ItemCallback itemCallback, MenuItemsAction menuItemsAction) {
        this.context = context;
        this.vcPrix = vcPrix;
        this.itemCallback = itemCallback;
        this.menuItemsAction = menuItemsAction;
        vcTypeCommunicationViewModel=new ViewModelProvider(viewModelStoreOwner).get(VCTypeCommunicationViewModel.class);
        vcListConcurrentViewModel=new ViewModelProvider(viewModelStoreOwner).get(VCListConcurrentViewModel.class);
        articleViewModel=new ViewModelProvider(viewModelStoreOwner).get(ArticleViewModel.class);

    }



    @NonNull
    @Override
    public VCPrixViewHolder getViewHolder(View v) {
        return new VCPrixViewHolder(v);
    }

    @Override
    public int getType() {
        return  R.id.fastadapter_ticket_item_id;
    }

    @Override
   // public int getLayoutRes() {return R.layout.vc_prix;}
    public int getLayoutRes() {return R.layout.ticket_item;}

    public class VCPrixViewHolder extends RecyclerView.ViewHolder{

        TextView code;
        TextView articleConcurrent;
        TextView article;
        TextView typeCommunication;
        TextView prixConcurrent;
        TextView date;
        ImageView tiketuserImV;

        LinearLayout footerLayout;
        public Toolbar toolbar;
        FrameLayout content;
        com.asmtunis.procaissemobility.ui.components.TicketView  ticketView;
        jp.shts.android.library.TriangleLabelView itemStatusLabel;

        public VCPrixViewHolder(@NonNull View itemView) {
            super(itemView);
            code = itemView.findViewById(R.id.code_prix);
            articleConcurrent= itemView.findViewById(R.id.article_concurrent_prix);
            article= itemView.findViewById(R.id.article_prix);
            typeCommunication =  itemView.findViewById(R.id.type_communication_prix);
            prixConcurrent =  itemView.findViewById(R.id.price_concurrent_prix);
            date   =  itemView.findViewById(R.id.date_promo_prix);




            ticketView = itemView.findViewById(R.id.layout_ticket);
            footerLayout = itemView.findViewById(R.id.footer_layout);
            toolbar = itemView.findViewById(R.id.toolbar);
            typeCommunication = itemView.findViewById(R.id.price);
            code = itemView.findViewById(R.id.ticketNumber);
            articleConcurrent = itemView.findViewById(R.id.ticketUser);
            date = itemView.findViewById(R.id.dateCreation);
            content = itemView.findViewById(R.id.content_layout);
            itemStatusLabel = itemView.findViewById(R.id.item_status_label);
            tiketuserImV = itemView.findViewById(R.id.tiketuserImV);
        }
    }

    @Override
    public void bindView(VCPrixViewHolder holder, List<Object> payloads) {
        super.bindView(holder, payloads);

        holder.code.setText(vcPrix.getCodeVCPrix());
        holder.tiketuserImV.setVisibility(View.INVISIBLE);
     //   holder.code.setText(vcPrix.getCodeVCPrixM());
        holder.articleConcurrent.setText("Article Concurrent : "+vcPrix.getArticleConcur());
      //  holder.article.setText(articleViewModel.getArticleByCode(vcPrix.getCodeArtLocal()).getaRTDesignation());
        holder.typeCommunication.setText(vcTypeCommunicationViewModel.getTypeCommunicationByCode(vcPrix.getCodeTypeCom()).toString());
//        holder.prixConcurrent.setText(String.valueOf(vcPrix.getPrixConcur()));
        holder.date.setText(vcPrix.getDateOp().replace(".000",""));

        if (itemCallback != null) {
            //   holder.itemView.setOnClickListener(v -> onViewClick(holder));

            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onViewClick(holder);
                }
            });
            holder.toolbar.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onViewClick(holder);
                }
            });
        }


        if(!vcPrix.isSync) {
          holder.date.setTextColor(context.getResources().getColor(R.color.warningColor));
            setTriangleView(holder.itemStatusLabel, 0);
        }
        else  {
            setTriangleView(holder.itemStatusLabel, -1);
            holder.date.setTextColor(context.getResources().getColor(R.color.successColor));
        }

    }

    void onViewClick(VCPrixViewHolder viewHolder) {
        if (vcPrix != null) {
            if (itemCallback == null) {
                return;
            } else {
                itemCallback.onItemClicked(viewHolder, vcPrix);
            }

        }
    }


    void setTriangleView(jp.shts.android.library.TriangleLabelView labelView, int status) {
        labelView.setVisibility(View.VISIBLE);
        switch (status) {
            case 0:
                labelView.setTriangleBackgroundColorResource(R.color.warningColor);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.notSync);
                labelView.setPrimaryTextColorResource(R.color.md_red_100);

                break;

            case 1:
                labelView.setTriangleBackgroundColorResource(R.color.md_green_800);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.new_label);
                labelView.setPrimaryTextColorResource(R.color.md_green_100);
                break;
            default:
                labelView.setVisibility(View.GONE);

                break;
        }

    }

}
