package com.asmtunis.procaissemobility.data.models;



import androidx.annotation.NonNull;
        import androidx.room.ColumnInfo;
        import androidx.room.Entity;
        import androidx.room.Ignore;
        import androidx.room.PrimaryKey;

        import com.google.gson.annotations.Expose;
        import com.google.gson.annotations.SerializedName;

        import java.io.Serializable;

/**
 * Created by Achraf on 26/09/2017.
 */
@Entity
public class Immobilisation implements Serializable {
    public Immobilisation(@NonNull String cLICode,
                          String cLINomPren,
                          Boolean cliImmo,
                          String cliImoCodeP,
                          String cliImoTypEmp,
                          String cliImoCB,
                          String tyEmpImNom) {
        this.cLICode = cLICode;
        this.cLINomPren = cLINomPren;
        this.cliImmo = cliImmo;
        this.cliImoCodeP = cliImoCodeP;
        this.cliImoTypEmp = cliImoTypEmp;
        this.cliImoCB = cliImoCB;
        this.tyEmpImNom = tyEmpImNom;
    }

    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "CLI_Code")
    @SerializedName("CLI_Code")
    @Expose
    public String cLICode;

    @ColumnInfo(name = "CLI_NomPren")
    @SerializedName("CLI_NomPren")
    @Expose
    public String cLINomPren;


    @ColumnInfo(name = "Clt_Immo")
    @SerializedName("Clt_Immo")
    @Expose
    private Boolean cliImmo;

    @ColumnInfo(name = "Clt_ImoCodeP")
    @SerializedName("Clt_ImoCodeP")
    @Expose
    private String cliImoCodeP;

    @ColumnInfo(name = "Clt_ImoTypEmp")
    @SerializedName("Clt_ImoTypEmp")
    @Expose
    private String cliImoTypEmp;

    @ColumnInfo(name = "Clt_ImoCB")
    @SerializedName("Clt_ImoCB")
    @Expose
    private String cliImoCB;

    @ColumnInfo(name = "TyEmpImNom")
    @SerializedName("TyEmpImNom")
    @Expose
    private String tyEmpImNom;


    public Boolean getCliImmo() {
        return cliImmo;
    }

    public void setCliImmo(Boolean cliImmo) {
        this.cliImmo = cliImmo;
    }

    public String getCliImoCodeP() {
        return cliImoCodeP;
    }

    public void setCliImoCodeP(String cliImoCodeP) {
        this.cliImoCodeP = cliImoCodeP;
    }

    public String getCliImoTypEmp() {
        return cliImoTypEmp;
    }

    public void setCliImoTypEmp(String cliImoTypEmp) {
        this.cliImoTypEmp = cliImoTypEmp;
    }

    public String getCliImoCB() {
        return cliImoCB;
    }

    public void setCliImoCB(String cliImoCB) {
        this.cliImoCB = cliImoCB;
    }

    public String getcLINomPren() {
        return cLINomPren;
    }

    public void setcLINomPren(String cLINomPren) {
        this.cLINomPren = cLINomPren;
    }

    public String getTyEmpImNom() {
        return tyEmpImNom;
    }

    public void setTyEmpImNom(String tyEmpImNom) {
        this.tyEmpImNom = tyEmpImNom;
    }



    public Immobilisation() {
        cLICode = "";
    }




    @NonNull
    public String getcLICode() {
        return cLICode;
    }


    public void setcLICode(@NonNull String cLICode) {
        this.cLICode = cLICode;
    }


    @NonNull
    @Override
    public String toString() {
        return "Client{" +
                "cLICode='" + cLICode + '\'' +
                ", cLINomPren='" + cLINomPren + '\'' +


                '}';
    }
}

