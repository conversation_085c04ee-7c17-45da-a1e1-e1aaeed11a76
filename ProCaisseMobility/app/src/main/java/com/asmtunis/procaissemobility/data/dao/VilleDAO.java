package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.Ville;

import java.util.List;

/**
 * Created by Oussama AZIZI on 3/22/22.
 */

@Dao
public interface VilleDAO {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertOne(Ville ville);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<Ville> villes);

    @Query("SELECT * FROM Ville")
    List<Ville> getVilles();

    @Query("Delete from Ville")
    void deleteAll();

    @Query("select count(*) from Ville")
    LiveData<Integer> count();

    @Delete
    void deleteOne(Ville traking);
}
