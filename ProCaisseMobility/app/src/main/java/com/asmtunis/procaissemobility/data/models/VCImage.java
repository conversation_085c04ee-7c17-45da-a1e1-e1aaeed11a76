package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * Created by Oussama AZIZI on 6/24/22.
 */

@Entity
public class VCImage extends BaseModel{

    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "Code_IMG")
    @SerializedName("Code_IMG")
    @Expose
    private String codeIMG;

    @ColumnInfo(name = "Code_Mob")
    @SerializedName("Code_Mob")
    @Expose
    private String codeMob;

    @ColumnInfo(name = "Type_VC")
    @SerializedName("Type_VC")
    @Expose
    private String typeVC;

    @ColumnInfo(name = "DateOp")
    @SerializedName("DateOp")
    @Expose
    private String dateOp;

    @ColumnInfo(name = "Chemin_Img")
    @SerializedName("Chemin_Img")
    @Expose
    private String cheminImg;

    @ColumnInfo(name = "Code_TypeVC")
    @SerializedName("Code_TypeVC")
    @Expose
    private String codeTypeVC;

    @ColumnInfo(name = "Image")
    @SerializedName("Image")
    @Expose
    private String image;

    @ColumnInfo(name = "CodeUser")
    @SerializedName("CodeUser")
    @Expose
    private Integer codeUser;


    @ColumnInfo(name = "imgUrl")
    @SerializedName("imgUrl")
    @Expose
    private String imgUrl;

    public String getCodeIMG() {
        return codeIMG;
    }

    public void setCodeIMG(String codeIMG) {
        this.codeIMG = codeIMG;
    }

    public String getCodeMob() {
        return codeMob;
    }

    public void setCodeMob(String codeMob) {
        this.codeMob = codeMob;
    }

    public String getTypeVC() {
        return typeVC;
    }

    public void setTypeVC(String typeVC) {
        this.typeVC = typeVC;
    }

    public String getDateOp() {
        return dateOp;
    }

    public void setDateOp(String dateOp) {
        this.dateOp = dateOp;
    }

    public String getCheminImg() {
        return cheminImg;
    }

    public void setCheminImg(String cheminImg) {
        this.cheminImg = cheminImg;
    }

    public String getCodeTypeVC() {
        return codeTypeVC;
    }

    public void setCodeTypeVC(String codeTypeVC) {
        this.codeTypeVC = codeTypeVC;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public Integer getCodeUser() {
        return codeUser;
    }

    public void setCodeUser(Integer codeUser) {
        this.codeUser = codeUser;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public VCImage(@NonNull String codeIMG, String codeMob, String typeVC, String dateOp, String codeTypeVC, String image, Integer codeUser) {
        this.codeIMG = codeIMG;
        this.codeMob = codeMob;
        this.typeVC = typeVC;
        this.dateOp = dateOp;
        this.codeTypeVC = codeTypeVC;
        this.image = image;
        this.codeUser = codeUser;
    }
}
