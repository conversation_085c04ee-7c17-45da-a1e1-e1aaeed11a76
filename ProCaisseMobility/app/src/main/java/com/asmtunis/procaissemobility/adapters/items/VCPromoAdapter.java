package com.asmtunis.procaissemobility.adapters.items;

import android.content.Context;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.Toolbar;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelStoreOwner;
import androidx.recyclerview.widget.RecyclerView;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.data.models.VCPromo;
import com.asmtunis.procaissemobility.data.viewModels.ArticleViewModel;
import com.asmtunis.procaissemobility.data.viewModels.VCListConcurrentViewModel;
import com.asmtunis.procaissemobility.data.viewModels.VCTypeCommunicationViewModel;
import com.asmtunis.procaissemobility.helper.utils.DateUtils;
import com.asmtunis.procaissemobility.listener.ItemCallback;
import com.asmtunis.procaissemobility.listener.MenuItemsAction;
import com.mikepenz.fastadapter.items.AbstractItem;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

public class VCPromoAdapter extends AbstractItem<VCNouveauProduitAdapter, VCPromoAdapter.VCPromoViewHolder> {

    private Context context;
    public VCPromo vcPromo;
    protected ItemCallback itemCallback;
    MenuItemsAction menuItemsAction;
    ArticleViewModel articleViewModel ;
    VCListConcurrentViewModel vcListConcurrentViewModel;
    VCTypeCommunicationViewModel vcTypeCommunicationViewModel;
    public VCPromoAdapter(Context context, ViewModelStoreOwner viewModelStoreOwner, VCPromo vcPromo, ItemCallback itemCallback, MenuItemsAction menuItemsAction) {
        this.context = context;
        this.vcPromo = vcPromo;
        this.itemCallback = itemCallback;
        this.menuItemsAction = menuItemsAction;
        articleViewModel  = new ViewModelProvider(viewModelStoreOwner).get(ArticleViewModel.class);
        vcListConcurrentViewModel=new ViewModelProvider(viewModelStoreOwner).get(VCListConcurrentViewModel.class);
        vcTypeCommunicationViewModel=new ViewModelProvider(viewModelStoreOwner).get(VCTypeCommunicationViewModel.class);
    }

    @Override
    public int getType() {
        return R.id.fastadapter_ticket_item_id;
    }

    @Override
   // public int getLayoutRes() {return R.layout.vc_promo;}
    public int getLayoutRes() {return R.layout.ticket_item;}
    @Override
    public void bindView(VCPromoViewHolder holder, List<Object> payloads) {
        super.bindView(holder, payloads);
        holder.codeTV.setText(vcPromo.getCodeVCPromo());
        holder.articleConcurrentTV.setText("Article Concurrent : "+vcPromo.getArticleConcur());
        holder.tiketuserImV.setVisibility(View.INVISIBLE);
//        holder.articleTV.setText(articleViewModel.getArticleByCode(vcPromo.getCodeArtLocal()).getaRTDesignation());
        holder.typeCommunication.setText(vcTypeCommunicationViewModel.getTypeCommunicationByCode(vcPromo.getCodeTypeCom()));
//        holder.priceConcurrentTV.setText(vcPromo.getPrixConcur().toString());
     //   holder.totalPromoTV.setText(vcPromo.getTauxPromo().toString());

        holder.datePromoTV.setText(vcPromo.getDateOp().replace(".000",""));



        if (itemCallback != null) {
          //  holder.itemView.setOnClickListener(v -> onViewClick(holder));

            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onViewClick(holder);
                }
            });
            holder.toolbar.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onViewClick(holder);
                }
            });
        }


        if(!vcPromo.isSync) {
            holder.datePromoTV.setTextColor(context.getResources().getColor(R.color.warningColor));
            setTriangleView(holder.itemStatusLabel, 0);
        }
        else  {
            setTriangleView(holder.itemStatusLabel, -1);
            holder.datePromoTV.setTextColor(context.getResources().getColor(R.color.successColor));
        }

    }


    @NonNull
    @Override
    public VCPromoViewHolder getViewHolder(View v) {
        return new VCPromoViewHolder(v);
    }

    public class VCPromoViewHolder extends RecyclerView.ViewHolder {
        TextView codeTV;
        TextView articleConcurrentTV;
        TextView articleTV;
        TextView priceConcurrentTV;
        TextView typeCommunication;
        TextView totalPromoTV;
        TextView datePromoTV;
        ImageView tiketuserImV;

        LinearLayout footerLayout;
        public Toolbar toolbar;
        FrameLayout content;
        com.asmtunis.procaissemobility.ui.components.TicketView  ticketView;
        jp.shts.android.library.TriangleLabelView itemStatusLabel;


        public VCPromoViewHolder(@NonNull View itemView) {
            super(itemView);
            codeTV = itemView.findViewById(R.id.code);
            articleConcurrentTV = itemView.findViewById(R.id.article_concurrent);
            articleTV = itemView.findViewById(R.id.article);
            typeCommunication=itemView.findViewById(R.id.type_communication_promo);
            priceConcurrentTV = itemView.findViewById(R.id.price_concurrent);
            totalPromoTV = itemView.findViewById(R.id.total_promo);
            datePromoTV = itemView.findViewById(R.id.date_promo);


            ticketView = itemView.findViewById(R.id.layout_ticket);
            footerLayout = itemView.findViewById(R.id.footer_layout);
            toolbar = itemView.findViewById(R.id.toolbar);
            typeCommunication = itemView.findViewById(R.id.price);
            codeTV = itemView.findViewById(R.id.ticketNumber);
            articleConcurrentTV = itemView.findViewById(R.id.ticketUser);
            datePromoTV = itemView.findViewById(R.id.dateCreation);
            content = itemView.findViewById(R.id.content_layout);
            itemStatusLabel = itemView.findViewById(R.id.item_status_label);
            tiketuserImV = itemView.findViewById(R.id.tiketuserImV);
        }
    }

    void onViewClick(VCPromoViewHolder viewHolder) {
        if (vcPromo != null) {
            if (itemCallback == null) {
                return;
            } else {
                itemCallback.onItemClicked(viewHolder, vcPromo);
            }

        }
    }



    void setTriangleView(jp.shts.android.library.TriangleLabelView labelView, int status) {
        labelView.setVisibility(View.VISIBLE);
        switch (status) {
            case 0:
                labelView.setTriangleBackgroundColorResource(R.color.warningColor);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.notSync);
                labelView.setPrimaryTextColorResource(R.color.md_red_100);

                break;

            case 1:
                labelView.setTriangleBackgroundColorResource(R.color.md_green_800);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.new_label);
                labelView.setPrimaryTextColorResource(R.color.md_green_100);
                break;
            default:
                labelView.setVisibility(View.GONE);

                break;
        }

    }

}
