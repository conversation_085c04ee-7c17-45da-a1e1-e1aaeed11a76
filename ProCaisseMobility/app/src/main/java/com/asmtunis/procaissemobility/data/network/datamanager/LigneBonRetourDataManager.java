package com.asmtunis.procaissemobility.data.network.datamanager;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.LigneBonRetour;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.LigneBonRetourService;

import java.util.List;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

public class LigneBonRetourDataManager {
    private static LigneBonRetourDataManager sInstance;
    private final LigneBonRetourService bonRetourService;

    public LigneBonRetourDataManager() {

        bonRetourService = new ServiceFactory<>(LigneBonRetourService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"LigneRetour")).makeService();
    }

    public static LigneBonRetourDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new LigneBonRetourDataManager();
        }
        return sInstance;
    }

    public void getLigneBonRetours(GenericObject genericObject,
                              RemoteCallback<List<LigneBonRetour>> listener) {
        bonRetourService.getLigneBonRetours(genericObject)
                .enqueue(listener);
    }

    public void addBatchLigneBonRetours(GenericObject genericObject,
                                    RemoteCallback<List<LigneBonRetour>> listener) {
        bonRetourService.addBatchLigneBonRetours(genericObject)
                .enqueue(listener);
    }
}

