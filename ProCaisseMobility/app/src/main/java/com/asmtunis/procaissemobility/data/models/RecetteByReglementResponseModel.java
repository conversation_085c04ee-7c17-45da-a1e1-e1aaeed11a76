package com.asmtunis.procaissemobility.data.models;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * Created by WAEL on 9/8/22.
 */
public class RecetteByReglementResponseModel {

    @SerializedName("Mnt_Espece")
    @Expose
    private String mntEspece;
    @SerializedName("Mnt_Carte_Bancaire")
    @Expose
    private String mntCarteBancaire;
    @SerializedName("Mnt_Cheque")
    @Expose
    private String mntCheque;
    @SerializedName("Mnt_Traite")
    @Expose
    private String mntTraite;
    @SerializedName("TotalRecette")
    @Expose
    private String totalRecette;
    @SerializedName("Mnt_Bonus")
    @Expose
    private String mntBonus;
    @SerializedName("Fond_Caisse")
    @Expose
    private String fondCaisse;
    @SerializedName("Dep_Caisse")
    @Expose
    private String depCaisse;
    @SerializedName("MntCarte_prepayee")
    @Expose
    private String mntCartePrepayee;
    @SerializedName("Mnt_PointMerci")
    @Expose
    private String mntPointMerci;
    @SerializedName("MntBonAchat")
    @Expose
    private String mntBonAchat;
    @SerializedName("TotalCaisse")
    @Expose
    private String totalCaisse;

    public String getMntEspece() {
        return mntEspece;
    }

    public void setMntEspece(String mntEspece) {
        this.mntEspece = mntEspece;
    }

    public String getMntCarteBancaire() {
        return mntCarteBancaire;
    }

    public void setMntCarteBancaire(String mntCarteBancaire) {
        this.mntCarteBancaire = mntCarteBancaire;
    }

    public String getMntCheque() {
        return mntCheque;
    }

    public void setMntCheque(String mntCheque) {
        this.mntCheque = mntCheque;
    }

    public String getMntTraite() {
        return mntTraite;
    }

    public void setMntTraite(String mntTraite) {
        this.mntTraite = mntTraite;
    }

    public String getTotalRecette() {
        return totalRecette;
    }

    public void setTotalRecette(String totalRecette) {
        this.totalRecette = totalRecette;
    }

    public String getMntBonus() {
        return mntBonus;
    }

    public void setMntBonus(String mntBonus) {
        this.mntBonus = mntBonus;
    }

    public String getFondCaisse() {
        return fondCaisse;
    }

    public void setFondCaisse(String fondCaisse) {
        this.fondCaisse = fondCaisse;
    }

    public String getDepCaisse() {
        return depCaisse;
    }

    public void setDepCaisse(String depCaisse) {
        this.depCaisse = depCaisse;
    }

    public String getMntCartePrepayee() {
        return mntCartePrepayee;
    }

    public void setMntCartePrepayee(String mntCartePrepayee) {
        this.mntCartePrepayee = mntCartePrepayee;
    }

    public String getMntPointMerci() {
        return mntPointMerci;
    }

    public void setMntPointMerci(String mntPointMerci) {
        this.mntPointMerci = mntPointMerci;
    }

    public String getMntBonAchat() {
        return mntBonAchat;
    }

    public void setMntBonAchat(String mntBonAchat) {
        this.mntBonAchat = mntBonAchat;
    }

    public String getTotalCaisse() {
        return totalCaisse;
    }

    public void setTotalCaisse(String totalCaisse) {
        this.totalCaisse = totalCaisse;
    }
}
