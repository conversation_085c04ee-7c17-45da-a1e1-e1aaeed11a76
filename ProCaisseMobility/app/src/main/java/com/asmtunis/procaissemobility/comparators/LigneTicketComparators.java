package com.asmtunis.procaissemobility.comparators;

import com.asmtunis.procaissemobility.data.models.LigneTicket;

import java.util.Comparator;

/**
 * Created by me on 10/8/2017.
 */


public final class LigneTicketComparators {

    private LigneTicketComparators() {
    }
    public static Comparator<LigneTicket> getLigneTicketQuantityComparator() {
        return new LigneTicketQuantityComparator();
    }

    public static Comparator<LigneTicket> getLigneTicketDiscountComparator() {
        return new LigneTicketDiscountComparator();
    }

    public static Comparator<LigneTicket> getLigneTicketNameComparator() {
        return new LigneTicketNameComparator();
    }

    public static Comparator<LigneTicket> getLigneTicketUnitPriceComparator() {
        return new LigneTicketUnitPriceComparator();
    }

    public static Comparator<LigneTicket> getLigneTicketPriceComparator() {
        return new LigneTicketPriceComparator();
    }


    private static class LigneTicketNameComparator implements Comparator<LigneTicket> {

        @Override
        public int compare(final LigneTicket ligneTicket1, final LigneTicket ligneTicket2) {
            return ligneTicket1.getArticle().getaRTDesignation().toLowerCase().compareTo(ligneTicket2.getArticle().getaRTDesignation().toLowerCase());
        }
    }


    private static class LigneTicketQuantityComparator implements Comparator<LigneTicket> {

        @Override
        public int compare(final LigneTicket ligneTicket1, final LigneTicket ligneTicket2) {
            if (ligneTicket1.getlTQte() < ligneTicket2.getlTQte()) return -1;
            if (ligneTicket1.getlTQte() > ligneTicket2.getlTQte()) return 1;
            return 0;
        }
    }

    private static class LigneTicketPriceComparator implements Comparator<LigneTicket> {

        @Override
        public int compare(final LigneTicket ligneTicket1, final LigneTicket ligneTicket2) {
            if (ligneTicket1.getlTMtTTC() < ligneTicket2.getlTMtTTC()) return -1;
            if (ligneTicket1.getlTMtTTC() > ligneTicket2.getlTMtTTC()) return 1;
            return 0;
        }
    }

    private static class LigneTicketDiscountComparator implements Comparator<LigneTicket> {

        @Override
        public int compare(final LigneTicket ligneTicket1, final LigneTicket ligneTicket2) {
            if (ligneTicket1.getlTTauxRemise() < ligneTicket2.getlTTauxRemise()) return -1;
            if (ligneTicket1.getlTTauxRemise() > ligneTicket2.getlTTauxRemise()) return 1;
            return 0;
        }
    }

    private static class LigneTicketUnitPriceComparator implements Comparator<LigneTicket> {

        @Override
        public int compare(final LigneTicket ligneTicket1, final LigneTicket ligneTicket2) {
            if (ligneTicket1.getArticle().getaRTPrixUnitaireHT() < ligneTicket2.getArticle().getaRTPrixUnitaireHT())
                return -1;
            if (ligneTicket1.getArticle().getaRTPrixUnitaireHT() > ligneTicket2.getArticle().getaRTPrixUnitaireHT())
                return 1;
            return 0;
        }
    }

}