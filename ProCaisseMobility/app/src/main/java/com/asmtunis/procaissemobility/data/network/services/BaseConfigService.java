package com.asmtunis.procaissemobility.data.network.services;


import static com.asmtunis.procaissemobility.helper.Globals.GET_BASE_CONFIG_URL;

import com.asmtunis.procaissemobility.data.models.Connexion;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;
import retrofit2.http.Path;

/**
 * Created by Achraf on 28/09/2017.
 */

public interface BaseConfigService {
    @POST("selection_base_config.php")
    @FormUrlEncoded
    Call<List<Connexion>> getBaseConfig(@Field("id_device") String id_device, @Field("produit") String produit);

    @POST("{id}")
    @FormUrlEncoded
    Call<List<Connexion>> getBaseConfigBackUp(@Path("id") String postId, @Field("id_device") String id_device, @Field("produit") String produit);


    @POST("getBaseConfigsById")
    Call<Connexion> getBaseConfigsById(@Body Connexion connexion);
}

