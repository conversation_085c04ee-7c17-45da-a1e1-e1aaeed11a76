package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

@Entity(primaryKeys = {"LG_VISNum","LG_VISExerc","LG_VISFamille","LG_VISTier"})
public class DN_LigneVisite extends BaseModel implements Serializable {


    public DN_LigneVisite() {
    }


    public DN_LigneVisite(
            @NonNull String lgVISNum,
            @NonNull String lgVISExerc,
            @NonNull String lgVISFamille,
            @NonNull String lgVISTier,
            String lgVISInfo1,
            String lgVISInfo2,
            String lgVISInfo3,
            String lgVISDDM) {
        this.lgVISNum = lgVISNum;
        this.lgVISDDM = lgVISDDM;
        this.lgVISFamille = lgVISFamille;
        this.lgVISExerc = lgVISExerc;
        this.lgVISInfo3 = lgVISInfo3;
        this.lgVISInfo2 = lgVISInfo2;
        this.lgVISInfo1 = lgVISInfo1;
        this.lgVISTier = lgVISTier;

    }



    @NonNull
    public String getLgVISNum() {
        return lgVISNum;
    }

    public void setLgVISNum(@NonNull String lgVISNum) {
        this.lgVISNum = lgVISNum;
    }

    @NonNull
    public String getLgVISFamille() {
        return lgVISFamille;
    }

    public void setLgVISFamille(@NonNull String lgVISFamille) {
        this.lgVISFamille = lgVISFamille;
    }

    @NonNull
    public String getLgVISExerc() {
        return lgVISExerc;
    }

    public void setLgVISExerc(@NonNull String lgVISExerc) {
        this.lgVISExerc = lgVISExerc;
    }

    @NonNull
    public String getLgVISTier() {
        return lgVISTier;
    }

    public void setLgVISTier(@NonNull String lgVISTier) {
        this.lgVISTier = lgVISTier;
    }

    public String getLgVISInfo1() {
        return lgVISInfo1;
    }

    public void setLgVISInfo1(String lgVISInfo1) {
        this.lgVISInfo1 = lgVISInfo1;
    }

    public String getLgVISInfo2() {
        return lgVISInfo2;
    }

    public void setLgVISInfo2(String lgVISInfo2) {
        this.lgVISInfo2 = lgVISInfo2;
    }

    public String getLgVISInfo3() {
        return lgVISInfo3;
    }

    public void setLgVISInfo3(String lgVISInfo3) {
        this.lgVISInfo3 = lgVISInfo3;
    }

    public String getLgVISDDM() {
        return lgVISDDM;
    }

    public void setLgVISDDM(String lgVISDDM) {
        this.lgVISDDM = lgVISDDM;
    }


    @NonNull
    @ColumnInfo(name = "LG_VISNum")
    @SerializedName("LG_VISNum")
    @Expose
    private String lgVISNum ="";

    @NonNull
    @ColumnInfo(name = "LG_VISFamille")
    @SerializedName("LG_VISFamille")
    @Expose
    private String lgVISFamille ="";


   /* public String getLgVISNumM() {
        return lgVISNumM;
    }

    public void setLgVISNumM(String lgVISNumM) {
        this.lgVISNumM = lgVISNumM;
    }

    @ColumnInfo(name = "LG_VISNumM")
    @SerializedName("LG_VISNumM")
    @Expose
    private String lgVISNumM;*/


    @NonNull
    @ColumnInfo(name = "LG_VISExerc")
    @SerializedName("LG_VISExerc")
    @Expose
    private String lgVISExerc ="";

    @NonNull
    @ColumnInfo(name = "LG_VISTier")
    @SerializedName("LG_VISTier")
    @Expose
    private String lgVISTier  ="";


    @ColumnInfo(name = "LG_VISInfo1")
    @SerializedName("LG_VISInfo1")
    @Expose
    private String lgVISInfo1;


    @ColumnInfo(name = "LG_VISInfo2")
    @SerializedName("LG_VISInfo2")
    @Expose
    private String lgVISInfo2;


    @ColumnInfo(name = "LG_VISInfo3")
    @SerializedName("LG_VISInfo3")
    @Expose
    private String lgVISInfo3;


    @ColumnInfo(name = "LG_VISDDM")
    @SerializedName("LG_VISDDM")
    @Expose
    private String lgVISDDM;
}
