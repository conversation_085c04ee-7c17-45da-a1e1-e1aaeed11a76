package com.asmtunis.procaissemobility.data.network.datamanager;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.BonCommandResponse;
import com.asmtunis.procaissemobility.data.models.BonCommande;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.InvPatBatchResponse;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.BonCommandeService;

import java.util.List;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

public class BonCommandeDataManager {
    private static BonCommandeDataManager sInstance;
    private final BonCommandeService bonCommandeService;

    public BonCommandeDataManager() {

        bonCommandeService = new ServiceFactory<>(BonCommandeService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Commande")).makeService();
    }

    public static BonCommandeDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new BonCommandeDataManager();
        }
        return sInstance;
    }

    public void getBonCommande(GenericObject genericObject,
                               RemoteCallback<List<BonCommande>> listener) {
        bonCommandeService.getBonCommandes(genericObject)
                .enqueue(listener);
    }

    public void addBatchBonCommande(GenericObject commandes,
                               RemoteCallback<InvPatBatchResponse> listener) {
        bonCommandeService.addBatchBonCommande(commandes)
                .enqueue(listener);
    }


    public void addBatchInvPat(GenericObject commandes,
                                    RemoteCallback<InvPatBatchResponse> listener) {
        bonCommandeService.addBatchinvPat(commandes)
                .enqueue(listener);
    }


    public void controlInventair(GenericObject commandes,
                                    RemoteCallback<BonCommandResponse> listener) {
        bonCommandeService.controlInventaire(commandes)
                .enqueue(listener);
    }

}
