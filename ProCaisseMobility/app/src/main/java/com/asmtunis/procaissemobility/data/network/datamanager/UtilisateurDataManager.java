package com.asmtunis.procaissemobility.data.network.datamanager;


import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Utilisateur;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.UtilisateurService;
import com.asmtunis.procaissemobility.helper.utils.Utils;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

/**
 * Created by Achraf on 25/09/2017.
 */

public class UtilisateurDataManager  {

    private static UtilisateurDataManager sInstance;

    private final UtilisateurService mUtilisateurService;

    public UtilisateurDataManager() {
        mUtilisateurService = new ServiceFactory<>(UtilisateurService.class, String.format(Utils.validateBaseUrl(), App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Utilisateur")).makeService();

    }

    public static UtilisateurDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new UtilisateurDataManager();
        }
        return sInstance;
    }
    public void authentification(GenericObject utilisateur,
                                RemoteCallback<Utilisateur> listener) {

        mUtilisateurService.authentification(utilisateur)
                .enqueue(listener);
    }


}