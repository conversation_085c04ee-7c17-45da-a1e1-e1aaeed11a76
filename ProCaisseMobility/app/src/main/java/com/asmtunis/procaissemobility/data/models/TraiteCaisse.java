package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;


@Entity(primaryKeys =
        {"TRAIT_Num", "TRAIT_Ordre", "TRAIT_Reglement", "TRAIT_IdSession", "TRAIT_Exercice"})

public class TraiteCaisse extends BaseModel implements Serializable {
    @NonNull
    @ColumnInfo(name = "TRAIT_Num")
    @SerializedName("TRAIT_Num")
    @Expose
    public String tRAITNum;
    @NonNull
    @ColumnInfo(name = "TRAIT_Ordre")
    @SerializedName("TRAIT_Ordre")
    @Expose
    public int tRAITOrdre;

    public String gettRAITNUM_M() {
        return tRAITNUM_M;
    }

    public void settRAITNUM_M(String tRAITNUM_M) {
        this.tRAITNUM_M = tRAITNUM_M;
    }

    @ColumnInfo(name = "TRAIT_Num_M")
    @SerializedName("TRAIT_Num_M")
    @Expose
    public String tRAITNUM_M;


    @NonNull
    @ColumnInfo(name = "TRAIT_Reglement")
    @SerializedName("TRAIT_Reglement")
    @Expose
    public String tRAITReglement;
    @NonNull
    @ColumnInfo(name = "TRAIT_IdSession")
    @SerializedName("TRAIT_IdSession")
    @Expose
    public String tRAITIdSession;
    @NonNull
    @ColumnInfo(name = "TRAIT_Exercice")
    @SerializedName("TRAIT_Exercice")
    @Expose
    public String tRAITExercice;
    @ColumnInfo(name = "TRAIT_Echeance")
    @SerializedName("TRAIT_Echeance")
    @Expose
    public String tRAITEcheance;
    @ColumnInfo(name = "TRAIT_Client")
    @SerializedName("TRAIT_Client")
    @Expose
    public String tRAITClient;
    @ColumnInfo(name = "TRAIT_Montant")
    @SerializedName("TRAIT_Montant")
    @Expose
    public double tRAITMontant;
    @ColumnInfo(name = "TRAIT_type")
    @SerializedName("TRAIT_type")
    @Expose
    public String tRAITType;
    @ColumnInfo(name = "TRAIT_Compte_local")
    @SerializedName("TRAIT_Compte_local")
    @Expose
    public String tRAITCompteLocal;

    @Ignore
    public transient CarteResto carteResto;

    public CarteResto getCarteResto() {
        return carteResto;
    }

    public void setCarteResto(CarteResto carteResto) {
        this.carteResto = carteResto;
    }

    public TraiteCaisse() {
    }

    @Ignore
    public TraiteCaisse(String tRAITNum, int tRAITOrdre, String tRAITReglement, String tRAITIdSession, String tRAITExercice, String tRAITEcheance, String tRAITClient, double tRAITMontant, String tRAITType, String tRAITCompteLocal) {
        this.tRAITNum = tRAITNum;
        this.tRAITOrdre = tRAITOrdre;
        this.tRAITReglement = tRAITReglement;
        this.tRAITIdSession = tRAITIdSession;
        this.tRAITExercice = tRAITExercice;
        this.tRAITEcheance = tRAITEcheance;
        this.tRAITClient = tRAITClient;
        this.tRAITMontant = tRAITMontant;
        this.tRAITType = tRAITType;
        this.tRAITCompteLocal = tRAITCompteLocal;



    }

    @Ignore
    public TraiteCaisse(String tRAITNum, int tRAITOrdre, String tRAITReglement, String tRAITIdSession, String tRAITExercice, String tRAITEcheance, String tRAITClient, double tRAITMontant, String tRAITType, String tRAITCompteLocal, CarteResto carteResto) {
        this.tRAITNum = tRAITNum;
        this.tRAITOrdre = tRAITOrdre;
        this.tRAITReglement = tRAITReglement;
        this.tRAITIdSession = tRAITIdSession;
        this.tRAITExercice = tRAITExercice;
        this.tRAITEcheance = tRAITEcheance;
        this.tRAITClient = tRAITClient;
        this.tRAITMontant = tRAITMontant;
        this.tRAITType = tRAITType;
        this.tRAITCompteLocal = tRAITCompteLocal;
     //   this.carteResto = carteResto;


    }

    @Ignore
    public TraiteCaisse(int tRAITOrdre, String tRAITIdSession, String tRAITExercice, String tRAITEcheance, String tRAITClient, double tRAITMontant/*, CarteResto carteResto*/, String tRAITNUM_M) {
        this.tRAITNum = "tRAITNum";
        this.tRAITOrdre = tRAITOrdre;
        this.tRAITReglement = "tRAITReglement";
        this.tRAITIdSession = tRAITIdSession;
        this.tRAITExercice = tRAITExercice;
        this.tRAITEcheance = tRAITEcheance;
        this.tRAITClient = tRAITClient;
        this.tRAITMontant = tRAITMontant;
      /*  if (carteResto != null){
            this.tRAITType = carteResto.getSociete();
            this.tRAITCompteLocal = carteResto.getCode();
            this.carteResto = carteResto;
        }
*/


        this.tRAITNUM_M = tRAITNUM_M;

    }


    @Ignore
    public TraiteCaisse(int tRAITOrdre, String tRAITIdSession, String tRAITExercice, String tRAITEcheance, String tRAITClient, double tRAITMontant, CarteResto carteResto) {
        this.tRAITNum = "tRAITNum";
        this.tRAITOrdre = tRAITOrdre;
        this.tRAITReglement = "tRAITReglement";
        this.tRAITIdSession = tRAITIdSession;
        this.tRAITExercice = tRAITExercice;
        this.tRAITEcheance = tRAITEcheance;
        this.tRAITClient = tRAITClient;
        this.tRAITMontant = tRAITMontant;
       if (carteResto != null){
            this.tRAITType = carteResto.getSociete();
            this.tRAITCompteLocal = carteResto.getCode();
            this.carteResto = carteResto;
        }




    }


    public String gettRAITNum() {
        return tRAITNum;
    }

    public void settRAITNum(String tRAITNum) {
        this.tRAITNum = tRAITNum;
    }

    public int gettRAITOrdre() {
        return tRAITOrdre;
    }

    public void settRAITOrdre(int tRAITOrdre) {
        this.tRAITOrdre = tRAITOrdre;
    }

    public String gettRAITReglement() {
        return tRAITReglement;
    }

    public void settRAITReglement(String tRAITReglement) {
        this.tRAITReglement = tRAITReglement;
    }

    public String gettRAITIdSession() {
        return tRAITIdSession;
    }

    public void settRAITIdSession(String tRAITIdSession) {
        this.tRAITIdSession = tRAITIdSession;
    }

    public String gettRAITExercice() {
        return tRAITExercice;
    }

    public void settRAITExercice(String tRAITExercice) {
        this.tRAITExercice = tRAITExercice;
    }

    public String gettRAITEcheance() {
        return tRAITEcheance;
    }

    public void settRAITEcheance(String tRAITEcheance) {
        this.tRAITEcheance = tRAITEcheance;
    }

    public String gettRAITClient() {
        return tRAITClient;
    }

    public void settRAITClient(String tRAITClient) {
        this.tRAITClient = tRAITClient;
    }

    public double gettRAITMontant() {
        return tRAITMontant;
    }

    public void settRAITMontant(double tRAITMontant) {
        this.tRAITMontant = tRAITMontant;
    }

    public String gettRAITType() {
        return tRAITType;
    }

    public void settRAITType(String tRAITType) {
        this.tRAITType = tRAITType;
    }

    public String gettRAITCompteLocal() {
        return tRAITCompteLocal;
    }

    public void settRAITCompteLocal(String tRAITCompteLocal) {
        this.tRAITCompteLocal = tRAITCompteLocal;
    }

  /*  public CarteResto getCarteResto() {
        return carteResto;
    }

    public void setCarteResto(CarteResto carteResto) {
        this.carteResto = carteResto;
    }*/

    @Override
    public String toString() {
        return "TraiteCaisse{" +
                "tRAITNum='" + tRAITNum + '\'' +
                ", tRAITOrdre=" + tRAITOrdre +
                ", tRAITReglement='" + tRAITReglement + '\'' +
                ", tRAITIdSession='" + tRAITIdSession + '\'' +
                ", tRAITExercice='" + tRAITExercice + '\'' +
                ", tRAITEcheance='" + tRAITEcheance + '\'' +
                ", tRAITClient='" + tRAITClient + '\'' +
                ", tRAITMontant=" + tRAITMontant +
                ", tRAITType='" + tRAITType + '\'' +
                ", tRAITCompteLocal='" + tRAITCompteLocal + '\'' +
              //  ", carteResto=" + carteResto+ '\'' +

                ", status=" + status +
                ", isSync=" + isSync +
                ", rEGC_Code_M=" + tRAITNUM_M +
                '}';
    }
}

