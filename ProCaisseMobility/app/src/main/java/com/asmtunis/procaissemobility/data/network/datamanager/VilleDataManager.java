package com.asmtunis.procaissemobility.data.network.datamanager;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Article;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Ville;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.VilleService;

import java.util.List;

/**
 * Created by Oussama AZIZI on 3/22/22.
 */

public class VilleDataManager {
    private static VilleDataManager sInstance;
    private final VilleService mVilleService;

    public VilleDataManager() {
        mVilleService = new ServiceFactory<>(VilleService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Ville")).makeService();
    }

    public static VilleDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new VilleDataManager();
        }
        return sInstance;
    }

    public void getVilles(GenericObject genericObject,
                            RemoteCallback<List<Ville>> listener) {
        mVilleService.getVilles(genericObject)
                .enqueue(listener);
    }

}
