package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * Created by Oussama AZIZI on 6/24/22.
 */

@Entity
public class VCListeConcurrent {

    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "Codeconcurrent")
    @SerializedName("Codeconcurrent")
    @Expose
    private String codeconcurrent;

    @ColumnInfo(name = "concurrent")
    @SerializedName("concurrent")
    @Expose
    private String concurrent;

    @ColumnInfo(name = "Note")
    @SerializedName("Note")
    @Expose
    private String note;

    @ColumnInfo(name = "Etatconcurrent")
    @SerializedName("Etatconcurrent")
    @Expose
    private int etatconcurrent;

    @ColumnInfo(name = "Info1")
    @SerializedName("Info1")
    @Expose
    private String info1;

    @ColumnInfo(name = "Info2")
    @SerializedName("Info2")
    @Expose
    private String info2;

    public String getCodeconcurrent() {
        return codeconcurrent;
    }

    public void setCodeconcurrent(String codeconcurrent) {
        this.codeconcurrent = codeconcurrent;
    }

    public String getConcurrent() {
        return concurrent;
    }

    public void setConcurrent(String concurrent) {
        this.concurrent = concurrent;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public int getEtatconcurrent() {
        return etatconcurrent;
    }

    public void setEtatconcurrent(int etatconcurrent) {
        this.etatconcurrent = etatconcurrent;
    }

    public String getInfo1() {
        return info1;
    }

    public void setInfo1(String info1) {
        this.info1 = info1;
    }

    public String getInfo2() {
        return info2;
    }

    public void setInfo2(String info2) {
        this.info2 = info2;
    }

    @Override
    public String toString() {
        return  concurrent ;
    }
}
