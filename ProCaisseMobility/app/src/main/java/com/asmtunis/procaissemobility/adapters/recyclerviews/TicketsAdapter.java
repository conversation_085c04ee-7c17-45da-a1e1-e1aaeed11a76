package com.asmtunis.procaissemobility.adapters.recyclerviews;

import android.content.Context;
import android.os.Build;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.EditText;
import android.widget.LinearLayout;

import com.arasthel.asyncjob.AsyncJob;
import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.adapters.tables.LigneTicketTableDataAdapter;
import com.asmtunis.procaissemobility.data.models.Article;
import com.asmtunis.procaissemobility.data.models.LigneTicket;
import com.asmtunis.procaissemobility.data.models.ReglementCaisse;
import com.asmtunis.procaissemobility.data.models.Ticket;
import com.asmtunis.procaissemobility.helper.Globals;
import com.asmtunis.procaissemobility.helper.utils.DateUtils;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.listener.ItemCallback;
import com.asmtunis.procaissemobility.listener.MenuItemClickListener;
import com.asmtunis.procaissemobility.listener.MenuItemsAction;
import com.asmtunis.procaissemobility.ui.components.SortableLigneTicketTableView;
import com.mikepenz.google_material_typeface_library.GoogleMaterial;
import com.mikepenz.iconics.IconicsDrawable;
import com.mikepenz.iconics.view.IconicsButton;

import net.cachapa.expandablelayout.ExpandableLayout;

import java.util.Comparator;
import java.util.List;

import static android.content.Context.LAYOUT_INFLATER_SERVICE;
import static net.cachapa.expandablelayout.ExpandableLayout.State.COLLAPSING;
import static net.cachapa.expandablelayout.ExpandableLayout.State.EXPANDING;

/**
 * Created by PC on 11/29/2017.
 */

public class TicketsAdapter extends BaseListAdapter<Ticket> implements  ExpandableLayout.OnExpansionUpdateListener {

    List<LigneTicket> result=null;

    public TicketsAdapter(Context context, RecyclerView recyclerView, List<Ticket> arrayList, Comparator comparator, int menuId, MenuItemsAction menuItemsAction,ItemCallback itemCallback) {
        super(context, arrayList, comparator, menuId, menuItemsAction,itemCallback);
    }


    @Override
    public void setViewHolder(ViewHolder viewHolder, int position) {
        final Ticket item = mArrayList.get(position);
        viewHolder.toolbar.setTitle(String.format(context.getString(R.string.ticket_number_field), item.gettIKNumTicket()+""));
        viewHolder.toolbar.setSubtitle(item.gettIKNomClient());

        viewHolder.price.setText(String.format("%s %s", item.gettIKMtTTC(), new PrefUtils(context).getCurrency()));
        viewHolder.dateCreation.setText(DateUtils.dateToStr(DateUtils.strToDate(item.gettIKDateHeureTicket(), "yyyy-MM-dd HH:mm"), "dd/MM/yyyy"));
        if (item.gettIKMtTTC() > 0) {
            viewHolder.price.setVisibility(View.VISIBLE);
            //  viewHolder.toolbar.inflateMenu(menuId);
            viewHolder.toolbar.setOnMenuItemClickListener(new MenuItemClickListener(position,
                    menuItemsAction));
                viewHolder.toolbar.getMenu().findItem(R.id.print_item).setIcon(new IconicsDrawable(context)
                        .icon(GoogleMaterial.Icon.gmd_print)
                        .color(context.getResources().getColor(R.color.material_teal700))
                        .sizeDp(20));

            viewHolder.layoutTicket.setOnClickListener(v -> {

                if (item != null) {

                    if (itemCallback == null) {
                        return;
                    } else {
                        itemCallback.onItemClicked(position);
                    }
                }

            });

            viewHolder.toolbar.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    if (item != null) {

                        if (itemCallback == null) {
                            return;
                        } else {
                            itemCallback.onItemClicked(position);
                        }
                    }

                }
            });

        }
    }

    private void initUI(final ViewHolder viewHolder, Ticket ticket) {


        AsyncJob.doInBackground(() -> {
            result =  App.database.ligneTicketDAO().getByTicket(ticket.gettIKNumTicket(),ticket.tIKIdCarnet,
                    ticket.tIKExerc);



//            Log.d("sdqqdqsd", result.get(0).toString() + "");
            AsyncJob.doOnMainThread(() -> {
                LayoutInflater inflater = (LayoutInflater) context.getSystemService(LAYOUT_INFLATER_SERVICE);
                viewHolder.content.addView(inflater.inflate(R.layout.ticket_subitem, null));


                ExpandableLayout productExpandableLayout =  viewHolder.content.findViewById(R.id.product_expandable_layout);
                ExpandableLayout paymentExpandableLayout =  viewHolder.content.findViewById(R.id.payment_expandable_layout);
                LinearLayout productDrawer =  viewHolder.content.findViewById(R.id.product_drawer);
                CardView cashLayout =  viewHolder.content.findViewById(R.id.cash_view);
                CardView checkLayout =  viewHolder.content.findViewById(R.id.check_view);
                CardView ticketRestoLayout =  viewHolder.content.findViewById(R.id.ticket_resto_view);
                LinearLayout paymentDrawer =  viewHolder.content.findViewById(R.id.payment_drawer);
                IconicsButton productDrawerButton =  viewHolder.content.findViewById(R.id.product_drawer_button);
                IconicsButton paymentDrawerButton =  viewHolder.content.findViewById(R.id.payment_drawer_button);
                SortableLigneTicketTableView ligneTicketTableView = viewHolder.content.findViewById(R.id.tableView);
                EditText amountInputField = viewHolder.content.findViewById(R.id.AmountInputField);
                EditText discountInputField = viewHolder.content.findViewById(R.id.DiscountInputField);
                EditText totalPaymentValue = (EditText) viewHolder.content.findViewById(R.id.total_payment_value);
                ReglementCaisse cashPaymenet = App.database.reglementCaisseDAO().getByCash(ticket.gettIKNumTicket());
                List<ReglementCaisse> checkPayments = App.database.reglementCaisseDAO().getByCheck(ticket.gettIKNumTicket());
                List<ReglementCaisse> ticketRestoPayments = App.database.reglementCaisseDAO().getByTicketResto(ticket.gettIKNumTicket());


                if (cashPaymenet!=null) {
//    totalPaymentValue.setCurrentText(StringUtils.priceFormat(cashPaymenet.getrEGCMntEspece()));
    totalPaymentValue.setText(StringUtils.priceFormat(cashPaymenet.getrEGCMntEspece()));
}else {cashLayout.setVisibility(View.GONE);}


                if (!checkPayments.isEmpty()) {
                }else {checkLayout.setVisibility(View.GONE);}

                if (!ticketRestoPayments.isEmpty() ) {
//    totalPaymentValue.setCurrentText(StringUtils.priceFormat(cashPaymenet.getrEGCMntEspece()));
                    //      totalPaymentValue.setText(StringUtils.priceFormat(cashPaymenet.getrEGCMntEspece()));
                }else {ticketRestoLayout.setVisibility(View.GONE);}

                if (!result.isEmpty()) {
                    for (LigneTicket ligneTicket:
                    result) {
                       // ligneTicket.getlTCodArt()
                        Article article = App.database.articleDAO().getOneByCodeAndStation(ligneTicket.getlTCodArt(),App.prefUtils.getUserStationId());
                        ligneTicket.setArticle(article);
                    }
                    ligneTicketTableView.setSwipeToRefreshEnabled(false);
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                        ligneTicketTableView.setElevation(10);
                        ligneTicketTableView.setNestedScrollingEnabled(true);
                    }
                    ligneTicketTableView.setHeaderBackground(R.color.material_teal500);
                    ligneTicketTableView.setVerticalScrollBarEnabled(true);
                    ligneTicketTableView.setScrollbarFadingEnabled(true);
                    ligneTicketTableView.setWeightSum(1);
                    //tableView.setHeaderBackground(getThemeAccentColor(context));
                    ligneTicketTableView.setDataAdapter(new
                            LigneTicketTableDataAdapter(context, result, ligneTicketTableView));


                 //  tableDataAdapter.setNotifyOnChange(true);
                    // tableDataAdapter.notifyDataSetChanged();
                } else {
                    ligneTicketTableView.setVisibility(View.GONE);
                }

                amountInputField.setText(ticket.gettIKMtTTC() + "");
                discountInputField.setText(ticket.gettIKTauxRemise() + "");
                discountInputField.setEnabled(false);



                onExpansionUpdate(productExpandableLayout,productDrawerButton);
                onExpansionUpdate(paymentExpandableLayout,paymentDrawerButton);
                productDrawer.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {

                        if (!productExpandableLayout.isExpanded()) {
                            productExpandableLayout.expand(true);
                            paymentExpandableLayout.collapse(true);
                        }
                        else {
                            productExpandableLayout.collapse(true);

                        }
                    }
                });

                paymentDrawer.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {

                        if (!paymentExpandableLayout.isExpanded()) {
                            paymentExpandableLayout.expand(true);
                            productExpandableLayout.collapse(true);

                            //       productDrawerButton.setText();

                        }
                        else {
                            paymentExpandableLayout.collapse(true);

                        }
                    }
                });



            });

        });    }



        void onExpansionUpdate(ExpandableLayout expandableLayout,com.mikepenz.iconics.view.IconicsButton button)
        {
            expandableLayout.setOnExpansionUpdateListener(new ExpandableLayout.OnExpansionUpdateListener() {
                @Override
                public void onExpansionUpdate(float expansionFraction, int state) {
                    switch (state)
                    {
                        case EXPANDING:
                            button.setText("{faw-chevron-up}");
                            break;

                        case COLLAPSING:
                            button.setText("{faw-chevron-down}");

                            break;
                    }
                }
            });
        }







    void setText(EditText editText, String s) {
        try {
            if (s.length() <= 1) {
                editText.setVisibility(View.GONE);
            } else {
                editText.setText(s);
            }
        } catch (NullPointerException e) {
        }
    }


    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {

    }

    @Override
    public void onExpansionUpdate(float expansionFraction, int state) {
        Log.d("ExpandableLayout", "State: " + state);
        if (state != ExpandableLayout.State.COLLAPSED) {
      //      recyclerView.smoothScrollToPosition(this.posgetAdapterPosition());
        }
    }
}