package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Transaction;

import com.asmtunis.procaissemobility.data.models.OrdreMission;
import com.asmtunis.procaissemobility.data.models.OrdreWithLines;

import java.util.List;

@Dao
public interface OrdreMissionDAO {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<OrdreMission> ordreMissions);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertOne(OrdreMission ordreMissions);

    @Query("select * from OrdreMission")
    LiveData<List<OrdreMission>> getAllLiveData();


    @Query("select * from OrdreMission")
    List<OrdreMission> getAll();

    @Query("select * from OrdreMission where ORD_Code=:code")
    OrdreMission getBycode(String code);


   // @Query("select * from OrdreMission where ORD_date=:date")
   @Query("SELECT * FROM OrdreMission " +
            "WHERE ORD_date >= CAST(strftime('%J','now') - strftime('%J','1970-01-01') AS INTEGER) " //+
            //"AND start_date <= CAST(strftime('%J','now') - strftime('%J','1970-01-01') AS INTEGER);"
   )
    OrdreMission getBydate(/*String date*/);

    @Transaction
    @Query("select * from OrdreMission where ORD_Code= :code")
    OrdreWithLines getOrdreWithLines(String code);

    @Query("Delete from OrdreMission")
    void deleteAll();
}
