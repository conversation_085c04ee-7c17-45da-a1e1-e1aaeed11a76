package com.asmtunis.procaissemobility.data.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.Caisse;

import java.util.List;

/**
 * Created by PC on 11/18/2017.
 */
@Dao
public interface CaisseDAO {

    @Query("SELECT * FROM Caisse")
    List<Caisse> getAll();

    @Query("SELECT * FROM Caisse WHERE CAI_IdCaisse = :id ")
    Caisse getOneById(String id);

    @Query("SELECT * FROM Caisse LIMIT 1")
    Caisse getOne();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(Caisse item);


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<Caisse> items);

    @Query("DELETE FROM Caisse where Status='SELECTED'")
    void deleteAll();


}
