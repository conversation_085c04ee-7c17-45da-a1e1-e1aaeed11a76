package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.DNFamille;
import com.asmtunis.procaissemobility.data.models.DNSuperficie;
import com.asmtunis.procaissemobility.data.models.DN_LigneVisite;

import java.util.List;

@Dao
public interface DNFamilleVisiteDAO {
    @Query("SELECT * FROM DNFamille")
    List<DNFamille> getAll();
    //LiveData<List<DNFamille>> getAll();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<DNFamille> dnFamille);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(DNFamille dnFamille);

    @Query("SELECT * FROM DNFamille where isSync=0 and  (Status='INSERTED'  or Status='UPDATED') ")
    List<DNFamille> getNoSynced();

    @Query("SELECT count(*) FROM DNFamille where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    LiveData<Integer> getNoSyncCountMubtale();

    @Query("SELECT * FROM DNFamille WHERE CodeFamille = :code")
    DNFamille getByCode(String code);

    @Query("SELECT * FROM DNFamille WHERE DesgFamille = :code")
    DNFamille getByname(String code);

    @Query("delete from DNFamille")
    void deleteAll();

    @Query("DELETE FROM DNFamille where CodeFamille=:codeFamille")
    void deleteById(String codeFamille);


//@Query("UPDATE DNTypeServices SET CodeVCPrix = :code_procaiss where CodeVCPrixM = :CodeMobile")
// void updateCloudCode(String code_procaiss, String CodeMobile);

}

