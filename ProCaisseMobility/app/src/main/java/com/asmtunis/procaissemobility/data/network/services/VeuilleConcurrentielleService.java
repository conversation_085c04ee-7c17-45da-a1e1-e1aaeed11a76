package com.asmtunis.procaissemobility.data.network.services;

import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.VCAutre;
import com.asmtunis.procaissemobility.data.models.VCAutreDeleteResponse;
import com.asmtunis.procaissemobility.data.models.VCImage;
import com.asmtunis.procaissemobility.data.models.VCListeConcurrent;
import com.asmtunis.procaissemobility.data.models.VCNewProduct;
import com.asmtunis.procaissemobility.data.models.VCPrix;
import com.asmtunis.procaissemobility.data.models.VCPromo;
import com.asmtunis.procaissemobility.data.models.VCTypeCommunication;
import com.asmtunis.procaissemobility.data.models.VcResponseBatchData;
import com.asmtunis.procaissemobility.data.models.uploadImageResponse;

import java.util.List;

import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.DELETE;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.HTTP;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * Created by Oussama AZIZI on 6/24/22.
 */

public interface VeuilleConcurrentielleService {
    @POST("getDataVConcu/VCLancementNP")
    Call<List<VCNewProduct>> getVCNewProduct(@Body GenericObject genericObject, @Query("user") String userCode);

    @POST("getDataVConcu/VCPromo")
    Call<List<VCPromo>> getVCPromo(@Body GenericObject genericObject, @Query("user") String userCode);

    @POST("getDataVConcu/VCPrix")
    Call<List<VCPrix>> getVCPrix(@Body GenericObject genericObject, @Query("user") String userCode);

    @POST("getDataVConcu/VCAutre")
    Call<List<VCAutre>> getVCAutre(@Body GenericObject genericObject, @Query("user") String userCode);

    @POST("getVCListeConcurrent")
    Call<List<VCListeConcurrent>> getVCListeConcurrent(@Body GenericObject genericObject);

    @POST("getVCTypeCommunication")
    Call<List<VCTypeCommunication>> getVCTypeCommunication(@Body GenericObject genericObject);

    @POST("getVCImage")
    Call<List<VCImage>> getVCImage(@Body GenericObject genericObject);

    @POST("upload")
    Call<List<uploadImageResponse>> addBatchVCImage(@Body GenericObject genericObject);

    @POST("addBatchDataVConcu")
    Call<List<VcResponseBatchData>> addBatchDataVConcu(@Body GenericObject genericObject, @Query("table") String table);

   // @DELETE("deleteDataVConcu")
  //  @FormUrlEncoded
   // Call<Boolean> deleteBatchDataVConcu(@Body GenericObject genericObject, @Query("table") String table);



    @HTTP(method = "DELETE", path = "deleteDataVConcu", hasBody = true)
    Call<List<VCAutreDeleteResponse>> deleteBatchDataVConcuAutre(@Body GenericObject object, @Query("table") String table);


    @HTTP(method = "DELETE", path = "deleteDataVConcu", hasBody = true)
    Call<List<VCAutreDeleteResponse>> deleteBatchDataVConcuPrix(@Body GenericObject object, @Query("table") String table);



    @HTTP(method = "DELETE", path = "deleteDataVConcu", hasBody = true)
    Call<List<VCAutreDeleteResponse>> deleteBatchDataVConcuPromotion(@Body GenericObject object, @Query("table") String table);


    @HTTP(method = "DELETE", path = "deleteDataVConcu", hasBody = true)
    Call<List<VCAutreDeleteResponse>> deleteBatchDataVConcuNewProduct(@Body GenericObject object, @Query("table") String table);


}
