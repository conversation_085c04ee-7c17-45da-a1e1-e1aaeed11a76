package com.asmtunis.procaissemobility.data.models;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 14/09/2017.
 */

public class TVA extends BaseModel{

    private Float TVA_Code;
    private String TVA_User;
    private String TVA_Station;

    public TVA() {
    }

    public TVA(Float TVA_Code, String TVA_User, String TVA_Station) {
        this.TVA_Code = TVA_Code;
        this.TVA_User = TVA_User;
        this.TVA_Station = TVA_Station;
    }



    public Float getTVA_Code() {
        return TVA_Code;
    }

    public void setTVA_Code(Float TVA_Code) {
        this.TVA_Code = TVA_Code;
    }

    public String getTVA_User() {
        return TVA_User;
    }

    public void setTVA_User(String TVA_User) {
        this.TVA_User = TVA_User;
    }

    public String getTVA_Station() {
        return TVA_Station;
    }

    public void setTVA_Station(String TVA_Station) {
        this.TVA_Station = TVA_Station;
    }


    @Override
    public String toString() {
        return "Unite_article{" +
                "TVA_Code=" + TVA_Code +
                ", TVA_User='" + TVA_User + '\'' +
                ", TVA_Station='" + TVA_Station + '\'' +

                '}';
    }

}
