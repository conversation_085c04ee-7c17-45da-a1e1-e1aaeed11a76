package com.asmtunis.procaissemobility.data.network.datamanager;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Article;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Ticket;
import com.asmtunis.procaissemobility.data.models.Traking;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.ArticleService;

import java.util.List;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

/**
 * Created by PC on 10/4/2017.
 */

public class ArticleDataManager  {
    private static ArticleDataManager sInstance;
    private final ArticleService mArticleService;

    public ArticleDataManager() {
        mArticleService = new ServiceFactory<>(ArticleService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Article")).makeService();
    }

    public static ArticleDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new ArticleDataManager();
        }
        return sInstance;
    }

    public void getArticles(GenericObject genericObject,
            RemoteCallback<List<Article>> listener) {
        String ddm = App.database.articleDAO().getDDM();
        mArticleService.getArticles(genericObject, ddm,  App.prefUtils.getUserStationId())
                .enqueue(listener);
    }

    public void getArticlesByStation(GenericObject genericObject,
                                     RemoteCallback<List<Article>> listener) {
        mArticleService.getArticlesByStation(genericObject)
                .enqueue(listener);
    }

}

