package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.Article;

import java.util.List;

/**
 * Created by PC on 11/18/2017.
 */
@Dao
public interface ArticleDAO {

    @Query("SELECT * FROM Article")
    List<Article> getAll();

    @Query("SELECT * FROM Article where Type_Produit not like '%patrimoine%' and SART_Qte>0")
    LiveData<List<Article>> getAllNotPatStockable();

    @Query("SELECT * FROM Article where Type_Produit not like '%patrimoine%'")
    LiveData<List<Article>> getAllNotPat();

    @Query("SELECT * FROM Article where Type_Produit like '%patrimoine%'")
    LiveData<List<Article>> getAllPat();

    //@Query("SELECT count(*) FROM Article WHERE SART_CodeSatation=:station")
    @Query("SELECT count(*) FROM Article")
    LiveData<Integer> getAllCount();

    @Query("SELECT count(*) FROM Article WHERE SART_CodeSatation=:station and SART_Qte>0")
    LiveData<Integer> getAllCountGreaterThanZero(String station);

    @Query("SELECT * FROM Article")
    LiveData<List<Article>> getAllMutable();

    @Query("SELECT * FROM Article WHERE SART_CodeSatation=:station")
    List<Article> getAllByStation(String station);

    @Query("SELECT * FROM Article WHERE SART_CodeSatation=:station and SART_Qte>0")
    List<Article> getAllByStationGreaterThanZero(String station);

    @Query("SELECT * FROM Article WHERE ART_Code = :code ")
    Article getOneByCode(String code);

    @Query("SELECT * FROM Article WHERE ART_Code = :code and SART_CodeSatation = :station ")
    Article getOneByCodeAndStation(String code, String station);

    //@Query("SELECT * FROM Article WHERE SART_CodeSatation = :code ")
    @Query("SELECT * FROM Article")
    LiveData<List<Article>> getByStationMutble();

    @Query("SELECT * FROM Article WHERE SART_CodeSatation=:station and SART_Qte>0")
    LiveData<List<Article>> getAllByStationGreaterThanZeroMutable(String station);

    @Query("SELECT * FROM Article WHERE SART_CodeSatation=:station and SART_Qte>0 and Type_Produit not like '%patrimoine%'")
    LiveData<List<Article>> getAllByStationGreaterThanZeroMutableNotPat(String station);

    @Query("SELECT * FROM Article WHERE SART_CodeSatation=:station and Type_Produit not like '%patrimoine%'")
    LiveData<List<Article>> getAllByStationMutableNotPat(String station);

    @Query("SELECT * FROM Article WHERE ART_CodeBar LIKE :code or Fils_CodeBar LIKE :code ")
    Article getByCodeBar(String code);

    @Query("SELECT * FROM Article WHERE (ART_CodeBar LIKE :code or Fils_CodeBar LIKE :code or ART_Code LIKE :code) and SART_CodeSatation= :station")
    Article getByCodeBarAndSation(String code, String station);




    @Query("SELECT * FROM Article WHERE photo_Path = :code")
    Article getByNumSerie(String code);

    @Query("SELECT COUNT(*) FROM Article")
    int count();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(Article item);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<Article> items);

    @Query("DELETE FROM Article")
    void deleteAll();

    @Query("SELECT strftime('%Y-%m-%d %H-%M',ddm) FROM Article order by strftime('%Y-%m-%d %H-%M',ddm) desc limit 1")
    String getDDM();
}
