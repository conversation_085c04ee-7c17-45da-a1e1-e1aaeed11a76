package com.asmtunis.procaissemobility.adapters;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentStatePagerAdapter;

import com.asmtunis.procaissemobility.ui.fragments.ClientsFragment;
import com.asmtunis.procaissemobility.ui.fragments.TicketInfoSubItemFragment;

/**
 * Created by PC on 11/30/2017.
 */

public class TicketPagerAdapter extends FragmentStatePagerAdapter {
    int mNumOfTabs;

    public TicketPagerAdapter(FragmentManager fragmentManager, int length) {
        super(fragmentManager);
        this.mNumOfTabs = length;
    }

    @NonNull
    @Override
    public Fragment getItem(int position) {
        Fragment fragment;
        switch (position) {
            case 0:
                fragment = new TicketInfoSubItemFragment();

                break;
            case 1:
                fragment = new ClientsFragment();
                break;
            default:
                fragment = TicketInfoSubItemFragment.newInstance();
                break;
        }
        return fragment;
    }

    @Override
    public int getCount() {

        //    Log.d("TicketInfoemFragment",mNumOfTabs+"");
        return mNumOfTabs;
    }


/*

    @Override
    public Object instantiateItem(ViewGroup collection, int position) {
        LayoutInflater inflater = LayoutInflater.from(context);
        ViewGroup unit_price_dialog = (ViewGroup) inflater.inflate(R.unit_price_dialog.ticket_subitem, collection, false);
        collection.addView(unit_price_dialog);
        return unit_price_dialog;
    }*/
/*
@Override
public Object instantiateItem(final ViewGroup container, final int position) {
    final View view = new View(context);
    container.addView(view);
    return view;
}*/


/*
    @Override
    public boolean isViewFromObject(final View view, final Object object) {
        return view.equals(object);
    }
*/
/*

    @Override
    public void destroyItem(final View container, final int position, final Object object) {
        ((ViewPager) container).removeView((View) object);
    }
*/

    /*
     */
/*

    @Override
    public Object instantiateItem(final ViewGroup container, final int position) {

        LayoutInflater inflater = LayoutInflater.from(container.getContext());
        View view = inflater.inflate(R.unit_price_dialog.ticket_subitem, container, false);
        container.addView(view);
        return view;
    }
*/


/*

    @Override
    public Object instantiateItem(final ViewGroup container, final int position) {



        LayoutInflater inflater = LayoutInflater.from(mContext);
        ViewGroup view = (ViewGroup) inflater.inflate(modelObject.getLayoutResId(), container, false);
        container.addView(unit_price_dialog);
        return unit_price_dialog;
        return view;
    }

    */


}
