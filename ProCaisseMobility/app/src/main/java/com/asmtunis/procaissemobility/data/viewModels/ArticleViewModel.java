package com.asmtunis.procaissemobility.data.viewModels;


import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelProviders;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.dao.ArticleDAO;
import com.asmtunis.procaissemobility.data.models.Article;

import java.util.List;

public class ArticleViewModel extends ViewModel {
    public ArticleDAO dao;
    private static ArticleViewModel instance;


    public static ArticleViewModel getInstance(Fragment activity) {
        if (instance == null)
            instance = new ViewModelProvider(activity).get(ArticleViewModel.class);
        instance.dao = App.database.articleDAO();

        return instance;
    }

    public static ArticleViewModel getInstance(FragmentActivity activity) {
        if (instance == null)
            instance = new ViewModelProvider(activity).get(ArticleViewModel.class);
        instance.dao = App.database.articleDAO();
        return instance;
    }


    public LiveData<List<Article>> getAll() {
        return App.database.articleDAO().getAllMutable();
    }

    public LiveData<List<Article>> getAllNotPat() {
        return dao.getAllNotPat();
    }

    public LiveData<List<Article>> getAllPat() {
        return dao.getAllPat();
    }

    public LiveData<List<Article>> getAllNotPatStockable() {
        return dao.getAllNotPatStockable();
    }

    public LiveData<List<Article>> getByStation(String code) {
        return dao.getByStationMutble();
    }

    public LiveData<List<Article>> getByStationMutable(String code) {
        return dao.getAllByStationGreaterThanZeroMutable(code);
    }

    public LiveData<List<Article>> getByStationMutableNotPat(String code) {
        return dao.getAllByStationGreaterThanZeroMutableNotPat(code);
    }

    public LiveData<List<Article>> getAllByStationMutableNotPat(String code) {
        return dao.getAllByStationMutableNotPat(code);
    }

    public LiveData<Integer> getAllCount(String station) {
        return dao.getAllCount();
    }

    public LiveData<Integer> getAllCountGreaterThanZero(String station) {
        return dao.getAllCountGreaterThanZero(station);
    }
    public Article getArticleByCode(String code){
        return App.database.articleDAO().getOneByCode(code);
    }

}
