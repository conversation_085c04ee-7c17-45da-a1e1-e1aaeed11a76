package com.asmtunis.procaissemobility.adapters.tables;

import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.asmtunis.procaissemobility.data.models.ChequeCaisse;
import com.asmtunis.procaissemobility.helper.utils.DateUtils;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.ui.components.SortableBankCheckLineTableView;

import java.text.NumberFormat;
import java.util.List;

import de.codecrafters.tableview.toolkit.LongPressAwareTableDataAdapter;

/**
 * ²
 */

public class LigneChequesTableDataAdapter extends LongPressAwareTableDataAdapter<ChequeCaisse> {

    private static final int TEXT_SIZE = 16;
    private static final NumberFormat PRICE_FORMATTER = NumberFormat.getNumberInstance();


    public LigneChequesTableDataAdapter(final Context context, final List<ChequeCaisse> data, final SortableBankCheckLineTableView tableView) {
        super(context, data, tableView);
    }

    @Override
    public View getDefaultCellView(int rowIndex, int columnIndex, ViewGroup parentView) {
        final ChequeCaisse chequeCaisse = getRowData(rowIndex);
        View renderedView = null;

        switch (columnIndex) {
            case 0:
                renderedView = renderString(chequeCaisse.getNumCheque());
                break;

            case 1:

                String date= DateUtils.dateToStr(DateUtils.strToDate(chequeCaisse.getEcheanceCheque(), "MM/dd/yyyy hh:mm:ss"), "dd/MM/yyyy");
                if (StringUtils.isEmptyString(date)){
                     date= DateUtils.dateToStr(DateUtils.strToDate(chequeCaisse.getEcheanceCheque(), "yyyy-MM-dd HH:mm"), "dd/MM/yyyy");
                }


                renderedView = renderString(

                        date

                );
                break;

            case 2:
                renderedView = renderString(chequeCaisse.getBanque());
                break;

            case 3:
                renderedView = renderString(PRICE_FORMATTER.format(chequeCaisse.getMontant()));
                break;
        }

        return renderedView;
    }

    @Override
    public View getLongPressCellView(int rowIndex, int columnIndex, ViewGroup parentView) {
        return null;
    }


    private View renderString(final String value) {
        final TextView textView = new TextView(getContext());
        textView.setText(value);
        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        textView.setHeight(140);
        textView.setGravity(Gravity.CENTER_VERTICAL);
        return textView;
    }


}