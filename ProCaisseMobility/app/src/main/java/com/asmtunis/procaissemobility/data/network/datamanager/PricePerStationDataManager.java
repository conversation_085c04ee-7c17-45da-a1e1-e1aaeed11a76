package com.asmtunis.procaissemobility.data.network.datamanager;


import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.PricePerStation;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.PricePerStationService;

import java.util.List;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

/**
 * Created by Achraf on 29/09/2017.
 */

public class PricePerStationDataManager {

    private static PricePerStationDataManager sInstance;
    private final PricePerStationService pricePerStationService;

    public PricePerStationDataManager() {
        pricePerStationService = new ServiceFactory<>(PricePerStationService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Price")).makeService();
    }

    public static PricePerStationDataManager getInstance( ) {
        if (sInstance == null) {
            sInstance = new PricePerStationDataManager();
        }
        return sInstance;
    }

    public void getPricesByStation(GenericObject genericObject,
            RemoteCallback<List<PricePerStation>> listener) {
        pricePerStationService.getPricesByStation(genericObject)
                .enqueue(listener);
    }


}


