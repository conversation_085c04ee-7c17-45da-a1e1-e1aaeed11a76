package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Embedded;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

@Entity
public class OrdreMission implements Serializable, Comparable<OrdreMission> {

    @PrimaryKey
    @NonNull
    @SerializedName("ORD_Code")
    @ColumnInfo(name = "ORD_Code")
    @Expose
    public String oRDCode;
    @SerializedName("ORD_exercice")
    @ColumnInfo(name = "ORD_exercice")
    @Expose
    public String oRDExercice;
    @SerializedName("ORD_date")
    @ColumnInfo(name = "ORD_date")
    @Expose
    public String oRDDate;
    @SerializedName("ORD_vendeur")
    @ColumnInfo(name = "ORD_vendeur")
    @Expose
    public String oRDVendeur;
    @SerializedName("ORD_note")
    @ColumnInfo(name = "ORD_note")
    @Expose
    public String oRDNote;
    @SerializedName("ORD_etat")
    @ColumnInfo(name = "ORD_etat")
    @Expose
    public String oRDEtat;
    @SerializedName("ORD_user")
    @ColumnInfo(name = "ORD_user")
    @Expose
    public String oRDUser;
    @SerializedName("ORD_DDM")
    @ColumnInfo(name = "ORD_DDM")
    @Expose
    public String ordDdm;
    @SerializedName("ORD_circuit")
    @ColumnInfo(name = "ORD_circuit")
    @Expose
    public String oRDCircuit;
    @SerializedName("ORD_station")
    @ColumnInfo(name = "ORD_station")
    @Expose
    public String oRDStation;
    @SerializedName("export")
    @ColumnInfo(name = "export")
    @Expose
    public Integer export;
    @SerializedName("DDm")
    @ColumnInfo(name = "DDm")
    @Expose
    public String dDm;
    @SerializedName("exportM")
    @ColumnInfo(name = "exportM")
    @Expose
    public Integer exportM;
    @SerializedName("DDmM")
    @ColumnInfo(name = "DDmM")
    @Expose
    public String dDmM;
    @SerializedName("ORD_stationDepart")
    @ColumnInfo(name = "ORD_stationDepart")
    @Expose
    public String oRDStationDepart;
    @SerializedName("ORD_dateDebut")
    @ColumnInfo(name = "ORD_dateDebut")
    @Expose
    public String oRD_dateDebut;
    @SerializedName("ORD_dateFin")
    @ColumnInfo(name = "ORD_dateFin")
    @Expose
    public String oRD_dateFin;

    @SerializedName("ORD_Session")
    @ColumnInfo(name = "ORD_Session")
    @Expose
    public String oRD_Session;

    @Ignore
    public OrdreMission(@NonNull String oRDCode, String oRDDate) {
        this.oRDCode = oRDCode;
        this.oRDDate = oRDDate;
    }

    public OrdreMission(@NonNull String oRDCode, String oRDExercice, String oRDDate, String oRDVendeur, String oRDNote, String oRDEtat, String oRDUser, String ordDdm, String oRDCircuit, String oRDStation, Integer export, String dDm, Integer exportM, String dDmM, String oRDStationDepart, String oRD_dateDebut, String oRD_dateFin, String oRD_Session) {
        this.oRDCode = oRDCode;
        this.oRDExercice = oRDExercice;
        this.oRDDate = oRDDate;
        this.oRDVendeur = oRDVendeur;
        this.oRDNote = oRDNote;
        this.oRDEtat = oRDEtat;
        this.oRDUser = oRDUser;
        this.ordDdm = ordDdm;
        this.oRDCircuit = oRDCircuit;
        this.oRDStation = oRDStation;
        this.export = export;
        this.dDm = dDm;
        this.exportM = exportM;
        this.dDmM = dDmM;
        this.oRDStationDepart = oRDStationDepart;
        this.oRD_dateDebut = oRD_dateDebut;
        this.oRD_dateFin = oRD_dateFin;
        this.oRD_Session = oRD_Session;
    }

    @Override
    public String toString() {
        return  oRDDate ;
    }

    @Override
    public int compareTo(OrdreMission o) {
        if (!this.oRD_dateDebut.equals(o.oRD_dateDebut) && this.oRD_dateFin.equals(o.oRD_dateFin)) return 0;
        else if (!this.oRD_dateFin.equals(o.oRD_dateFin) && this.oRD_dateDebut.equals(o.oRD_dateDebut)) return 1;
        else if(!this.oRD_dateFin.equals(o.oRD_dateFin) && !this.oRD_dateDebut.equals(o.oRD_dateDebut)) return 2;
        return -1;
    }
}
