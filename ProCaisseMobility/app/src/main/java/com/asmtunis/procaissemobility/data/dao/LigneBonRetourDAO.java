package com.asmtunis.procaissemobility.data.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.LigneBonRetour;

import java.util.List;

@Dao
public interface LigneBonRetourDAO {
    @Query("SELECT * FROM LigneBonRetour")
    List<LigneBonRetour> getAll();

    @Query("SELECT * FROM LigneBonRetour where NumBon_Retour=:code")
    List<LigneBonRetour> getByBRNum(String code );



    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(LigneBonRetour item);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<LigneBonRetour> items);

    @Query("UPDATE LigneBonRetour SET NumBon_Retour = :newNumRetour where NumBon_Retour = :oldNumRetour")
    void updateCodeBonRetour(String newNumRetour, String oldNumRetour);

    @Query("DELETE FROM LigneBonRetour")
    void deleteAll();
    @Query("SELECT sum(lIGBonEntreeMntTTC) FROM LigneBonRetour where NumBon_Retour=:bonRetourNum and LIG_BonEntree_Exerc=:exercie ")
    double getSumPriceByBr(String bonRetourNum, String exercie);
}
