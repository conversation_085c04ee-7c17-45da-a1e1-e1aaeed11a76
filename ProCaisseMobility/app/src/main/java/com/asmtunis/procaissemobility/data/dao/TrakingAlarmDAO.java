package com.asmtunis.procaissemobility.data.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.TrakingAlarm;

import java.util.List;

@Dao
public interface TrakingAlarmDAO {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertTrAlarm(TrakingAlarm trakingAlarm);

    @Query("select * from TrakingAlarm where CodeOrdreMission= :code")
    TrakingAlarm getWithOrdreMission(String code);

    @Query("select CodeOrdreMission from TrakingAlarm where Started = 1 and Ended = 0")
    String getCurrentOrdreMission();

    @Query("select * from TrakingAlarm")
    List<TrakingAlarm> getAll();

    @Query("select CodeOrdreMission from TrakingAlarm where Ended = 0 limit 1")
    String getCodeOrdreMissionActif();

    @Query("Delete from TrakingAlarm")
    void deleteAll();
}
