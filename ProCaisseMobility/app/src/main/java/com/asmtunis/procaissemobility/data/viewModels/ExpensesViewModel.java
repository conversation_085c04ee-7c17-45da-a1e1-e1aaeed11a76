package com.asmtunis.procaissemobility.data.viewModels;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProviders;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.dao.DepenceTypeDAO;
import com.asmtunis.procaissemobility.data.dao.DepenceCaisseDAO;
import com.asmtunis.procaissemobility.data.models.DepenceType;
import com.asmtunis.procaissemobility.data.models.DepenceCaisse;

import java.util.List;

/**
 * Created by Oussama AZIZI on 8/29/22.
 */

public class ExpensesViewModel extends ViewModel {
    public DepenceTypeDAO dao;
    public DepenceCaisseDAO depenceCaisseDAO;
    private static ExpensesViewModel instance;


    public static ExpensesViewModel getInstance(Fragment activity) {
        if (instance == null)
            instance = ViewModelProviders.of(activity).get(ExpensesViewModel.class);
        instance.dao = App.database.depenceTypeDAO();
        instance.depenceCaisseDAO = App.database.depenceCaisseDAO();

        return instance;
    }

    public static ExpensesViewModel getInstance(FragmentActivity activity) {
        if (instance == null)
            instance = ViewModelProviders.of(activity).get(ExpensesViewModel.class);
        instance.dao = App.database.depenceTypeDAO();
        instance.depenceCaisseDAO = App.database.depenceCaisseDAO();

        return instance;
    }

    public LiveData<Integer> getAllCountBySessionMutable(String session) {
        return depenceCaisseDAO.getAllCountBySessionMutable(session);
    }

    public LiveData<Integer> getNoSyncCount() {
        return depenceCaisseDAO.getNoSyncCountMutable();
    }

    public LiveData<Integer> getNoSyncCountDepenseType() {
        return dao.getNoSyncCountMutable();
    }


    public LiveData<List<DepenceType>> getAll() {
        return App.database.depenceTypeDAO().getAllMutable();
    }

    public LiveData<List<DepenceCaisse>> getDepenceCaisseByCaisseId(String session) {
        return depenceCaisseDAO.getAllDepenceCaisseBySessionMutable(session);
    }
}