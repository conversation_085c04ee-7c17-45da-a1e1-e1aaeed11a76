package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.BonCommande;
import com.asmtunis.procaissemobility.data.models.DNFamille;
import com.asmtunis.procaissemobility.data.models.VCNewProduct;
import com.asmtunis.procaissemobility.data.models.VCPromo;

import java.util.List;

@Dao
public interface VCNewProductDAO {
    @Query("SELECT * FROM VCNewProduct where Status !='DELETED' order by strftime('%Y-%m-%d %H-%M-%S',DateOp) desc")
    LiveData<List<VCNewProduct>>  getAll();

    @Query("SELECT * FROM VCNewProduct WHERE CodeVCLanPM = :code")
    VCNewProduct getByCodeM(String code);


    @Query("SELECT * FROM VCNewProduct WHERE CodeVCLanP = :code")
    VCNewProduct getByCode(String code);


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<VCNewProduct> vcNewProductList);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(VCNewProduct vcNewProduct );

    @Query("SELECT count(*) FROM VCNewProduct where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    LiveData<Integer> getNoSyncCountMubtale();

    @Query("SELECT count(*) FROM VCNewProduct where isSync=0 and Status='DELETED' ")
    LiveData<Integer> getCountNoSyncedToDeleteMubtale();


    @Query("SELECT * FROM VCNewProduct where isSync=0 and  (Status='INSERTED'  or Status='UPDATED') ")
    List<VCNewProduct> getNoSyncedToAddOrUpdate();

    @Query("SELECT * FROM VCNewProduct where isSync=0 and Status='DELETED' ")
    List<VCNewProduct> getNoSyncedToDelete();

    @Query("SELECT count(*) FROM VCNewProduct where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    int getCountNonSync();

    @Query("SELECT count(*) FROM VCNewProduct where isSync=0 and Status='DELETED' ")
    int getCountNoSyncedToDelete();



    @Query("delete from VCNewProduct")
    void deleteAll();

    @Query("DELETE FROM VCNewProduct where CodeVCLanP=:CodeAutre")
    void deleteById(String CodeAutre);


    @Query("DELETE FROM VCNewProduct where CodeVCLanP=:CodeAutre or CodeVCLanPM = :CodeMobile")
    void deleteByIdAndCodeM(String CodeAutre, String CodeMobile);


    @Query("UPDATE VCNewProduct SET CodeVCLanP = :code_procaiss  where CodeVCLanPM = :CodeMobile")
    void updateCloudCode(String code_procaiss, String CodeMobile);



}
