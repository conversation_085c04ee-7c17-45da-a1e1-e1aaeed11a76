package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
@Entity( primaryKeys =
        {"NumBon_Retour","LIG_BonEntree_CodeArt","LIG_BonEntree_Exerc","LIG_BonEntree_Unite"})
public class LigneBonRetour extends BaseModel{

    @NonNull
    @ColumnInfo(name = "NumBon_Retour")
    @SerializedName("NumBon_Retour")
    @Expose
    private String numBonRetour;
    @NonNull
    @ColumnInfo(name = "LIG_BonEntree_CodeArt")
    @SerializedName("LIG_BonEntree_CodeArt")
    @Expose
    private String lIGBonEntreeCodeArt;
    @NonNull
    @ColumnInfo(name = "LIG_BonEntree_Exerc")
    @SerializedName("LIG_BonEntree_Exerc")
    @Expose
    private String lIGBonEntreeExerc;
    @SerializedName("LIG_BonEntree_Qte")
    @Expose
    private String lIGBonEntreeQte;
    @NonNull
    @ColumnInfo(name = "LIG_BonEntree_Unite")
    @SerializedName("LIG_BonEntree_Unite")
    @Expose
    private String lIGBonEntreeUnite;
    @SerializedName("LIG_BonEntree_PUHT")
    @Expose
    private String lIGBonEntreePUHT;
    @SerializedName("LIG_BonEntree_MntNetHt")
    @Expose
    private String lIGBonEntreeMntNetHt;
    @SerializedName("LIG_BonEntree_MntTva")
    @Expose
    private String lIGBonEntreeMntTva;
    @SerializedName("LIG_BonEntree_Tva")
    @Expose
    private String lIGBonEntreeTva;
    @SerializedName("LIG_BonEntree_MntTTC")
    @Expose
    private String lIGBonEntreeMntTTC;
    @SerializedName("LIG_BonEntree_PUTTC")
    @Expose
    private String lIGBonEntreePUTTC;
    @SerializedName("LIG_BonEntree_Type")
    @Expose
    private String lIGBonEntreeType;
    @SerializedName("LIG_BonEntree_Station")
    @Expose
    private String lIGBonEntreeStation;
    @SerializedName("LIG_BonEntree_User")
    @Expose
    private String lIGBonEntreeUser;
    @SerializedName("LIG_BonEntree_NumOrdre")
    @Expose
    private String lIGBonEntreeNumOrdre;
    @SerializedName("LIG_BonEntree_Taux")
    @Expose
    private String lIGBonEntreeTaux;
    @SerializedName("LIG_BonEntree_QtePiece")
    @Expose
    private String lIGBonEntreeQtePiece;
    @SerializedName("LIG_BonEntree_Remise")
    @Expose
    private String lIGBonEntreeRemise;
    @SerializedName("LIG_BonEntree_QteVendu")
    @Expose
    private String lIGBonEntreeQteVendu;
    @SerializedName("LIG_BonEntree_QteRejete")
    @Expose
    private String lIGBonEntreeQteRejete;
    @SerializedName("LIG_BonEntree_DatePerisage")
    @Expose
    private String lIGBonEntreeDatePerisage;
    @SerializedName("LIG_BonEntree_TauxFodec")
    @Expose
    private String lIGBonEntreeTauxFodec;
    @SerializedName("LIG_BonEntree_TauxDc")
    @Expose
    private String lIGBonEntreeTauxDc;
    @SerializedName("LIG_BonEntree_MntBrutHT")
    @Expose
    private String lIGBonEntreeMntBrutHT;
    @SerializedName("LIG_BonEntree_QteGratuite")
    @Expose
    private String lIGBonEntreeQteGratuite;
    @SerializedName("LIG_BonEntree_export")
    @Expose
    private String lIGBonEntreeExport;
    @SerializedName("LIG_BonEntree_DDm")
    @Expose
    private String lIGBonEntreeDDm;
    @SerializedName("LIG_MargeVente")
    @Expose
    private String lIGMargeVente;
    @SerializedName("LIG_PrixVentePub")
    @Expose
    private String lIGPrixVentePub;
    @SerializedName("LIG_QteVendue")
    @Expose
    private String lIGQteVendue;
    @SerializedName("LIG_PVENTEHT")
    @Expose
    private String lIGPVENTEHT;
    @SerializedName("LIG_TauxEchange")
    @Expose
    private String lIGTauxEchange;
    @SerializedName("LIG_BonEntree_TauxComp")
    @Expose
    private String lIGBonEntreeTauxComp;
    @SerializedName("CMP")
    @Expose
    private String cMP;
    @SerializedName("QteAStock")
    @Expose
    private String qteAStock;
    @SerializedName("LIG_QteRetour")
    @Expose
    private String lIGQteRetour;
    @SerializedName("LIG_Retour")
    @Expose
    private String lIGRetour;
    @SerializedName("LIG_BonEntree_MntFodec")
    @Expose
    private String lIGBonEntreeMntFodec;
    @SerializedName("LIG_BonEntree_MntDc")
    @Expose
    private String lIGBonEntreeMntDc;
    @SerializedName("LIG_NBL_Ticket")
    @Expose
    private String lIGNBLTicket;
    @Ignore
    public Article article;

    public Article getArticle() {
        return article;
    }

    public void setArticle(Article article) {
        this.article = article;
    }

    public String getNumBonRetour() {
        return numBonRetour;
    }

    public void setNumBonRetour(String numBonRetour) {
        this.numBonRetour = numBonRetour;
    }

    public String getLIGBonEntreeCodeArt() {
        return lIGBonEntreeCodeArt;
    }

    public void setLIGBonEntreeCodeArt(String lIGBonEntreeCodeArt) {
        this.lIGBonEntreeCodeArt = lIGBonEntreeCodeArt;
    }

    public String getLIGBonEntreeExerc() {
        return lIGBonEntreeExerc;
    }

    public void setLIGBonEntreeExerc(String lIGBonEntreeExerc) {
        this.lIGBonEntreeExerc = lIGBonEntreeExerc;
    }

    public String getLIGBonEntreeQte() {
        return lIGBonEntreeQte;
    }

    public void setLIGBonEntreeQte(String lIGBonEntreeQte) {
        this.lIGBonEntreeQte = lIGBonEntreeQte;
    }

    public String getLIGBonEntreeUnite() {
        return lIGBonEntreeUnite;
    }

    public void setLIGBonEntreeUnite(String lIGBonEntreeUnite) {
        this.lIGBonEntreeUnite = lIGBonEntreeUnite;
    }

    public String getLIGBonEntreePUHT() {
        return lIGBonEntreePUHT;
    }

    public void setLIGBonEntreePUHT(String lIGBonEntreePUHT) {
        this.lIGBonEntreePUHT = lIGBonEntreePUHT;
    }

    public String getLIGBonEntreeMntNetHt() {
        return lIGBonEntreeMntNetHt;
    }

    public void setLIGBonEntreeMntNetHt(String lIGBonEntreeMntNetHt) {
        this.lIGBonEntreeMntNetHt = lIGBonEntreeMntNetHt;
    }

    public String getLIGBonEntreeMntTva() {
        return lIGBonEntreeMntTva;
    }

    public void setLIGBonEntreeMntTva(String lIGBonEntreeMntTva) {
        this.lIGBonEntreeMntTva = lIGBonEntreeMntTva;
    }

    public String getLIGBonEntreeTva() {
        return lIGBonEntreeTva;
    }

    public void setLIGBonEntreeTva(String lIGBonEntreeTva) {
        this.lIGBonEntreeTva = lIGBonEntreeTva;
    }

    public String getLIGBonEntreeMntTTC() {
        return lIGBonEntreeMntTTC;
    }

    public void setLIGBonEntreeMntTTC(String lIGBonEntreeMntTTC) {
        this.lIGBonEntreeMntTTC = lIGBonEntreeMntTTC;
    }

    public String getLIGBonEntreePUTTC() {
        return lIGBonEntreePUTTC;
    }

    public void setLIGBonEntreePUTTC(String lIGBonEntreePUTTC) {
        this.lIGBonEntreePUTTC = lIGBonEntreePUTTC;
    }

    public String getLIGBonEntreeType() {
        return lIGBonEntreeType;
    }

    public void setLIGBonEntreeType(String lIGBonEntreeType) {
        this.lIGBonEntreeType = lIGBonEntreeType;
    }

    public String getLIGBonEntreeStation() {
        return lIGBonEntreeStation;
    }

    public void setLIGBonEntreeStation(String lIGBonEntreeStation) {
        this.lIGBonEntreeStation = lIGBonEntreeStation;
    }

    public String getLIGBonEntreeUser() {
        return lIGBonEntreeUser;
    }

    public void setLIGBonEntreeUser(String lIGBonEntreeUser) {
        this.lIGBonEntreeUser = lIGBonEntreeUser;
    }

    public String getLIGBonEntreeNumOrdre() {
        return lIGBonEntreeNumOrdre;
    }

    public void setLIGBonEntreeNumOrdre(String lIGBonEntreeNumOrdre) {
        this.lIGBonEntreeNumOrdre = lIGBonEntreeNumOrdre;
    }

    public String getLIGBonEntreeTaux() {
        return lIGBonEntreeTaux;
    }

    public void setLIGBonEntreeTaux(String lIGBonEntreeTaux) {
        this.lIGBonEntreeTaux = lIGBonEntreeTaux;
    }

    public String getLIGBonEntreeQtePiece() {
        return lIGBonEntreeQtePiece;
    }

    public void setLIGBonEntreeQtePiece(String lIGBonEntreeQtePiece) {
        this.lIGBonEntreeQtePiece = lIGBonEntreeQtePiece;
    }

    public String getLIGBonEntreeRemise() {
        return lIGBonEntreeRemise;
    }

    public void setLIGBonEntreeRemise(String lIGBonEntreeRemise) {
        this.lIGBonEntreeRemise = lIGBonEntreeRemise;
    }

    public String getLIGBonEntreeQteVendu() {
        return lIGBonEntreeQteVendu;
    }

    public void setLIGBonEntreeQteVendu(String lIGBonEntreeQteVendu) {
        this.lIGBonEntreeQteVendu = lIGBonEntreeQteVendu;
    }

    public String getLIGBonEntreeQteRejete() {
        return lIGBonEntreeQteRejete;
    }

    public void setLIGBonEntreeQteRejete(String lIGBonEntreeQteRejete) {
        this.lIGBonEntreeQteRejete = lIGBonEntreeQteRejete;
    }

    public String getLIGBonEntreeDatePerisage() {
        return lIGBonEntreeDatePerisage;
    }

    public void setLIGBonEntreeDatePerisage(String lIGBonEntreeDatePerisage) {
        this.lIGBonEntreeDatePerisage = lIGBonEntreeDatePerisage;
    }

    public String getLIGBonEntreeTauxFodec() {
        return lIGBonEntreeTauxFodec;
    }

    public void setLIGBonEntreeTauxFodec(String lIGBonEntreeTauxFodec) {
        this.lIGBonEntreeTauxFodec = lIGBonEntreeTauxFodec;
    }

    public String getLIGBonEntreeTauxDc() {
        return lIGBonEntreeTauxDc;
    }

    public void setLIGBonEntreeTauxDc(String lIGBonEntreeTauxDc) {
        this.lIGBonEntreeTauxDc = lIGBonEntreeTauxDc;
    }

    public String getLIGBonEntreeMntBrutHT() {
        return lIGBonEntreeMntBrutHT;
    }

    public void setLIGBonEntreeMntBrutHT(String lIGBonEntreeMntBrutHT) {
        this.lIGBonEntreeMntBrutHT = lIGBonEntreeMntBrutHT;
    }

    public String getLIGBonEntreeQteGratuite() {
        return lIGBonEntreeQteGratuite;
    }

    public void setLIGBonEntreeQteGratuite(String lIGBonEntreeQteGratuite) {
        this.lIGBonEntreeQteGratuite = lIGBonEntreeQteGratuite;
    }

    public String getLIGBonEntreeExport() {
        return lIGBonEntreeExport;
    }

    public void setLIGBonEntreeExport(String lIGBonEntreeExport) {
        this.lIGBonEntreeExport = lIGBonEntreeExport;
    }

    public String getLIGBonEntreeDDm() {
        return lIGBonEntreeDDm;
    }

    public void setLIGBonEntreeDDm(String lIGBonEntreeDDm) {
        this.lIGBonEntreeDDm = lIGBonEntreeDDm;
    }

    public String getLIGMargeVente() {
        return lIGMargeVente;
    }

    public void setLIGMargeVente(String lIGMargeVente) {
        this.lIGMargeVente = lIGMargeVente;
    }

    public String getLIGPrixVentePub() {
        return lIGPrixVentePub;
    }

    public void setLIGPrixVentePub(String lIGPrixVentePub) {
        this.lIGPrixVentePub = lIGPrixVentePub;
    }

    public String getLIGQteVendue() {
        return lIGQteVendue;
    }

    public void setLIGQteVendue(String lIGQteVendue) {
        this.lIGQteVendue = lIGQteVendue;
    }

    public String getLIGPVENTEHT() {
        return lIGPVENTEHT;
    }

    public void setLIGPVENTEHT(String lIGPVENTEHT) {
        this.lIGPVENTEHT = lIGPVENTEHT;
    }

    public String getLIGTauxEchange() {
        return lIGTauxEchange;
    }

    public void setLIGTauxEchange(String lIGTauxEchange) {
        this.lIGTauxEchange = lIGTauxEchange;
    }

    public String getLIGBonEntreeTauxComp() {
        return lIGBonEntreeTauxComp;
    }

    public void setLIGBonEntreeTauxComp(String lIGBonEntreeTauxComp) {
        this.lIGBonEntreeTauxComp = lIGBonEntreeTauxComp;
    }

    public String getCMP() {
        return cMP;
    }

    public void setCMP(String cMP) {
        this.cMP = cMP;
    }

    public String getQteAStock() {
        return qteAStock;
    }

    public void setQteAStock(String qteAStock) {
        this.qteAStock = qteAStock;
    }

    public String getLIGQteRetour() {
        return lIGQteRetour;
    }

    public void setLIGQteRetour(String lIGQteRetour) {
        this.lIGQteRetour = lIGQteRetour;
    }

    public String getLIGRetour() {
        return lIGRetour;
    }

    public void setLIGRetour(String lIGRetour) {
        this.lIGRetour = lIGRetour;
    }

    public String getLIGBonEntreeMntFodec() {
        return lIGBonEntreeMntFodec;
    }

    public void setLIGBonEntreeMntFodec(String lIGBonEntreeMntFodec) {
        this.lIGBonEntreeMntFodec = lIGBonEntreeMntFodec;
    }

    public String getLIGBonEntreeMntDc() {
        return lIGBonEntreeMntDc;
    }

    public void setLIGBonEntreeMntDc(String lIGBonEntreeMntDc) {
        this.lIGBonEntreeMntDc = lIGBonEntreeMntDc;
    }

    public String getLIGNBLTicket() {
        return lIGNBLTicket;
    }

    public void setLIGNBLTicket(String lIGNBLTicket) {
        this.lIGNBLTicket = lIGNBLTicket;
    }

}
