package com.asmtunis.procaissemobility.recivers;

import static com.blankj.utilcode.util.ServiceUtils.bindService;
import static com.mapbox.mapboxsdk.Mapbox.getApplicationContext;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.PowerManager;
import android.widget.Toast;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.TrakingAlarm;
import com.asmtunis.procaissemobility.helper.utils.ServiceUtils;
import com.asmtunis.procaissemobility.listener.MapObserverListener;
import com.asmtunis.procaissemobility.services.LocationUpdatesService;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class AlarmReceiver extends BroadcastReceiver {

    private Context context;
    MapObserverListener mapObserverListener;

    public AlarmReceiver(Context context, MapObserverListener mapObserverListener) {
        this.context = context;
        this.mapObserverListener = mapObserverListener;
    }

    public AlarmReceiver(Context context) {
        this.context = context;
    }

    public AlarmReceiver() {
        super();
    }

    public void setStartServiceAlarm(String dateStr, String codeOrdre) {
        SimpleDateFormat dt = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        try {
            final int id = (int) System.currentTimeMillis();
            TrakingAlarm trakingAlarm = App.database.trakingAlarmDAO().getWithOrdreMission(codeOrdre);
            Date date = dt.parse(dateStr); // GET TIME HERE
            System.out.println("date " + date);
            AlarmManager am = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
            Intent i = new Intent(context, AlarmReceiver.class);
            i.putExtra("type", "start");
            i.putExtra("code", codeOrdre);
            PendingIntent pi = PendingIntent.getBroadcast(context, id, i, PendingIntent.FLAG_ONE_SHOT);
            // Set the alarm to start at approximately 2:00 p.m.
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(System.currentTimeMillis());
            if (date != null) {
                calendar.setTime(date);
            }
            am.set(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pi);
            trakingAlarm.setted = true;
            trakingAlarm.startAlarmID = String.valueOf(id);
            App.database.trakingAlarmDAO().insertTrAlarm(trakingAlarm);
        } catch (ParseException e) {
            e.printStackTrace();
        }
    }

    public void stopServiceAlarm(int id) {
        AlarmManager alarmManager = (AlarmManager) this.context.getSystemService(Context.ALARM_SERVICE);
        Intent myIntent = new Intent(getApplicationContext(), AlarmReceiver.class);
        PendingIntent pendingIntent = PendingIntent.getBroadcast(getApplicationContext(), id, myIntent, PendingIntent.FLAG_CANCEL_CURRENT);
        alarmManager.cancel(pendingIntent);
    }

    public void setEndServiceAlarm(String dateStr, String codeOrdre) {
        SimpleDateFormat dt = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        try {
            final int id = (int) System.currentTimeMillis();
            TrakingAlarm trakingAlarm = App.database.trakingAlarmDAO().getWithOrdreMission(codeOrdre);
            Date date = dt.parse(dateStr); // GET TIME HERE
            System.out.println("date " + date);
            AlarmManager am =( AlarmManager)context.getSystemService(Context.ALARM_SERVICE);
            Intent i = new Intent(context, AlarmReceiver.class);
            i.putExtra("type", "end");
            i.putExtra("code", codeOrdre);
            PendingIntent pi = PendingIntent.getBroadcast(context, id, i, PendingIntent.FLAG_ONE_SHOT);
            // Set the alarm to start at approximately 2:00 p.m.
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(System.currentTimeMillis());
            calendar.setTime(date);
            am.set(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pi);
            trakingAlarm.setted = true;
            trakingAlarm.endAlarmID = String.valueOf(id);
            App.database.trakingAlarmDAO().insertTrAlarm(trakingAlarm);
        } catch (ParseException e) {
            e.printStackTrace();
        }
    }

    public void stopEndServiceAlarm() {
        Intent intent = new Intent(this.context, AlarmReceiver.class);
        PendingIntent sender = PendingIntent.getBroadcast(this.context, 1, intent, 0);
        AlarmManager alarmManager = (AlarmManager) this.context.getSystemService(Context.ALARM_SERVICE);
        alarmManager.cancel(sender);
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        PowerManager pm = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
        PowerManager.WakeLock wl = pm.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, "wk");
        wl.acquire();
        // Toast.makeText(context, "Alarm !!", Toast.LENGTH_LONG).show(); // For example
        String type = intent.getStringExtra("type");
        String code = intent.getStringExtra("code");
        if(type.equals("start")) {
            if(ServiceUtils.mService != null) {
                ServiceUtils.mService.requestLocationUpdates(mapObserverListener);
            }
            else bindService(new Intent(context, LocationUpdatesService.class), ServiceUtils.mServiceConnection, Context.BIND_AUTO_CREATE);

            TrakingAlarm trakingAlarm = App.database.trakingAlarmDAO().getWithOrdreMission(code);
            trakingAlarm.started = true;
            App.database.trakingAlarmDAO().insertTrAlarm(trakingAlarm);
            Toast.makeText(context, "Tournée en cours ...", Toast.LENGTH_LONG).show(); // For example
        }
        else if(type.equals("end")) {
            if(ServiceUtils.mService != null) {
                ServiceUtils.mService.removeLocationUpdates();
            }
            TrakingAlarm trakingAlarm = App.database.trakingAlarmDAO().getWithOrdreMission(code);
            trakingAlarm.ended = true;
            App.database.trakingAlarmDAO().insertTrAlarm(trakingAlarm);
            Toast.makeText(context, "Tournée finie", Toast.LENGTH_LONG).show(); // For example
        }
        // Put here YOUR code.

        wl.release();
    }
}
