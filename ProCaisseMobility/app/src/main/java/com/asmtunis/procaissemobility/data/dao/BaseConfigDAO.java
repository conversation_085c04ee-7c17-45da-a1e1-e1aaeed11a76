package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.Connexion;

import java.util.List;


@Dao
public interface BaseConfigDAO {
    @Query("SELECT * FROM Connexion")
    List<Connexion> getAll();

    @Query("SELECT * FROM Connexion")
    LiveData<List<Connexion>> getAllMutable();


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(Connexion item);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<Connexion> items);



    @Query("SELECT count(*) FROM Connexion")
    int getCount();


    @Query("DELETE FROM Connexion")
    void deleteAll();

 //   @Query("DELETE FROM Connexion where BOR_Numero= :codeRetour and BOR_Exercice=:exercice")
  //  void deleteById(String codeRetour,String exercice);




}
