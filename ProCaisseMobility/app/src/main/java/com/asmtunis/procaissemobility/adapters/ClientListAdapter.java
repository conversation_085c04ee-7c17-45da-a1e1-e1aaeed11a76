package com.asmtunis.procaissemobility.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.recyclerview.widget.RecyclerView;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.data.models.EtatOrdreMission;
import com.asmtunis.procaissemobility.data.models.LigneOrdreMission;

import java.util.List;

public class ClientListAdapter extends RecyclerView.Adapter<ClientListAdapter.ItemViewHolder> {

    private final List<LigneOrdreMission> items;
    private ItemCallback itemCallback;


    public ClientListAdapter(List<LigneOrdreMission> items) {
        this.items = items;
    }

    public void setCallbacks(ItemCallback itemCallback) {
        this.itemCallback = itemCallback;
    }

    @NonNull
    @Override
    public ItemViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        final View view =
                LayoutInflater.from(parent.getContext())
                        .inflate(R.layout.view_client_item, parent, false);
        return new ItemViewHolder(view, this);
    }

    @Override
    public void onBindViewHolder(ItemViewHolder holder, int position) {
        Client client = App.database.clientDAO().getOneByCode(items.get(position).lIGORClt);
        EtatOrdreMission etatOrdreMission = App.database.etatOrdreMissionDAO().getOne(items.get(position).lIGOREtat);
        holder.title.setText(client.cLINomPren);
        holder.subtitle.setText(etatOrdreMission.libelleEtatOrd == null ? "" : etatOrdreMission.libelleEtatOrd);
    }

    @Override
    public int getItemCount() {
        return items.size();
    }

    public interface ItemCallback {
        void onItemClicked(int itemIndex);
        void onItemLongClick(int itemIndex);
    }


    static class ItemViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener, View.OnLongClickListener {

        final AppCompatTextView title;
        final AppCompatTextView subtitle;
        final ClientListAdapter adapter;

        ItemViewHolder(View itemView, ClientListAdapter adapter) {
            super(itemView);
            title = (AppCompatTextView) itemView.findViewById(R.id.title);
            subtitle = (AppCompatTextView) itemView.findViewById(R.id.subtitle);
            this.adapter = adapter;
            itemView.setOnClickListener(this);
            itemView.setOnLongClickListener(this);
        }

        @Override
        public void onClick(View view) {
            if (adapter.itemCallback == null) {
                return;
            }
            adapter.itemCallback.onItemClicked(getAdapterPosition());
        }

        @Override
        public boolean onLongClick(View v) {
            if (adapter.itemCallback == null) {
                return false;
            }
            adapter.itemCallback.onItemLongClick(
                    getAdapterPosition());
            return false;
        }
    }
}
