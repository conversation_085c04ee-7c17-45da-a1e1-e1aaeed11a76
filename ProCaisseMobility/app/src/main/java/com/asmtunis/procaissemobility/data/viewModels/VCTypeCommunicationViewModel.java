package com.asmtunis.procaissemobility.data.viewModels;

import androidx.fragment.app.Fragment;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelProviders;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.dao.VCTypeCommunicationDAO;
import com.asmtunis.procaissemobility.data.models.VCTypeCommunication;

import java.util.List;

public class VCTypeCommunicationViewModel extends ViewModel {
   public VCTypeCommunicationDAO vcTypeCommunicationDAO;
   private static VCTypeCommunicationViewModel instance;

    public static VCTypeCommunicationViewModel getInstance(Fragment fragment) {
        if(instance ==null){
            instance = new ViewModelProvider(fragment).get(VCTypeCommunicationViewModel.class);
            instance.vcTypeCommunicationDAO=App.database.vcTypeCommunicationDAO();
        }
        return instance;
    }

    public LiveData<List<VCTypeCommunication>> getAllVCTypeCommunication(){
        return App.database.vcTypeCommunicationDAO().getAll();
    }
    public String getTypeCommunicationByCode(String code){
        return App.database.vcTypeCommunicationDAO().getTypeCommunication(code);
    }



}
