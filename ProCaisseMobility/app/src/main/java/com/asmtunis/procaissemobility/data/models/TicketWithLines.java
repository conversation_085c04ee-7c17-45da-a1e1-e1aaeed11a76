package com.asmtunis.procaissemobility.data.models;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

/**
 * Created by PC on 10/12/2017.
 */

public class TicketWithLines extends  BaseModel implements Serializable {

    @SerializedName("ticket")
    @Expose
    Ticket ticket;
    @SerializedName("lignesTicket")
    @Expose
    List<LigneTicket> ligneTicket;

    public TicketWithLines() {
    }

    public TicketWithLines(Ticket ticket, List<LigneTicket> ligneTicket) {
        this.ticket = ticket;
        this.ligneTicket = ligneTicket;
    }

    public Ticket getTicket() {
        return ticket;
    }

    public void setTicket(Ticket ticket) {
        this.ticket = ticket;
    }

    public List<LigneTicket> getLigneTicket() {
        return ligneTicket;
    }

    public void setLigneTicket(List<LigneTicket> ligneTicket) {
        this.ligneTicket = ligneTicket;
    }

    @Override
    public String toString() {
        return "TicketWithLine{" +
                "ticket=" + ticket +
                ", ligneTicket=" + ligneTicket +
                '}';
    }
}
