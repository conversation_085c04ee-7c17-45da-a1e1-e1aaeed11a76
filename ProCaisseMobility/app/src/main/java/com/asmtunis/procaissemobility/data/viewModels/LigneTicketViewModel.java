package com.asmtunis.procaissemobility.data.viewModels;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelProviders;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.dao.LigneTicketDAO;
import com.asmtunis.procaissemobility.data.models.Article;
import com.asmtunis.procaissemobility.data.models.LigneTicket;
import com.asmtunis.procaissemobility.data.models.StationStock;

import java.util.List;

/**
 * Created by Oussama AZIZI on 3/25/22.
 */

public class LigneTicketViewModel extends ViewModel {
    public LigneTicketDAO dao;

    private static LigneTicketViewModel instance;


    public static LigneTicketViewModel getInstance(Fragment activity) {
        if (instance == null)
            instance = new ViewModelProvider(activity).get(LigneTicketViewModel.class);
        instance.dao = App.database.ligneTicketDAO();

        return instance;
    }

    public static LigneTicketViewModel getInstance(FragmentActivity activity) {
        if (instance == null)
            instance = new ViewModelProvider(activity).get(LigneTicketViewModel.class);
        instance.dao = App.database.ligneTicketDAO();
        return instance;
    }

    public void deleteByTicket(int numTicket, boolean chStock, String carnet, String exerc) {
        if(chStock) {
            List<LigneTicket> ligneTicketList = dao.getByTicket(numTicket, carnet, exerc);
            if(!ligneTicketList.isEmpty()) {
                for (LigneTicket ligneTicket : ligneTicketList) {
                    Article article = App.database.articleDAO().getOneByCode(ligneTicket.getlTCodArt());
                    StationStock stationStock = App.database.stationStockDAO().getOneByCode(article.getaRTCode(), App.prefUtils.getUserStationId());
                    double SART_Qte = stationStock.getSARTQte() + ligneTicket.getlTQte();
                    article.setsARTQte(SART_Qte);
                    stationStock.setSARTQte(SART_Qte);
                    App.database.articleDAO().insert(article);
                    App.database.stationStockDAO().insertOne(stationStock);
                }
            }
        }
        dao.deleteByTicket(numTicket, carnet, exerc);
    }

}
