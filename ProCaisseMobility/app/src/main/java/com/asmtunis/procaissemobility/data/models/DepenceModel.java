package com.asmtunis.procaissemobility.data.models;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * Created by Oussama AZIZI on 8/30/22.
 */

public class DepenceModel {
    @SerializedName("DEP_Code")
    @Expose
    private String dEPCode;
    @SerializedName("DEP_Lib")
    @Expose
    private String dEPLib;
    @SerializedName("DEP_IsTactile")
    @Expose
    private String dEPIsTactile;
    @SerializedName("DEP_user")
    @Expose
    private Object dEPUser;
    @SerializedName("DEP_station")
    @Expose
    private String dEPStation;
    @SerializedName("DEP_export")
    @Expose
    private String dEPExport;
    @SerializedName("DEP_DDm")
    @Expose
    private Object dEPDDm;

    public String getDEPCode() {
        return dEPCode;
    }

    public void setDEPCode(String dEPCode) {
        this.dEPCode = dEPCode;
    }

    public String getDEPLib() {
        return dEPLib;
    }

    public void setDEPLib(String dEPLib) {
        this.dEPLib = dEPLib;
    }

    public String getDEPIsTactile() {
        return dEPIsTactile;
    }

    public void setDEPIsTactile(String dEPIsTactile) {
        this.dEPIsTactile = dEPIsTactile;
    }

    public Object getDEPUser() {
        return dEPUser;
    }

    public void setDEPUser(Object dEPUser) {
        this.dEPUser = dEPUser;
    }

    public String getDEPStation() {
        return dEPStation;
    }

    public void setDEPStation(String dEPStation) {
        this.dEPStation = dEPStation;
    }

    public String getDEPExport() {
        return dEPExport;
    }

    public void setDEPExport(String dEPExport) {
        this.dEPExport = dEPExport;
    }

    public Object getDEPDDm() {
        return dEPDDm;
    }

    public void setDEPDDm(Object dEPDDm) {
        this.dEPDDm = dEPDDm;
    }
}
