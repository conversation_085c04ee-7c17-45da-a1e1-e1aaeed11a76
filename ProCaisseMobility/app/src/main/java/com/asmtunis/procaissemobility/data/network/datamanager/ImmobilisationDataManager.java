package com.asmtunis.procaissemobility.data.network.datamanager;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Immobilisation;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.ImmobilisationService;

import java.util.List;

 

public class ImmobilisationDataManager  {

    private static ImmobilisationDataManager sInstance;

    private final ImmobilisationService mImmobilisationService;

    public ImmobilisationDataManager( ) {
        mImmobilisationService = new ServiceFactory<>(ImmobilisationService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"")).makeService();
    }

    public static ImmobilisationDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new ImmobilisationDataManager();
        }
        return sInstance;
    }

    public void getImmobilisation(GenericObject genericObject,
                           RemoteCallback<List<Immobilisation>> listener) {

        mImmobilisationService.getImmobilisation(genericObject)
                .enqueue(listener);
    }




}