package com.asmtunis.procaissemobility.data.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.Timbre;

import java.util.List;

@Dao
public interface TimbreDAO {
    @Query("SELECT * FROM Timbre")
    List<Timbre> getAll();

    @Query("SELECT * FROM Timbre WHERE TIMB_Etat='Actif'")
    Timbre getActif();

    @Query("SELECT * FROM Timbre WHERE TIMB_Code=:code")
    Timbre getByID(String code);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<Timbre> timbres);

    @Query("DELETE FROM Timbre")
    void deleteAll();
}
