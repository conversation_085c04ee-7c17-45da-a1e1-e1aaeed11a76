package com.asmtunis.procaissemobility.data.network.services;

import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Marque;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Headers;
import retrofit2.http.POST;


public interface MarqueService {


    @Headers("User-Agent: android-api-client")
    @POST("getMarques")
    Call<List<Marque>> getMarques(@Body GenericObject genericObject);


}
