package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
@Entity
public class BonRetour extends BaseModel{

    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "BOR_Numero")
    @SerializedName("BOR_Numero")
    @Expose
    private String bORNumero;

    @ColumnInfo(name = "BOR_date")
    @SerializedName("BOR_date")
    @Expose
    private String bORDate;

    @ColumnInfo(name = "BOR_codefrs")
    @SerializedName("BOR_codefrs")
    @Expose
    private String bORCodefrs;

    @ColumnInfo(name = "BOR_Exercice")
    @SerializedName("BOR_Exercice")
    @Expose
    private String bORExercice;

    @ColumnInfo(name = "BOR_NumBE")
    @SerializedName("BOR_NumBE")
    @Expose
    private String bORNumBE;

    @ColumnInfo(name = "BOR_Mnt_HT")
    @SerializedName("BOR_Mnt_HT")
    @Expose
    private String bORMntHT;

    @ColumnInfo(name = "BOR_Mnt_Fodec")
    @SerializedName("BOR_Mnt_Fodec")
    @Expose
    private String bORMntFodec;

    @ColumnInfo(name = "BOR_Mnt_Remise")
    @SerializedName("BOR_Mnt_Remise")
    @Expose
    private String bORMntRemise;

    @ColumnInfo(name = "BOR_Mnt_MntNetHt")
    @SerializedName("BOR_Mnt_MntNetHt")
    @Expose
    private String bORMntMntNetHt;

    @ColumnInfo(name = "BOR_Mnt_Tva")
    @SerializedName("BOR_Mnt_Tva")
    @Expose
    private String bORMntTva;

    @ColumnInfo(name = "BOR_Mnt_TTC")
    @SerializedName("BOR_Mnt_TTC")
    @Expose
    private String bORMntTTC;

    @ColumnInfo(name = "BOR_Station")
    @SerializedName("BOR_Station")
    @Expose
    private String bORStation;
    @ColumnInfo(name = "BOR_Type")
    @SerializedName("BOR_Type")
    @Expose
    private String bORType;
    @ColumnInfo(name = "BOR_Session")
    @SerializedName("BOR_Session")
    @Expose
    private String bORSession;
    @ColumnInfo(name = "BOR_Mnt_Achat")
    @SerializedName("BOR_Mnt_Achat")
    @Expose
    private String bORMntAchat;

    @ColumnInfo(name = "Observation")
    @SerializedName("Observation")
    @Expose
    private String observation;

    @ColumnInfo(name = "BON_ENT_MntFodec")
    @SerializedName("BON_ENT_MntFodec")
    @Expose
    private String bONENTMntFodec;

    @ColumnInfo(name = "BON_ENT_MntDC")
    @SerializedName("BON_ENT_MntDC")
    @Expose
    private String bONENTMntDC;

   /* @ColumnInfo(name = "BOR_Numero_M")
    @SerializedName("BOR_Numero_M")
    @Expose
    public String borNUMM;*/

    public String getBORNumero() {
        return bORNumero;
    }

    public void setBORNumero(String bORNumero) {
        this.bORNumero = bORNumero;
    }

    public String getBORDate() {
        return bORDate;
    }

    public void setBORDate(String bORDate) {
        this.bORDate = bORDate;
    }

    public String getBORCodefrs() {
        return bORCodefrs;
    }

    public void setBORCodefrs(String bORCodefrs) {
        this.bORCodefrs = bORCodefrs;
    }

    public String getBORExercice() {
        return bORExercice;
    }

    public void setBORExercice(String bORExercice) {
        this.bORExercice = bORExercice;
    }

    public String getBORNumBE() {
        return bORNumBE;
    }

    public void setBORNumBE(String bORNumBE) {
        this.bORNumBE = bORNumBE;
    }

    public String getBORMntHT() {
        return bORMntHT;
    }

    public void setBORMntHT(String bORMntHT) {
        this.bORMntHT = bORMntHT;
    }

    public String getBORMntFodec() {
        return bORMntFodec;
    }

    public void setBORMntFodec(String bORMntFodec) {
        this.bORMntFodec = bORMntFodec;
    }

    public String getBORMntRemise() {
        return bORMntRemise;
    }

    public void setBORMntRemise(String bORMntRemise) {
        this.bORMntRemise = bORMntRemise;
    }

    public String getBORMntMntNetHt() {
        return bORMntMntNetHt;
    }

    public void setBORMntMntNetHt(String bORMntMntNetHt) {
        this.bORMntMntNetHt = bORMntMntNetHt;
    }

    public String getBORMntTva() {
        return bORMntTva;
    }

    public void setBORMntTva(String bORMntTva) {
        this.bORMntTva = bORMntTva;
    }

    public String getBORMntTTC() {
        return bORMntTTC;
    }

    public void setBORMntTTC(String bORMntTTC) {
        this.bORMntTTC = bORMntTTC;
    }

    public String getBORStation() {
        return bORStation;
    }

    public void setBORStation(String bORStation) {
        this.bORStation = bORStation;
    }

    public String getBORType() {
        return bORType;
    }

    public void setBORType(String bORType) {
        this.bORType = bORType;
    }

    public String getBORSession() {
        return bORSession;
    }

    public void setBORSession(String bORSession) {
        this.bORSession = bORSession;
    }

    public String getBORMntAchat() {
        return bORMntAchat;
    }

    public void setBORMntAchat(String bORMntAchat) {
        this.bORMntAchat = bORMntAchat;
    }

    public String getObservation() {
        return observation;
    }

    public void setObservation(String observation) {
        this.observation = observation;
    }

    public String getBONENTMntFodec() {
        return bONENTMntFodec;
    }

    public void setBONENTMntFodec(String bONENTMntFodec) {
        this.bONENTMntFodec = bONENTMntFodec;
    }

    public String getBONENTMntDC() {
        return bONENTMntDC;
    }

    public void setBONENTMntDC(String bONENTMntDC) {
        this.bONENTMntDC = bONENTMntDC;
    }

}
