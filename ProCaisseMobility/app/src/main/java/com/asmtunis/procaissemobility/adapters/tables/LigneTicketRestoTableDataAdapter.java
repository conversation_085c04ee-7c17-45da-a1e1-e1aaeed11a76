package com.asmtunis.procaissemobility.adapters.tables;

import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.asmtunis.procaissemobility.data.models.TraiteCaisse;
import com.asmtunis.procaissemobility.ui.components.SortableRestoTicketTableView;
import com.blankj.utilcode.util.StringUtils;

import java.text.NumberFormat;
import java.util.List;

import de.codecrafters.tableview.toolkit.LongPressAwareTableDataAdapter;

//import com.blankj.utilcode.util.StringUtils;

/**
 * Created by pc on 10/26/17.
 */

public class LigneTicketRestoTableDataAdapter extends LongPressAwareTableDataAdapter<TraiteCaisse> {

    private static final int TEXT_SIZE = 16;
    private static final NumberFormat PRICE_FORMATTER = NumberFormat.getNumberInstance();


    public LigneTicketRestoTableDataAdapter(final Context context, final List<TraiteCaisse> data, final SortableRestoTicketTableView tableView) {
        super(context, data, tableView);
    }

    @Override
    public View getDefaultCellView(int rowIndex, int columnIndex, ViewGroup parentView) {
        final TraiteCaisse traiteCaisse = getRowData(rowIndex);
        View renderedView = null;

        switch (columnIndex) {
            case 0:
                renderedView = renderString(traiteCaisse.getCarteResto().getCode());

                break;

            case 1:
                renderedView = renderString(traiteCaisse.getCarteResto().getSociete());
                break;

            case 2:
                renderedView = renderString(com.asmtunis.procaissemobility.helper.utils.StringUtils.priceFormat(traiteCaisse.gettRAITMontant()));
                break;

        }

        return renderedView;
    }

    @Override
    public View getLongPressCellView(int rowIndex, int columnIndex, ViewGroup parentView) {
        return null;
    }


    private View renderString(final String value) {
        final TextView textView = new TextView(getContext());
        if (!StringUtils.isEmpty(value))
        {textView.setText(value);}

        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        textView.setHeight(140);
        textView.setGravity(Gravity.CENTER_VERTICAL);
        return textView;
    }


}
