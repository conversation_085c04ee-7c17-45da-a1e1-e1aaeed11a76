package com.asmtunis.procaissemobility.data.models;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.annotation.NonNull;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * Created by PC on 10/25/2017.
 */
@Entity( primaryKeys =
        {"NumCheque","Reglement"})
public class ChequeCaisse  extends BaseModel implements Serializable {
    @NonNull
    @ColumnInfo(name = "NumCheque")
    @SerializedName("NumCheque")
    @Expose
    public String numCheque;
    @NonNull
    @ColumnInfo(name = "Reglement")
    @SerializedName("Reglement")
    @Expose
    public String reglement;

    public String getReglement_m() {
        return reglement_m;
    }

    public void setReglement_m(String reglement_m) {
        this.reglement_m = reglement_m;
    }

    @ColumnInfo(name = "Reglement_M")
    @SerializedName("Reglement_M")
    @Expose
    public String reglement_m;

    public String getnOMCLIENT() {
        return nOMCLIENT;
    }

    public void setnOMCLIENT(String nOMCLIENT) {
        this.nOMCLIENT = nOMCLIENT;
    }

    @ColumnInfo(name = "CC_user")
    @SerializedName("CC_user")
    @Expose
    public String nOMCLIENT;




    @ColumnInfo(name = "reglement_idsession")
    @SerializedName("reglement_idsession")
    @Expose
    public String reglementidsession;


    @ColumnInfo(name = "reglement_exercice")
    @SerializedName("reglement_exercice")
    @Expose
    public String reglementExercice;
    @ColumnInfo(name = "EcheanceCheque")
    @SerializedName("EcheanceCheque")
    @Expose
    public String echeanceCheque;
    @ColumnInfo(name = "Banque")
    @SerializedName("Banque")
    @Expose
    public String banque;
    @ColumnInfo(name = "Montant")
    @SerializedName("Montant")
    @Expose
    public double montant;

    public ChequeCaisse() {
    }
/*
    public ChequeCaisse(Connexion connexion, String numCheque, String reglement, String reglementIdsession, String reglementExercice, String echeanceCheque, String banque, double montant) {
        super(connexion);
        this.numCheque = numCheque;
        this.reglement = reglement;
        this.reglementIdsession = reglementIdsession;
        this.reglementExercice = reglementExercice;
        this.echeanceCheque = echeanceCheque;
        this.banque = banque;
        this.montant = montant;
    }
*/

    @Ignore
    public ChequeCaisse(String numCheque, String reglement, String reglementidsession, String reglementExercice, String echeanceCheque, String banque, double montant) {
        this.numCheque = numCheque;
        this.reglement = reglement;
        this.reglementidsession = reglementidsession;
        this.reglementExercice = reglementExercice;
        this.echeanceCheque = echeanceCheque;
        this.banque = banque;
        this.montant = montant;

    }
    @Ignore
    public ChequeCaisse(String numCheque, String reglement, String reglementidsession, String reglementExercice, String echeanceCheque, String banque, double montant, String nOMCLIENT) {
        this.numCheque = numCheque;
        this.reglement = reglement;
        this.reglementidsession = reglementidsession;
        this.reglementExercice = reglementExercice;
        this.echeanceCheque = echeanceCheque;
        this.banque = banque;
        this.montant = montant;

        this.nOMCLIENT =  nOMCLIENT;
    }


    public String getNumCheque() {
        return numCheque;
    }

    public void setNumCheque(String numCheque) {
        this.numCheque = numCheque;
    }

    public String getReglement() {
        return reglement;
    }

    public void setReglement(String reglement) {
        this.reglement = reglement;
    }

    public String getReglementidsession() {
        return reglementidsession;
    }

    public void setReglementidsession(String reglementidsession) {
        this.reglementidsession = reglementidsession;
    }

    public String getReglementExercice() {
        return reglementExercice;
    }

    public void setReglementExercice(String reglementExercice) {
        this.reglementExercice = reglementExercice;
    }

    public String getEcheanceCheque() {
        return echeanceCheque;
    }

    public void setEcheanceCheque(String echeanceCheque) {
        this.echeanceCheque = echeanceCheque;
    }

    public String getBanque() {
        return banque;
    }

    public void setBanque(String banque) {
        this.banque = banque;
    }

    public double getMontant() {
        return montant;
    }

    public void setMontant(double montant) {
        this.montant = montant;
    }

    @Override
    public String toString() {
        return "ChequeCaisse{" +
                "numCheque='" + numCheque + '\'' +
                ", reglement='" + reglement + '\'' +
                ", reglementIdsession='" + reglementidsession + '\'' +
                ", reglementExercice=" + reglementExercice +
                ", echeanceCheque='" + echeanceCheque + '\'' +
                ", banque='" + banque + '\'' +
                ", montant=" + montant +
                '}';
    }
}

