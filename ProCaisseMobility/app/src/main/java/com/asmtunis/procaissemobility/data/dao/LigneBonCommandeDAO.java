package com.asmtunis.procaissemobility.data.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.LigneBonCommande;

import java.util.List;
@Dao
public interface LigneBonCommandeDAO {
    @Query("SELECT * FROM LigneBonCommande")
    List<LigneBonCommande> getAll();

    @Query("SELECT * FROM LigneBonCommande where LG_DEV_Code_M =:code")
    List<LigneBonCommande> getByBCCodeM(String code);


    @Query("SELECT * FROM LigneBonCommande where LG_DEV_Code_M =:code")
    LigneBonCommande getOneByBCCodeM(String code);

    @Query("SELECT * FROM LigneBonCommande where LG_DEV_NumBon =:code")
    List<LigneBonCommande> getByBCCode(String code);


    @Query("SELECT * FROM LigneBonCommande where LG_DEV_NumSerie =:code")
    LigneBonCommande getByNumSerie(String code);
    //List<LigneBonCommande> getByNumSerie(String code);


    @Query("SELECT * FROM LigneBonCommande where LG_DEV_NumSerie =:code order by strftime('%Y-%m-%d %H-%M-%S',lGDEVDDm) desc")
    List<LigneBonCommande> getByNumSerieList(String code);

    @Query("SELECT * FROM LigneBonCommande where LG_DEV_NumSerie =:code")
    LigneBonCommande getByNumSerieandCodeClient(String code);

    @Query("SELECT * FROM LigneBonCommande where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    List<LigneBonCommande> getNoSynced();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(LigneBonCommande item);

    @Query("UPDATE LigneBonCommande SET LG_DEV_NumBon = :newNumCommande where LG_DEV_NumBon = :oldNumCommande")
    void updateCodeBonCommande(String newNumCommande, String oldNumCommande);



    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<LigneBonCommande> items);

    @Query("DELETE FROM LigneBonCommande")
    void deleteAll();

    @Query("DELETE FROM LigneBonCommande where LG_DEV_NumBon =:code")
    void deleteByCmd(String code);


    @Query("DELETE FROM LigneBonCommande where LG_DEV_Code_M =:code")
    void deleteByCmdM(String code);

    @Query("DELETE FROM LigneBonCommande where LG_DEV_NumBon= :codeCommande and LG_DEV_Exerc=:exercie ")
    void deleteById(String codeCommande ,String exercie );
}
