package com.asmtunis.procaissemobility.data.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.CarteResto;

import java.util.List;

/**
 * Created by PC on 11/18/2017.
 */
@Dao
public interface CarteRestoDAO {

    @Query("SELECT * FROM CarteResto")
    List<CarteResto> getAll();

    @Query("SELECT * FROM CarteResto WHERE Code = :code ")
   CarteResto getOneByCode(String code);

    @Query("SELECT * FROM CarteResto WHERE Societe = :societe ")
    CarteResto getOneByName(String societe);

    @Query("SELECT * FROM CarteResto LIMIT 1")
    CarteResto getOne();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(CarteResto item);


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<CarteResto> items);

    @Query("DELETE FROM CarteResto where Status='SELECTED'")
    void deleteAll();



}
