package com.asmtunis.procaissemobility.data.network.datamanager;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.BonRetour;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.BonRetourService;

import java.util.List;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

public class BonRetourDataManager {
    private static BonRetourDataManager sInstance;
    private final BonRetourService bonRetourService;

    public BonRetourDataManager() {
        bonRetourService = new ServiceFactory<>(BonRetourService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Retour")).makeService();
    }

    public static BonRetourDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new BonRetourDataManager();
        }
        return sInstance;
    }

    public void getBonRetours(GenericObject genericObject,
                                 RemoteCallback<List<BonRetour>> listener) {
        bonRetourService.getBonRetours(genericObject)
                .enqueue(listener);
    }

    public void addBatchBonRetours(GenericObject commandes,
                                    RemoteCallback<List<BonRetour>> listener) {
        bonRetourService.addBatchBonRetour(commandes)
                .enqueue(listener);
    }
}
