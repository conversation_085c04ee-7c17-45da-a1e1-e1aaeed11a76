package com.asmtunis.procaissemobility.data.converters;

import com.google.gson.ExclusionStrategy;
import com.google.gson.FieldAttributes;

/**
 * Created by PC on 12/18/2017.
 */

public class Exclusion implements ExclusionStrategy {

    public boolean shouldSkipField(FieldAttributes f) {
        return f.getAnnotation(Exclude.class) != null;
    }

    public boolean shouldSkipClass(Class<?> clazz) {
        return false;
    }

}