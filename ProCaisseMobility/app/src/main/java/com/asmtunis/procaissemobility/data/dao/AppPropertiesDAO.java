package com.asmtunis.procaissemobility.data.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.AppProperties;

import java.util.List;

/**
 * Created by PC on 11/18/2017.
 */
@Dao
public interface AppPropertiesDAO {

    @Query("SELECT * FROM AppProperties")
    List<AppProperties> getAll();
    @Query("SELECT * FROM AppProperties  order by Synced_at desc limit 1")
    AppProperties getLastSync();

    @Query("SELECT * FROM AppProperties  order by Updated_at desc limit 1")
    AppProperties getLastUpdate();
    @Query("SELECT * FROM AppProperties WHERE Id = :id ")
    AppProperties getOneById(int id);

    @Query("SELECT * FROM AppProperties  order by Synced_at desc limit 1 ")
    AppProperties getOne();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(AppProperties item);


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<AppProperties> items);


    @Query("DELETE FROM AppProperties")
    void deleteAll();


}
