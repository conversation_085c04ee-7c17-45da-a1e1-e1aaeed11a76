package com.asmtunis.procaissemobility.adapters.items;

import android.content.Context;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.BonRetour;
import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.helper.utils.DateUtils;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.listener.ItemCallback;
import com.asmtunis.procaissemobility.listener.MenuItemClickListener;
import com.asmtunis.procaissemobility.listener.MenuItemsAction;
import com.mikepenz.fastadapter.items.AbstractItem;
import com.mikepenz.fontawesome_typeface_library.FontAwesome;
import com.mikepenz.iconics.IconicsDrawable;

import java.util.List;

public class RetourItem extends AbstractItem<RetourItem, RetourItem.ViewHolder> {

    private final int UNSELECTED = -1;
    private Context context;
    public BonRetour ticket;
    private int selectedItem = UNSELECTED;
    MenuItemsAction menuItemsAction;
    protected ItemCallback itemCallback;
    boolean isOrder;
    int menuId = -1;
    private ViewPager mViewPager;

    public RetourItem(Context context, boolean isOrder, BonRetour ticket, int menuId, MenuItemsAction
            menuItemsAction, ItemCallback itemCallback) {
        this.ticket = ticket;
        this.context = context;
        this.menuItemsAction = menuItemsAction;
        this.menuId = menuId;
        this.itemCallback = itemCallback;
        this.isOrder = isOrder;

    }

    public BonRetour getTicket() {
        return ticket;
    }

    public void setTicket(BonRetour ticket) {
        this.ticket = ticket;
    }

    //The unique ID for this type of item
    @Override
    public int getType() {
        return R.id.fastadapter_ticket_item_id;
    }

    //The unit_price_dialog to be used for this type of item
    @Override
    public int getLayoutRes() {
        return R.layout.ticket_item;
    }

    //The logic to bind your data to the view
    @Override
    public void bindView(final RetourItem.ViewHolder viewHolder, List<Object> payloads) {
        //call super so the selection is already handled for you
        super.bindView(viewHolder, payloads);
        //bind our data
        //set the text for the name
        Client client = App.database.clientDAO().getOneByCode(ticket.getBORCodefrs());
        viewHolder.price.setTextColor(context.getResources().getColor(R.color.successColor));
        viewHolder.setIsRecyclable(false);
        viewHolder.price.setText(String.format("%s %s", StringUtils.priceFormat(Double.parseDouble(
                ticket.getBORMntTTC())), new PrefUtils(context).getCurrency()));

        viewHolder.ticketNumber.setText(String.format(context.getString(R.string.br_number_field), ticket.getBORNumero()));
        viewHolder.ticketUser.setText((client != null) ? client.cLINomPren : ticket.getBORNumero());
        viewHolder.dateCreation.setText(ticket.getBORDate().replace(".000",""));

        viewHolder.toolbar.setTag("toolbar_" + ticket.getBORNumero());
        viewHolder.itemStatusLabel.setTag("itemStatusLabel_" + ticket.getBORNumero());
        viewHolder.price.setVisibility(View.VISIBLE);

        viewHolder.toolbar.inflateMenu(menuId);
        viewHolder.toolbar.setOnMenuItemClickListener(new MenuItemClickListener<BonRetour>(ticket,
                menuItemsAction));
        viewHolder.toolbar.getMenu().findItem(R.id.print_item).setIcon(new IconicsDrawable(context)
                .icon(FontAwesome.Icon.faw_print)
                .color(context.getResources().getColor(R.color.material_teal700))
                .sizeDp(20));


        if (itemCallback != null) {
            viewHolder.itemView.setOnClickListener(v -> onViewClick(viewHolder));
            viewHolder.toolbar.setOnClickListener(v -> onViewClick(viewHolder));
        }

        if (!ticket.isSync) {
            setTriangleView(viewHolder.itemStatusLabel, 0);
            viewHolder.price.setTextColor(context.getResources().getColor(R.color.warningColor));
            viewHolder.dateCreation.setTextColor(context.getResources().getColor(R.color.warningColor));

        }
        else {
            setTriangleView(viewHolder.itemStatusLabel, -1);
            viewHolder.dateCreation.setTextColor(context.getResources().getColor(R.color.successColor));
        }


    }


    void onViewClick(RetourItem.ViewHolder viewHolder) {
        if (ticket != null) {
            if (itemCallback == null) {
                return;
            } else {
                itemCallback.onItemClicked(viewHolder, ticket);
            }
        }
    }


    @Override
    public void unbindView(final RetourItem.ViewHolder holder) {
        super.unbindView(holder);

    }

    @Override
    public RetourItem.ViewHolder getViewHolder(View v) {
        return new RetourItem.ViewHolder(v, menuId, menuItemsAction, ticket);
    }

    //The viewHolder used for this item. This viewHolder is always reused by the RecyclerView so scrolling is blazing fast
    public static class ViewHolder extends RecyclerView.ViewHolder {
        com.asmtunis.procaissemobility.ui.components.TicketView ticketView;
        LinearLayout footerLayout;

        public Toolbar toolbar;
        TextView dateCreation;
        TextView price;
        TextView ticketNumber;
        TextView ticketUser;
        FrameLayout content;


        jp.shts.android.library.TriangleLabelView itemStatusLabel;
        BonRetour ticket;

        public ViewHolder(View view, int menuId, MenuItemsAction menuItemsAction, BonRetour ticket) {
            super(view);
            this.ticket = ticket;
            ticketView = view.findViewById(R.id.layout_ticket);
            footerLayout = view.findViewById(R.id.footer_layout);
            toolbar = view.findViewById(R.id.toolbar);
            ticketNumber = view.findViewById(R.id.ticketNumber);
            ticketUser = view.findViewById(R.id.ticketUser);
            price = view.findViewById(R.id.price);
            dateCreation = view.findViewById(R.id.dateCreation);
            content = view.findViewById(R.id.content_layout);
            itemStatusLabel = view.findViewById(R.id.item_status_label);

        }
    }


    void setTriangleView(jp.shts.android.library.TriangleLabelView labelView, int status) {
        labelView.setVisibility(View.VISIBLE);
        switch (status) {
            case 0:
                labelView.setTriangleBackgroundColorResource(R.color.warningColor);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.notSync);
                labelView.setPrimaryTextColorResource(R.color.md_red_100);

                break;

            case 1:
                labelView.setTriangleBackgroundColorResource(R.color.md_green_800);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.new_label);
                labelView.setPrimaryTextColorResource(R.color.md_green_100);
                break;
            default:
                labelView.setVisibility(View.GONE);

                break;
        }

    }

}


