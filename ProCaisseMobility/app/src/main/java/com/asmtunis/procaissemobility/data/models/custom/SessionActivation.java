package com.asmtunis.procaissemobility.data.models.custom;

import com.blankj.utilcode.util.DeviceUtils;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

    public class SessionActivation {

    @SerializedName("session")
    @Expose
    String session;
    @SerializedName("caisse")
    @Expose
    String caisse;
    @SerializedName("utilisateur")
    @Expose
    String utilisateur;
    @SerializedName("carnet")
    @Expose
    String carnet;
    @SerializedName("station")
    @Expose
    String station;
    @SerializedName("fondCaisse")
    @Expose
    String fondCaisse;
    @SerializedName("nomMachine")
    @Expose
    String nomMachine;

    public SessionActivation() {
    }

    public SessionActivation(String caisse, String utilisateur, String carnet, String station, String fondCaisse, String nomMachine) {
        this.caisse = caisse;
        this.utilisateur = utilisateur;
        this.carnet = carnet;
        this.station = station;
        this.fondCaisse = fondCaisse;
        this.nomMachine = nomMachine;
    }

    public String getSession() {
        return session;
    }

    public void setSession(String session) {
        this.session = session;
    }

    public String getCaisse() {
        return caisse;
    }

    public void setCaisse(String caisse) {
        this.caisse = caisse;
    }

    public String getUtilisateur() {
        return utilisateur;
    }

    public void setUtilisateur(String utilisateur) {
        this.utilisateur = utilisateur;
    }

    public String getCarnet() {
        return carnet;
    }

    public void setCarnet(String carnet) {
        this.carnet = carnet;
    }

    public String getStation() {
        return station;
    }

    public void setStation(String station) {
        this.station = station;
    }

    public String getFondCaisse() {
        return fondCaisse;
    }

    public void setFondCaisse(String fondCaisse) {
        this.fondCaisse = fondCaisse;
    }

    public String getNomMachine() {
        return DeviceUtils.getUniqueDeviceId();
    }

    public void setNomMachine(String nomMachine) {
        this.nomMachine = nomMachine;
    }
}
