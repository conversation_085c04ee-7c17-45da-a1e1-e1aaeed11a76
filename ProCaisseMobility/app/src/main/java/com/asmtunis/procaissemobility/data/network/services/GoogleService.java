package com.asmtunis.procaissemobility.data.network.services;


import com.asmtunis.procaissemobility.data.models.google.DistanceResponse;

import java.util.Map;

import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.QueryMap;

public interface GoogleService {
    @GET("maps/api/distancematrix/json")
    Call<DistanceResponse> getDistanceInfo(
            @QueryMap Map<String, String> parameters
    );
}
