package com.asmtunis.procaissemobility.data.network.datamanager;


import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Devise;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.DeviseService;

import java.util.List;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

/**
 * Created by Achraf on 29/09/2017.
 */

public class DeviseDataManager   {

    private static DeviseDataManager sInstance;
    private final DeviseService mDeviseService;

    public DeviseDataManager( ) {
        mDeviseService = new ServiceFactory<>(DeviseService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Devise")).makeService();
    }

    public static DeviseDataManager getInstance( ) {
        if (sInstance == null) {
            sInstance = new DeviseDataManager();
        }
        return sInstance;
    }

    public void getDevises(GenericObject genericObject,
            RemoteCallback<List<Devise>> listener) {
        mDeviseService.getDevises(genericObject)
                .enqueue(listener);
    }


}


