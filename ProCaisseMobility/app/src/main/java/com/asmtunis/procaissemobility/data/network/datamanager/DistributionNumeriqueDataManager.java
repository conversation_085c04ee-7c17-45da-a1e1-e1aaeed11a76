package com.asmtunis.procaissemobility.data.network.datamanager;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.DNFamille;
import com.asmtunis.procaissemobility.data.models.DNResponseBatchData;
import com.asmtunis.procaissemobility.data.models.DNSuperficie;
import com.asmtunis.procaissemobility.data.models.DNTypePVente;
import com.asmtunis.procaissemobility.data.models.DNTypeServices;
import com.asmtunis.procaissemobility.data.models.DNVIsite;
import com.asmtunis.procaissemobility.data.models.DN_LigneVisite;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.VCAutre;
import com.asmtunis.procaissemobility.data.models.VCImage;
import com.asmtunis.procaissemobility.data.models.VCListeConcurrent;
import com.asmtunis.procaissemobility.data.models.VCNewProduct;
import com.asmtunis.procaissemobility.data.models.VCPrix;
import com.asmtunis.procaissemobility.data.models.VCPromo;
import com.asmtunis.procaissemobility.data.models.VCTypeCommunication;
import com.asmtunis.procaissemobility.data.models.VcResponseBatchData;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.DistributionNumeriqueService;
import com.asmtunis.procaissemobility.data.network.services.VeuilleConcurrentielleService;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;

import java.util.List;

public class DistributionNumeriqueDataManager {




    private static DistributionNumeriqueDataManager sInstance;
    private final DistributionNumeriqueService distributionNumeriqueService;

    public DistributionNumeriqueDataManager() {
        distributionNumeriqueService = new ServiceFactory<>(DistributionNumeriqueService.class,
                String.format(BASE_URL, PrefUtils.getServerIPAddress(), PrefUtils.getServerPort(),"Visite")).makeService();
    }

    public static DistributionNumeriqueDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new DistributionNumeriqueDataManager();
        }
        return sInstance;
    }

    public void getAllTypeService(GenericObject genericObject,
                                 RemoteCallback<List<DNTypeServices>> listener) {
        distributionNumeriqueService.getAllTypeService(genericObject)
                .enqueue(listener);
    }

    public void getAllSuperficies(GenericObject genericObject,
                                  RemoteCallback<List<DNSuperficie>> listener) {
        distributionNumeriqueService.getAllSuperficie(genericObject)
                .enqueue(listener);
    }


    public void getAllTypePVentes(GenericObject genericObject,
                                  RemoteCallback<List<DNTypePVente>> listener) {
        distributionNumeriqueService.getAllTypePVente(genericObject)
                .enqueue(listener);
    }


    public void getDNVIsitesByuser(GenericObject genericObject,
                                   RemoteCallback<List<DNVIsite>> listener) {
        distributionNumeriqueService.getAllVisiteByUser(genericObject,App.prefUtils.getUserId())
                .enqueue(listener);
    }


    public void getAllLignevisiteByUser(GenericObject genericObject,
                             RemoteCallback<List<DN_LigneVisite>> listener) {
        distributionNumeriqueService.getAllLigneVisiteByUser(genericObject,App.prefUtils.getUserId())
                .enqueue(listener);
    }

    public void getalldnFamille(GenericObject genericObject,
                                RemoteCallback<List<DNFamille>> listener) {
        distributionNumeriqueService.getAllFamille(genericObject)
                .enqueue(listener);
    }



    public void getVisitebyCode(GenericObject genericObject,
                             RemoteCallback<DNVIsite> listener) {
        distributionNumeriqueService.getVisiteByCode(genericObject)
                .enqueue(listener);
    }

    public void addBatchVisites(GenericObject genericObject,
                                RemoteCallback<List<DNResponseBatchData>> listener) {
        distributionNumeriqueService.addBatchVisite(genericObject)
                .enqueue(listener);
    }


    public void deleteVisites(GenericObject genericObject,
                                RemoteCallback<List<DNResponseBatchData>> listener) {
        distributionNumeriqueService.deleteVisite(genericObject)
                .enqueue(listener);
    }


   /* public void getAllSuperficie(GenericObject genericObject,
                                  RemoteCallback<List<VCNewProduct>> listener) {
        distributionNumeriqueService.getAllSuperficie(genericObject)
                .enqueue(listener);
    }


    public void deleteBatchVC(GenericObject genericObject, RemoteCallback <Boolean> listener, String table){
        veuilleConcurrentielleService.deleteBatchDataVConcu(genericObject,table).enqueue(listener);
    }*/

}
