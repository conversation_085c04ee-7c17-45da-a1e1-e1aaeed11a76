package com.asmtunis.procaissemobility.data.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.Station;

import java.util.List;
@Dao
public interface StationDAO {
    @Query("SELECT * FROM Station")
    List<Station> getAll();

    @Query("SELECT * FROM Station WHERE sTATCode = :code ")
    Station getOneByCode(String code);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<Station> items);

    @Query("DELETE FROM Station")
    void deleteAll();
}
