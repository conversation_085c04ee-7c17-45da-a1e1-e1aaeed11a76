package com.asmtunis.procaissemobility.data.network.services;

import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Statistics;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Headers;
import retrofit2.http.POST;

/**
 * Created by PC on 10/9/2017.
 */

    public interface MiscService {


    @Headers("User-Agent: android-api-client")
    @POST("Statistics/getStatistics")
    Call<Statistics> getStatistics(@Body GenericObject genericObject);



    @Headers("User-Agent: android-api-client")
    @POST("DB/checkConnectivity")
    Call<Boolean> checkConnectivity(@Body GenericObject genericObject);


}
