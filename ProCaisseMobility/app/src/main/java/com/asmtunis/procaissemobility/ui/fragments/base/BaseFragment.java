package com.asmtunis.procaissemobility.ui.fragments.base;/*
 * Copyright (C) 2014 <PERSON>.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;

import com.asmtunis.procaissemobility.helper.ActivityResultBus;
import com.asmtunis.procaissemobility.helper.ActivityResultEvent;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;
import com.mapbox.mapboxsdk.Mapbox;
import com.squareup.otto.Subscribe;

import butterknife.ButterKnife;
import butterknife.Unbinder;

public abstract class BaseFragment extends Fragment {

    public static String KEY;
    protected Activity context;
    protected PrefUtils prefUtils;
    protected Intent intent;
    private Unbinder unbinder;

    public BaseFragment() {
        // Required empty public constructor
        KEY =  this.getClass().getSimpleName();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        context = getActivity();
        prefUtils = new PrefUtils(context);
        Mapbox.getInstance(context);
        return inflater.inflate(getFragmentLayout(), container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        context = getActivity();
        super.onViewCreated(view, savedInstanceState);
        injectViews(view);
    }

    /**
     * Every fragment has to inflate a unit_price_dialog in the onCreateView method. We have added this method to
     * avoid duplicate all the inflate code in every fragment. You only have to return the unit_price_dialog to
     * inflate in this method when extends BaseFragment.
     */
    protected abstract int getFragmentLayout();


    /**
     * Replace every field annotated with ButterKnife annotations like @InjectView with the proper
     * value.
     *
     * @param view to extract each widget injected in the fragment.
     */
    private void injectViews(final View view) {
        unbinder = ButterKnife.bind(this, view);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }


    @Override
    public void onStart() {
        super.onStart();
        ActivityResultBus.getInstance().register(mActivityResultSubscriber);
    }

    @Override
    public void onStop() {
        super.onStop();
        ActivityResultBus.getInstance().unregister(mActivityResultSubscriber);
    }


    private final Object mActivityResultSubscriber = new Object() {
        @Subscribe
        public void onActivityResultReceived(ActivityResultEvent event) {
            int requestCode = event.getRequestCode();
            int resultCode = event.getResultCode();
            Intent data = event.getData();
            onActivityResult(requestCode, resultCode, data);
        }
    };

}
