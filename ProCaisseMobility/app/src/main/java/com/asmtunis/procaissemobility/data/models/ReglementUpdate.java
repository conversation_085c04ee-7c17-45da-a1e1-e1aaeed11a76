package com.asmtunis.procaissemobility.data.models;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class ReglementUpdate {

    public String getREGC_Code() {
        return REGC_Code;
    }

    public void setREGC_Code(String REGC_Code) {
        this.REGC_Code = REGC_Code;
    }

    public String gettIKIdCarnet() {
        return tIKIdCarnet;
    }

    public void settIKIdCarnet(String tIKIdCarnet) {
        this.tIKIdCarnet = tIKIdCarnet;
    }

    public String gettIKExerc() {
        return tIKExerc;
    }

    public void settIKExerc(String tIKExerc) {
        this.tIKExerc = tIKExerc;
    }

    public String getREGC_Code_M() {
        return REGC_Code_M;
    }

    public void setREGC_Code_M(String REGC_Code_M) {
        this.REGC_Code_M = REGC_Code_M;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @SerializedName("REGC_Code")
    @Expose
    private String REGC_Code;

    @SerializedName("tIKNumTicket")
    @Expose
    private String tIKIdCarnet;

    @SerializedName("REGC_Exercice")
    @Expose
    private String tIKExerc;



    @SerializedName("REGC_Code_M")
    @Expose
    private String REGC_Code_M;
    @SerializedName("message")
    @Expose
    private String message;


    public String getREGC_IdSCaisse() {
        return REGC_IdSCaisse;
    }

    public void setREGC_IdSCaisse(String REGC_IdSCaisse) {
        this.REGC_IdSCaisse = REGC_IdSCaisse;
    }

    @SerializedName("REGC_IdSCaisse")
    @Expose
    private String REGC_IdSCaisse;


    @SerializedName("code")
    @Expose
    private String code;


    public String getCodeClient() {
        return codeClient;
    }

    public void setCodeClient(String codeClient) {
        this.codeClient = codeClient;
    }

    public String getSoldeClient() {
        return soldeClient;
    }

    public void setSoldeClient(String soldeClient) {
        this.soldeClient = soldeClient;
    }

    @SerializedName("CodeClient")
    @Expose
    private String codeClient;


    @SerializedName("SoldeClient")
    @Expose
    private String soldeClient;


    public String getDebit() {
        return Debit;
    }

    public void setDebit(String debit) {
        Debit = debit;
    }

    public String getCredit() {
        return Credit;
    }

    public void setCredit(String credit) {
        Credit = credit;
    }

    @SerializedName("Debit")
    @Expose
    private String Debit;

    @SerializedName("Credit")
    @Expose
    private String Credit;
}
