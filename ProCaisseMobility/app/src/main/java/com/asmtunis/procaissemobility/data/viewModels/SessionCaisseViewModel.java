package com.asmtunis.procaissemobility.data.viewModels;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelProviders;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.dao.SessionCaisseDAO;
import com.asmtunis.procaissemobility.data.dao.TicketDAO;
import com.asmtunis.procaissemobility.data.models.SessionCaisse;
import com.asmtunis.procaissemobility.data.models.Ticket;

import java.util.List;


public class SessionCaisseViewModel extends ViewModel {

    public SessionCaisseDAO dao;

    private static SessionCaisseViewModel instance;


    public static SessionCaisseViewModel getInstance(Fragment activity) {
        if (instance == null)
        instance = new ViewModelProvider(activity).get(SessionCaisseViewModel.class);

        instance.dao = App.database.sessionCaisseDAO();

        return instance;
    }

    public static SessionCaisseViewModel getInstance(FragmentActivity activity) {
        if (instance == null)
        instance = new ViewModelProvider(activity).get(SessionCaisseViewModel.class);

        instance.dao = App.database.sessionCaisseDAO();
        return instance;
    }



    public LiveData<List<SessionCaisse>> getAllSessionCaisseMutable() {
        return dao.getAllMutable();
    }



}
