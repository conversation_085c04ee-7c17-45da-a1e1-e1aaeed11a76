package com.asmtunis.procaissemobility.data.network.services;


import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.TraiteCaisse;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Headers;
import retrofit2.http.POST;

/**
 * Created by Achraf on 29/09/2017.
 */

public interface TraiteCaisseService {

/*
    @Headers("User-Agent: android-api-client")
    @POST("getLigneTicketByCarnetId")
    Call<List<LigneTicket>> getTraiteCaisse(@Body GenericObject genericObject);
*/

    @Headers("User-Agent: android-api-client")
    @POST("getTraiteCaisseByReglement")
    Call<List<TraiteCaisse>> getTraiteCaisseByReglement(@Body GenericObject genericObject);

    @Headers("User-Agent: android-api-client")
    @POST("getTraiteCaisseByReglements")
    Call<List<List<TraiteCaisse>>> getTraiteCaisseByReglements(@Body GenericObject genericObject);


}
