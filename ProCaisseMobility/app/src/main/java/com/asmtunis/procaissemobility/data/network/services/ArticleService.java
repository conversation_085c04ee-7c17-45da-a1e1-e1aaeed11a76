package com.asmtunis.procaissemobility.data.network.services;



import com.asmtunis.procaissemobility.data.models.Article;
import com.asmtunis.procaissemobility.data.models.GenericObject;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * Created by Achraf on 29/09/2017.
 */

public interface ArticleService {

    @POST("getArticles")
    Call<List<Article>> getArticles(@Body GenericObject genericObject,
                                    @Query("ddm") String date,
                                    @Query("station") String station);

    @POST("getArticlesByStation")
   Call<List<Article>> getArticlesByStation(@Body GenericObject genericObject);

}
