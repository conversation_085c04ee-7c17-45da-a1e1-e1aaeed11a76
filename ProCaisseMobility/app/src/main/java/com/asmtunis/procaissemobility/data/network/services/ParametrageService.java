package com.asmtunis.procaissemobility.data.network.services;

import com.asmtunis.procaissemobility.data.models.Famille;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Parametrages;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;



public interface ParametrageService {

    @POST("getParametrage")
    Call<Parametrages> getParametrage(@Body GenericObject genericObject);

}