package com.asmtunis.procaissemobility.data.network.services;


import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.LigneTicket;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Headers;
import retrofit2.http.POST;

/**
 * Created by Achraf on 29/09/2017.
 */

public interface LignesTicketService {


    @Headers("User-Agent: android-api-client")
    @POST("getLigneTicketByCarnetId")
    Call<List<LigneTicket>> getLignesTicket(@Body GenericObject genericObject);


    @Headers("User-Agent: android-api-client")
    @POST("getLigneTicketByTicket")
    Call<List<LigneTicket>> getLigneTicketByTicket(@Body GenericObject genericObject);

    @Headers("User-Agent: android-api-client")
    @POST("getLigneTicketByTickets")
    Call<List<List<LigneTicket>>> getLigneTicketByTickets(@Body GenericObject genericObject);


}
