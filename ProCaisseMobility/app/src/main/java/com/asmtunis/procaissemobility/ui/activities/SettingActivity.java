package com.asmtunis.procaissemobility.ui.activities;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.widget.CompoundButton;
import android.widget.LinearLayout;
import android.widget.RadioGroup;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.appcompat.widget.Toolbar;

import com.afollestad.materialdialogs.MaterialDialog;
import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.AppProperties;
import com.asmtunis.procaissemobility.data.models.Article;
import com.asmtunis.procaissemobility.data.models.Banque;
import com.asmtunis.procaissemobility.data.models.CarteResto;
import com.asmtunis.procaissemobility.data.models.ChequeCaisse;
import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.data.models.ClientArticlePrix;
import com.asmtunis.procaissemobility.data.models.Connexion;
import com.asmtunis.procaissemobility.data.models.Devise;
import com.asmtunis.procaissemobility.data.models.Fournisseur;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.LigneTicket;
import com.asmtunis.procaissemobility.data.models.Prefixe;
import com.asmtunis.procaissemobility.data.models.PricePerStation;
import com.asmtunis.procaissemobility.data.models.Reclamation;
import com.asmtunis.procaissemobility.data.models.ReglementCaisse;
import com.asmtunis.procaissemobility.data.models.StationStock;
import com.asmtunis.procaissemobility.data.models.Statistics;
import com.asmtunis.procaissemobility.data.models.Ticket;
import com.asmtunis.procaissemobility.data.models.TraiteCaisse;
import com.asmtunis.procaissemobility.data.models.Utilisateur;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.datamanager.ArticleDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.BanqueDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.BaseConfigDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.CarteRestoDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.ChequeCaisseDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.ClientArticlePrixDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.ClientDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.DeviseDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.FournisseurDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.LigneTicketDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.MiscDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.PrefixeDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.PricePerStationDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.ReclamationDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.ReglementCaisseDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.StationStockDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.TicketDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.TraiteCaisseDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.UtilisateurDataManager;
import com.asmtunis.procaissemobility.enums.PrinterType;
import com.asmtunis.procaissemobility.enums.PrinterWidth;
import com.asmtunis.procaissemobility.helper.utils.DateUtils;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;
import com.asmtunis.procaissemobility.ui.dialogs.MultiInputMaterialDialogBuilder;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import es.dmoral.toasty.Toasty;
import io.paperdb.Paper;

import static com.asmtunis.procaissemobility.App.prefUtils;
import static com.asmtunis.procaissemobility.helper.Globals.LEFT_LENGTH;
import static com.asmtunis.procaissemobility.helper.Globals.LEFT_TEXT_MAX_LENGTH;
import static com.asmtunis.procaissemobility.helper.Globals.RIGHT_LENGTH;
import static com.asmtunis.procaissemobility.helper.Globals.STATISTICS_DB_KEY;
import static com.asmtunis.procaissemobility.helper.Globals.nbCharAllowed;

public class SettingActivity extends BaseActivity implements
        CompoundButton.OnCheckedChangeListener{

    ProgressDialog mprogress;
    Activity context;
    LinearLayout updateDatabase;
    LinearLayout configbase;
    Toolbar toolbar;
    SwitchCompat autoSyncSwitcher, autoClotSwitch, ean13CheckDigit, a4print, checkUpdate, printTax;
    RadioGroup printerwidthGroupradio, printerTypeRadiogroupe;
    @Override
    public void onBackPressed() {

        startActivity(new Intent(this, MainActivity.class));
        finish();
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        printTax = findViewById(R.id.printTaxSwitcher);
        autoSyncSwitcher = findViewById(R.id.AutoSyncSwitcher);
        autoClotSwitch = findViewById(R.id.autoClotSwitcher);
        ean13CheckDigit = findViewById(R.id.ean13_check_digit);
        a4print = findViewById(R.id.a4_print);
        checkUpdate = findViewById(R.id.check_update);
        autoClotSwitch.setChecked(prefUtils.getClotSess());
        ean13CheckDigit.setChecked(prefUtils.getEan13CheckDigitEnabled());
        a4print.setChecked(prefUtils.getPrintA4Enabled());
        checkUpdate.setChecked(prefUtils.getCheckUpdateEnabled());
        updateDatabase = findViewById(R.id.updateDatabase);
        configbase = findViewById(R.id.ConfigBase);
        toolbar = findViewById(R.id.expanded_toolbar);
        setSupportActionBar(toolbar);

        printerwidthGroupradio = findViewById(R.id.printerwidth_groupradio);
        printerTypeRadiogroupe = findViewById(R.id.type_impriment_radio_groupe);

        // Uncheck or reset the radio buttons initially
        //   radioGroup.clearCheck();

        // Add the Listener to the RadioGroup


        switch (prefUtils.getPrinterWidth()) {
            // case PrinterWidth.FOUR_EIGHT.printerWidth():
            case 48:
                printerwidthGroupradio.check(R.id.radio_48);
                break;
            // case PrinterWidth.THREE_EIGHT.printerWidth():
            case 32:
                printerwidthGroupradio.check(R.id.radia_38);
                break;
        }

        printerwidthGroupradio.setOnCheckedChangeListener((group, checkedId) -> {
            switch (checkedId) {
                case R.id.radio_48:
                    prefUtils.setPrinterWidth(PrinterWidth.FOUR_EIGHT.printerWidth());

                    nbCharAllowed = PrinterWidth.FOUR_EIGHT.printerWidth();

                    LEFT_TEXT_MAX_LENGTH = nbCharAllowed/3;

                    LEFT_LENGTH = (int) (nbCharAllowed/2);

                    RIGHT_LENGTH = (int) (nbCharAllowed/2);
                    break;
                case R.id.radia_38:
                    prefUtils.setPrinterWidth(PrinterWidth.THREE_ZERO.printerWidth());
                    nbCharAllowed = PrinterWidth.THREE_ZERO.printerWidth();

                    LEFT_TEXT_MAX_LENGTH = nbCharAllowed/3;

                    LEFT_LENGTH = (int) (nbCharAllowed/2);

                    RIGHT_LENGTH = (int) (nbCharAllowed/2);
                    break;
            }
        });


        switch (prefUtils.getPrinterType()) {
            // case PrinterWidth.FOUR_EIGHT.printerWidth():
            case "ESC/POS":
                printerTypeRadiogroupe.check(R.id.esc);
                break;
            // case PrinterWidth.THREE_EIGHT.printerWidth():
            case "TSC":
                printerTypeRadiogroupe.check(R.id.tsc);
                break;
        }

        printerTypeRadiogroupe.setOnCheckedChangeListener((group, checkedId) -> {
            switch (checkedId) {
                case R.id.esc:
                    prefUtils.setPrinterType(PrinterType.ESC_POS.PrinterType());
                    break;
                case R.id.tsc:
                    prefUtils.setPrinterType(PrinterType.TSC.PrinterType());
                    break;
            }
        });



        Objects.requireNonNull(getSupportActionBar()).setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setDisplayShowHomeEnabled(true);
        getSupportActionBar().setTitle(R.string.settings_title);
        printTax.setChecked(PrefUtils.getPrintArticleTax());
        autoSyncSwitcher.setChecked(PrefUtils.isAutoSync());

        printTax.setOnCheckedChangeListener(this);
        autoSyncSwitcher.setOnCheckedChangeListener(this);
        ean13CheckDigit.setOnCheckedChangeListener(this);
        a4print.setOnCheckedChangeListener(this);
        checkUpdate.setOnCheckedChangeListener(this);

        mprogress = new ProgressDialog(SettingActivity.this);
        context = this;
        updateDatabase.setOnClickListener(v -> {
            updateLocalData();
        });
        configbase.setOnClickListener(v -> {
            updateConfigBase();
        });
        superAdminAuthentication();
    }

    @SuppressLint("NonConstantResourceId")
    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        switch (buttonView.getId()) {
            case R.id.AutoSyncSwitcher:
                PrefUtils.setAutoSync(isChecked);
                break;
            case R.id.ean13_check_digit:
                prefUtils.setEan13CheckDigitEnabled(isChecked);
                break;
            case R.id.a4_print:
                prefUtils.setPrintA4Enabled(isChecked);
                break;

            case R.id.check_update:
                prefUtils.setCheckUpdateEnabled(isChecked);
                break;

            case R.id.printTaxSwitcher:
                prefUtils.setPrintArticleTax(isChecked);
                break;

                case R.id.autoClotSwitcher:
                prefUtils.setClotSess(isChecked);
                break;
        }
    }

    private void updateConfigBase() {
        mprogress.setMessage(getString(R.string.loading_content));
        BaseConfigDataManager.getInstance().getBaseConfigById(prefUtils.getBaseConfig(),
                new RemoteCallback<Connexion>(this,false) {
            @Override
            public void onSuccess(Connexion response) {
                mprogress.hide();
                prefUtils.setBaseConfig(response);
                Toasty.success(context, getString(R.string.update_config_dataBase_with_success)).show();
            }

            @Override
            public void onUnauthorized() {
                mprogress.hide();
            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.hide();
                Toasty.success(context, getString(R.string.error) + " : " + throwable.getMessage()).show();
            }
        });
    }

    @Override
    protected int setContentView() {
        return R.layout.activity_setting;
    }

    private void updateLocalData() {
        mprogress.show();
        mprogress.setCancelable(false);
        getPrefixes();
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }

    private void superAdminAuthentication() {
        new MultiInputMaterialDialogBuilder(context)
                .addInput("", getString(R.string.username_title), input -> {
                    if(input.length() <= 0) {
                        return getString(R.string.error_field_required);
                    }
                    return null;
                })
                .addInput("", getString(R.string.password_title), input -> {
                    if(input.length() <= 0) {
                        return getString(R.string.error_field_required);
                    }
                    return null;
                })
                .inputs((dialog, inputs, allInputsValidated) -> {
                    String userName;
                    String password;
                    if(allInputsValidated) {
                        userName = inputs.get(0).toString();
                        password = inputs.get(0).toString();
                        authenticate(dialog, userName, password);
                    }
                })
                .positiveText(getString(R.string.dialog_ok))
                .negativeText("CANCEL")
                .onNegative((dialog, which) -> finish())
                .title(R.string.super_user)
                .cancelable(false)
                .show();
    }

    private void authenticate(MaterialDialog dialog, String userName, String password) {
        UtilisateurDataManager.getInstance().authentification(new GenericObject(App.prefUtils.getBaseConfig(),
                new Utilisateur(userName, password)), new RemoteCallback<Utilisateur>(context, true) {
            @Override
            public void onSuccess(Utilisateur response) {
                if (response.getCodeUt() != null) {
                    dialog.dismiss();
                } else {
                    Toast.makeText(context, "Données invalides", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onUnauthorized() {
                //loginButton.setEnabled(true);
            }

            @Override
            public void onFailed(Throwable throwable) {
                //loginButton.setEnabled(true);
            }
        });

    }

    /**
     * get prefixes data from server
     */
    void getPrices() {
        mprogress.setMessage("Chargement des données prix ...");
        PricePerStationDataManager.getInstance().getPricesByStation(new GenericObject(prefUtils.getBaseConfig()),
                new RemoteCallback<List<PricePerStation>>(context, false) {
                    @Override
                    public void onSuccess(List<PricePerStation> response) {
                        App.database.pricePerStationDAO().insertAll(response);
                        getBanques();
                    }

                    @Override
                    public void onUnauthorized() {
                        mprogress.dismiss();
                    }

                    @Override
                    public void onFailed(Throwable throwable) {
                        mprogress.dismiss();
                    }
                });
    }

    void getBanques() {
        mprogress.setMessage("Chargement des données banque ...");
        BanqueDataManager.getInstance().getBanques(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<ArrayList<Banque>>(context, false) {
            @Override
            public void onSuccess(ArrayList<Banque> response) {
                App.database.banqueDAO().insertAll(response);
                getClients();
            }

            @Override
            public void onUnauthorized() {

                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {

                mprogress.dismiss();
            }
        });
    }


    void getClients() {
        mprogress.setMessage("Chargement des données client ...");
        List<String> zones = prefUtils.getUserAccount().getZone();

        ClientDataManager.getInstance().getClients(new GenericObject(prefUtils.getBaseConfig(), zones), new RemoteCallback<List<Client>>(context, false) {
            @Override
            public void onSuccess(List<Client> response) {
                App.database.clientDAO().insertAll(response);
                getArticles();

            }

            @Override
            public void onUnauthorized() {

                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();

            }
        }, prefUtils.getUserAccount().getTypeUser().equalsIgnoreCase("Client")  ? prefUtils.getUserAccount().getCltEquivalent() : null);
    }

    void getArticles() {

        mprogress.setMessage("Chargement des données article ...");
        ArticleDataManager.getInstance().getArticles(new GenericObject(prefUtils.getBaseConfig(), prefUtils.getCaisseStationId()), new RemoteCallback<List<Article>>(context, false) {
            @Override
            public void onSuccess(List<Article> response) {
                if (response != null) {
                    App.database.articleDAO().deleteAll();
                    App.database.articleDAO().insertAll(response);
                    getArticlesStock();
                } else {
                    com.asmtunis.procaissemobility.helper.utils.UIUtils.showDialog(context, "erreur", "0 produit trouvé", (dialog, which) -> finish());
                }
            }

            @Override
            public void onUnauthorized() {

                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();

            }
        });
    }

    void getArticlesStock() {

        mprogress.setMessage("Chargement des données stock ...");
        StationStockDataManager.getInstance().getstockArticle(new GenericObject(prefUtils.getBaseConfig(), prefUtils.getCaisseStationId()), new RemoteCallback<List<StationStock>>(context, false) {
            @Override
            public void onSuccess(List<StationStock> response) {

                if (response != null) {

                    App.database.stationStockDAO().deleteAll();
                    App.database.stationStockDAO().insertAll(response);
                    for (StationStock stationStock : response) {
                        Article article = App.database.articleDAO().getOneByCodeAndStation(stationStock.getSARTCodeArt(),
                                stationStock.getSARTStation());
                        if (article != null) {
                            try {
                                article.setaRTQteStock((stationStock.getSARTQte()));
                            } catch (Exception e) {
                                Log.d("err", e.getMessage());
                            }

                            App.database.articleDAO().insert(article);
                        }
                    }
                    getReclamations();
                } else {
                    com.asmtunis.procaissemobility.helper.utils.UIUtils.showDialog(context, "erreur", "0 produit trouvé", (dialog, which) -> finish());
                }


            }

            @Override
            public void onUnauthorized() {

                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();

            }
        });
    }

    void getReclamations() {

        mprogress.setMessage("Chargement des données reclamations ...");

        ReclamationDataManager.getInstance().getReclamations(new GenericObject(prefUtils.getBaseConfig(), prefUtils.getCaisseId()), new RemoteCallback<List<Reclamation>>(context, false) {
            @Override
            public void onSuccess(List<Reclamation> response) {
                List<Reclamation> reclamations = new ArrayList<>();
                if (response != null || !response.isEmpty()) {

                    if (!reclamations.isEmpty()) {
                        App.database.reclamationDAO().insertAll(reclamations);
                    }

                }
                getTickets();

            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();

            }

            @Override
            public void onFailed(Throwable throwable) {

                mprogress.dismiss();
            }
        });
    }

    void getTickets() {
        mprogress.setMessage("Chargement des données B.L. ...");
        TicketDataManager.getInstance().getTicketsByCaisseId(new GenericObject(prefUtils.getBaseConfig(), prefUtils.getCaisseId()), new RemoteCallback<List<Ticket>>(context, false) {
            @Override
            public void onSuccess(List<Ticket> response) {
                List<Ticket> tickets = new ArrayList<>();
                App.database.ticketDAO().deleteAll();
                if (response != null || !response.isEmpty()) {
                    for (Ticket ticket :
                            response) {
                        if (ticket.gettIKMtTTC() > 0) {
                            tickets.add(ticket);
                        }
                    }

                    if (!tickets.isEmpty()) {
                        App.database.ticketDAO().insertAll(tickets);
                        getLigneTickets(tickets);
                    } else {
                        // startActivity(MainActivity.class);
                        mprogress.dismiss();
                    }

                } else {
                    //  startActivity(MainActivity.class);
                    mprogress.dismiss();
                }
            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();

            }

            @Override
            public void onFailed(Throwable throwable) {

                mprogress.dismiss();
            }
        }, false, true);
    }


    void getLigneTickets(List<Ticket> tickets) {
        mprogress.setMessage("Chargement des données lignes des B.L. ...");
        LigneTicketDataManager.getInstance().getLigneTicketByTickets(new GenericObject(prefUtils.getBaseConfig(), tickets), new RemoteCallback<List<List<LigneTicket>>>(context, false) {
            @Override
            public void onSuccess(List<List<LigneTicket>> response) {

                List<LigneTicket> finalList = new ArrayList<>();
                App.database.ligneTicketDAO().deleteAll();
                if (response.get(0) != null) {
                    for (List<LigneTicket> list :
                            response) {
                        for (LigneTicket ligneTicket : list) {
                            finalList.add(ligneTicket);
                        }
                    }

                    if (!finalList.isEmpty()) {
                        App.database.ligneTicketDAO().insertAll(finalList);
                        getReglementsCaisse(tickets);
                    } else {
                        mprogress.dismiss();
                    }


                } else {
                    mprogress.dismiss();
                }

            }

            @Override
            public void onUnauthorized() {

                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {

                mprogress.dismiss();
            }
        });


    }


 /*   void getReglementsCaisse(List<Ticket> tickets) {
        mprogress.setMessage("Chargement des données réglements (1)...");


        ReglementCaisseDataManager.getInstance().getReglementCaisseByTickets(new GenericObject(prefUtils.getBaseConfig(), tickets), new RemoteCallback<List<List<ReglementCaisse>>>(context, false) {
            @Override
            public void onSuccess(List<List<ReglementCaisse>> response) {


                if (response != null) {

                    App.database.reglementCaisseDAO().deleteAll();

                    List<ReglementCaisse> finalList = new ArrayList<>();

                    for (List<ReglementCaisse> list : response) {

                        for (int i = 0; i < response.size(); i++){
                            if(list.get(i).getrEGCCode_M()==null){
                                list.get(i).setrEGCCode_M(list.get(i).rEGCCode);
                            }

                            list.get(i).setrEGCDateReg(DateUtils.dateToStr(
                                    DateUtils.strToDate( list.get(i).getrEGCDateReg().replaceAll("/", "-"),
                                            "yyyy-MM-dd HH:mm:ss"), "yyyy-MM-dd HH:mm:ss"));
                        }

                        finalList.addAll(list);


                    }


                    if (!finalList.isEmpty()) {
                        App.database.reglementCaisseDAO().deleteAll();

                        App.database.reglementCaisseDAO().insertAll(finalList);
                        getChequesCaisse(finalList);

                    } else {
                        mprogress.dismiss();
                    }
                }

            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();

            }

            @Override
            public void onFailed(Throwable throwable) {

                mprogress.dismiss();
            }
        });


    }

*/
void getReglementsCaisse(List<Ticket> tickets) {
    mprogress.setMessage("Chargement des données réglements (1)...");

    ReglementCaisseDataManager.getInstance().getReglementCaisseByTickets(new GenericObject(prefUtils.getBaseConfig(), tickets), new RemoteCallback<List<List<ReglementCaisse>>>(context, false) {
        @Override
        public void onSuccess(List<List<ReglementCaisse>> response) {

            if (response != null) {

                App.database.reglementCaisseDAO().deleteAll();

                List<ReglementCaisse> finalList = new ArrayList<>();

                for (List<ReglementCaisse> list : response) {
                    for (int i = 0; i < list.size(); i++) { // Fix loop to iterate over 'list', not 'response'
                        ReglementCaisse reglement = list.get(i);
                        if (reglement.getrEGCCode_M() == null) {
                            reglement.setrEGCCode_M(reglement.getrEGCCode());
                        }

                        reglement.setrEGCDateReg(DateUtils.dateToStr(
                                DateUtils.strToDate(reglement.getrEGCDateReg().replaceAll("/", "-"),
                                        "yyyy-MM-dd HH:mm:ss"), "yyyy-MM-dd HH:mm:ss"));
                    }

                    finalList.addAll(list);
                }

                if (!finalList.isEmpty()) {
                    App.database.reglementCaisseDAO().deleteAll();
                    App.database.reglementCaisseDAO().insertAll(finalList);
                    getChequesCaisse(finalList);

                } else {
                    mprogress.dismiss();
                }
            }

        }

        @Override
        public void onUnauthorized() {
            mprogress.dismiss();
        }

        @Override
        public void onFailed(Throwable throwable) {
            mprogress.dismiss();
        }
    });
}

    void getChequesCaisse(List<ReglementCaisse> reglementsCaisse) {
        mprogress.setMessage("Chargement des données réglements (2)...");

        if (!reglementsCaisse.isEmpty()) {
            ChequeCaisseDataManager.getInstance().getChequeCaisseByReglements(new GenericObject(prefUtils.getBaseConfig(), reglementsCaisse), new RemoteCallback<List<List<ChequeCaisse>>>(context, false) {
            @Override
            public void onSuccess(List<List<ChequeCaisse>> response) {
                if (response != null) {
                    App.database.chequeCaisseDAO().deleteAll();
                    App.database.chequeCaisseDAO().insertAll(response.get(0));
                    getTraitesCaisse(reglementsCaisse);
                } else {
                    mprogress.dismiss();

                }

            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();

            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();

            }
        });
        } else {
            mprogress.dismiss();
        }

    }

    void getClientsArticlePrix() {
        mprogress.setMessage("Chargement des données clients articles prix ...");
        ClientArticlePrixDataManager.getInstance().getClientArticlePrix(new GenericObject(App.prefUtils.getBaseConfig()), new RemoteCallback<List<ClientArticlePrix>>(context, false) {
            @Override
            public void onSuccess(List<ClientArticlePrix> response) {
                App.database.clientArticlePrixDAO().insertAll(response);
                prefUtils.setIsInitiated(false);
                prefUtils.setLoadData("full");
                App.database.appPropertiesDAO().insert(new AppProperties(new Date().getTime(), 0L));
                mprogress.dismiss();
            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();
            }
        });
    }



    void getTraitesCaisse(List<ReglementCaisse> reglementsCaisse) {
        mprogress.setMessage("Chargement des données réglements (3)...");
        TraiteCaisseDataManager.getInstance().getTraiteCaisseByReglements(new GenericObject(prefUtils.getBaseConfig(), reglementsCaisse), new RemoteCallback<List<List<TraiteCaisse>>>(context, false) {
            @Override
            public void onSuccess(List<List<TraiteCaisse>> response) {

                if (!response.isEmpty()) {
                    App.database.traiteCaisseDAO().deleteAll();
                    App.database.traiteCaisseDAO().insertAll(response.get(1));
                }
                getClientsArticlePrix();


            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();

            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();

            }
        });


    }


    void getDevises() {
        mprogress.setMessage("Chargement des données devises ...");
        DeviseDataManager.getInstance().getDevises(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<Devise>>(context, false) {
            @Override
            public void onSuccess(List<Devise> response) {
                App.database.deviseDAO().insertAll(response);
                Devise devise = App.database.deviseDAO().getActiveOne();
                if (devise != null) {
                    prefUtils.setCurrency(devise.getSymbole());
                    prefUtils.setDecimalCount(devise.getNbreChiffreVirgule());
                }
                getStatistics();
            }

            @Override
            public void onUnauthorized() {

                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();

            }
        });
    }

    void getPrefixes() {
        mprogress.setMessage("Chargement des données prefixes ...");
        PrefixeDataManager.getInstance().getPrefixes(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<Prefixe>>(context, false) {
            @Override
            public void onSuccess(List<Prefixe> response) {
                App.database.prefixeDAO().insertAll(response);
                getDevises();
            }

            @Override
            public void onUnauthorized() {

                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {

                mprogress.dismiss();
            }
        });
    }


    void getStatistics() {
        mprogress.setMessage("Chargement des données statistiques ...");

        MiscDataManager.getInstance().getStatistics(new GenericObject(prefUtils.getBaseConfig(), prefUtils.getSessionCaisseId()), new RemoteCallback<Statistics>(context, false) {
            @Override
            public void onSuccess(Statistics response) {
                response.setUpdated_at(new Date().getTime());
                Paper.book().write(STATISTICS_DB_KEY, response);
                getFornisseurs();
            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();
            }
        });
    }

    void getFornisseurs() {
        mprogress.setMessage("Chargement des données fornisseurs ...");
        FournisseurDataManager.getInstance().getFournisseurs(new GenericObject(App.prefUtils.getBaseConfig()), new RemoteCallback<List<Fournisseur>>(context, false) {
            @Override
            public void onSuccess(List<Fournisseur> response) {
                App.database.fournisseurDAO().insertAll(response);
                getCartesResto();
            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();

            }
        });
    }

    void getCartesResto() {
        mprogress.setMessage("Chargement des données carte resto ...");
        CarteRestoDataManager.getInstance().getCartesResto(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<CarteResto>>(context, false) {
            @Override
            public void onSuccess(List<CarteResto> response) {
                App.database.carteRestoDAO().insertAll(response);
                getPrices();
            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();

            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();

            }
        });
    }

}
