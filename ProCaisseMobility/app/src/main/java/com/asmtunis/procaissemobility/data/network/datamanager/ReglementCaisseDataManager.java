package com.asmtunis.procaissemobility.data.network.datamanager;


import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.ReglementCaisse;
import com.asmtunis.procaissemobility.data.models.ReglementUpdate;
import com.asmtunis.procaissemobility.data.models.TicketWithLinesAndPayments;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.ReglementCaisseService;

import java.util.List;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

/**
 * Created by PC on 10/4/2017.
 */

public class ReglementCaisseDataManager  {
    private static ReglementCaisseDataManager sInstance;

    private final ReglementCaisseService mReglementCaisseService;

    public ReglementCaisseDataManager( ) {
        mReglementCaisseService = new ServiceFactory<>(ReglementCaisseService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"ReglementCaisse")).makeService();

    }

    public static ReglementCaisseDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new ReglementCaisseDataManager();
        }
        return sInstance;
    }



    public void getReglementCaisseByTicket(GenericObject genericObject,
                                RemoteCallback<List<ReglementCaisse>> listener) {
        mReglementCaisseService.getReglementCaisseByTicket(genericObject)
                .enqueue(listener);
    }
    public void getReglementCaisseByTickets(GenericObject genericObject,
                                       RemoteCallback<List<List<ReglementCaisse>>> listener) {
        mReglementCaisseService.getReglementCaisseByTickets(genericObject)
                .enqueue(listener);
    }

    public void getReglementCaisseBySession(GenericObject genericObject,
                                            RemoteCallback<List<ReglementCaisse>> listener, Boolean archive) {
        mReglementCaisseService.getReglementCaisseBySession(genericObject, App.prefUtils.getExercice(), archive)
                .enqueue(listener);
    }
    public void addBatchPayments(GenericObject genericObject,
                                            RemoteCallback<List<ReglementUpdate>> listener) {
        mReglementCaisseService.addBatchPayments(genericObject)
                .enqueue(listener);
    }


}

