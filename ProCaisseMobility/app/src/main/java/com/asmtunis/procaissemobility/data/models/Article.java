package com.asmtunis.procaissemobility.data.models;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.annotation.NonNull;

import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * Created by Achraf on 14/09/2017.
 */
@Entity( primaryKeys = {"ART_Code"})
public class Article extends BaseModel implements Serializable {

  @Ignore
  private int colorCode =0 ;

  public int getColorCode() {
    return colorCode;
  }

  public void setColorCode(int colorCode) {
    this.colorCode = colorCode;
  }

  @SerializedName("prixGros1")
  @ColumnInfo(name = "prixGros1")
  @Expose
  private String prixGros1;
  @SerializedName("prixGros2")
  @ColumnInfo(name = "prixGros2")
  @Expose
  private String prixGros2;
  @SerializedName("prixGros3")
  @ColumnInfo(name = "prixGros3")
  @Expose
  private String prixGros3;
  @NonNull
  @ColumnInfo(name = "ART_Code")
  @SerializedName("ART_Code")
  @Expose
  public String aRTCode;
  @ColumnInfo(name = "ART_CodeBar")
  @SerializedName("ART_CodeBar")
  @Expose
  public String aRTCodeBar;
  @ColumnInfo(name = "Fils_CodeBar")
  @SerializedName("Fils_CodeBar")
  @Expose
  private String filsCodeBar;
  @ColumnInfo(name = "ART_Designation")
  @SerializedName("ART_Designation")
  @Expose
  public String aRTDesignation;
  @ColumnInfo(name = "ART_PrixUnitaireHT")
  @SerializedName("ART_PrixUnitaireHT")
  @Expose
  public double aRTPrixUnitaireHT;

  public String getPhotoPath() {
    return photoPath;
  }

  public void setPhotoPath(String photoPath) {
    this.photoPath = photoPath;
  }

  @ColumnInfo(name = "photo_Path")
  @SerializedName("photo_Path")
  @Expose
  public String photoPath;
  @ColumnInfo(name = "ART_TVA")
  @SerializedName("ART_TVA")
  @Expose
  public double aRTTVA;
  @ColumnInfo(name = "ART_QteStock")
  @SerializedName("ART_QteStock")
  @Expose
  public double aRTQteStock;
  @ColumnInfo(name = "pvttc")
  @SerializedName("pvttc")
  @Expose
  public double pvttc;
  @ColumnInfo(name = "MAR_Designation")
  @SerializedName("MAR_Designation")
  @Expose
  public String mARDesignation;
  @ColumnInfo(name = "PrixSolde")
  @SerializedName("PrixSolde")
  @Expose
  public String prixSolde;
  @ColumnInfo(name = "TauxSolde")
  @SerializedName("TauxSolde")
  @Expose
  public String tauxSolde;
  @SerializedName("FAM_Lib")
  @ColumnInfo(name = "FAM_Lib")
  @Expose
  public String fAMLib;
  @SerializedName("SART_Qte")
  @ColumnInfo(name = "SART_Qte")
  @Expose
  public double sARTQte;
  @ColumnInfo(name = "SART_CodeSatation")
  @SerializedName("SART_CodeSatation")
  @Expose
  public String sARTCodeSatation;
  @ColumnInfo(name = "FAM_Code")
  @SerializedName("FAM_Code")
  @Expose
  public String fAMCode;
  @SerializedName("UNITE_ARTICLE_QtePiece")
  @ColumnInfo(name = "UNITE_ARTICLE_QtePiece")
  @Expose
  public int uNITEARTICLEQtePiece;
  @SerializedName("UNITE_ARTICLE_CodeUnite")
  @ColumnInfo(name = "UNITE_ARTICLE_CodeUnite")
  @Expose
  public String uNITEARTICLECodeUnite;
  @SerializedName("Art_MaxTRemise")
  @ColumnInfo(name = "Art_MaxTRemise")
  @Expose
  private String artMaxTRemise;
  @SerializedName("ART_PrixPublique")
  @ColumnInfo(name = "ART_PrixPublique")
  @Expose
  private String artPrixPublique;

  @SerializedName("Type_Produit")
  @ColumnInfo(name = "Type_Produit")
  @Expose
  public String typeProduit;

  @SerializedName("ddm")
  @ColumnInfo(name = "ddm")
  @Expose
  private String ddm;

  public double prix;

  public double remise;

  public double count;

  public Marque getSelectedMarque() {
    return selectedMarque;
  }

  public void setSelectedMarque(Marque selectedMarque) {
    this.selectedMarque = selectedMarque;
  }

  @Ignore
  public Marque selectedMarque= new Marque("","","","");

  public Article() {
  }

  @Ignore
  public Article(double aRTPrixUnitaireHT, double pvttc, double sARTQte) {
    this.aRTPrixUnitaireHT = aRTPrixUnitaireHT;
    this.pvttc = pvttc;
    this.sARTQte = sARTQte;
  }

  @Ignore
  public Article(@NonNull String aRTCode, String photoPath) {
    this.aRTCode = aRTCode;
    this.photoPath = photoPath;
  }

  @Ignore
  public Article(@NonNull String aRTCode, String aRTCodeBar, String aRTDesignation, double aRTPrixUnitaireHT, double aRTTVA, String aRTQteStock, double pvttc, String mARDesignation, String prixSolde, String tauxSolde, String fAMLib, double sARTQte, String sARTCodeSatation, String fAMCode, int uNITEARTICLEQtePiece, String uNITEARTICLECodeUnite, double prix, double remise) {
    this.aRTCode = aRTCode;
    this.aRTCodeBar = aRTCodeBar;
    this.aRTDesignation = aRTDesignation;
    this.aRTPrixUnitaireHT = aRTPrixUnitaireHT;
    this.aRTTVA = aRTTVA;
    this.aRTQteStock = Double.parseDouble(aRTQteStock);
    this.pvttc = pvttc;
    this.mARDesignation = mARDesignation;
    this.prixSolde = prixSolde;
    this.tauxSolde = tauxSolde;
    this.fAMLib = fAMLib;
    this.sARTQte = sARTQte;
    this.sARTCodeSatation = sARTCodeSatation;
    this.fAMCode = fAMCode;
    this.uNITEARTICLEQtePiece = uNITEARTICLEQtePiece;
    this.uNITEARTICLECodeUnite = uNITEARTICLECodeUnite;
    this.prix = prix;
    this.remise = remise;
  }

  @Ignore
  public Article(Article article) {
    this.aRTCode = article.getaRTCode();
    this.aRTCodeBar = article.getaRTCodeBar();
    this.aRTDesignation = article.getaRTDesignation();
    this.aRTPrixUnitaireHT = article.getaRTPrixUnitaireHT();
    this.aRTTVA = article.getaRTTVA();
    this.aRTQteStock = article.getaRTQteStock();
    this.pvttc = article.getPvttc();
    this.mARDesignation = article.getmARDesignation();
    this.prixSolde = article.getPrixSolde();
    this.tauxSolde = article.getTauxSolde();
    this.fAMLib = article.getfAMLib();
    this.sARTQte = article.getsARTQte();
    this.sARTCodeSatation = article.getsARTCodeSatation();
    this.fAMCode = article.getfAMCode();
    this.uNITEARTICLEQtePiece = article.getuNITEARTICLEQtePiece();
    this.uNITEARTICLECodeUnite = article.getuNITEARTICLECodeUnite();

  }

  public String getDdm() {
    return ddm;
  }

  public void setDdm(String ddm) {
    this.ddm = ddm;
  }

  public double getCount() {
    return count;
  }

  public void setCount(double count) {
    this.count = count;
  }

  public String getaRTCode() {
    return aRTCode;
  }

  public void setaRTCode(String aRTCode) {
    this.aRTCode = aRTCode;
  }

  public String getaRTCodeBar() {
    return aRTCodeBar;
  }

  public void setaRTCodeBar(String aRTCodeBar) {
    this.aRTCodeBar = aRTCodeBar;
  }

  public String getFilsCodeBar() {
    if (filsCodeBar == null)
      return "null";
    else
      return filsCodeBar;
  }

  public String getArtMaxTRemise() {
    return artMaxTRemise;
  }

  public void setArtMaxTRemise(String artMaxTRemise) {
    this.artMaxTRemise = artMaxTRemise;
  }

  public void setFilsCodeBar(String filsCodeBar) {
    this.filsCodeBar = filsCodeBar;
  }

  public String getaRTDesignation() {
    return aRTDesignation;
  }

  public void setaRTDesignation(String aRTDesignation) {
    this.aRTDesignation = aRTDesignation;
  }

  public double getaRTPrixUnitaireHT() {
    return aRTPrixUnitaireHT;
  }

  public void setaRTPrixUnitaireHT(double aRTPrixUnitaireHT) {
    this.aRTPrixUnitaireHT = aRTPrixUnitaireHT;
  }

  public double getaRTTVA() {
    return aRTTVA;
  }

  public void setaRTTVA(double aRTTVA) {
    this.aRTTVA = aRTTVA;
  }

  public double getaRTQteStock() {
    return aRTQteStock;
  }

  public void  setaRTQteStock(double aRTQteStock) {
    this.aRTQteStock = aRTQteStock;
    this.sARTQte = aRTQteStock;
  }

  public double getPvttc() {
    return pvttc;
  }

  public void setPvttc(double pvttc) {
    this.pvttc = pvttc;
  }

  public String getmARDesignation() {
    return mARDesignation;
  }

  public void setmARDesignation(String mARDesignation) {
    this.mARDesignation = mARDesignation;
  }



  public String getPrixSolde() {
    return prixSolde;
  }

  public void setPrixSolde(String prixSolde) {
    this.prixSolde = prixSolde;
  }

  public String getTauxSolde() {
    return tauxSolde;
  }

  public void setTauxSolde(String tauxSolde) {
    this.tauxSolde = tauxSolde;
  }

  public String getfAMLib() {
    return fAMLib;
  }

  public void setfAMLib(String fAMLib) {
    this.fAMLib = fAMLib;
  }


  public double getsARTQte() {
    return sARTQte;
  }

  public void setsARTQte(double sARTQte) {
    this.sARTQte = sARTQte;
    this.aRTQteStock = sARTQte;
  }

  public String getsARTCodeSatation() {
    return sARTCodeSatation;
  }

  public void setsARTCodeSatation(String sARTCodeSatation) {
    this.sARTCodeSatation = sARTCodeSatation;
  }

  public double getPrix() {
    return prix;
  }

  public void setPrix(double prix) {
    this.prix = prix;
  }

  public double getRemise() {
    return remise;
  }

  public void setRemise(double remise) {
    this.remise = remise;
  }

  public String getfAMCode() {
    return fAMCode;
  }

  public void setfAMCode(String fAMCode) {
    this.fAMCode = fAMCode;
  }

  public int getuNITEARTICLEQtePiece() {
    return uNITEARTICLEQtePiece;
  }

  public void setuNITEARTICLEQtePiece(int uNITEARTICLEQtePiece) {
    this.uNITEARTICLEQtePiece = uNITEARTICLEQtePiece;
  }

  public String getuNITEARTICLECodeUnite() {
    return uNITEARTICLECodeUnite;
  }

  public void setuNITEARTICLECodeUnite(String uNITEARTICLECodeUnite) {
    this.uNITEARTICLECodeUnite = uNITEARTICLECodeUnite;
  }


  public String getPrixGros1() {
    return prixGros1;
  }

  public void setPrixGros1(String prixGros1) {
    this.prixGros1 = prixGros1;
  }

  public String getPrixGros2() {
    return prixGros2;
  }

  public void setPrixGros2(String prixGros2) {
    this.prixGros2 = prixGros2;
  }

  public String getPrixGros3() {
    return prixGros3;
  }

  public void setPrixGros3(String prixGros3) {
    this.prixGros3 = prixGros3;

  }


  public String getArtPrixPublique() {
    return artPrixPublique;
  }

  public void setArtPrixPublique(String artPrixPublique) {
    this.artPrixPublique = artPrixPublique;
  }

  @NonNull
  @Override
  public String toString() {
    return aRTDesignation;
  }
}

