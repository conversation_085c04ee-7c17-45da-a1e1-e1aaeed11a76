package com.asmtunis.procaissemobility.data.models;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;
import androidx.annotation.NonNull;

import com.asmtunis.procaissemobility.data.converters.Exclude;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * Created by Achraf on 03/10/2017.
 */
@Entity
public class Ticket extends BaseModel implements Serializable {

    @ColumnInfo(name = "TIK_NumTicket")
    @SerializedName("TIK_NumTicket")
    @Expose
    public int tIKNumTicket;
    @ColumnInfo(name = "TIK_Exerc")
    @SerializedName("TIK_Exerc")
    @Expose
    public String tIKExerc;
    @ColumnInfo(name = "TIK_IdCarnet")
    @SerializedName("TIK_IdCarnet")
    @Expose
    public String tIKIdCarnet;

    @ColumnInfo(name = "TIK_DateHeureTicket")
    @SerializedName("TIK_DateHeureTicket")
    @Expose
    public String tIKDateHeureTicket;
    @ColumnInfo(name = "TIK_IdSCaisse")
    @SerializedName("TIK_IdSCaisse")
    @Expose
    public String tIKIdSCaisse;
    @ColumnInfo(name = "TIK_CodClt")
    @SerializedName("TIK_CodClt")
    @Expose
    public String tIKCodClt;
    @ColumnInfo(name = "TIK_NumFact")
    @SerializedName("TIK_NumFact")
    @Expose
    public double tIKNumFact;

    @ColumnInfo(name = "TIK_Regler")
    @SerializedName("TIK_Regler")
    @Expose
    public double tIKRegler;

    @ColumnInfo(name = "TIK_MtTTC")
    @SerializedName("TIK_MtTTC")
    @Expose
    public double tIKMtTTC;

    @ColumnInfo(name = "TIK_MtRemise")
    @SerializedName("TIK_MtRemise")
    @Expose
    public double tIKMtRemise;

    @ColumnInfo(name = "TIK_MtHT")
    @SerializedName("TIK_MtHT")
    @Expose
    public double tIKMtHT;

    @ColumnInfo(name = "TIK_MtTVA")
    @SerializedName("TIK_MtTVA")
    @Expose
    public double tIKMtTVA;

    @ColumnInfo(name = "TIK_TypeTiket")
    @SerializedName("TIK_TypeTiket")
    @Expose
    public String tIKTypeTiket;

    @ColumnInfo(name = "TIK_MtEspece")
    @SerializedName("TIK_MtEspece")
    @Expose
    public double tIKMtEspece;

    @ColumnInfo(name = "TIK_MtCheque")
    @SerializedName("TIK_MtCheque")
    @Expose
    public String tIKMtCheque;

    @ColumnInfo(name = "TIK_NumCheque")
    @SerializedName("TIK_NumCheque")
    @Expose
    public String tIKNumCheque;

    @ColumnInfo(name = "TIK_Echeance")
    @SerializedName("TIK_Echeance")
    @Expose
    public String tIKEcheance;

    @ColumnInfo(name = "TIK_MtCarteBanq")
    @SerializedName("TIK_MtCarteBanq")
    @Expose
    public double tIKMtCarteBanq;

    @ColumnInfo(name = "TIK_Mtrecue")
    @SerializedName("TIK_Mtrecue")
    @Expose
    public double tIKMtrecue;

    @ColumnInfo(name = "TIK_Mtrendue")
    @SerializedName("TIK_Mtrendue")
    @Expose
    public double tIKMtrendue;


    public double gettIKMtCredit() {
        return tIKMtCredit;
    }

    public void settIKMtCredit(double tIKMtCredit) {
        this.tIKMtCredit = tIKMtCredit;
    }

    @ColumnInfo(name = "TIK_MtCredit")
    @Exclude
    public double tIKMtCredit;

    @ColumnInfo(name = "TIK_Etat")
    @SerializedName("TIK_Etat")
    @Expose
    public String tIKEtat;

    @ColumnInfo(name = "TIK_DateLivraison")
    @SerializedName("TIK_DateLivraison")
    @Expose
    public String tIKDateLivraison;

    @ColumnInfo(name = "TIK_NomClient")
    @SerializedName("TIK_NomClient")
    @Expose
    public String tIKNomClient;

    @ColumnInfo(name = "TIK_user")
    @SerializedName("TIK_user")
    @Expose
    public String tIKUser;

    @ColumnInfo(name = "TIK_station")
    @SerializedName("TIK_station")
    @Expose
    public String tIKStation;

    @ColumnInfo(name = "TIK_export")
    @SerializedName("TIK_export")
    @Expose
    public int tIKExport;

    @ColumnInfo(name = "TIK_DDm")
    @SerializedName("TIK_DDm")
    @Expose
    public String tIKDDm;

    @ColumnInfo(name = "TIK_Annuler")
    @SerializedName("TIK_Annuler")
    @Expose
    public int tIKAnnuler;

    @ColumnInfo(name = "TIK_CODE_COMMERCIAL")
    @SerializedName("TIK_CODE_COMMERCIAL")
    @Expose
    public String tIKCODECOMMERCIAL;

    @ColumnInfo(name = "TIK_DESIG_COMMERCIAL")
    @SerializedName("TIK_DESIG_COMMERCIAL")
    @Expose
    public String tIKDESIGCOMMERCIAL;

    @ColumnInfo(name = "TIK_is_Contrat")
    @SerializedName("TIK_is_Contrat")
    @Expose
    public int tIKIsContrat;

    @ColumnInfo(name = "TIK_Num_Contrat")
    @SerializedName("TIK_Num_Contrat")
    @Expose
    public String tIKNumContrat;

    @ColumnInfo(name = "TIK_Date_Mariage")
    @SerializedName("TIK_Date_Mariage")
    @Expose
    public String tIKDateMariage;

    @ColumnInfo(name = "TIK_Emplacement_Mariage")
    @SerializedName("TIK_Emplacement_Mariage")
    @Expose
    public String tIKEmplacementMariage;

    @ColumnInfo(name = "TIK_Contrat_Champ1")
    @SerializedName("TIK_Contrat_Champ1")
    @Expose
    public String tIKContratChamp1;

    @ColumnInfo(name = "TIK_Contrat_Champ2")
    @SerializedName("TIK_Contrat_Champ2")
    @Expose
    public String tIKContratChamp2;

    @ColumnInfo(name = "TIK_Nbre_Pts_Gain")
    @SerializedName("TIK_Nbre_Pts_Gain")
    @Expose
    public int tIKNbrePtsGain;

    @ColumnInfo(name = "TIK_Nbre_Total_Pts")
    @SerializedName("TIK_Nbre_Total_Pts")
    @Expose
    public double tIKNbreTotalPts;

    @ColumnInfo(name = "TIK_Num_Carte")
    @SerializedName("TIK_Num_Carte")
    @Expose
    public String tIKNumCarte;
    @SerializedName("TIK_Mnt_Bonus")

    @ColumnInfo(name = "TIK_Mnt_Bonus")
    @Expose
    public String tIKMntBonus;

    @SerializedName("TIK_Source")
    @ColumnInfo(name = "TIK_Source")
    @Expose
    public String tIK_Source;

    @ColumnInfo(name = "TIK_TauxRemise")
    @SerializedName("TIK_TauxRemise")
    @Expose
    public double tIKTauxRemise;

    @ColumnInfo(name = "TIK_NumeroBL")
    @SerializedName("TIK_NumeroBL")
    @Expose
    public String tIKNumeroBL;

    @ColumnInfo(name = "TIK_EnvWebServ")
    @SerializedName("TIK_EnvWebServ")
    @Expose
    public int tIKEnvWebServ;

    @SerializedName("TIK_LatitudeEv")
    @Expose
    private double tIKLatitudeEv;
    @SerializedName("TIK_LongitudeEv")
    @Expose
    private double tIKLongitudeEv;
    @SerializedName("TIK_LatitudeSv")
    @Expose
    private double tIKLatitudeSv;
    @SerializedName("TIK_LongitudeSv")
    @Expose
    private double tIKLongitudeSv;
    @ColumnInfo(name = "TIK_Timbre")
    @SerializedName("TIK_Timbre")
    @Expose
    public double timbre;


    public Double getMntRevImp() {
        return mntRevImp;
    }

    public void setMntRevImp(Double mntRevImp) {
        this.mntRevImp = mntRevImp;
    }

    @ColumnInfo(name = "Mnt_RevImp")
    @SerializedName("Mnt_RevImp")
    @Expose
    public Double mntRevImp;


    public String getNbrLinTIK() {
        return nbrLinTIK;
    }

    public void setNbrLinTIK(String nbrLinTIK) {
        this.nbrLinTIK = nbrLinTIK;
    }

    @SerializedName("TIK_Nbr_Lin")
    @ColumnInfo(name = "TIK_Nbr_Lin")
    @Expose
    public String nbrLinTIK;




    public String getDebugDuplicatedTikNUm() {
        return debugDuplicatedTikNUm;
    }

    public void setDebugDuplicatedTikNUm(String debugDuplicatedTikNUm) {
        this.debugDuplicatedTikNUm = debugDuplicatedTikNUm;
    }

    @ColumnInfo(name = "debugDuplicatedTikNUm")
    @SerializedName("debugDuplicatedTikNUm")
    @Expose
    public String debugDuplicatedTikNUm;

    @NonNull
    public String getTikNumTicketM() {
        return tikNumTicketM;
    }

    public void setTikNumTicketM(@NonNull String tikNumTicketM) {
        this.tikNumTicketM = tikNumTicketM;
    }

    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "TIK_NumTicket_M")
    @SerializedName("TIK_NumTicket_M")
    @Expose
    public String tikNumTicketM;

    public String gettIK_NumeroBL() {
        return tIKNumeroBL;
    }

    public void settIK_NumeroBL(String tIK_NumeroBL) {
        this.tIKNumeroBL = tIK_NumeroBL;
    }

    public Ticket() {
    }

    @Ignore
    public Ticket(int tIKNumTicket,
                  String tIKExerc,
                  String tIKIdCarnet,
                  String tIKDateHeureTicket,
                  String tIKDateMariage,
                  String tIKIdSCaisse,
                  String tIKCodClt,
                  double tIKMtTTC,
                  double tIKMtRemise,
                  double tIKMtHT,
                  double tIKMtTVA,
                  double tIKMtrecue,
                  double tIKMtrendue,
                  String tIKNomClient,
                  String tIKUser,
                  int tIKAnnuler,
                  int tIKIsContrat,
                  String tIKNumCarte,
                  double tIKTauxRemise,
                  int tIKEnvWebServ,
                  boolean isSync,
                  String status,
                  String tIKStation,
                  String tIK_Source,
                  double timbre) {
        this.tIKNumTicket = tIKNumTicket;
        this.tIKExerc = tIKExerc;
        this.tIKIdCarnet = tIKIdCarnet;
        this.tIKDateHeureTicket = tIKDateHeureTicket;
        this.tIKDateMariage = tIKDateMariage;
        this.tIKIdSCaisse = tIKIdSCaisse;
        this.tIKCodClt = tIKCodClt;
        this.tIKMtTTC = tIKMtTTC;
        this.tIKMtRemise = tIKMtRemise;
        this.tIKMtHT = tIKMtHT;
        this.tIKMtTVA = tIKMtTVA;
        this.tIKMtrecue = tIKMtrecue;
        this.tIKMtrendue = tIKMtrendue;
        this.tIKNomClient = tIKNomClient;
        this.tIKUser = tIKUser;
        this.tIKAnnuler = tIKAnnuler;
        this.tIKIsContrat = tIKIsContrat;
        this.tIKNumCarte = tIKNumCarte;
        this.tIKTauxRemise = tIKTauxRemise;
        this.tIKEnvWebServ = tIKEnvWebServ;
        this.status = status;
        this.isSync = isSync;
        this.tIKStation = tIKStation;
        this.tIK_Source = tIK_Source;
        this.timbre = timbre;
    }


    @Ignore
    public Ticket(int tIKNumTicket,
                  String tikNumTicketM,
                  String tIKExerc,
                  String tIKIdCarnet,
                  String tIKDateHeureTicket,
                  String tIKDateMariage,
                  String tIKIdSCaisse,
                  String tIKCodClt,
                  double tIKMtTTC,
                  double tIKMtRemise,
                  double tIKMtHT,
                  double tIKMtTVA,
                  double tIKMtrecue,
                  double tIKMtrendue,
                  String tIKNomClient,
                  String tIKUser,
                  int tIKAnnuler,
                  int tIKIsContrat,
                  String tIKNumCarte,
                  double tIKTauxRemise,
                  int tIKEnvWebServ,
                  boolean isSync,
                  String status,
                  String tIKStation,
                  String tIK_Source,
                  String nbrLinTIK) {
        this.tIKNumTicket = tIKNumTicket;
        this.tikNumTicketM = tikNumTicketM;
        this.tIKExerc = tIKExerc;
        this.tIKIdCarnet = tIKIdCarnet;
        this.tIKDateHeureTicket = tIKDateHeureTicket;
        this.tIKDateMariage = tIKDateMariage;
        this.tIKIdSCaisse = tIKIdSCaisse;
        this.tIKCodClt = tIKCodClt;
        this.tIKMtTTC = tIKMtTTC;
        this.tIKMtRemise = tIKMtRemise;
        this.tIKMtHT = tIKMtHT;
        this.tIKMtTVA = tIKMtTVA;
        this.tIKMtrecue = tIKMtrecue;
        this.tIKMtrendue = tIKMtrendue;
        this.tIKNomClient = tIKNomClient;
        this.tIKUser = tIKUser;
        this.tIKAnnuler = tIKAnnuler;
        this.tIKIsContrat = tIKIsContrat;
        this.tIKNumCarte = tIKNumCarte;
        this.tIKTauxRemise = tIKTauxRemise;
        this.tIKEnvWebServ = tIKEnvWebServ;
        this.status = status;
        this.isSync = isSync;
        this.tIKStation = tIKStation;
        this.tIK_Source = tIK_Source;
        this.nbrLinTIK = nbrLinTIK;

    }

    @Ignore
    public Ticket(int tIKNumTicket,
                  String tIKExerc,
                  String tIKIdCarnet,
                  String tIKDateHeureTicket,
                  String tIKDateMariage,
                  String tIKIdSCaisse,
                  String tIKCodClt,
                  double tIKMtTTC,
                  double tIKMtRemise,
                  double tIKMtHT,
                  double tIKMtTVA,
                  double tIKMtrecue,
                  double tIKMtrendue,
                  String tIKNomClient,
                  String tIKUser,
                  int tIKAnnuler,
                  int tIKIsContrat,
                  String tIKNumCarte,
                  double tIKTauxRemise,
                  int tIKEnvWebServ,
                  boolean isSync,
                  String status,
                  String tIKStation,
                  String tIK_Source,
                  String nbrLinTIK) {
        this.tIKNumTicket = tIKNumTicket;
        this.tIKExerc = tIKExerc;
        this.tIKIdCarnet = tIKIdCarnet;
        this.tIKDateHeureTicket = tIKDateHeureTicket;
        this.tIKDateMariage = tIKDateMariage;
        this.tIKIdSCaisse = tIKIdSCaisse;
        this.tIKCodClt = tIKCodClt;
        this.tIKMtTTC = tIKMtTTC;
        this.tIKMtRemise = tIKMtRemise;
        this.tIKMtHT = tIKMtHT;
        this.tIKMtTVA = tIKMtTVA;
        this.tIKMtrecue = tIKMtrecue;
        this.tIKMtrendue = tIKMtrendue;
        this.tIKNomClient = tIKNomClient;
        this.tIKUser = tIKUser;
        this.tIKAnnuler = tIKAnnuler;
        this.tIKIsContrat = tIKIsContrat;
        this.tIKNumCarte = tIKNumCarte;
        this.tIKTauxRemise = tIKTauxRemise;
        this.tIKEnvWebServ = tIKEnvWebServ;
        this.status = status;
        this.isSync = isSync;
        this.tIKStation = tIKStation;
        this.tIK_Source = tIK_Source;
        this.nbrLinTIK = nbrLinTIK;

    }

    public double getTimbre() {
        return timbre;
    }

    public void setTimbre(double timbre) {
        this.timbre = timbre;
    }

    public double getTIKLatitudeEv() {
        return tIKLatitudeEv;
    }

    public void setTIKLatitudeEv(double tIKLatitudeEv) {
        this.tIKLatitudeEv = tIKLatitudeEv;
    }

    public double getTIKLongitudeEv() {
        return tIKLongitudeEv;
    }

    public void setTIKLongitudeEv(double tIKLongitudeEv) {
        this.tIKLongitudeEv = tIKLongitudeEv;
    }

    public double getTIKLatitudeSv() {
        return tIKLatitudeSv;
    }

    public void setTIKLatitudeSv(double tIKLatitudeSv) {
        this.tIKLatitudeSv = tIKLatitudeSv;
    }

    public double getTIKLongitudeSv() {
        return tIKLongitudeSv;
    }

    public void setTIKLongitudeSv(double tIKLongitudeSv) {
        this.tIKLongitudeSv = tIKLongitudeSv;
    }

    public int gettIKNumTicket() {
        return tIKNumTicket;
    }

    public void settIKNumTicket(int tIKNumTicket) {
        this.tIKNumTicket = tIKNumTicket;
    }

    public String gettIKExerc() {
        return tIKExerc;
    }

    public void settIKExerc(String tIKExerc) {
        this.tIKExerc = tIKExerc;
    }

    public String gettIKIdCarnet() {
        return tIKIdCarnet;
    }

    public void settIKIdCarnet(String tIKIdCarnet) {
        this.tIKIdCarnet = tIKIdCarnet;
    }

    public String gettIKDateHeureTicket() {
        return tIKDateHeureTicket;
    }

    public void settIKDateHeureTicket(String tIKDateHeureTicket) {
        this.tIKDateHeureTicket = tIKDateHeureTicket;
    }

    public String gettIKIdSCaisse() {
        return tIKIdSCaisse;
    }

    public void settIKIdSCaisse(String tIKIdSCaisse) {
        this.tIKIdSCaisse = tIKIdSCaisse;
    }

    public String gettIKCodClt() {
        return tIKCodClt;
    }

    public void settIKCodClt(String tIKCodClt) {
        this.tIKCodClt = tIKCodClt;
    }

    public double gettIKNumFact() {
        return tIKNumFact;
    }

    public void settIKNumFact(double tIKNumFact) {
        this.tIKNumFact = tIKNumFact;
    }

    public double gettIKRegler() {
        return tIKRegler;
    }

    public void settIKRegler(double tIKRegler) {
        this.tIKRegler = tIKRegler;
    }

    public double gettIKMtTTC() {
        return tIKMtTTC;
    }

    public void settIKMtTTC(double tIKMtTTC) {
        this.tIKMtTTC = tIKMtTTC;
    }

    public double gettIKMtRemise() {
        return tIKMtRemise;
    }

    public void settIKMtRemise(double tIKMtRemise) {
        this.tIKMtRemise = tIKMtRemise;
    }

    public double gettIKMtHT() {
        return tIKMtHT;
    }

    public void settIKMtHT(double tIKMtHT) {
        this.tIKMtHT = tIKMtHT;
    }

    public double gettIKMtTVA() {
        return tIKMtTVA;
    }

    public void settIKMtTVA(double tIKMtTVA) {
        this.tIKMtTVA = tIKMtTVA;
    }

    public String gettIKTypeTiket() {
        return tIKTypeTiket;
    }

    public void settIKTypeTiket(String tIKTypeTiket) {
        this.tIKTypeTiket = tIKTypeTiket;
    }

    public double gettIKMtEspece() {
        return tIKMtEspece;
    }

    public void settIKMtEspece(double tIKMtEspece) {
        this.tIKMtEspece = tIKMtEspece;
    }

    public String gettIKMtCheque() {
        return tIKMtCheque;
    }

    public void settIKMtCheque(String tIKMtCheque) {
        this.tIKMtCheque = tIKMtCheque;
    }

    public String gettIKNumCheque() {
        return tIKNumCheque;
    }

    public void settIKNumCheque(String tIKNumCheque) {
        this.tIKNumCheque = tIKNumCheque;
    }

    public String gettIKEcheance() {
        return tIKEcheance;
    }

    public void settIKEcheance(String tIKEcheance) {
        this.tIKEcheance = tIKEcheance;
    }

    public double gettIKMtCarteBanq() {
        return tIKMtCarteBanq;
    }

    public void settIKMtCarteBanq(double tIKMtCarteBanq) {
        this.tIKMtCarteBanq = tIKMtCarteBanq;
    }

    public double gettIKMtrecue() {
        return tIKMtrecue;
    }

    public void settIKMtrecue(double tIKMtrecue) {
        this.tIKMtrecue = tIKMtrecue;
    }

    public double gettIKMtrendue() {
        return tIKMtrendue;
    }

    public void settIKMtrendue(double tIKMtrendue) {
        this.tIKMtrendue = tIKMtrendue;
    }

    public String gettIKEtat() {
        return tIKEtat;
    }

    public void settIKEtat(String tIKEtat) {
        this.tIKEtat = tIKEtat;
    }

    public String gettIKDateLivraison() {
        return tIKDateLivraison;
    }

    public void settIKDateLivraison(String tIKDateLivraison) {
        this.tIKDateLivraison = tIKDateLivraison;
    }

    public String gettIKNomClient() {
        return tIKNomClient;
    }

    public void settIKNomClient(String tIKNomClient) {
        this.tIKNomClient = tIKNomClient;
    }

    public String gettIKUser() {
        return tIKUser;
    }

    public void settIKUser(String tIKUser) {
        this.tIKUser = tIKUser;
    }

    public String gettIKStation() {
        return tIKStation;
    }

    public void settIKStation(String tIKStation) {
        this.tIKStation = tIKStation;
    }

    public int gettIKExport() {
        return tIKExport;
    }

    public void settIKExport(int tIKExport) {
        this.tIKExport = tIKExport;
    }

    public String gettIKDDm() {
        return tIKDDm;
    }

    public int gettIKAnnuler() {
        return tIKAnnuler;
    }

    public void settIKAnnuler(int tIKAnnuler) {
        this.tIKAnnuler = tIKAnnuler;
    }

    public String gettIKCODECOMMERCIAL() {
        return tIKCODECOMMERCIAL;
    }

    public void settIKCODECOMMERCIAL(String tIKCODECOMMERCIAL) {
        this.tIKCODECOMMERCIAL = tIKCODECOMMERCIAL;
    }

    public String gettIKDESIGCOMMERCIAL() {
        return tIKDESIGCOMMERCIAL;
    }

    public void settIKDESIGCOMMERCIAL(String tIKDESIGCOMMERCIAL) {
        this.tIKDESIGCOMMERCIAL = tIKDESIGCOMMERCIAL;
    }

    public int gettIKIsContrat() {
        return tIKIsContrat;
    }

    public void settIKIsContrat(int tIKIsContrat) {
        this.tIKIsContrat = tIKIsContrat;
    }

    public String gettIKNumContrat() {
        return tIKNumContrat;
    }

    public void settIKNumContrat(String tIKNumContrat) {
        this.tIKNumContrat = tIKNumContrat;
    }

    public String gettIKDateMariage() {
        return tIKDateMariage;
    }

    public void settIKDateMariage(String tIKDateMariage) {
        this.tIKDateMariage = tIKDateMariage;
    }

    public String gettIKEmplacementMariage() {
        return tIKEmplacementMariage;
    }

    public void settIKEmplacementMariage(String tIKEmplacementMariage) {
        this.tIKEmplacementMariage = tIKEmplacementMariage;
    }

    public String gettIKContratChamp1() {
        return tIKContratChamp1;
    }

    public void settIKContratChamp1(String tIKContratChamp1) {
        this.tIKContratChamp1 = tIKContratChamp1;
    }

    public String gettIKContratChamp2() {
        return tIKContratChamp2;
    }

    public void settIKContratChamp2(String tIKContratChamp2) {
        this.tIKContratChamp2 = tIKContratChamp2;
    }

    public int gettIKNbrePtsGain() {
        return tIKNbrePtsGain;
    }

    public void settIKNbrePtsGain(int tIKNbrePtsGain) {
        this.tIKNbrePtsGain = tIKNbrePtsGain;
    }

    public double gettIKNbreTotalPts() {
        return tIKNbreTotalPts;
    }

    public void settIKNbreTotalPts(double tIKNbreTotalPts) {
        this.tIKNbreTotalPts = tIKNbreTotalPts;
    }

    public String gettIKNumCarte() {
        return tIKNumCarte;
    }

    public void settIKNumCarte(String tIKNumCarte) {
        this.tIKNumCarte = tIKNumCarte;
    }

    public String gettIKMntBonus() {
        return tIKMntBonus;
    }

    public void settIKMntBonus(String tIKMntBonus) {
        this.tIKMntBonus = tIKMntBonus;
    }

    public double gettIKTauxRemise() {
        return tIKTauxRemise;
    }

    public void settIKTauxRemise(double tIKTauxRemise) {
        this.tIKTauxRemise = tIKTauxRemise;
    }

    public String gettIKNumeroBL() {
        return tIKNumeroBL;
    }

    public void settIKNumeroBL(String tIKNumeroBL) {
        this.tIKNumeroBL = tIKNumeroBL;
    }

    public int gettIKEnvWebServ() {
        return tIKEnvWebServ;
    }

    public void settIKEnvWebServ(int tIKEnvWebServ) {
        this.tIKEnvWebServ = tIKEnvWebServ;
    }

    @Override
    public String toString() {
        return "Ticket{" +
                "tIKNumTicket=" + tIKNumTicket +
                ", tIKExerc='" + tIKExerc + '\'' +
                ", tIKIdCarnet='" + tIKIdCarnet + '\'' +
                ", tIKDateHeureTicket='" + tIKDateHeureTicket + '\'' +
                ", tIKIdSCaisse='" + tIKIdSCaisse + '\'' +
                ", tIKCodClt='" + tIKCodClt + '\'' +
                ", tIKNumFact=" + tIKNumFact +
                ", tIKRegler=" + tIKRegler +
                ", tIKMtTTC=" + tIKMtTTC +
                ", tIKMtRemise=" + tIKMtRemise +
                ", tIKMtHT=" + tIKMtHT +
                ", tIKMtTVA=" + tIKMtTVA +
                ", tIKTypeTiket='" + tIKTypeTiket + '\'' +
                ", tIKMtEspece='" + tIKMtEspece + '\'' +
                ", tIKMtCheque='" + tIKMtCheque + '\'' +
                ", tIKNumCheque='" + tIKNumCheque + '\'' +
                ", tIKEcheance='" + tIKEcheance + '\'' +
                ", tIKMtCarteBanq=" + tIKMtCarteBanq +
                ", tIKMtrecue=" + tIKMtrecue +
                ", tIKMtrendue=" + tIKMtrendue +
                ", tIKEtat='" + tIKEtat + '\'' +
                ", tIKDateLivraison='" + tIKDateLivraison + '\'' +
                ", tIKNomClient='" + tIKNomClient + '\'' +
                ", tIKUser='" + tIKUser + '\'' +
                ", tIKStation='" + tIKStation + '\'' +
                ", tIKExport=" + tIKExport +
                ", tIKDDm='" + tIKDDm + '\'' +
                ", tIKAnnuler=" + tIKAnnuler +
                ", tIKCODECOMMERCIAL='" + tIKCODECOMMERCIAL + '\'' +
                ", tIKDESIGCOMMERCIAL='" + tIKDESIGCOMMERCIAL + '\'' +
                ", tIKIsContrat='" + tIKIsContrat + '\'' +
                ", tIKNumContrat='" + tIKNumContrat + '\'' +
                ", tIKDateMariage='" + tIKDateMariage + '\'' +
                ", tIKEmplacementMariage='" + tIKEmplacementMariage + '\'' +
                ", tIKContratChamp1='" + tIKContratChamp1 + '\'' +
                ", tIKContratChamp2='" + tIKContratChamp2 + '\'' +
                ", tIKNbrePtsGain=" + tIKNbrePtsGain +
                ", tIKNbreTotalPts=" + tIKNbreTotalPts +
                ", tIKNumCarte='" + tIKNumCarte + '\'' +
                ", tIKMntBonus='" + tIKMntBonus + '\'' +
                ", tIKTauxRemise=" + tIKTauxRemise +
                ", tIKNumeroBL='" + tIKNumeroBL + '\'' +
                ", tIKEnvWebServ=" + tIKEnvWebServ +
                ", issync=" + isSync +
                ", status=" + status +
                ", tIKEnvWebServ=" + tIKEnvWebServ +
                '}';
    }
}
