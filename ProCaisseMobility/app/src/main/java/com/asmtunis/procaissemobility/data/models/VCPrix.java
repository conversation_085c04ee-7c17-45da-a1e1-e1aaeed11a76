package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * Created by Oussama AZIZI on 6/24/22.
 */

@Entity
public class VCPrix extends BaseModel {

    @PrimaryKey()
    @NonNull
    @ColumnInfo(name = "CodeVCPrix")
    @SerializedName("CodeVCPrix")
    @Expose
    private String codeVCPrix;


    @ColumnInfo(name = "CodeVCPrixM")
    @SerializedName("CodeVCPrixM")
    @Expose
    private String codeVCPrixM;

    @ColumnInfo(name = "CodeArtLocal")
    @SerializedName("CodeArtLocal")
    @Expose
    private String codeArtLocal;

    @ColumnInfo(name = "ArticleConcur")
    @SerializedName("ArticleConcur")
    @Expose
    private String articleConcur;

    @ColumnInfo(name = "DateOp")
    @SerializedName("DateOp")
    @Expose
    private String dateOp;

    @ColumnInfo(name = "CodeConcur")
    @SerializedName("CodeConcur")
    @Expose
    private String codeConcur;

    @ColumnInfo(name = "NoteOp")
    @SerializedName("NoteOp")
    @Expose
    private String noteOp;

    @ColumnInfo(name = "PrixConcur")
    @SerializedName("PrixConcur")
    @Expose
    private Double prixConcur;

    @ColumnInfo(name = "CodeUser")
    @SerializedName("CodeUser")
    @Expose
    private Integer codeUser;

    @ColumnInfo(name = "InfoOp1")
    @SerializedName("InfoOp1")
    @Expose
    private String infoOp1;

    @ColumnInfo(name = "CodeTypeCom")
    @SerializedName("CodeTypeCom")
    @Expose
    private String codeTypeCom;

    public String getCodeVCPrix() {
        return codeVCPrix;
    }

    public void setCodeVCPrix(String codeVCPrix) {
        this.codeVCPrix = codeVCPrix;
    }

    public String getCodeVCPrixM() {
        return codeVCPrixM;
    }

    public void setCodeVCPrixM(String codeVCPrixM) {
        this.codeVCPrixM = codeVCPrixM;
    }

    public String getCodeArtLocal() {
        return codeArtLocal;
    }

    public void setCodeArtLocal(String codeArtLocal) {
        this.codeArtLocal = codeArtLocal;
    }

    public String getArticleConcur() {
        return articleConcur;
    }

    public void setArticleConcur(String articleConcur) {
        this.articleConcur = articleConcur;
    }

    public String getDateOp() {
        return dateOp;
    }

    public void setDateOp(String dateOp) {
        this.dateOp = dateOp;
    }

    public String getCodeConcur() {
        return codeConcur;
    }

    public void setCodeConcur(String codeConcur) {
        this.codeConcur = codeConcur;
    }

    public String getNoteOp() {
        return noteOp;
    }

    public void setNoteOp(String noteOp) {
        this.noteOp = noteOp;
    }

    public Double getPrixConcur() {
        return prixConcur;
    }

    public void setPrixConcur(Double prixConcur) {
        this.prixConcur = prixConcur;
    }

    public int getCodeUser() {
        return codeUser;
    }

    public void setCodeUser(int codeUser) {
        this.codeUser = codeUser;
    }

    public String getInfoOp1() {
        return infoOp1;
    }

    public void setInfoOp1(String infoOp1) {
        this.infoOp1 = infoOp1;
    }

    public String getCodeTypeCom() {
        return codeTypeCom;
    }

    public void setCodeTypeCom(String codeTypeCom) {
        this.codeTypeCom = codeTypeCom;
    }


    public VCPrix(String codeVCPrix) {
        this.codeVCPrix = codeVCPrix;


    }



    public VCPrix() {


    }
}
