package com.asmtunis.procaissemobility.data.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;


import com.asmtunis.procaissemobility.data.models.TraiteCaisse;

import java.util.List;


@Dao
public interface TraiteCaisseDAO {

    @Query("SELECT * FROM TraiteCaisse")
    List<TraiteCaisse> getAll();


    @Query("SELECT * FROM TraiteCaisse WHERE TRAIT_Num_M = :code")
    List<TraiteCaisse> getByReglementM(String code);


    @Query("SELECT * FROM TraiteCaisse WHERE TRAIT_Num = :num ")
    TraiteCaisse getOneByCode(String num);

    @Query("SELECT * FROM TraiteCaisse WHERE   TRAIT_Num_M =:rEGCNumTicket")
    List<TraiteCaisse>  getByTicket(String rEGCNumTicket);

    @Query("SELECT * FROM TraiteCaisse LIMIT 1")
    TraiteCaisse getOne();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(TraiteCaisse item);


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<TraiteCaisse> items);

    @Query("DELETE FROM TraiteCaisse where Status='SELECTED'")
    void deleteAll();
}
