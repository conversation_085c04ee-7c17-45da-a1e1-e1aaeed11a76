package com.asmtunis.procaissemobility.helper.utils;

import static com.asmtunis.procaissemobility.helper.Globals.CURRENT_USER_KEY;
import static com.asmtunis.procaissemobility.helper.Globals.DEFAULT_VALUE;
import static com.asmtunis.procaissemobility.helper.Globals.FILTRECLT_AUHTORISATION_ID_KEY;

import android.content.Context;
import android.util.Log;

import androidx.lifecycle.LiveData;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Authorization;
import com.asmtunis.procaissemobility.data.models.Connexion;
import com.asmtunis.procaissemobility.data.models.SessionCaisse;
import com.asmtunis.procaissemobility.data.models.Utilisateur;
import com.asmtunis.procaissemobility.data.models.Ville;
import com.asmtunis.procaissemobility.enums.PrinterType;
import com.asmtunis.procaissemobility.enums.PrinterWidth;
import com.asmtunis.procaissemobility.helper.Globals;
import com.blankj.utilcode.util.ObjectUtils;

import io.paperdb.Paper;

/**
 * Created by me on 3/19/2017.
 */

public class PrefUtils extends license.utils.PrefUtils {

    static PrefUtils INSTANCE;

    public PrefUtils(Context context) {
        super(context);
    }

    public PrefUtils(Context context, String preferencesName) {
        super(context, preferencesName);
    }


    public static PrefUtils getInstance(Context context) {
        if (INSTANCE == null) {
            INSTANCE =
                    new PrefUtils(context);
        }
        return INSTANCE;
    }


    public static String getServerIPAddress() {
        return sharedpreferences.getString(Globals.BASE_CONFIG_WEB_SERVICE_IP_KEY, DEFAULT_VALUE);
    }

    public void setServerIPAddress(String ipAddress) {
        editor.putString(Globals.BASE_CONFIG_WEB_SERVICE_IP_KEY, ipAddress);
        editor.apply();
    }

    public static int getServerPort() {
        return sharedpreferences.getInt(Globals.PORT_KEY_PREFERENCE, 8012);
    }

    public void setServerPort(int port) {
        editor.putInt(Globals.PORT_KEY_PREFERENCE, port);
        editor.apply();
    }

    public static boolean isInitiated() {
        return sharedpreferences.getBoolean(Globals.IS_INITIATED_KEY_PREFERENCE, true);
    }

    public void setIsInitiated(boolean isInitiated) {
        editor.putBoolean(Globals.IS_INITIATED_KEY_PREFERENCE, isInitiated);
        editor.apply();
    }

    public void setCurLat(String lat) {
        editor.putString(Globals.CURRENT_LAT, lat);
        editor.apply();
    }

    public void setCurLong(String longitude) {
        editor.putString(Globals.CURRENT_LONG, longitude);
        editor.apply();
    }

    public String getCurLong() {
        return sharedpreferences.getString(Globals.CURRENT_LONG, "null");
    }

    public String getCurLat() {
        return sharedpreferences.getString(Globals.CURRENT_LAT, "null");
    }

    public void setIsAutoScan(boolean isautoScan) {
        editor.putBoolean(Globals.AUTO_SCAN, isautoScan);
        editor.apply();
    }

    public boolean getIsAutoScan() {
        return sharedpreferences.getBoolean(Globals.AUTO_SCAN, false);
    }

    public static String getURL(String s) {
        return String.format("%s%s/", getServerIPAddress(), s);
    }

    public static String getUserLogin() {
        return sharedpreferences.getString(Globals.USER_LOGIN_KEY, DEFAULT_VALUE);
    }

    public void setUserLogin(String name) {
        editor.putString(Globals.USER_LOGIN_KEY, name);
        editor.apply();
    }

    @Override
    protected String getPreferencesName() {
        return "com.asmtunis.procaissemobile" + " SharedPreferences ";
    }

    public void clearSharedPreferences() {
        editor.clear();
        editor.commit();
        //editor.apply();
    }

    public String getUserStationId() {
        return sharedpreferences.getString(Globals.USER_STATION_ID_KEY, Globals.USER_STATION_ID_KEY);
    }

    public void setUserStationId(String stationId) {
        editor.putString(Globals.USER_STATION_ID_KEY, stationId);
        editor.apply();
    }

    public String getCaisseStationId() {
        return sharedpreferences.getString(Globals.CAISSE_STATION_ID_KEY, Globals.CAISSE_STATION_ID_KEY);
    }

    public void setCaisseStationId(String stationId) {
        editor.putString(Globals.CAISSE_STATION_ID_KEY, stationId);
        editor.apply();
    }

    public String getCurrency() {
        return sharedpreferences.getString(Globals.CURRENCY_ID_KEY, "DT");
    }

    public void setCurrency(String currency) {
        editor.putString(Globals.CURRENCY_ID_KEY, currency);
        editor.apply();
    }

    public String getUserName() {
        return sharedpreferences.getString(Globals.USER_NAME_KEY, DEFAULT_VALUE);
    }

    public void setUserName(String name) {
        editor.putString(Globals.USER_NAME_KEY, name);
        editor.apply();
    }


    public String getEntrepriseIcon() {
        return sharedpreferences.getString(Globals.ENTREPRISE_ICON, DEFAULT_VALUE);
    }

    public void setEntrepriseIcon(String name) {
        editor.putString(Globals.ENTREPRISE_ICON, name);
        editor.apply();
    }



    public String getUserId() {
        return sharedpreferences.getString(Globals.USER_ID_KEY, DEFAULT_VALUE);
    }

    public void setUserId(String id) {
        editor.putString(Globals.USER_ID_KEY, id);
        editor.apply();
    }

    public void setIsConnected(boolean connected) {
        editor.putBoolean(Globals.USER_IS_CONNECTED, connected);
        editor.apply();
    }

    public boolean isConnected() {
        return sharedpreferences.getBoolean(Globals.USER_IS_CONNECTED, false);
    }

    public String getCaisseId() {
        return sharedpreferences.getString(Globals.CAISSE_ID_KEY, DEFAULT_VALUE);
    }

    public void setCaisseId(String id) {
        editor.putString(Globals.CAISSE_ID_KEY, id);
        editor.apply();
    }

    public String getSessionCaisseId() {
        return sharedpreferences.getString(Globals.CAISSE_ID_KEY, DEFAULT_VALUE);
    }

    public String getCaisseCode() {
        return sharedpreferences.getString(Globals.CAISSE_CODE_KEY, DEFAULT_VALUE);
    }

    public void setCaisseCode(String id) {
        editor.putString(Globals.CAISSE_CODE_KEY, id);
        editor.apply();
    }

    public String getCarnetId() {
        return sharedpreferences.getString(Globals.CARNET_ID_KEY, DEFAULT_VALUE);
    }

    public void setCarnetId(String id) {
        editor.putString(Globals.CARNET_ID_KEY, id);
        editor.apply();

    }

    public String getUserType() {

        return sharedpreferences.getString(Globals.USER_TYPE_KEY, DEFAULT_VALUE);
    }

    public void setUserType(String name) {
        editor.putString(Globals.USER_TYPE_KEY, name);
        editor.apply();
    }

    public void setUserPassword(String name) {
        editor.putString(Globals.USER_PASSWORD_KEY, name);
        editor.apply();
    }

    public void setMaxNumTicket(Integer maxNumTicket) {
        editor.putInt(Globals.MAX_NUM_TICKET, maxNumTicket);
        editor.apply();
    }

    public Integer getMaxNumTicket() {
        return sharedpreferences.getInt(Globals.MAX_NUM_TICKET, 0);
    }

    public String getUserPassword() {
        return sharedpreferences.getString(Globals.USER_PASSWORD_KEY, DEFAULT_VALUE);
    }

    public String getUserState() {
        return sharedpreferences.getString(Globals.USER_STATE_KEY, DEFAULT_VALUE);
    }

    public void setUserState(String name) {
        editor.putString(Globals.USER_STATE_KEY, name);
        editor.apply();
    }


    public String getExercice() {
        return sharedpreferences.getString(Globals.EXERCICE_KEY, DEFAULT_VALUE);
    }

    public void setExercice(String exercice) {
        editor.putString(Globals.EXERCICE_KEY, exercice);
        editor.apply();
    }

    public String getDiscountAuthorization() {
        if (sharedpreferences.getString(Globals.DISCOUNT_AUHTORISATION_ID_KEY, "-").equals("*") ||
                sharedpreferences.getString(Globals.DISCOUNT_AUHTORISATION_ID_KEY, "-").equals("+")
        )
            return sharedpreferences.getString(Globals.DISCOUNT_AUHTORISATION_ID_KEY, "-");
        try {
            if ((Integer.parseInt(sharedpreferences.getString(Globals.DISCOUNT_AUHTORISATION_ID_KEY, "-")) <= 100 &&
                    Integer.parseInt(sharedpreferences.getString(Globals.DISCOUNT_AUHTORISATION_ID_KEY, "-")) >= 0)) {
                return sharedpreferences.getString(Globals.DISCOUNT_AUHTORISATION_ID_KEY, "-");
            }
        } catch (Exception e) {
            return "-";
        }
        return "-";
    }

    public boolean getBcAuthorization() {
        return sharedpreferences.getBoolean(Globals.BC_AUHTORISATION_ID_KEY, false);
    }

    public boolean getBlAuthorization() {
        return sharedpreferences.getBoolean(Globals.BL_AUHTORISATION_ID_KEY, false);

    }

    public boolean getBctoBlAuthorization() {
        return sharedpreferences.getBoolean(Globals.BC_TO_BL_AUHTORISATION_ID_KEY, false);
    }
    public boolean getchoosePriceCategorieAuthorization() {
        return sharedpreferences.getBoolean(Globals.CHOOSE_PRICE_CAYEGORIE_AUHTORISATION_ID_KEY, false);
    }


    public boolean getModifyClientAuthorization() {
        return sharedpreferences.getBoolean(Globals.MODIFY_CLIENT_AUHTORISATION_ID_KEY, false);
    }


    public boolean getDepenseAuthorization() {
        return sharedpreferences.getBoolean(Globals.DEPENSE_AUHTORISATION_ID_KEY, false);

    }

    public boolean getBrAuthorization() {
        return sharedpreferences.getBoolean(Globals.BR_AUHTORISATION_ID_KEY, false);
    }


    public boolean getModifyPriceAuthorization() {
        return sharedpreferences.getBoolean(Globals.MODIFY_PRICE_AUHTORISATION_ID_KEY, false);
    }
    public boolean getCrCtAuthorization() {
        return sharedpreferences.getBoolean(Globals.CRCT_AUHTORISATION_ID_KEY, false);
    }

    public boolean getCltAuthorization() {
        return sharedpreferences.getBoolean(Globals.CLT_AUHTORISATION_ID_KEY, false);
    }

    public boolean getTourAuthorization() {
        return sharedpreferences.getBoolean(Globals.TOUR_AUHTORISATION_ID_KEY, false);
    }

    public boolean getFiltreCltAuthorization() {
        return sharedpreferences.getBoolean(Globals.FILTRECLT_AUHTORISATION_ID_KEY, false);
    }

    public boolean getInvPAuthorization() {
        return sharedpreferences.getBoolean(Globals.PATRIM_AUHTORISATION_ID_KEY, false);
    }


    public boolean getArtZeroStockAuthorization() {
        return sharedpreferences.getBoolean(Globals.ADD_ART_STOCK_ZERO_ID_KEY, false);
    }

    public boolean getIsChahiaAuthorization() {
        return sharedpreferences.getBoolean(Globals.IS_CHAHIA_CLIENT_AUHTORISATION_ID_KEY, false);
    }

    public int getPriceCategorie() {
        return sharedpreferences.getInt(Globals.PRICE_CATEGORY, R.id.publicPrice);
    }
    public void settPriceCategorie(int id) {
        editor.putInt(Globals.PRICE_CATEGORY, id);
        editor.apply();
    }
    public void setMaxPassager(Ville ville) {
        editor.putFloat(Globals.MAX_PASSAGER_VALUE, (float) ville.getMaxEspPassagM());
        editor.apply();
    }

    public void setGPSParams(double STATMetrageM, double STATSecondeM) {
        editor.putFloat(Globals.STATMetrageM, (float) STATMetrageM);
        editor.putFloat(Globals.STATSecondeM, (float) STATSecondeM);
        editor.apply();
    }

    public static double getStatMetrageM() {
        if (sharedpreferences.getFloat(Globals.STATMetrageM, 50) < 50) {
            return 50;
        } else {
            return sharedpreferences.getFloat(Globals.STATMetrageM, 50);
        }
    }

    public static double getStatSecondeM() {
        if (sharedpreferences.getFloat(Globals.STATSecondeM, 60000) < 60000) {
            return 60000;
        } else {
            return sharedpreferences.getFloat(Globals.STATSecondeM, 60000);
        }
    }

    public double getMaxPassager() {
        return sharedpreferences.getFloat(Globals.MAX_PASSAGER_VALUE, 0);
    }

    public Boolean getClotSess() {
        return sharedpreferences.getBoolean(Globals.CLOTURE_SESSION_AUTO, false);
    }

    public void setClotSess(Boolean State) {
        editor.putBoolean(Globals.CLOTURE_SESSION_AUTO, State);
        editor.apply();
    }

    public Boolean getEan13CheckDigitEnabled() {
        return sharedpreferences.getBoolean(Globals.EAN_13_CHECK_DIGIT_ENABLED, false);
    }




    public Boolean getCustomLocationAuth() {
        return sharedpreferences.getBoolean(Globals.CUSTOM_LOC_AUHTORISATION_ID_KEY, false);
    }
    public Boolean getReglementPartielAuth() {
        return sharedpreferences.getBoolean(Globals.REGLEMENT_PARTIEL_AUHTORISATION_ID_KEY, false);
    }


    public Boolean getAutoFactureEnabled() {
        return sharedpreferences.getBoolean(Globals.AUTO_FACTURE, true);
    }
    public Boolean getAutoBLTimbreEnabled() {
        return sharedpreferences.getBoolean(Globals.AUTO_TIMBER, true);
    }

    public void setEan13CheckDigitEnabled(Boolean ean13Enabled) {
        editor.putBoolean(Globals.EAN_13_CHECK_DIGIT_ENABLED, ean13Enabled);
        editor.apply();
    }

    public Boolean getPrintA4Enabled() {
        return sharedpreferences.getBoolean(Globals.PRINT_A4_ENABLED, false);
    }

    public void setPrintA4Enabled(Boolean printA4) {
        editor.putBoolean(Globals.PRINT_A4_ENABLED, printA4);
        editor.apply();
    }


    public Boolean getCheckUpdateEnabled() {
        return sharedpreferences.getBoolean(Globals.CHECK_UPDATE_ENABLED, true);
    }

    public void setCheckUpdateEnabled(Boolean checkupdtae) {
        editor.putBoolean(Globals.CHECK_UPDATE_ENABLED, checkupdtae);
        editor.apply();
    }

    public Boolean getCrtourAuto() {
        return sharedpreferences.getBoolean(Globals.CR_TOURNEE_AUTO, false);
    }

    public Boolean getVeilleConcurentielle() {
        return sharedpreferences.getBoolean(Globals.VEILLE_CONCURENTIELLE_ID_KEY, true);
    }

    public String getBaseConfigName() {
        return sharedpreferences.getString(Globals.BASE_CONFIG_NAME, "");
    }

    public void setUserAccount(Utilisateur user) {

        if (ObjectUtils.isNotEmpty(user.getAutorisationUser())) {
            App.database.authorizationDAO().deleteAll();
            App.database.authorizationDAO().insertAll(user.getAutorisationUser());
        }
        else {
            //if use have no active authorisation then delete previous auth in db
            App.database.authorizationDAO().deleteAll();
        }

        if (ObjectUtils.isNotEmpty(user.getStation())) {
            App.prefUtils.setCaisseStationId(user.getStation());
        }

        Paper.book().write(CURRENT_USER_KEY, user);
        editor.putString(Globals.USER_STATION_ID_KEY, user.getStation());
        editor.putString(Globals.USER_TYPE_KEY, user.getTypeUser());
        editor.putString(Globals.USER_LOGIN_KEY, user.getLogin());
        editor.putString(Globals.USER_PASSWORD_KEY, user.getPasse());
        editor.putString(Globals.USER_NAME_KEY, String.format("%s %s", user.getNom(), user.getPrenom()));
        editor.putString(Globals.USER_STATE_KEY, user.getEtat());
        editor.putString(Globals.USER_ID_KEY, user.getCodeUt());
        editor.putBoolean(Globals.VEILLE_CONCURENTIELLE_ID_KEY, App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.veille_concurentielle)));

        boolean freeDiscount = App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.free_discout));
        boolean artRemDiscount = App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.art_rem_discount));
        boolean noDiscount = App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.no_discount));
        Authorization fixedDiscount = App.database.authorizationDAO().getAuthState(context.getResources().getInteger(R.integer.fixed_discount));
        boolean customLocation = App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.custom_location));

        if (freeDiscount) {
            editor.putString(Globals.DISCOUNT_AUHTORISATION_ID_KEY, Globals.WDWD);
        }
        if (artRemDiscount) {
            editor.putString(Globals.DISCOUNT_AUHTORISATION_ID_KEY, Globals.WDND);
        }
        if (noDiscount) {
            editor.putString(Globals.DISCOUNT_AUHTORISATION_ID_KEY, "-");
        }
        if (ObjectUtils.isNotEmpty(fixedDiscount) && fixedDiscount.getAutEtat()) {
            editor.putString(Globals.DISCOUNT_AUHTORISATION_ID_KEY, fixedDiscount.getAuthValues());
        }



        editor.putBoolean(Globals.BL_AUHTORISATION_ID_KEY, App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.bl)));
        editor.putBoolean(Globals.MODIFY_PRICE_AUHTORISATION_ID_KEY, App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.modify_price)));
        editor.putBoolean(Globals.DEPENSE_AUHTORISATION_ID_KEY, App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.depense)));
        editor.putBoolean(Globals.BC_AUHTORISATION_ID_KEY, App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.bc)));
        editor.putBoolean(Globals.BR_AUHTORISATION_ID_KEY, App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.br)));
        editor.putBoolean(Globals.REGLEMENT_PARTIEL_AUHTORISATION_ID_KEY, App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.reglement_partiel)));
        editor.putBoolean(Globals.CRCT_AUHTORISATION_ID_KEY, App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.credit_client)));
        editor.putBoolean(Globals.CLT_AUHTORISATION_ID_KEY, App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.ajout_client)));
        editor.putBoolean(Globals.TOUR_AUHTORISATION_ID_KEY, App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.tournee)));
        editor.putBoolean(Globals.BC_TO_BL_AUHTORISATION_ID_KEY, App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.bc_to_bl)));
        editor.putBoolean(Globals.CHOOSE_PRICE_CAYEGORIE_AUHTORISATION_ID_KEY, App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.choose_price_categorie)));
        editor.putBoolean(Globals.MODIFY_CLIENT_AUHTORISATION_ID_KEY, App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.Modify_Client)));

        editor.putBoolean(Globals.FILTRECLT_AUHTORISATION_ID_KEY, App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.flitre_clt)));
        editor.putBoolean(Globals.CLOTURE_SESSION_AUTO, App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.clot_sess_auto)));
        editor.putBoolean(Globals.CR_TOURNEE_AUTO, App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.creation_tournee_auto)));
        editor.putBoolean(Globals.PATRIM_AUHTORISATION_ID_KEY, App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.patrimoine)));
        editor.putBoolean(Globals.ADD_ART_STOCK_ZERO_ID_KEY, App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.art_zero_stock)));
        editor.putBoolean(Globals.IS_CHAHIA_CLIENT_AUHTORISATION_ID_KEY, App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.is_chahia_client)));
        editor.putBoolean(Globals.AUTO_FACTURE, App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.auto_facture)));
        editor.putBoolean(Globals.AUTO_TIMBER, App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.BL_Timbre)));

        editor.putBoolean(Globals.CUSTOM_LOC_AUHTORISATION_ID_KEY, customLocation);
        editor.apply();

    }

    public Utilisateur getUserAccount() {
        return Paper.book().read(CURRENT_USER_KEY, null);
    }

    public void deleteUserAccount() {
        Paper.book().delete(CURRENT_USER_KEY);

        editor.putString(Globals.CARNET_ID_KEY, DEFAULT_VALUE);
        editor.putString(Globals.CAISSE_ID_KEY, DEFAULT_VALUE);
        editor.apply();



    }

    public String getSessionDateOuv() {
        return sharedpreferences.getString(Globals.SESSION_DATE_OUV, "");
    }
    public void setSessionDateOuv(String date) {
        editor.putString(Globals.SESSION_DATE_OUV, date);
        editor.apply();
    }


    public void setSessionCaisse(SessionCaisse sessionCaisse) {
        //editor.putString(Globals.USER_ID_KEY, user.get());
        editor.putString(Globals.CAISSE_ID_KEY, sessionCaisse.getsCIdSCaisse());
        editor.putString(Globals.SESSION_DATE_OUV, sessionCaisse.getsCDateHeureCrea()/*getSessionDateOuv()*/);
        editor.putString(Globals.CAISSE_STATION_ID_KEY, sessionCaisse.getsCStation());
        setCaisseCode(sessionCaisse.getsCCaisse());
        editor.putString(Globals.CARNET_ID_KEY, sessionCaisse.getsCIdCarnet());
        //editor.putString(Globals.CARNET_ID_KEY, sessionCaisse.getsCIdCarnet());
        editor.apply();
    }

    public Connexion getBaseConfig() {


        Connexion connexion = new Connexion(
                String.valueOf(sharedpreferences.getLong(Globals.BASE_CONFIG_ID_KEY, 0)),
                sharedpreferences.getString(Globals.BASE_CONFIG_DESIGNATION_KEY, null)
                , sharedpreferences.getString(Globals.BASE_CONFIG_SERIAL_KEY, null)
                , sharedpreferences.getString(Globals.BASE_CONFIG_DB_NAME_KEY, null)
                , sharedpreferences.getString(Globals.BASE_CONFIG_WEB_SERVICE_IP_KEY, null)
                , String.valueOf(sharedpreferences.getInt(Globals.BASE_CONFIG_PORT_KEY, 8000))
                , sharedpreferences.getString(Globals.BASE_CONFIG_DB_IP_KEY, null)
                , sharedpreferences.getString(Globals.BASE_CONFIG_USERNAME_KEY, null)
                , sharedpreferences.getString(Globals.BASE_CONFIG_PASSWORD_KEY, null));
        return connexion;
    }

    public void setBaseconfigName(String BaseName) {
        editor.putString(Globals.BASE_CONFIG_NAME, BaseName);
        editor.apply();
    }

    public void setBaseConfig(Connexion connexion) {
        editor.putLong(Globals.BASE_CONFIG_ID_KEY, Long.parseLong(connexion.getIdBaseConfig()));
        editor.putString(Globals.BASE_CONFIG_USERNAME_KEY, connexion.getUsername());
        editor.putString(Globals.BASE_CONFIG_DB_IP_KEY, connexion.getDbIpAddress());
        editor.putString(Globals.BASE_CONFIG_DB_NAME_KEY, connexion.getDbName());
        editor.putString(Globals.BASE_CONFIG_SERIAL_KEY, connexion.getKeyBase());
        editor.putString(Globals.BASE_CONFIG_PASSWORD_KEY, connexion.getPassword());


        editor.putString(Globals.BASE_CONFIG_WEB_SERVICE_IP_KEY, connexion.getAdresseIp());
        editor.putString(Globals.BASE_CONFIG_DESIGNATION_KEY, connexion.getDesignationBase());
        editor.putInt(Globals.BASE_CONFIG_PORT_KEY, Integer.parseInt(connexion.getPort()));
        editor.apply();
    }


    public void setSerialKey(String key) {

        editor.putString(Globals.SERIAL_KEY, key);
        editor.apply();
    }

    public String getSerialKey() {
        return sharedpreferences.getString(Globals.SERIAL_KEY, "");
    }




    public void setPrinterWidth(int width) {

        editor.putInt(Globals.PRINTER_WIDTH_KEY, width);
        editor.apply();
    }

    public int getPrinterWidth() {
        return sharedpreferences.getInt(Globals.PRINTER_WIDTH_KEY, PrinterWidth.FOUR_EIGHT.printerWidth());
    }


    public void setPrinterType(String type) {

        editor.putString(Globals.PRINTER_TYPE_KEY, type);
        editor.apply();
    }

    public String getPrinterType() {
        return sharedpreferences.getString(Globals.PRINTER_TYPE_KEY, PrinterType.ESC_POS.PrinterType());
    }

    public void setLoadData(String key) {
        editor.putString(Globals.LOAD_DATA, key);
        editor.apply();
    }

    public String getLoadData() {
        return sharedpreferences.getString(Globals.LOAD_DATA, "");
    }

    public static int getDecimalCount() {
        return sharedpreferences.getInt(Globals.DECIMAL_COUNT_KEY_PREFERENCE, 6);
    }

    public void setDecimalCount(int decimalCount) {
        editor.putInt(Globals.DECIMAL_COUNT_KEY_PREFERENCE, decimalCount);
        editor.apply();
    }

    public static boolean isAutoSync() {
        return Boolean.TRUE.equals(Paper.book("Params").read(Globals.AUTO_SYNC_LABEL, true));
    }

    public static void setAutoSync(boolean isAutoSync) {
        Paper.book("Params").write(Globals.AUTO_SYNC_LABEL, isAutoSync);
    }




    public void setPrintArticleTax(boolean type) {

        editor.putBoolean(Globals.PRINT_ARTICLE_TAX, type);
        editor.apply();
    }

    public static boolean getPrintArticleTax() {
        return sharedpreferences.getBoolean(Globals.PRINT_ARTICLE_TAX, false);
    }




    public void setGetAllArticle(boolean type) {

        editor.putBoolean(Globals.GET_ALL_ARTICLE, type);
        editor.apply();
    }

    public static boolean getGetAllArticle() {
        return sharedpreferences.getBoolean(Globals.GET_ALL_ARTICLE, false);
    }


}
