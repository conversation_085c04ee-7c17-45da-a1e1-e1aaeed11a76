package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.VCPromo;

import java.util.List;

/**
 * Created by Oussama AZIZI on 6/24/22.
 */

@Dao
public interface VCPromosDAO {
    @Query("SELECT * FROM VCPromo where Status !='DELETED' order by strftime('%Y-%m-%d %H-%M-%S',DateOp) desc")
    LiveData<List<VCPromo>> getAll();

    @Query("SELECT * FROM VCPromo WHERE CodeVCPromoM = :code")
    VCPromo getByCodeM(String code);


    @Query("SELECT * FROM VCPromo WHERE CodeVCPromo = :code")
    VCPromo getByCode(String code);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<VCPromo> vcPromos);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(VCPromo vcPromos);

    @Query("SELECT * FROM VCPromo where isSync=0 and  (Status='INSERTED'  or Status='UPDATED') ")
    List<VCPromo> getNoSyncedToAddOrUpdate();

    @Query("SELECT * FROM VCPromo where isSync=0 and Status='DELETED' ")
    List<VCPromo> getNoSyncedToDelete();

    @Query("SELECT count(*) FROM VCPROMO where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    LiveData<Integer> getNoSyncCountMubtale();

    @Query("SELECT count(*) FROM VCPromo where isSync=0 and Status='DELETED' ")
    LiveData<Integer> getCountNoSyncedToDeleteMubtale();


    @Query("SELECT count(*) FROM VCPROMO where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    int getCountNonSync();

    @Query("SELECT count(*) FROM VCPromo where isSync=0 and Status='DELETED' ")
    int getCountNoSyncedToDelete();


    @Query("delete from VCPROMO")
    void deleteAll();

    @Query("DELETE FROM VCPROMO where CodeVCPromo=:CodeAutre")
    void deleteById(String CodeAutre);

    @Query("DELETE FROM VCPROMO where CodeVCPromo=:CodeAutre or CodeVCPromoM = :CodeMobile")
    void deleteByIdAndCodeM(String CodeAutre, String CodeMobile);



    @Query("UPDATE VCPROMO SET CodeVCPromo = :code_procaiss where CodeVCPromoM = :CodeMobile")
    void updateCloudCode(String code_procaiss, String CodeMobile);

}
