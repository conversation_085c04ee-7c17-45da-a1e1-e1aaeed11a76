package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
@Entity( primaryKeys =
        {"SART_CodeSatation","SART_CodeArt"})
public class StationStock {
    @NonNull
    @ColumnInfo(name = "SART_CodeSatation")
    @SerializedName("SART_CodeSatation")
    @Expose
    private String sARTCodeSatation;
    @NonNull
    @ColumnInfo(name = "SART_CodeArt")
    @SerializedName("SART_CodeArt")
    @Expose
    private String sARTCodeArt;
    @ColumnInfo(name = "SART_Qte")
    @SerializedName("SART_Qte")
    @Expose
    private double sARTQte;
    @SerializedName("SART_QteDeclaree")
    @Expose
    private double sARTQteDeclaree;
    @SerializedName("SART_QTEmin")
    @Expose
    private double sARTQTEmin;
    @SerializedName("SART_QTEmax")
    @Expose
    private double sARTQTEmax;
    @SerializedName("SART_user")
    @Expose
    private String sARTUser;
    @SerializedName("SART_station")
    @Expose
    private String sARTStation;
    @SerializedName("SART_export")
    @Expose
    private String sARTExport;
    @SerializedName("SART_DDm")
    @Expose
    private String sARTDDm;
    @SerializedName("Regularisation")
    @Expose
    private String regularisation;

    public String getSARTCodeSatation() {
        return sARTCodeSatation;
    }

    public void setSARTCodeSatation(String sARTCodeSatation) {
        this.sARTCodeSatation = sARTCodeSatation;
    }

    public String getSARTCodeArt() {
        return sARTCodeArt;
    }

    public void setSARTCodeArt(String sARTCodeArt) {
        this.sARTCodeArt = sARTCodeArt;
    }

    public double getSARTQte() {
        return sARTQte;
    }

    public void setSARTQte(double sARTQte) {
        this.sARTQte = sARTQte;
    }

    public double getSARTQteDeclaree() {
        return sARTQteDeclaree;
    }

    public void setSARTQteDeclaree(double sARTQteDeclaree) {
        this.sARTQteDeclaree = sARTQteDeclaree;
    }

    public double getSARTQTEmin() {
        return sARTQTEmin;
    }

    public void setSARTQTEmin(double sARTQTEmin) {
        this.sARTQTEmin = sARTQTEmin;
    }

    public double getSARTQTEmax() {
        return sARTQTEmax;
    }

    public void setSARTQTEmax(double sARTQTEmax) {
        this.sARTQTEmax = sARTQTEmax;
    }

    public String getSARTUser() {
        return sARTUser;
    }

    public void setSARTUser(String sARTUser) {
        this.sARTUser = sARTUser;
    }

    public String getSARTStation() {
        return sARTStation;
    }

    public void setSARTStation(String sARTStation) {
        this.sARTStation = sARTStation;
    }

    public String getSARTExport() {
        return sARTExport;
    }

    public void setSARTExport(String sARTExport) {
        this.sARTExport = sARTExport;
    }

    public String getSARTDDm() {
        return sARTDDm;
    }

    public void setSARTDDm(String sARTDDm) {
        this.sARTDDm = sARTDDm;
    }

    public String getRegularisation() {
        return regularisation;
    }

    public void setRegularisation(String regularisation) {
        this.regularisation = regularisation;
    }

    @Override
    public String toString() {
        return "StationStock{" +
                "sARTCodeSatation='" + sARTCodeSatation + '\'' +
                ", sARTCodeArt='" + sARTCodeArt + '\'' +
                ", sARTQte='" + sARTQte + '\'' +
                ", sARTQteDeclaree='" + sARTQteDeclaree + '\'' +
                ", sARTQTEmin='" + sARTQTEmin + '\'' +
                ", sARTQTEmax='" + sARTQTEmax + '\'' +
                ", sARTUser='" + sARTUser + '\'' +
                ", sARTStation='" + sARTStation + '\'' +
                ", sARTExport='" + sARTExport + '\'' +
                ", sARTDDm='" + sARTDDm + '\'' +
                ", regularisation='" + regularisation + '\'' +
                '}';
    }
}
