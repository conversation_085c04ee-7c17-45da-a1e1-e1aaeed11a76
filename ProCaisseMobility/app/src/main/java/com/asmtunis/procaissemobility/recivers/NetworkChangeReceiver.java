package com.asmtunis.procaissemobility.recivers;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.asmtunis.procaissemobility.helper.NetworkUtils;

public class NetworkChangeReceiver extends BroadcastReceiver {
    public static  NetworkReceiverListener networkReceiverListener;

    public interface NetworkReceiverListener {
        void onNetworkConnectionChanged(boolean isConnected);
    }

    @Override
    public void onReceive(final Context context, final Intent intent) {
        int status = 0;
        try {
            status = NetworkUtils.getConnectivityStatusString(context);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        if (networkReceiverListener != null)
            if (status == NetworkUtils.NETWORK_STATUS_NOT_CONNECTED) {
                networkReceiverListener.onNetworkConnectionChanged(false);
            } else {
                networkReceiverListener.onNetworkConnectionChanged(true);
            }

    }
}

