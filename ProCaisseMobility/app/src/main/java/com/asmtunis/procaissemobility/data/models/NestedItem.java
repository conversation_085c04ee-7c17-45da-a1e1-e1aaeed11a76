package com.asmtunis.procaissemobility.data.models;

import java.io.Serializable;
import java.util.List;

public class NestedItem<P,C extends List> implements Serializable {
    P parent;
    C children;

    public P getParent() {
        return parent;
    }

    public void setParent(P parent) {
        this.parent = parent;
    }

    public void setChildren(C children) {
        this.children = children;
    }

    public C getChildren() {
        return children;
    }
}
