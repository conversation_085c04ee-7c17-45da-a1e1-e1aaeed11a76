package com.asmtunis.procaissemobility.data.network.datamanager;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Fournisseur;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.FournisseurService;

import java.util.List;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;


/**
 * Created by Achraf on 25/09/2017.
 */

public class FournisseurDataManager {

    private static FournisseurDataManager sInstance;

    private final FournisseurService mFournisseurService;

    public FournisseurDataManager( ) {
        mFournisseurService = new ServiceFactory<>(FournisseurService.class, String.format(BASE_URL,  App.prefUtils.getServerIPAddress(), App.prefUtils.getBaseConfig().getPort(),"Fournisseur")).makeService();

    }

    public static FournisseurDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new FournisseurDataManager();
        }
        return sInstance;
    }



    public void getFournisseurs(GenericObject genericObject,
                                RemoteCallback<List<Fournisseur>> listener) {
        mFournisseurService.getFournisseurs(genericObject)
                .enqueue(listener);
    }


}