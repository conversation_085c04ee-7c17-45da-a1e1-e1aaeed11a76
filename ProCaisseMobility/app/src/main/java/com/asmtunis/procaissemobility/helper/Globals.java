package com.asmtunis.procaissemobility.helper;

import static com.asmtunis.procaissemobility.App.prefUtils;

import androidx.fragment.app.Fragment;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.ui.fragments.ArchievedCommadFragment;
import com.asmtunis.procaissemobility.ui.fragments.ArchievedRecapTicketFragment;
import com.asmtunis.procaissemobility.ui.fragments.ArchievedRetourFragment;
import com.asmtunis.procaissemobility.ui.fragments.ArchievedTicketFragment;
import com.asmtunis.procaissemobility.ui.fragments.ArchivedPaymentFragment;
import com.asmtunis.procaissemobility.ui.fragments.ClientsFragment;
import com.asmtunis.procaissemobility.ui.fragments.CommandFragment;
import com.asmtunis.procaissemobility.ui.fragments.DNVisiteFragment;
import com.asmtunis.procaissemobility.ui.fragments.DashboardFragment;
import com.asmtunis.procaissemobility.ui.fragments.DNClientsFragment;
import com.asmtunis.procaissemobility.ui.fragments.ExpenseTicketFragment;
import com.asmtunis.procaissemobility.ui.fragments.ExpensesFragment;
import com.asmtunis.procaissemobility.ui.fragments.MapFragment;
import com.asmtunis.procaissemobility.ui.fragments.OrdersFragment;
import com.asmtunis.procaissemobility.ui.fragments.PaymentsFragment;
import com.asmtunis.procaissemobility.ui.fragments.ProductsFragment;
import com.asmtunis.procaissemobility.ui.fragments.ReceipRecapFragment;
import com.asmtunis.procaissemobility.ui.fragments.RetourFragment;
import com.asmtunis.procaissemobility.ui.fragments.SalesRecapStatusFragment;
import com.asmtunis.procaissemobility.ui.fragments.SyncFragment;
import com.asmtunis.procaissemobility.ui.fragments.TicketInfoSubItemFragment;
import com.asmtunis.procaissemobility.ui.fragments.TicketsFragment;
import com.asmtunis.procaissemobility.ui.fragments.VCAutreFragment;
import com.asmtunis.procaissemobility.ui.fragments.VCNewProductFragment;
import com.asmtunis.procaissemobility.ui.fragments.VCPrixFragment;
import com.asmtunis.procaissemobility.ui.fragments.VCPromoFragment;
import com.asmtunis.procaissemobility.ui.fragments.inv_pat.AffectationPatrimoineFragment;
import com.asmtunis.procaissemobility.ui.fragments.inv_pat.DepInPatrimoineFragment;
import com.asmtunis.procaissemobility.ui.fragments.inv_pat.DepOutPatrimoineFragment;
import com.asmtunis.procaissemobility.ui.fragments.inv_pat.InvPatrimoineFragment;
import com.blankj.utilcode.util.AppUtils;

import java.util.HashMap;

//import com.blankj.utilcode.util.AppUtils;

/**
 * Created by Achraf on 25/09/2017.
 */

// tous les constantes (param)

public class Globals {
    public static int nbCharAllowed = 48;
    //private static final int LEFT_TEXT_MAX_LENGTH = 16;
    public static  int LEFT_TEXT_MAX_LENGTH = nbCharAllowed/3;

    // private static final int LEFT_LENGTH = 20;
    public static  int LEFT_LENGTH = (int) (nbCharAllowed/2);

    //private static final int RIGHT_LENGTH = 28;
    public static  int RIGHT_LENGTH = (int) (nbCharAllowed/2);

    public static String CREDIT_NO_REGLEMENT = "Credit sans réglement";
    public static String FROM_INV_SCAN = "from scan inventaire";

    //region Variables
    public static final String SESSION_DATE_OUV = "SESSION_DATE_OUV";
    public static final String MAX_NUM_TICKET = "MAX_NUM_TICKET";
    public static final String CR_TOURNEE_AUTO = "CR_TOURNEE_AUTO";
    public static final String BASE_CONFIG_NAME = "BASE_CONFIG_NAME";
    public static final String PATRIM_AUHTORISATION_ID_KEY = "PATRIM_AUHTORISATION_ID_KEY";
    public static final String ADD_ART_STOCK_ZERO_ID_KEY = "ADD_ART_STOCK_ZERO_ID_KEY";
    public static final String AUTO_FACTURE = "AUTO_FACTURE";
    public static final String CUSTOM_LOC_AUHTORISATION_ID_KEY = "CUSTOM_LOC_AUHTORISATION_ID_KEY";
    public static final String BL_AUHTORISATION_ID_KEY = "BL_AUHTORISATION_ID_KEY";
    public static final String BC_TO_BL_AUHTORISATION_ID_KEY = "BC_TO_BL_AUHTORISATION_ID_KEY";
    public static final String IS_CHAHIA_CLIENT_AUHTORISATION_ID_KEY = "IS_CHAHIA_CLIENT_AUHTORISATION_ID_KEY";
    public static final String CHOOSE_PRICE_CAYEGORIE_AUHTORISATION_ID_KEY = "CHOOSE_PRICE_CAYEGORIE_AUHTORISATION_ID_KEY";
    public static final String MODIFY_CLIENT_AUHTORISATION_ID_KEY = "MODIFY_CLIENT_AUHTORISATION_ID_KEY";
    public static final String DEPENSE_AUHTORISATION_ID_KEY = "DEPENSE_AUHTORISATION_ID_KEY";
    public static final String USER_IS_CONNECTED = "USER_ID_CONNECTED";
    public static String BASE_URL = "https://%s:%s/%s/";

    public static String CHECK_LICENCE_URL = "";
    public static String CHECK_LICENCE_BASEURL = "";
    public static  String GET_BASE_CONFIG_URL = "";
    public static  String GET_BASE_CONFIG_BASEURL = "";


    public static String IMAGE_BASE_URL = "http://%s:%s/";
    public static String SERIAL_KEY = "serialKey";
    public static String PRINT_ARTICLE_TAX = "print_articleTax";
    public static String GET_ALL_ARTICLE = "get_all_autorisation_article";
    public static String PRINTER_WIDTH_KEY = "printer_widthKey";
    public static String PRINTER_TYPE_KEY = "printer_typeKey";
    public static String LOAD_DATA = "load_data";
    public static String ORD_LOCAL_MOBILE = "ORD_LOCAL_MOBILE";

    public static String STATISTICS_DB_KEY = "statistics";
    public static String AUTO_SYNC_LABEL = "autosync";
    public static String BASE_CONFIG_ID_KEY = "BASE_CONFIG_ID_KEY";
    public static String BASE_CONFIG_USERNAME_KEY = "BASE_CONFIG_USERNAME_KEY";
    public static String BASE_CONFIG_DB_IP_KEY = "BASE_CONFIG_DB_IP_KEY";
    public static String BASE_CONFIG_DB_NAME_KEY = "BASE_CONFIG_DB_NAME_KEY";
    public static String BASE_CONFIG_SERIAL_KEY = "BASE_CONFIG_SERIAL_KEY";
    public static String BASE_CONFIG_PASSWORD_KEY = "BASE_CONFIG_PASSWORD_KEY";
    public static String BASE_CONFIG_WEB_SERVICE_IP_KEY = "BASE_CONFIG_WEB_SERVICE_IP_KEY";
    public static String BASE_CONFIG_DESIGNATION_KEY = "BASE_CONFIG_DESIGNATION_KEY";
    public static String BASE_CONFIG_PORT_KEY = "BASE_CONFIG_PORT_KEY";
    public static String MAX_PASSAGER_VALUE = "MAX_PASSAGER_VALUE";
    public static String TICKET_TO_UPDATE = "TICKET_TO_UPDATE";
    public static String CLIENT_TO_UPDATE = "CLIENT_TO_UPDATE";
    public static String UPDATE_TICKET = "UPDATE_TICKET";
    public static String UPDATE_CLIENT = "UPDATE_CLIENT";
    public static String PRICE_CATEGORY = "PRICE_CATEGORY";
    public static String CLT_CODE = "CLT_CODE";
    public static String INV_FROM = "InvFrom";
    public static String TICKETS_RECAP_FROM = "TICKETS_RECAP_FROM";
    public static String PAT_FROM = "PAT_FROM";
    public static String TICKET_WITH_LINES_KEY = "TICKET_WITH_LINES";
    public static String REGLEMENT_PARTIEL_KEY = "REGLEMENT_PARTIEL_KEY";
    public static String TICKET_REG_PARTIEL_KEY = "TICKET_REG_PARTIEL_KEY";
    public static String CLIENT_REG_PARTIEL_KEY = "CLIENT_REG_PARTIEL_KEY";
    public static String TICKET_WITH_LINES_AND_PAYEMENT_KEY = "TICKET_WITH_LINES_AND_PAYEMENT";
    public static String USER_STATION_ID_KEY = "useridstation";
    public static String CURRENCY_ID_KEY = "currency";
    public static String USER_NAME_KEY = "username";
    public static String ENTREPRISE_ICON = "ENTREPRISE_ICON";
    public static String USER_TYPE_KEY = "idtype";
    public static String CURRENT_USER_KEY = "CURRENT_USER_KEY";
    public static final String AUTO_TIMBER = "AUTO_TIMBER";

    public static String USER_ID_KEY = "iduser";
    public static String DISCOUNT_AUHTORISATION_ID_KEY = "iddiscountauth";
    public static String MODIFY_PRICE_AUHTORISATION_ID_KEY = "MODIFY_PRICE_AUHTORISATION_ID_KEY";
    public static String BC_AUHTORISATION_ID_KEY = "BC_AUHTORISATION_ID_KEY";
    public static String BR_AUHTORISATION_ID_KEY = "BR_AUHTORISATION_ID_KEY";
    public static String REGLEMENT_PARTIEL_AUHTORISATION_ID_KEY = "BR_AUHTORISATION_ID_KEY";
    public static String CRCT_AUHTORISATION_ID_KEY = "CRCT_AUHTORISATION_ID_KEY";
    public static String CLT_AUHTORISATION_ID_KEY = "CLT_AUHTORISATION_ID_KEY";
    public static String TOUR_AUHTORISATION_ID_KEY = "TOUR_AUHTORISATION_ID_KEY";
    public static String FILTRECLT_AUHTORISATION_ID_KEY = "FILTRECLT_AUHTORISATION_ID_KEY";
    public static String CLOTURE_SESSION_AUTO = "CLOTURE_SESSION_AUTO";
    public static String VEILLE_CONCURENTIELLE_ID_KEY = "VEILLE_CONCURENTIELLE_ID_KEY";
    public static String EAN_13_CHECK_DIGIT_ENABLED = "EAN_13_CHECK_DIGIT_ENABLED";
    public static String PRINT_A4_ENABLED = "PRINT_A4_ENABLED";
    public static String CHECK_UPDATE_ENABLED = "CHECK_UPDATE_ENABLED";
    public static String CLIENT_INTENT_ID_KEY = "addclientintent";
    public static String RECLAMATION_INTENT_ID_KEY = "addclientintent";
    public static String CAISSE_ID_KEY = "idcaisse";
    public static String CAISSE_STATION_ID_KEY = "caisseidstation";
    public static String CAISSE_CODE_KEY = "codecaisse";
    public static String CARNET_ID_KEY = "idcarnet";
    public static String USER_LOGIN_KEY = "loginuser";
    public static String USER_PASSWORD_KEY = "passworduser";
    public static String EXERCICE_KEY = "exercice";
    public static String USER_STATE_KEY = "stateuser";
    public static String DEFAULT_VALUE = "DEFAULT_VALUE";
    public static String TICKET_NUMBER_VALUE = "TICKET_NUMBER_VALUE";
    public static String PORT_KEY_PREFERENCE = "prefport";
    public static String DECIMAL_COUNT_KEY_PREFERENCE = "DECIMAL_COUNT_KEY_PREFERENCE";
    public static String IS_INITIATED_KEY_PREFERENCE = "IS_INITIATED_KEY_PREFERENCE";
    public static String PAYEMENT_REMARK = "vente du jour";
    public static String AUTO_SCAN = "AUTO_SCAN";
    public static String ALL_PERMISSIONS_GRANTED = "ALL_PERMISSIONS_GRANTED";
    public static String CURRENT_LAT = "CURRENT_POS";
    public static String CURRENT_LONG = "CURRENT_LONG";
    public static String CREDIT_PAYEMENT_REMARK = "Regler Acpt";
    public static String SOURCE = "MOBILE";
    public static String STATMetrageM = "STATMetrageM";
    public static String STATSecondeM = "STATSecondeM";

    public static String DEFAULT_ENCODING = "Windows-1256";//"ISO-8859-6";//"GB18030";//"UTF-8";
    public static String REPLACE_PATTERN = "\\{\\$(.+?)\\}";
    public static String DATE_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static String WDND = "+";
    public static String WDWD = "*";

    public static String UNIT_PRICE_TYPE_KEY = "UNIT_PRICE_TYPE";


    public static String FROM_SERVICE = "";

    static HashMap<Integer, Fragment> fragmentHashMap() {
        HashMap<Integer, Fragment> FragmentMap = new HashMap<>();
        FragmentMap.put(R.id.dashboard_drawer_item, DashboardFragment.newInstance());
        FragmentMap.put(R.id.products_drawer_item, ProductsFragment.newInstance());
        FragmentMap.put(R.id.clients_drawer_item, ClientsFragment.newInstance());
        FragmentMap.put(R.id.tickets_drawer_item, TicketsFragment.newInstance());
        FragmentMap.put(R.id.expenses_drawer_item, ExpensesFragment.newInstance());
        FragmentMap.put(R.id.commande_drawer_item, CommandFragment.newInstance());
        FragmentMap.put(R.id.retour_drawer_item, RetourFragment.newInstance());
        FragmentMap.put(R.id.orders_drawer_item, OrdersFragment.newInstance());
        FragmentMap.put(R.id.payments_drawer_item, PaymentsFragment.newInstance());
        FragmentMap.put(R.id.sync_drawer_item, SyncFragment.newInstance());
        FragmentMap.put(R.id.ticket_info_pageview_item, TicketInfoSubItemFragment.newInstance());
        FragmentMap.put(R.id.ticket_payment_pageview_item, TicketInfoSubItemFragment.newInstance());
        FragmentMap.put(R.id.tournee_drawer_item, MapFragment.newInstance());
        FragmentMap.put(R.id.tickets_arc_drawer_item, ArchievedTicketFragment.newInstance());
        FragmentMap.put(R.id.command_arc_drawer_item, ArchievedCommadFragment.newInstance());
        FragmentMap.put(R.id.retour_arc_drawer_item, ArchievedRetourFragment.newInstance());
        FragmentMap.put(R.id.payment_arc_drawer_item, ArchivedPaymentFragment.newInstance());
        FragmentMap.put(R.id.recipe_details_drawer_item, SalesRecapStatusFragment.newInstance());
        FragmentMap.put(R.id.recipe_recap_drawer_item, ReceipRecapFragment.newInstance());
        FragmentMap.put(R.id.expense_list_arc_drawer_item, ExpenseTicketFragment.newInstance());
        FragmentMap.put(R.id.expense_ticket_arc_drawer_item, ExpensesFragment.newInstance());
        FragmentMap.put(R.id.inv_drawer_item, InvPatrimoineFragment.newInstance());
        FragmentMap.put(R.id.inv_Affectation_drawer_subitem, AffectationPatrimoineFragment.newInstance());
        FragmentMap.put(R.id.inv_deplacement_out_drawer_subitem, DepOutPatrimoineFragment.newInstance());
        FragmentMap.put(R.id.inv_deplacement_in_drawer_subitem, DepInPatrimoineFragment.newInstance());
        FragmentMap.put(R.id.vc_new_product, VCNewProductFragment.newInstance());
        FragmentMap.put(R.id.vc_promo, VCPromoFragment.newInstance());
        FragmentMap.put(R.id.vc_price, VCPrixFragment.newInstance());
        FragmentMap.put(R.id.vc_others, VCAutreFragment.newInstance());
        FragmentMap.put(R.id.dn_client, new DNClientsFragment());
     //   FragmentMap.put(R.id.archive_Recap_ticket, ArchievedRecapTicketFragment.newInstance());
        FragmentMap.put(R.id.archive_Recap_ticket,new ArchievedRecapTicketFragment());
        FragmentMap.put(R.id.tickets_recap_drawer_item,new ArchievedRecapTicketFragment());

        FragmentMap.put(R.id.dn_visite, new DNVisiteFragment());

        return FragmentMap;
    }

    public static String getApplicationVerion() {
        return String.format("Version: %s", AppUtils.getAppVersionName());
    }


    //endregion
    //region ENUMS
    public enum Boolean {
        TRUE(1), FALSE(0);
        private final int value;

        Boolean(int value) {
            this.value = value;
        }

        public int getValue() {
            return this.value;
        }
    }

    public enum TICKET_STATE {
        PAYED(0), CREDIT(1);
        private final int value;

        TICKET_STATE(int value) {
            this.value = value;
        }

        public String getValue() {
            String state = null;
            switch (value) {
                case 0:

                    state = "Regler";

                    break;
                case 1:

                    state = "Credit";
                    break;


            }
            return state;
        }
    }


    public enum AdditionalQuery {
        NULL("");
        private final String value;

        AdditionalQuery(String value) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    public enum PAYEMENT_MODE {
        CASH("Espèce"), CHECK("Chèque"), TICKET("Carte Resto"), VARIOUS("Divers");
        private final String value;

        PAYEMENT_MODE(String value) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    public enum UNIT_PRICE_TYPE {
        PUBLIC("Prix publique"),
        MULTIPLE("Multiple"),
        GROS1("Prix gros 1"),
        GROS2("Prix gros 2"),
        GROS3("Prix gros 3");
        String pirceType;

        UNIT_PRICE_TYPE(String pirceType) {
            this.pirceType = pirceType;
        }

        public String priceType() {
            return this.pirceType;
        }
    }

    public enum DRAWER_ITEMS {
        DASHBOARD(R.id.dashboard_drawer_item),
        TICKETS(R.id.tickets_drawer_item),
        EXPENSES(R.id.expenses_drawer_item),
        PRODUCTS(R.id.products_drawer_item),
        CLIENTS(R.id.clients_drawer_item),
        COMMANDE(R.id.commande_drawer_item),
        RETOUR(R.id.retour_drawer_item),
        PAYMENTS(R.id.payments_drawer_item),
        ORDERS(R.id.orders_drawer_item),
        SYNC(R.id.sync_drawer_item),
        TOURNEE(R.id.tournee_drawer_item),
        TICKETS_ARC(R.id.tickets_arc_drawer_item),
        TICKETS_RECAP_ARC(R.id.archive_Recap_ticket),
        RECAP_TICKETS(R.id.tickets_recap_drawer_item),
        COMMAND_ARC(R.id.command_arc_drawer_item),
        RETOUR_ARC(R.id.retour_arc_drawer_item),
        PAYMENT_ARC(R.id.payment_arc_drawer_item),
        RECIPE_RECAP(R.id.recipe_recap_drawer_item),
        RECAP_DETAILS(R.id.recipe_details_drawer_item),
        EXPENSE_LIST_ARC(R.id.expense_list_arc_drawer_item),
        EXPENSE_TICKET_ARC(R.id.expense_ticket_arc_drawer_item),
        INVPATRIMOINE(R.id.inv_drawer_item),
        AFFECTATIONPATRIMOINE(R.id.inv_Affectation_drawer_subitem),
        DEPINPATRIMOINE(R.id.inv_deplacement_in_drawer_subitem),
        DEPOUTPATRIMOINE(R.id.inv_deplacement_out_drawer_subitem),
        VC_NEWPRODUCT(R.id.vc_new_product),
        VC_PROMO(R.id.vc_promo),
        PRICE(R.id.vc_price),
        DNClients(R.id.dn_client),
        DNVisiteFragment(R.id.dn_visite) ,
        OTHERS(R.id.vc_others);

        HashMap<Integer, Fragment> FragmentMap = fragmentHashMap();
        int id;

        DRAWER_ITEMS(int id) {
            this.id = id;
        }

        public Fragment getFragment() {
            return FragmentMap.get(id);
        }

    }

    public enum ITEM_STATUS {
        SELECTED("SELECTED"),
        UPDATED("UPDATED"),
        INSERTED("INSERTED"),
        INSERTED_REG_FROM_REGLEMENT("INSERTED_REG_FROM_REGLEMENT"),
        DELETED("DELETED"),
        VALIDATED("VALIDATED"),
        TOUPDATE("TOUPDATE"),
        WAITING("WAITING");
        String status;

        ITEM_STATUS(String status) {
            this.status = status;
        }

        public String getStatus() {

            return status;
        }
    }

    public enum TYPE_VC {
        VCLANCEMENTNP("VC Lancement NP"),
        VCPRIX("VC Prix"),
        VCPROMO("VC Promo"),
        VCAUTRE("VC Autre");

        String typeVC;

        TYPE_VC(String typeVC) {
            this.typeVC = typeVC;
        }

        public String getTypeVC() {
            return typeVC;
        }
    }



    public enum TYPE_PATRIMOINE {
        AFFECTATION("affectation"),
        INVENTAIRE("inventaire"),
        SORTIE("sortie"),
        ENTREE("entree");

        String typePat;

        TYPE_PATRIMOINE(String typePat) {
            this.typePat = typePat;
        }

        public String getTypePat() {
            return typePat;
        }
    }

    //endregion
}
