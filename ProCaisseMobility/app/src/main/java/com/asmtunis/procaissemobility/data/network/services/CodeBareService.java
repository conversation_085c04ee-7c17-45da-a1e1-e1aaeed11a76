package com.asmtunis.procaissemobility.data.network.services;


import com.asmtunis.procaissemobility.data.models.ArticleCodeBar;
import com.asmtunis.procaissemobility.data.models.GenericObject;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

public interface CodeBareService {
    @POST("getCodeTest")
    Call<List<ArticleCodeBar>> getArticleCodeBare(@Body GenericObject genericObject);

    @POST("addArticleCodeBarMobile")
    Call<List<ArticleCodeBar>> addArticleCodeBarMobile(@Body GenericObject genericObject);



}
