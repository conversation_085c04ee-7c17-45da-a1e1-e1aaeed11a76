package com.asmtunis.procaissemobility.data.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.PricePerStation;

import java.util.List;

/**
 * Created by PC on 11/18/2017.
 */
@Dao
public interface PricePerStationDAO {

    @Query("SELECT * FROM PricePerStation")
    List<PricePerStation> getAll();

    @Query("SELECT COUNT(*) FROM PricePerStation")
    int count();

    @Query("SELECT * FROM PricePerStation LIMIT 1")
    PricePerStation getOne();

    @Query("SELECT * FROM PricePerStation where UNITE_ARTICLE_station = :station and UNITE_ARTICLE_CodeArt = :article LIMIT 1")
    PricePerStation getOneByArticle(String article, String station);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(PricePerStation item);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<PricePerStation> items);

    @Query("DELETE FROM PricePerStation")
    void deleteAll();

}
