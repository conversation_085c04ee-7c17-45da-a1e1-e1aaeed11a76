package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.VCListeConcurrent;
import com.asmtunis.procaissemobility.data.models.VCTypeCommunication;

import java.util.List;

/**
 * Created by Oussama AZIZI on 6/24/22.
 */

@Dao
public interface VCTypeCommunicationDAO  {
    @Query("SELECT * FROM VCTypeCommunication")
    LiveData<List<VCTypeCommunication>> getAll();

    @Query("SELECT TypeCommunication FROM VCTypeCommunication WHERE CodeTypeCom=:code")
    String getTypeCommunication(String code);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<VCTypeCommunication> vcTypeCommunications);

    @Query("delete from VCTypeCommunication")
    void deleteAll();

}
