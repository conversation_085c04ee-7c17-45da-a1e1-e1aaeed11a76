package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.DNTypeServices;
import com.asmtunis.procaissemobility.data.models.Ticket;
import com.asmtunis.procaissemobility.data.models.VCPrix;

import java.util.List;

@Dao
public interface DNTypeServicesDAO  {
    @Query("SELECT * FROM DNTypeServices")
    List<DNTypeServices> getAll();
    //LiveData<List<DNTypeServices>> getAll();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<DNTypeServices> DNtypeServices);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(DNTypeServices DNtypeService);

    @Query("SELECT * FROM DNTypeServices where isSync=0 and  (Status='INSERTED'  or Status='UPDATED') ")
    List<DNTypeServices> getNoSynced();

    @Query("SELECT count(*) FROM DNTypeServices where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    LiveData<Integer> getNoSyncCountMubtale();


    @Query("SELECT * FROM DNTypeServices WHERE CodeTypeSv = :codeTypeSv")
    DNTypeServices getbyCode(String codeTypeSv);




    @Query("delete from DNTypeServices")
    void deleteAll();

    @Query("DELETE FROM DNTypeServices where CodeTypeSv=:codeTypePV")
    void deleteById(String codeTypePV);


    //@Query("UPDATE DNTypeServices SET CodeVCPrix = :code_procaiss where CodeVCPrixM = :CodeMobile")
   // void updateCloudCode(String code_procaiss, String CodeMobile);
}
