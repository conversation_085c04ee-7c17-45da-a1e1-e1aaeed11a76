package com.asmtunis.procaissemobility.data.network.datamanager;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Famille;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Parametrages;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.FamilleService;
import com.asmtunis.procaissemobility.data.network.services.ParametrageService;
import com.asmtunis.procaissemobility.helper.utils.Utils;

import java.util.List;





public class ParametragesDataManager {
    private static ParametragesDataManager sInstance;

    private final ParametrageService mParametrageService;

    public ParametragesDataManager( ) {
        mParametrageService = new ServiceFactory<>(ParametrageService.class, String.format(Utils.validateBaseUrl(), App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Parametrage")).makeService();

    }

    public static ParametragesDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new ParametragesDataManager();
        }
        return sInstance;
    }

    public void getParametrage(GenericObject genericObject,
                            RemoteCallback<Parametrages> listener) {
        mParametrageService.getParametrage(genericObject)
                .enqueue(listener);
    }

}