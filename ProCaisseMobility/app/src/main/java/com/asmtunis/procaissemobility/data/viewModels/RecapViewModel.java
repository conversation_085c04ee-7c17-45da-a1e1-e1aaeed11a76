package com.asmtunis.procaissemobility.data.viewModels;

import android.app.Activity;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProviders;

import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.RecetteByReglementResponseModel;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.datamanager.RecapDataManager;

/**
 * Created by Oussama AZIZI on 9/7/22.
 */

public class RecapViewModel extends ViewModel {

    private static RecapViewModel instance;
    RecetteByReglementResponseModel recetteByReglementResponseModel = null;

    public static RecapViewModel getInstance(Fragment activity) {
        if (instance == null)
            instance = ViewModelProviders.of(activity).get(RecapViewModel.class);

        return instance;
    }

    public static RecapViewModel getInstance(FragmentActivity activity) {
        if (instance == null)
            instance = ViewModelProviders.of(activity).get(RecapViewModel.class);

        return instance;
    }

    public RecetteByReglementResponseModel getDataToPrintRecap(GenericObject genericObject, Activity context) {

        RecapDataManager.getInstance().getRecapeData(genericObject, new RemoteCallback<RecetteByReglementResponseModel>(context, false) {




            @Override
            public void onSuccess(RecetteByReglementResponseModel response) throws NoSuchFieldException, InstantiationException, IllegalAccessException {
                recetteByReglementResponseModel =  response;
            }

            @Override
            public void onUnauthorized() {

            }

            @Override
            public void onFailed(Throwable throwable) {

            }
        });

        return recetteByReglementResponseModel;
    }
}
