package com.asmtunis.procaissemobility.data.network.datamanager;


import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Statistics;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

/**
 * Created by PC on 10/4/2017.
 */

public class MiscDataManager  {
    private static MiscDataManager sInstance;

    private final com.asmtunis.procaissemobility.data.network.services.MiscService mMiscService;

    public MiscDataManager() {
        mMiscService = new ServiceFactory<>(com.asmtunis.procaissemobility.data.network.services.MiscService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Miscs")).makeService();

    }

    public static MiscDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new MiscDataManager();
        }
        return sInstance;
    }


    public void checkConnectivity(GenericObject genericObject,
                                       RemoteCallback<Boolean> listener) {
        mMiscService.checkConnectivity(genericObject)
                .enqueue(listener);
    }

    public void getStatistics(GenericObject genericObject,
                                  RemoteCallback<Statistics> listener) {
        mMiscService.getStatistics(genericObject)
                .enqueue(listener);
    }


}

