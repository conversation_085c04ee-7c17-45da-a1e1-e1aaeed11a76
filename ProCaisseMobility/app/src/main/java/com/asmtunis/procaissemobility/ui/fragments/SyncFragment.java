package com.asmtunis.procaissemobility.ui.fragments;

import android.graphics.Color;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.Toolbar;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.asmtunis.procaissemobility.data.viewModels.DNVisiteViewModel;
import com.asmtunis.procaissemobility.data.viewModels.VCAutreViewModel;
import com.asmtunis.procaissemobility.data.viewModels.VCNewProductViewModel;
import com.asmtunis.procaissemobility.data.viewModels.VCPrixViewModel;
import com.asmtunis.procaissemobility.data.viewModels.VCPromoViewModel;
import com.asmtunis.procaissemobility.data.viewModels.ExpensesViewModel;
import com.asmtunis.procaissemobility.enums.DataSyncStatus;
import com.asmtunis.procaissemobility.listener.IDataSyncListener;
import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.TicketWithLinesAndPayments;
import com.asmtunis.procaissemobility.data.viewModels.BonComandeViewModel;
import com.asmtunis.procaissemobility.data.viewModels.BonRetourViewModel;
import com.asmtunis.procaissemobility.data.viewModels.ClientViewModel;
import com.asmtunis.procaissemobility.data.viewModels.ReglementCaisseViewModel;
import com.asmtunis.procaissemobility.data.viewModels.TicketViewModel;
import com.asmtunis.procaissemobility.helper.DataSyncHelper;
import com.asmtunis.procaissemobility.ui.dialogs.LoadingDialog;
import com.asmtunis.procaissemobility.ui.fragments.base.BaseFragment;
import com.mikepenz.fontawesome_typeface_library.FontAwesome;
import com.mikepenz.iconics.IconicsDrawable;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import es.dmoral.toasty.Toasty;

/**
 * Created by me on 10/7/2017.
 */
public class SyncFragment extends BaseFragment {


    @BindView(R.id.swipeRefreshLayout)
    SwipeRefreshLayout swipeRefreshLayout;

    @BindView(R.id.payment_toolbar)
    Toolbar paymentToolbar;
    @BindView(R.id.client_toolbar)
    Toolbar clientToolbar;
    @BindView(R.id.ticket_toolbar)
    Toolbar ticketToolbar;
    @BindView(R.id.commande_toolbar)
    Toolbar commande_toolbar;
    @BindView(R.id.retour_toolbar)
    Toolbar retour_toolbar;
    @BindView(R.id.depense_toolbar)
    Toolbar depense_toolbar;
    @BindView(R.id.depense_type_toolbar)
    Toolbar depense_type_toolbar;


    @BindView(R.id.disnum_toolbar)
    Toolbar disnum_toolbar;

    @BindView(R.id.vcpromo_toolbar)
    Toolbar vcpromo_toolbar;

    @BindView(R.id.vcprix_toolbar)
    Toolbar vcprix_toolbar;
    @BindView(R.id.vcnewprod_toolbar)
    Toolbar vcnewprod_toolbar;
    @BindView(R.id.vcautre_toolbar)
    Toolbar vcautre_toolbar;


    List<TicketWithLinesAndPayments> ticketWithLinesAndPayments = new ArrayList<>();
    MenuItem sync;

    public SyncFragment() {
    }

    public static SyncFragment newInstance() {
        SyncFragment fragment = new SyncFragment();
        Bundle args = new Bundle();
        args.putString("Title", KEY);
        fragment.setArguments(args);
        return (fragment);
    }

    @Override
    protected int getFragmentLayout() {
        return R.layout.fragment_sync;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return super.onCreateView(inflater, container, savedInstanceState);

    }

    @Override
    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        setHasOptionsMenu(true);
        ticketWithLinesAndPayments = new ArrayList<>();

        swipeRefreshLayout.setColorSchemeResources(R.color.material_teal500);
        swipeRefreshLayout.setOnRefreshListener(() -> {

            if (swipeRefreshLayout.isRefreshing()) {
                swipeRefreshLayout.setRefreshing(false);
            }
        });
        updateCountNonSyncs();
    }

    /**
     * check local database for none synced items
     */
    private void updateCountNonSyncs() {
        try {
            ReglementCaisseViewModel.getInstance(this).getNoSyncCount().observe(getViewLifecycleOwner(), integer -> paymentToolbar.setTitle(String.format("%s (%d)", getString(R.string.payments_label), integer)));



            ClientViewModel.getInstance(this).getNoSyncCount().observe(getViewLifecycleOwner(), integer -> {
                ClientViewModel.getInstance(this).getNoSyncCountToUpdate().observe(getViewLifecycleOwner(), integer1 -> clientToolbar.setTitle(String.format("%s (%d)", getString(R.string.clients_title), integer + integer1)));
            });

            ExpensesViewModel.getInstance(this).getNoSyncCount().observe(getViewLifecycleOwner(), integer -> depense_toolbar.setTitle(String.format("%s (%d)", getString(R.string.depenses), integer)));

            ExpensesViewModel.getInstance(this).getNoSyncCountDepenseType().observe(getViewLifecycleOwner(), integer -> depense_type_toolbar.setTitle(String.format("%s (%d)", getString(R.string.depenses_type), integer)));
            BonComandeViewModel.getInstance(this).getNoSyncCount().observe(getViewLifecycleOwner(),
                    integer -> commande_toolbar.setTitle(String.format("%s (%d)", getString(R.string.commande_title), integer)));

            TicketViewModel.getInstance(this).getNoSyncCount().observe(getViewLifecycleOwner(), integer -> ticketToolbar.setTitle(String.format("%s (%d)", getString(R.string.tickets_title), integer)));
            BonComandeViewModel.getInstance(this).getNoSyncCount().observe(getViewLifecycleOwner(), integer -> commande_toolbar.setTitle(String.format("%s (%d)", getString(R.string.commande_title), integer)));
            BonRetourViewModel.getInstance(this).getNoSyncCount().observe(getViewLifecycleOwner(), integer -> retour_toolbar.setTitle(String.format("%s (%d)", getString(R.string.retour_title), integer)));
            DNVisiteViewModel.getInstance(this).getNoSyncCount().observe(getViewLifecycleOwner(), integer -> disnum_toolbar.setTitle(String.format("%s (%d)", getString(R.string.dn_title), integer)));


            VCPromoViewModel.getInstance(this).getNoSyncCountMubtale().observe(getViewLifecycleOwner(), integer -> {
                VCPromoViewModel.getInstance(this).getNoSyncCountMubtaleToDelete().observe(getViewLifecycleOwner(), integer1 -> vcpromo_toolbar.setTitle(String.format("%s (%d)", getString(R.string.vc_promo), integer+integer1)));
            });
            VCPrixViewModel.getInstance(this).getNoSyncCountMubtale().observe(getViewLifecycleOwner(), integer -> {
                VCPrixViewModel.getInstance(this).getCountNoSyncedToDeleteMubtale().observe(getViewLifecycleOwner(), integer1 -> vcprix_toolbar.setTitle(String.format("%s (%d)", getString(R.string.vc_prix), integer+integer1)));

            });
            VCNewProductViewModel.getInstance(this).getNoSyncCountMubtale().observe(getViewLifecycleOwner(), integer -> {
                VCNewProductViewModel.getInstance(this).getNoSyncCountMubtaleToDelete().observe(getViewLifecycleOwner(), integer1 -> vcnewprod_toolbar.setTitle(String.format("%s (%d)", getString(R.string.vc_newprod), integer+integer1)));

            });
            VCAutreViewModel.getInstance(this).getNoSyncCountMubtale().observe(getViewLifecycleOwner(), integer -> {
                VCAutreViewModel.getInstance(this).getNoSyncCountMubtaleToDelete().observe(getViewLifecycleOwner(), integer1 -> vcautre_toolbar.setTitle(String.format("%s (%d)", getString(R.string.vc_autre),integer1+ integer)));
            });




            checkVisibility();
        } catch (Exception e) {

        }
    }

    void checkVisibility() {
        try {
            if (!(App.database.clientDAO().getNoSyncCount() + App.database.ticketDAO().getNoSyncCount() + App.database.depenceCaisseDAO().getNoSyncCount() + App.database.depenceTypeDAO().getNoSyncCount() + App.database.reglementCaisseDAO().getNoSyncCount() + App.database.bonCommandeDAO().getCountNonSync() + App.database.bonRetourDAO().getCountNonSync() > 0)) {
                sync.setVisible(false);
            } else {
                sync.setVisible(true);
            }
        }catch (Exception e){
            Log.d("ee",e.getMessage());
        }

    }


    @Override
    public void onCreateOptionsMenu(@NonNull Menu menu, MenuInflater inflater) {
        inflater.inflate(R.menu.sync_menu, menu);
        sync = menu.findItem(R.id.sync);
        checkVisibility();
        sync.setIcon(new IconicsDrawable(requireContext()).icon(FontAwesome.Icon.faw_sync_alt)
                .color(Color.WHITE).sizeDp(20));
        sync.setOnMenuItemClickListener(item -> {
            sync.setEnabled(false);
            DataSyncHelper.getInstance(getContext(), new IDataSyncListener() {
                @Override
                public void onLoading(String msg) {
                    Log.d("syncLog", "onLoading");
                    if (getActivity() != null && !getActivity().isFinishing()) {
                        getActivity().runOnUiThread(() -> {
                            LoadingDialog.getInstance(getActivity()).setLoadingText(msg);
                            LoadingDialog.getInstance(getActivity()).show();
                        });
                    }
                }

                @Override
                public void onFailure(String msg, DataSyncStatus syncStatus) {
                    Log.d("syncLog", "onFailure");
                    sync.setEnabled(true);
                    if (getActivity() != null && !getActivity().isFinishing())
                        getActivity().runOnUiThread(() -> {
                            LoadingDialog.getInstance(getActivity()).dismiss();
                        });
                    if (getActivity() != null && !getActivity().isFinishing()) {
                        getActivity().runOnUiThread(() -> {
                            Toasty.info(App.getInstance(), msg).show();
                        });
                    }
                }

                @Override
                public void onComplete() {
                    Log.d("syncLog", "onComplete");
                    if (getActivity() != null && !getActivity().isFinishing())
                        getActivity().runOnUiThread(() -> {
                            LoadingDialog.getInstance(getActivity()).dismiss();

                        });
                }

                @Override
                public void onComplete(Object object) {
                    Log.d("syncLog", "onComplete  object");
                }
            }).syncData("4");
            return false;
        });
    }

    @Override
    public void onResume() {
        super.onResume();
        if (getView() == null) {
            return;
        }
    }

}