package com.asmtunis.procaissemobility.data.network.services;


import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.SessionCaisse;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Field;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Query;

/**
 * Created by <PERSON>chraf on 29/09/2017.
 */

public interface SessionCaisseService {

    @POST("getSessionCaisses")
    Call<List<SessionCaisse>> getSessionCaisses(@Body GenericObject genericObject);


    @POST("addClient")
    Call<String> addSessionCaisse(@Body SessionCaisse sessionCaisse);


    @POST("/getSessionCaisseByX")
    Call<SessionCaisseService> getSessionCaisseByX(@Query("tagged") String tags);

    @PUT("/updateSessionCaisse")
    Call<String> UpdateSessionCaisse(@Query("tagged") String tags);


    @POST("getSessionCaisseByUser")
    Call<SessionCaisse> getSessionCaisseByUser(@Body GenericObject genericObject);

    @POST("addSessionVendeur")
    Call<SessionCaisse> addSessionVendeur(@Body GenericObject genericObject);

    @POST("closeSessionVendeur")
    Call<Boolean> closeSessionVendeur(@Body GenericObject genericObject, @Query("facture") Boolean facture);
}
