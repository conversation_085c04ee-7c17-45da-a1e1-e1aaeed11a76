package com.asmtunis.procaissemobility.data.network.datamanager;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Exercice;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.ExerciceService;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

/**
 * Created by Achraf on 29/09/2017.
 */

public class ExerciceDataManager  {

    private static ExerciceDataManager sInstance;

    private final ExerciceService mExerciceService;

    public ExerciceDataManager() {
        mExerciceService = new ServiceFactory<>(ExerciceService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Exercice")).makeService();


    }

    public static ExerciceDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new ExerciceDataManager();
        }
        return sInstance;
    }

    public void getExercice(GenericObject genericObject,
            RemoteCallback<Exercice> listener) {
        mExerciceService.getExercice(genericObject)
                .enqueue(listener);
    }


}


