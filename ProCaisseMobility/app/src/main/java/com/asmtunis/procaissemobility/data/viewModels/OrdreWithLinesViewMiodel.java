package com.asmtunis.procaissemobility.data.viewModels;

import android.util.Log;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelProviders;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.dao.OrdreWithLinesDAO;
import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.data.models.LigneOrdreMission;
import com.asmtunis.procaissemobility.data.models.OrdreWithLines;
import com.asmtunis.procaissemobility.helper.utils.UIUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class OrdreWithLinesViewMiodel extends ViewModel {
    public OrdreWithLinesDAO dao;
    private static OrdreWithLinesViewMiodel instance;

    public static OrdreWithLinesViewMiodel getInstance(Fragment activity) {
        if (instance == null)
            instance = new ViewModelProvider(activity).get(OrdreWithLinesViewMiodel.class);
        instance.dao = App.database.ordreWithLinesDAO();

        return instance;
    }

    public static OrdreWithLinesViewMiodel getInstance(FragmentActivity activity) {
        if (instance == null)
            instance = new ViewModelProvider(activity).get(OrdreWithLinesViewMiodel.class);
        instance.dao = App.database.ordreWithLinesDAO();
        return instance;
    }

    public LiveData<List<OrdreWithLines>> getOrdreMissions() {
        return dao.getOrdresWithLinesMutable();
    }

    public LigneOrdreMission getLineWithOrdreAndClient(String ordCode, String cltCode) {
        return dao.getLineWithOrdreAndClient(ordCode, cltCode);
    }

    public static List<Object> getClientListOfCurrentTournee() {
        boolean conform = true;
        String codeTournee = App.database.trakingAlarmDAO().getCodeOrdreMissionActif();
          List<LigneOrdreMission> ligneOrdreMissionList = App.database.ordreWithLinesDAO().getOrdreWithLines(codeTournee).ligneOrdreMission;
     //   List<LigneOrdreMission> ligneOrdreMissionList =    App.database.ordreMissionDAO().getBycode(codeTournee);
      //  List<LigneOrdreMission> ligneOrdreMissionList =    App.database.ordreMissionDAO().getOrdreWithLines(codeTournee).ligneOrdreMission;
        List<Client> clients = new ArrayList<>();
        for (LigneOrdreMission ligneOrdreMission: ligneOrdreMissionList) {
            Client client = App.database.clientDAO().getOneByCode(ligneOrdreMission.lIGORClt);
            if(client != null) clients.add(client);
            else conform = false;
        }


        return new ArrayList<>(Arrays.asList(clients, conform));
    }
}
