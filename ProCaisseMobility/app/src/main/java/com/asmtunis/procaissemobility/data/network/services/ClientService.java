package com.asmtunis.procaissemobility.data.network.services;


import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.data.models.GenericObject;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Headers;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * Created by Achraf on 26/09/2017.
 */

public interface ClientService {
    @Headers("User-Agent: android-api-client")
    @POST("getClients")
    Call<List<Client>> getClients(@Body GenericObject genericObject, @Query("CltEquivalent") String cltEquivalent);

    @Headers("User-Agent: android-api-client")
    @POST("addClient")
    Call<Client> addClient(@Body GenericObject client);

    @Headers("User-Agent: android-api-client")
    @POST("addBatchClient")
    Call<List<Client>> addBatchClient(@Body GenericObject clients);



    @Headers("User-Agent: android-api-client")
    @POST("updateClient")
    Call<Boolean> updateClient(@Body GenericObject clients);
}
