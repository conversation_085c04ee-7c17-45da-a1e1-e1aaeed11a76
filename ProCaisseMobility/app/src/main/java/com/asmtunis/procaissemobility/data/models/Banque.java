package com.asmtunis.procaissemobility.data.models;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;
import androidx.annotation.NonNull;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * Created by PC on 10/25/2017.
 */
 @Entity
public class Banque extends  BaseModel implements Serializable {
    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "BAN_Code")
    @SerializedName("BAN_Code")
    @Expose
    public String bANCode;
    @ColumnInfo(name = "BAN_Des")
    @SerializedName("BAN_Des")
    @Expose
    public String bANDes;

    public Banque() {
    }


    @Ignore
    public Banque(String bANCode, String bANDes) {
        this.bANCode = bANCode;
        this.bANDes = bANDes;
    }

    public String getbANCode() {
        return bANCode;
    }

    public void setbANCode(String bANCode) {
        this.bANCode = bANCode;
    }

    public String getbANDes() {
        return bANDes;
    }

    public void setbANDes(String bANDes) {
        this.bANDes = bANDes;
    }

    @Override
    public String toString() {
        return "Banque{" +
                "bANCode='" + bANCode + '\'' +
                ", bANDes='" + bANDes + '\'' +
                '}';
    }
}
