package com.asmtunis.procaissemobility.data.models.google;

import com.google.gson.annotations.SerializedName;
import java.io.Serializable;
import java.util.List;

public class DistanceResponse implements Serializable {

    @SerializedName("destination_addresses")
    private List<String> destinationAddresses = null;
    @SerializedName("origin_addresses")
    private List<String> originAddresses = null;
    @SerializedName("rows")
    private List<Row> rows = null;
    @SerializedName("status")
    private String status;

    @SerializedName("destination_addresses")
    public List<String> getDestinationAddresses() {
        return destinationAddresses;
    }

    @SerializedName("origin_addresses")
    public List<String> getOriginAddresses() {
        return originAddresses;
    }

    @SerializedName("rows")
    public List<Row> getRows() {
        return rows;
    }

    @SerializedName("status")
    public String getStatus() {
        return status;
    }

}
