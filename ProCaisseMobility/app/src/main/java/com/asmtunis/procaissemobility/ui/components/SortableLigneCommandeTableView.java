package com.asmtunis.procaissemobility.ui.components;

import android.content.Context;
import android.graphics.Typeface;
import android.util.AttributeSet;

import androidx.core.content.ContextCompat;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.comparators.LigneBonCommandeComparators;
import com.asmtunis.procaissemobility.data.models.LigneBonCommande;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;

import de.codecrafters.tableview.SortableTableView;
import de.codecrafters.tableview.model.TableColumnWeightModel;
import de.codecrafters.tableview.toolkit.SimpleTableHeaderAdapter;
import de.codecrafters.tableview.toolkit.SortStateViewProviders;
import de.codecrafters.tableview.toolkit.TableDataRowBackgroundProviders;

/**
 * Created by PC on 10/3/2017.
 */

public class SortableLigneCommandeTableView extends SortableTableView<LigneBonCommande> {

    public SortableLigneCommandeTableView(final Context context) {
        this(context, null);
    }

    public SortableLigneCommandeTableView(final Context context, final AttributeSet attributes) {
        this(context, attributes, android.R.attr.listViewStyle);
    }

    public SortableLigneCommandeTableView(final Context context, final AttributeSet attributes, final int styleAttributes) {
        super(context, attributes, styleAttributes);

        LigneTicketTableViewWithDiscount(context);

    }


    void LigneTicketTableViewWithDiscount(final Context context) {
        final SimpleTableHeaderAdapter simpleTableHeaderAdapter = new SimpleTableHeaderAdapter(context,
                context.getResources().getString(R.string.quantity_field_short_title),
                context.getResources().getString(R.string.product_field_title),
                String.format(context.getResources().getString(R.string.unit_price_with_currency_field_short_title), new PrefUtils(context).getCurrency()),
                context.getResources().getString(R.string.discount_field_short_title),
                String.format(context.getResources().getString(R.string.total_with_currency_field_title), new PrefUtils(context).getCurrency()));
        simpleTableHeaderAdapter.setTextColor(ContextCompat.getColor(context, R.color.material_grey100));

        setHeaderAdapter(simpleTableHeaderAdapter);

        final int rowColorEven = ContextCompat.getColor(context, R.color.md_white_1000);
        final int rowColorOdd = ContextCompat.getColor(context, R.color.material_grey100);
        setDataRowBackgroundProvider(TableDataRowBackgroundProviders.alternatingRowColors(rowColorEven, rowColorOdd));
        setHeaderSortStateViewProvider(SortStateViewProviders.brightArrows());

        final TableColumnWeightModel tableColumnWeightModel = new TableColumnWeightModel(5);
        tableColumnWeightModel.setColumnWeight(0, 2);
        tableColumnWeightModel.setColumnWeight(1, 3);
        tableColumnWeightModel.setColumnWeight(2, 2);
        tableColumnWeightModel.setColumnWeight(3, 2);
        tableColumnWeightModel.setColumnWeight(4, 3);
        setColumnModel(tableColumnWeightModel);

        setColumnComparator(0, LigneBonCommandeComparators.getLigneTicketQuantityComparator());
        setColumnComparator(1, LigneBonCommandeComparators.getLigneTicketNameComparator());
        setColumnComparator(2, LigneBonCommandeComparators.getLigneTicketUnitPriceComparator());
        setColumnComparator(3, LigneBonCommandeComparators.getLigneTicketDiscountComparator());
        setColumnComparator(4, LigneBonCommandeComparators.getLigneTicketPriceComparator());
    //    simpleTableHeaderAdapter.setTypeface(Typeface.createFromAsset(getContext().getAssets(), "fonts/Exo2-Medium.otf").getStyle());

    }


    void LigneTicketTableView(final Context context) {
        final SimpleTableHeaderAdapter simpleTableHeaderAdapter = new SimpleTableHeaderAdapter(context,
                context.getResources().getString(R.string.quantity_field_short_title),
                context.getResources().getString(R.string.product_field_title),
                String.format(context.getResources().getString(R.string.unit_price_with_currency_field_short_title), new PrefUtils(context).getCurrency()),
                String.format(context.getResources().getString(R.string.total_with_currency_field_title), new PrefUtils(context).getCurrency()));

        simpleTableHeaderAdapter.setTextColor(ContextCompat.getColor(context, R.color.material_grey100));

        setHeaderAdapter(simpleTableHeaderAdapter);

        final int rowColorEven = ContextCompat.getColor(context, R.color.md_white_1000);
        final int rowColorOdd = ContextCompat.getColor(context, R.color.material_grey100);
        setDataRowBackgroundProvider(TableDataRowBackgroundProviders.alternatingRowColors(rowColorEven, rowColorOdd));
        setHeaderSortStateViewProvider(SortStateViewProviders.brightArrows());

        final TableColumnWeightModel tableColumnWeightModel = new TableColumnWeightModel(4);
        tableColumnWeightModel.setColumnWeight(0, 2);
        tableColumnWeightModel.setColumnWeight(1, 4);
        tableColumnWeightModel.setColumnWeight(2, 2);
        tableColumnWeightModel.setColumnWeight(3, 3);
        setColumnModel(tableColumnWeightModel);

        setColumnComparator(0, LigneBonCommandeComparators.getLigneTicketQuantityComparator());
        setColumnComparator(1, LigneBonCommandeComparators.getLigneTicketNameComparator());
        setColumnComparator(2, LigneBonCommandeComparators.getLigneTicketUnitPriceComparator());
        setColumnComparator(3, LigneBonCommandeComparators.getLigneTicketPriceComparator());
   //     simpleTableHeaderAdapter.setTypeface(Typeface.createFromAsset(getContext().getAssets(), "fonts/Exo2-Medium.otf").getStyle());

    }


}