package com.asmtunis.procaissemobility.ui.fragments.inv_pat;

import static android.app.Activity.RESULT_OK;
import static android.content.Context.LAYOUT_INFLATER_SERVICE;
import static com.asmtunis.procaissemobility.helper.Globals.DEFAULT_ENCODING;
import static com.asmtunis.procaissemobility.helper.Globals.DEFAULT_VALUE;
import static net.cachapa.expandablelayout.ExpandableLayout.State.COLLAPSING;
import static net.cachapa.expandablelayout.ExpandableLayout.State.EXPANDED;
import static net.cachapa.expandablelayout.ExpandableLayout.State.EXPANDING;

import android.annotation.SuppressLint;
import android.app.SearchManager;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.appcompat.widget.SearchView;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.afollestad.materialdialogs.MaterialDialog;
import com.arasthel.asyncjob.AsyncJob;
import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.adapters.items.InvPatItem;
import com.asmtunis.procaissemobility.adapters.tables.LigneInventaireTableDataAdapter;
import com.asmtunis.procaissemobility.adapters.tables.LigneTicketRestoTableDataAdapter;
import com.asmtunis.procaissemobility.comparators.AdapterComparators;
import com.asmtunis.procaissemobility.data.models.BonCommande;
import com.asmtunis.procaissemobility.data.models.LigneBonCommande;
import com.asmtunis.procaissemobility.data.models.TraiteCaisse;
import com.asmtunis.procaissemobility.data.viewModels.BonComandeViewModel;
import com.asmtunis.procaissemobility.helper.BluetoothService;
import com.asmtunis.procaissemobility.helper.Globals;
import com.asmtunis.procaissemobility.helper.PrinterHelper;
import com.asmtunis.procaissemobility.helper.utils.IOUtils;
import com.asmtunis.procaissemobility.helper.utils.Utils;
import com.asmtunis.procaissemobility.listener.ItemCallback;
import com.asmtunis.procaissemobility.listener.MenuItemsAction;
import com.asmtunis.procaissemobility.ui.activities.DeviceListActivity;
import com.asmtunis.procaissemobility.ui.activities.InvPatrimoineActivity;
import com.asmtunis.procaissemobility.ui.activities.MainActivity;
import com.asmtunis.procaissemobility.ui.components.LockableBottomSheetBehavior;
import com.asmtunis.procaissemobility.ui.components.SortableLigneInvTableView;
import com.asmtunis.procaissemobility.ui.components.SortableRestoTicketTableView;
import com.asmtunis.procaissemobility.ui.fragments.base.BaseListFragment;
import com.blankj.utilcode.util.ObjectUtils;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.snackbar.Snackbar;
import com.mikepenz.google_material_typeface_library.GoogleMaterial;
import com.mikepenz.iconics.IconicsDrawable;
import com.mikepenz.iconics.view.IconicsButton;

import net.cachapa.expandablelayout.ExpandableLayout;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import butterknife.BindView;
import es.dmoral.toasty.Toasty;
import id.ionbit.ionalert.IonAlert;



public class DepOutPatrimoineFragment extends BaseListFragment<InvPatItem> {
    public static final int REQUEST_CONNECT_DEVICE = 845;
    public static final int REQUEST_ENABLE_BT = 875;
    public static BluetoothDevice con_dev = null;
    public BluetoothService mService = null;

    List<LigneBonCommande> lgBonCommandes;
    BonCommande bc;
    int ticketNumber = 0;
    public final static int REQUEST_TICKET_CODE = 15645;
    MenuItem add;
    SearchView searchView;
    @BindView(R.id.layout_bottom_sheet)
    RelativeLayout mLayoutBottomSheet;
    private BottomSheetBehavior mBottomSheetBehavior;
    @BindView(R.id.bottom_sheet_content)
    FrameLayout bottomSheetContent;
    List<TraiteCaisse> traiteCaisseList = new ArrayList<>();
    private static final int UNSELECTED = -1;

    String Clt_code_from_extra="";
    @Override
    protected Comparator getComparator() {
        return AdapterComparators.getInvPatrimoineItemComparator();
    }

    public DepOutPatrimoineFragment() {
    }


    public static DepOutPatrimoineFragment newInstance() {
        DepOutPatrimoineFragment fragment = new DepOutPatrimoineFragment();
        Bundle args = new Bundle();
        args.putString("Title", KEY);
        fragment.setArguments(args);
        return (fragment);
    }

    @Override
    protected int getFragmentLayout() {
        return R.layout.list_recycler_view;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        Clt_code_from_extra = getArguments().getString(Globals.CLT_CODE);

        return super.onCreateView(inflater, container, savedInstanceState);

    }

    @Override
    public void onViewCreated(View view, final Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);






        mBottomSheetBehavior = LockableBottomSheetBehavior.from(mLayoutBottomSheet);
        //region mBottomSheetBehavior
        mBottomSheetBehavior.setBottomSheetCallback(new LockableBottomSheetBehavior.BottomSheetCallback() {
            @SuppressLint("WrongConstant")
            @Override
            public void onStateChanged(@NonNull View bottomSheet, int newState) {
                switch (newState) {
                    case LockableBottomSheetBehavior.STATE_COLLAPSED:
                        bottomSheetContent.removeAllViews();

                        break;
                    case LockableBottomSheetBehavior.STATE_EXPANDED:
                        if (mBottomSheetBehavior instanceof LockableBottomSheetBehavior) {
                            ((LockableBottomSheetBehavior) mBottomSheetBehavior).setLocked(true);
                        }
                        break;
                    case BottomSheetBehavior.STATE_SETTLING:

                        break;
                    case BottomSheetBehavior.STATE_DRAGGING:
                        mBottomSheetBehavior.setState(EXPANDED);
                        break;

                }
            }

            @Override
            public void onSlide(@NonNull View bottomSheet, float slideOffset) {

            }
        });



       /*     BonComandeViewModel.getInstance(this).getByStation(App.prefUtils.getUserStationId(), "Patrimoine").observe(getViewLifecycleOwner(), bonCommandes -> {
                final LinearLayoutManager layoutManager = new LinearLayoutManager(context);
                layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
                recyclerView.setLayoutManager(layoutManager);
                if (!bonCommandes.isEmpty()) {
                    showList(bonCommandes);
                } else {
                    emptyView.setVisibility(View.VISIBLE);
                    swipeRefreshLayout.setVisibility(View.GONE);
                }


            });*/


    }

    @Override
    protected void getData(boolean getFromService) {


        if(Clt_code_from_extra!= null) {
            if(!Clt_code_from_extra.equals("")) {
                String clt_name ="";
                clt_name = App.database.clientDAO().getOneByCode(Clt_code_from_extra).cLINomPren;
                ((MainActivity) requireActivity()).setActionBarTitle(getString(R.string.patrimoine_deplacement_out_title) + " ("+clt_name+")");


                BonComandeViewModel.getInstance(this).getByCodeClientandPatEtat(Clt_code_from_extra, getFrom()).observe(this, bonCommandes -> {
                    final LinearLayoutManager layoutManager = new LinearLayoutManager(context);
                    layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
                    recyclerView.setLayoutManager(layoutManager);
                    if (!bonCommandes.isEmpty()) {
                        showList(bonCommandes);
                    } else {
                        emptyView.setVisibility(View.VISIBLE);
                        swipeRefreshLayout.setVisibility(View.GONE);
                    }
                });
            }

        }
        else {
            BonComandeViewModel.getInstance(this).getByPatStationAndType(App.prefUtils.getUserStationId(), "patrimoine",getFrom(),getFrom()).observe(this, bonCommandes -> {
                final LinearLayoutManager layoutManager = new LinearLayoutManager(context);
                layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
                recyclerView.setLayoutManager(layoutManager);
                if (!bonCommandes.isEmpty()) {
                    showList(bonCommandes);
                } else {
                    emptyView.setVisibility(View.VISIBLE);
                    swipeRefreshLayout.setVisibility(View.GONE);
                }
            });
        }



    }

    private String getFrom(){
        String PatFrom = "";

         PatFrom = Globals.TYPE_PATRIMOINE.SORTIE.getTypePat();

        return PatFrom;
    }
    private void showList(List<BonCommande> bonCommandes) {
        items = new ArrayList<>();
        for (BonCommande item : bonCommandes) {
            List<LigneBonCommande> ligneBonCommandes;
            if(item.isSync) ligneBonCommandes = App.database.ligneBonCommandeDAO().getByBCCode(item.getDEVNum());
            else ligneBonCommandes = App.database.ligneBonCommandeDAO().getByBCCode(item.getDevCodeM());
            putArticleInLignes(ligneBonCommandes);
            calcuteTotalAndRemisePrices(item, ligneBonCommandes);
            if (ObjectUtils.isNotEmpty(item))
                items.add(new InvPatItem(context, false, item, R.menu.ticket_item_menu, new MenuItemsAction<BonCommande>() {

                    @Override
                    public boolean itemsAction(MenuItem menuItem, int position) {
                        return false;
                    }

                    @Override
                    public boolean itemsAction(MenuItem menuItem, BonCommande item) {
                        if (menuItem.getItemId() == R.id.delete_item) {
                            App.database.bonCommandeDAO().deleteByIdM(item.devCodeM);
                            App.database.ligneBonCommandeDAO().deleteByCmd(item.devCodeM);
                            Toasty.info(context, "Deleted").show();
                        }
                        return false;
                    }

                }, new ItemCallback<BonCommande, InvPatItem.ViewHolder>() {
                    @Override
                    public void onItemClicked(BonCommande itemIndex) {


                    }

                    @Override
                    public void onItemClicked(InvPatItem.ViewHolder viewHolder, BonCommande itemIndex) {
                        lgBonCommandes = ligneBonCommandes;
                        bc = itemIndex;
                        if(Objects.equals(itemIndex.status, Globals.ITEM_STATUS.WAITING.getStatus())){
                            intent = new Intent(MainActivity.instance, InvPatrimoineActivity.class);
                            intent.putExtra(Globals.TICKET_TO_UPDATE, item);
                            intent.putExtra(Globals.UPDATE_TICKET, true);

                            intent.putExtra(Globals.PAT_FROM, getString(R.string.patrimoine_deplacement_out_title));

                            MainActivity.instance.startActivityForResult(intent, REQUEST_TICKET_CODE);
                        }
                        else   if (mBottomSheetBehavior.getState() != BottomSheetBehavior.STATE_EXPANDED) {
                            mBottomSheetBehavior.setState(BottomSheetBehavior.STATE_EXPANDED);
                            initUI(itemIndex, ligneBonCommandes);
                        }
                    }


                }));
        }
        // Send the result to the UI thread and show it on a Toast
        AsyncJob.doOnMainThread(() -> {
            try {
                fastItemAdapter.clear();
            } catch (Exception e) {

            }
            fastItemAdapter.add(items);
            fastItemAdapter.withSelectable(false);
            fastItemAdapter.notifyAdapterDataSetChanged();
            if (ObjectUtils.isNotEmpty(fastItemAdapter)) {
                recyclerView.setAdapter(fastItemAdapter);
                if (emptyView != null && swipeRefreshLayout != null) {
                    emptyView.setVisibility(View.GONE);
                    swipeRefreshLayout.setVisibility(View.VISIBLE);
                }

            } else {
                emptyView.setVisibility(View.VISIBLE);
                swipeRefreshLayout.setVisibility(View.GONE);
            }


        });
    }

    private void printTicket(BonCommande item, List<LigneBonCommande> ligneBonCommandes) {
        if (ligneBonCommandes != null && ligneBonCommandes.size() > 0) {
            mService = new BluetoothService(context, new Handler() {
                @Override
                public void handleMessage(Message msg2) {
                    switch (msg2.what) {
                        case BluetoothService.MESSAGE_STATE_CHANGE:
                            switch (msg2.arg1) {
                                case BluetoothService.STATE_CONNECTED:
                                    Toast.makeText(context, "Connect successful",
                                            Toast.LENGTH_SHORT).show();

                                    try {
                                        new PrinterHelper(mService, DEFAULT_ENCODING).printBonCommande(context, item, ligneBonCommandes, false);
                                    } catch (IOException e) {
                                        e.printStackTrace();
                                    }
                                    break;
                                case BluetoothService.STATE_CONNECTING:
                                    break;
                                case BluetoothService.STATE_LISTEN:
                                case BluetoothService.STATE_NONE:
                                    break;
                            }
                            break;
                        case BluetoothService.MESSAGE_CONNECTION_LOST:
                            Toast.makeText(context, "Device connection was lost",
                                    Toast.LENGTH_SHORT).show();

                            break;
                        case BluetoothService.MESSAGE_UNABLE_CONNECT:
                            Toast.makeText(context, "Unable to connect device",
                                    Toast.LENGTH_SHORT).show();
                            break;
                    }
                }

            });
            if (!mService.isBTopen()) {
                Intent enableIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
                startActivityForResult(enableIntent, REQUEST_ENABLE_BT);
            }
            if (!mService.isAvailable()) {
                Toast.makeText(context, "Bluetooth is not available", Toast.LENGTH_LONG).show();
            } else {
                Intent serverIntent = new Intent(context, DeviceListActivity.class);
                startActivityForResult(serverIntent, REQUEST_CONNECT_DEVICE);
            }
        } else {
            Toasty.info(context, "Aucune ligne à été trouvée pour cette B.C!!").show();
        }
    }

    @Override
    protected boolean getFilter(InvPatItem item, CharSequence constraint) {
        return (
                IOUtils.unAccent(item.ticket.getDEVClient()).contains(String.valueOf(constraint))
                        ||  IOUtils.unAccent(item.ticket.getCode()).contains(String.valueOf(constraint))
                        ||  IOUtils.unAccent(item.ticket.getDEVNum()).contains(String.valueOf(constraint))
                        ||  IOUtils.unAccent(item.ticket.getDevCodeM()).contains(String.valueOf(constraint))
                        ||  IOUtils.unAccent(item.ticket.getDEVCodeClient()).contains(String.valueOf(constraint))
        );
    }

    private void calcuteTotalAndRemisePrices(BonCommande item, List<LigneBonCommande> ligneBonRetours) {

        double mntRemise = 0.0;
        double total = 0.0;
        for (LigneBonCommande ligneTicket : ligneBonRetours) {
            try {
                mntRemise += Double.parseDouble(ligneTicket.getLGDEVQte()) *
                        ligneTicket.getArticle().pvttc * Double.parseDouble(ligneTicket.getLGDEVRemise()) / 100;
                total += Double.parseDouble(ligneTicket.getLGDEVQte()) * ligneTicket.getArticle().pvttc;
            } catch (Exception e) {
                Log.d("errcoammabdefragment", e.getMessage());
            }
        }
        item.setDEVRemise(mntRemise + "");
        item.setDEVMntTTC(total + "");
    }

    private void putArticleInLignes(List<LigneBonCommande> ligneTickets) {
        for (int i = 0; i < ligneTickets.size(); i++) {
            ligneTickets.get(i).setArticle(App.database.articleDAO().getOneByCode(ligneTickets.get(i).getLGDEVCodeArt()));
        }
    }

    void onExpansionUpdate(ExpandableLayout expandableLayout, IconicsButton button) {
        expandableLayout.setOnExpansionUpdateListener((expansionFraction, state) -> {
            switch (state) {
                case EXPANDING:
                    button.setText("{faw-chevron-up}");
                    break;

                case COLLAPSING:
                    button.setText("{faw-chevron-down}");
                    break;
            }
        });
    }


    @RequiresApi(api = Build.VERSION_CODES.O)
    @Override
    public void onCreateOptionsMenu(Menu menu, MenuInflater inflater) {
        inflater.inflate(R.menu.ticket_menu, menu);
        SearchManager searchManager =
                (SearchManager) context.getSystemService(Context.SEARCH_SERVICE);
        searchView =
                (SearchView) menu.findItem(R.id.search).getActionView();
        searchView.setSearchableInfo(
                searchManager.getSearchableInfo(context.getComponentName()));

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB) {
            searchView.setOnQueryTextListener(new SearchView.OnQueryTextListener() {
                @Override
                public boolean onQueryTextSubmit(String s) {
                    return true;
                }

                @Override
                public boolean onQueryTextChange(String s) {
                    if (mBottomSheetBehavior.getState() != BottomSheetBehavior.STATE_EXPANDED)
                        fastItemAdapter.filter(s);
                    else{
                        // Create a copy of the original list
                        Log.d("kkjnhh","bb "+ String.valueOf(lgBonCommandes.size()));
                        List<LigneBonCommande> originalList = new ArrayList<>(lgBonCommandes);

                        // Filter the copy based on the search query
                        List<LigneBonCommande> filteredList = originalList.stream()
                                .filter(ligneBonCommande -> ligneBonCommande.lGDevNumSerie.toLowerCase().contains(s.toLowerCase())||
                                        ligneBonCommande.getArticle().getaRTDesignation().toLowerCase().contains(s.toLowerCase()))
                                .collect(Collectors.toList());


                        SortableLigneInvTableView ligneTicketTableView = context.findViewById(R.id.invtableView);

                        ligneTicketTableView.setDataAdapter(new LigneInventaireTableDataAdapter(context, filteredList, ligneTicketTableView));

                    }                    return true;
                }
            });
        } else {
            searchView.setVisibility(View.GONE);
        }

        add = menu.findItem(R.id.add);
        add.setIcon(new IconicsDrawable(context).icon(GoogleMaterial.Icon.gmd_add_shopping_cart)
                .color(Color.WHITE).sizeDp(24));

        add.setOnMenuItemClickListener(item -> {
            //  if (prefUtils.getClotSess() && !Utils.isNotSessionCaisseExpired(prefUtils.getSessionDateOuv())/* == -1*/) {
            if ((prefUtils.getClotSess() && Utils.isSessionCaisseExpired(prefUtils.getSessionDateOuv()) == -1)
                    ||(prefUtils.getSessionDateOuv().equals("") && App.prefUtils.getBlAuthorization())) {

                new MaterialDialog.Builder(context)
                        .title(R.string.confirmation)
                        .content(R.string.session_caisse_expired)
                        .positiveText(R.string.yes)
                        .negativeText(R.string.no)
                        .onPositive((dialog, which) -> {
                            MainActivity.instance.closeSession(true);
                        }).onNegative((dialog, which) -> dialog.dismiss())
                        .show();
            } else {
                if(App.prefUtils.getSessionCaisseId().equals(DEFAULT_VALUE) && prefUtils.getBlAuthorization()){
                    new MaterialDialog.Builder(context)
                            //  .title(R.string.confirmation)
                            .content("ID Session Invalid")
                            .positiveText(R.string.quitter)
                            // .negativeText(R.string.no)
                            .onPositive((dialog, which) -> {
                                dialog.dismiss();
                            })

                            //.onNegative((dialog, which) -> dialog.dismiss())
                            .show();

                }else {
                    if (App.database.stationStockDAO().getByCodeStation(App.prefUtils.getUserStationId()).isEmpty()&& !Objects.equals(App.prefUtils.getUserType(), "Operateur patrimoine")) {
                        new IonAlert(getActivity(), IonAlert.WARNING_TYPE)
                                .setTitleText(getString(R.string.no_article_found))
                                .setContentText(getString(R.string.no_article_found_in_this_station))
                                .setConfirmText("OK")
                                .show();
                        return false;
                    }
                    intent = new Intent(context, InvPatrimoineActivity.class);
                    intent.putExtra(Globals.PAT_FROM, getString(R.string.patrimoine_deplacement_out_title));
                    add.setEnabled(false);

                    intent.putExtra(Globals.TICKET_NUMBER_VALUE, ticketNumber);
                    requireActivity().startActivityForResult(intent, REQUEST_TICKET_CODE);
                }

            }
            return false;
        });

    }

    private void initUI(BonCommande ticket, List<LigneBonCommande> lignesTicket) {
        LayoutInflater inflater = (LayoutInflater) context.getSystemService(LAYOUT_INFLATER_SERVICE);
        bottomSheetContent.addView(inflater.inflate(R.layout.inventaire_sub_item, null));
        SortableLigneInvTableView ligneTicketTableView = context.findViewById(R.id.invtableView);

        SortableRestoTicketTableView ligneTraiteTableView = context.findViewById(R.id.restoTicketDataTableView);
        Toolbar toolbar = context.findViewById(R.id.expanded_toolbar);
        ExpandableLayout productExpandableLayout = context.findViewById(R.id.product_expandable_layout);
        ExpandableLayout paymentExpandableLayout = context.findViewById(R.id.payment_expandable_layout);
        LinearLayout productDrawer = context.findViewById(R.id.product_drawer);
        CardView cashLayout = context.findViewById(R.id.cash_view);
        CardView checkLayout = context.findViewById(R.id.check_view);
        CardView ticketRestoLayout = context.findViewById(R.id.ticket_resto_view);
        LinearLayout paymentDrawer = context.findViewById(R.id.payment_drawer);
        IconicsButton productDrawerButton = context.findViewById(R.id.product_drawer_button);
        IconicsButton paymentDrawerButton = context.findViewById(R.id.payment_drawer_button);
        toolbar.inflateMenu(R.menu.ticket_subitem_menu);
        MenuItem closeButton = toolbar.getMenu().findItem(R.id.close);
        closeButton.setIcon(new IconicsDrawable(context)
                .icon(GoogleMaterial.Icon.gmd_close)
                .color(context.getResources().getColor(R.color.material_white))
                .sizeDp(16));
        closeButton.setOnMenuItemClickListener(item -> {
            mBottomSheetBehavior.setState(BottomSheetBehavior.STATE_COLLAPSED);
            return false;
        });
        toolbar.setTitle(String.format(getString(R.string.invpat_number_field), ticket.getDEVNum() + ""));
        toolbar.setSubtitle(ticket.getDEVClient());
        cashLayout.setVisibility(View.GONE);
        checkLayout.setVisibility(View.GONE);
        ligneTraiteTableView.setDataAdapter(new LigneTicketRestoTableDataAdapter(context, traiteCaisseList, ligneTraiteTableView));
        ticketRestoLayout.setVisibility(View.GONE);
        if (!lignesTicket.isEmpty() && lignesTicket != null) {
            ligneTicketTableView.setSwipeToRefreshEnabled(false);
            ligneTicketTableView.setHeaderBackground(R.color.material_teal500);
            ligneTicketTableView.setVerticalScrollBarEnabled(true);
            ligneTicketTableView.setScrollbarFadingEnabled(true);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                ligneTicketTableView.setNestedScrollingEnabled(true);
                ligneTicketTableView.setElevation(10);
            }
            ligneTicketTableView.setWeightSum(1);
            ligneTicketTableView.setDataAdapter(new LigneInventaireTableDataAdapter(context, lignesTicket, ligneTicketTableView));
            productExpandableLayout.setExpanded(true);
        } else {
            productExpandableLayout.setExpanded(false);
            ligneTicketTableView.setVisibility(View.GONE);
        }

        onExpansionUpdate(productExpandableLayout, productDrawerButton);
        onExpansionUpdate(paymentExpandableLayout, paymentDrawerButton);
        productDrawer.setOnClickListener(v -> {
            System.out.println("product");
            if (!productExpandableLayout.isExpanded()) {
                productExpandableLayout.expand(true);
                paymentExpandableLayout.collapse(true);
            } else {
                productExpandableLayout.collapse(true);

            }
        });
        paymentDrawer.setOnClickListener(v -> {

            if (!paymentExpandableLayout.isExpanded()) {
                paymentExpandableLayout.expand(true);
                productExpandableLayout.collapse(true);
            } else {
                paymentExpandableLayout.collapse(true);

            }
        });


    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mService != null)
            mService.stop();
        mService = null;
    }

    @Override
    public void onResume() {
        super.onResume();
        //     getData(false);
        if (add != null) {
            add.setEnabled(true);
        }
    }

    @Override
    public void itemsFiltered(@Nullable CharSequence constraint, @Nullable List results) {

    }


    @Override
    public void onReset() {

    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);


        switch (requestCode) {
            case REQUEST_ENABLE_BT:
                if (resultCode == RESULT_OK) {
                    Toast.makeText(context, "Bluetooth open successful", Toast.LENGTH_LONG).show();
                } else {
                    Toast.makeText(context, "Bluetooth failed to connect", Toast.LENGTH_LONG).show();
                }
                break;
            case REQUEST_CONNECT_DEVICE:
                if (resultCode == RESULT_OK) {
                    String address = data.getExtras()
                            .getString(DeviceListActivity.EXTRA_DEVICE_ADDRESS);
                    con_dev = mService.getDevByMac(address);

                    mService.connect(con_dev);
                }
                break;
            case REQUEST_TICKET_CODE:
                if (resultCode == RESULT_OK) {
                    //   Toast.makeText(context, "Bluetooth open successful", Toast.LENGTH_LONG).show();
                    getData(false);
                    if (context.getCurrentFocus() != null)
                        Snackbar.make(context.getCurrentFocus(), R.string.added_succesfully, Snackbar.LENGTH_LONG)
                                .show();
                }
                break;


        }


    }


}
