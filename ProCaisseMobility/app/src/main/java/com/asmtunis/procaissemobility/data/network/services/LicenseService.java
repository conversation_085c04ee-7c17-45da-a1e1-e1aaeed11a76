package com.asmtunis.procaissemobility.data.network.services;

import com.asmtunis.procaissemobility.data.models.BonRetour;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.LicenseResponse;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;



public interface LicenseService {

    @POST("getLicensesUrl")
    Call<LicenseResponse> getLicensesUrl(@Body GenericObject genericObject);

}

