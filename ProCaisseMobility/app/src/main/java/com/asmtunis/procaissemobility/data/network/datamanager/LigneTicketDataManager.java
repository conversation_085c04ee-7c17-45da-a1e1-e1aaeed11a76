package com.asmtunis.procaissemobility.data.network.datamanager;


import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.LigneTicket;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.LignesTicketService;

import java.util.List;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

/**
 * Created by PC on 10/4/2017.
 */

public class LigneTicketDataManager {
    private static LigneTicketDataManager sInstance;

    private final LignesTicketService mLignesTicketService;

    public LigneTicketDataManager() {
        mLignesTicketService = new ServiceFactory<>(LignesTicketService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"LigneTicket")).makeService();

    }

    public static LigneTicketDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new LigneTicketDataManager();
        }
        return sInstance;
    }

    public void getLigneTicketByTickets(GenericObject genericObject,
                                       RemoteCallback<List<List<LigneTicket>>> listener) {
        mLignesTicketService.getLigneTicketByTickets(genericObject)
                .enqueue(listener);
    }
}

