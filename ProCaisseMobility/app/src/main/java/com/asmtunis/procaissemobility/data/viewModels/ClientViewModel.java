package com.asmtunis.procaissemobility.data.viewModels;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelProviders;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.dao.ClientDAO;
import com.asmtunis.procaissemobility.data.models.Client;

import java.util.List;

public class ClientViewModel extends ViewModel {
    public ClientDAO dao;
    private static ClientViewModel instance;

    public static ClientViewModel getInstance(Fragment activity) {
        if (instance == null)
            instance = new ViewModelProvider(activity).get(ClientViewModel.class);
        instance.dao = App.database.clientDAO();
        return instance;
    }
    public static ClientViewModel getInstance(FragmentActivity activity) {
        if (instance == null)
            instance = new ViewModelProvider(activity).get(ClientViewModel.class);
        instance.dao = App.database.clientDAO();
        return instance;
    }

    public LiveData<Integer> getNoSyncCount() {
        return dao.getNoSyncCountMutable();
    }

    public LiveData<Integer> getNoSyncCountToUpdate() {
        return dao.getNoSyncToUpdateCountMutable();
    }



    public Integer getNoSyncCountNonMutable() {
        return dao.getNoSyncCountNonMutable();
    }

    public LiveData<List<Client>> getAll() {
        return dao.getAllMutable();
    }

    public LiveData<List<Client>> getAllDNClient(String typeClient, String from) {
        if(from.equals("Clients")){
            return dao.getAllByexepProspectMutable(typeClient);
        }
        else{
            return dao.getAllProspectMutable(typeClient);
        }

    }

    public LiveData<List<Client>> getAllProspectClient(String typeClient) {
        return dao.getAllProspectMutable(typeClient);
    }

    public LiveData<List<Client>> getByStation(Boolean authoriseFiltre, String station) {
        //if(authoriseFiltre) return dao.getAllByStationMutable(station);
         return dao.getByStationMutable();
    }

    public LiveData<Integer> getAllCountBySessionMutable(String station) {
        return dao.getAllCountBySessionMutable(station);
    }


}
