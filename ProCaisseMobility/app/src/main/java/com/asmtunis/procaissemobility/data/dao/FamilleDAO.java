package com.asmtunis.procaissemobility.data.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.Famille;

import java.util.List;

/**
 * Created by Oussama AZIZI on 4/13/22.
 */

@Dao
public interface FamilleDAO {
        @Insert(onConflict = OnConflictStrategy.REPLACE)
        void insertAll(List<Famille> familleList);

        @Query("delete from Famille")
        void deleteAll();

        @Query("select * from Famille")
        List<Famille> getAll();

}
