package com.asmtunis.procaissemobility.data.network.datamanager;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.DepenceType;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.custom.AddBatchDepenseModel;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.DepenceService;

import java.util.List;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

/**
 * Created by WAEL on 8/30/22.
 */
public class DepenceDataManager   {

    private static DepenceDataManager sInstance;
    private final DepenceService mDepenceService;

    public DepenceDataManager() {
        mDepenceService = new ServiceFactory<>(DepenceService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Depense")).makeService();
    }

    public static DepenceDataManager getInstance( ) {
        if (sInstance == null) {
            sInstance = new DepenceDataManager();
        }
        return sInstance;
    }

    public void getDepence(GenericObject connexion, RemoteCallback<List<DepenceType>> listener) {
        mDepenceService.getDepences(connexion)
                .enqueue(listener);
    }

    public void addBatchDepenseType(GenericObject genericObject, RemoteCallback<List<AddBatchDepenseModel>> listener) {

        mDepenceService.addBatchDepenceType(genericObject)
                .enqueue(listener);
    }

}