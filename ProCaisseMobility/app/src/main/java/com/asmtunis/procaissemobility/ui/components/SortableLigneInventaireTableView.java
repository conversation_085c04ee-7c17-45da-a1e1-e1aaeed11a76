package com.asmtunis.procaissemobility.ui.components;

import android.content.Context;
import android.util.AttributeSet;

import androidx.core.content.ContextCompat;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.comparators.LigneTicketComparators;
import com.asmtunis.procaissemobility.data.models.LigneTicket;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;

import de.codecrafters.tableview.SortableTableView;
import de.codecrafters.tableview.model.TableColumnWeightModel;
import de.codecrafters.tableview.toolkit.SimpleTableHeaderAdapter;
import de.codecrafters.tableview.toolkit.SortStateViewProviders;
import de.codecrafters.tableview.toolkit.TableDataRowBackgroundProviders;

/**
 * Created by Oussama AZIZI on 5/26/22.
 */

public class SortableLigneInventaireTableView extends SortableLigneTicketTableView {


    public SortableLigneInventaireTableView(final Context context) {
        this(context, null);
    }

    public SortableLigneInventaireTableView(final Context context, final AttributeSet attributes) {
        this(context, attributes, android.R.attr.listViewStyle);
    }

    public SortableLigneInventaireTableView(final Context context, final AttributeSet attributes, final int styleAttributes) {
        super(context, attributes, styleAttributes);
        LigneTicketTableViewWithDiscount(context);

    }


    void LigneTicketTableViewWithDiscount(final Context context)
    {
        final SimpleTableHeaderAdapter simpleTableHeaderAdapter = new SimpleTableHeaderAdapter(context,
            //    context.getResources().getString(R.string.quantity_field_short_title),
                context.getResources().getString(R.string.num_serie_field_short_title),
                context.getResources().getString(R.string.product_field_title));
        simpleTableHeaderAdapter.setTextColor(ContextCompat.getColor(context, R.color.material_grey100));

        setHeaderAdapter(simpleTableHeaderAdapter);

        final int rowColorEven = ContextCompat.getColor(context, R.color.md_white_1000);
        final int rowColorOdd = ContextCompat.getColor(context, R.color.material_grey100);
        setDataRowBackgroundProvider(TableDataRowBackgroundProviders.alternatingRowColors(rowColorEven, rowColorOdd));
        setHeaderSortStateViewProvider(SortStateViewProviders.brightArrows());

        final TableColumnWeightModel tableColumnWeightModel = new TableColumnWeightModel(2);
        tableColumnWeightModel.setColumnWeight(0, 1);
        tableColumnWeightModel.setColumnWeight(1, 1);
     //   tableColumnWeightModel.setColumnWeight(2, 1);

        setColumnModel(tableColumnWeightModel);

       // setColumnComparator(0, LigneTicketComparators.getLigneTicketQuantityComparator());
       // setColumnComparator(1, LigneTicketComparators.getLigneTicketNameComparator());
        //setColumnComparator(3, LigneTicketComparators.getLigneTicketNameComparator());
    }

}
