package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.DNTypePVente;
import com.asmtunis.procaissemobility.data.models.DNTypeServices;
import com.asmtunis.procaissemobility.data.models.VCPrix;

import java.util.List;

@Dao
public interface DNTypePVenteDAO {
    @Query("SELECT * FROM DNTypePVente")
   // LiveData<List<DNTypePVente>> getAll();
     List<DNTypePVente> getAll();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<DNTypePVente> DNtypeServices);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(DNTypePVente dnTypePVente);

    @Query("SELECT * FROM DNTypePVente where isSync=0 and  (Status='INSERTED'  or Status='UPDATED') ")
    List<DNTypePVente> getNoSynced();

    @Query("SELECT count(*) FROM DNTypePVente where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    LiveData<Integer> getNoSyncCountMubtale();


    @Query("SELECT * FROM DNTypePVente WHERE CodeTypePV = :codeTypePV")
    DNTypePVente getbyCode(String codeTypePV);

    @Query("delete from DNTypePVente")
    void deleteAll();

    @Query("DELETE FROM DNTypePVente where codeTypePV=:codeTypePV")
    void deleteById(String codeTypePV);


    //@Query("UPDATE DNTypeServices SET CodeVCPrix = :code_procaiss where CodeVCPrixM = :CodeMobile")
    // void updateCloudCode(String code_procaiss, String CodeMobile);
}

