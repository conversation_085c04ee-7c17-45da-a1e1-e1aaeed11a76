package com.asmtunis.procaissemobility.adapters.items;

import android.content.Context;
import android.graphics.Paint;
import android.graphics.Typeface;
import android.os.Build;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.adapters.tables.LigneTicketTableDataAdapter;
import com.asmtunis.procaissemobility.data.models.BonCommande;
import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.data.models.LigneBonCommande;
import com.asmtunis.procaissemobility.data.models.LigneTicket;
import com.asmtunis.procaissemobility.data.models.Ticket;
import com.asmtunis.procaissemobility.helper.Globals;
import com.asmtunis.procaissemobility.helper.utils.DateUtils;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.listener.ItemCallback;
import com.asmtunis.procaissemobility.listener.MenuItemClickListener;
import com.asmtunis.procaissemobility.listener.MenuItemsAction;
import com.asmtunis.procaissemobility.ui.components.SortableLigneTicketTableView;
import com.mikepenz.fastadapter.items.AbstractItem;
import com.mikepenz.fontawesome_typeface_library.FontAwesome;
import com.mikepenz.iconics.IconicsDrawable;
import com.rengwuxian.materialedittext.MaterialEditText;

import java.util.List;

import static android.content.Context.LAYOUT_INFLATER_SERVICE;
import static com.asmtunis.procaissemobility.App.prefUtils;

public class CommandeItem extends AbstractItem<CommandeItem, CommandeItem.ViewHolder> {

    private final int UNSELECTED = -1;
    private Context context;
    public BonCommande ticket;
    public List<LigneBonCommande> ligneBonCommande;
    private int selectedItem = UNSELECTED;
    MenuItemsAction menuItemsAction;
    protected ItemCallback itemCallback;
    boolean isOrder;
    int menuId;

    public CommandeItem(Context context, boolean isOrder, BonCommande ticket, List<LigneBonCommande> ligneBonCommande, int menuId, MenuItemsAction
            menuItemsAction, ItemCallback itemCallback) {
        this.ticket = ticket;
        this.ligneBonCommande = ligneBonCommande;
        this.context = context;
        this.menuItemsAction = menuItemsAction;
        this.menuId = menuId;
        this.itemCallback = itemCallback;
        this.isOrder = isOrder;

    }

    public BonCommande getTicket() {
        return ticket;
    }

    public void setTicket(BonCommande ticket) {
        this.ticket = ticket;
    }

    @Override
    public int getType() {
        return R.id.fastadapter_ticket_item_id;
    }

    @Override
    public int getLayoutRes() {
        return R.layout.ticket_item;
    }

    @Override
    public void bindView(final CommandeItem.ViewHolder viewHolder, List<Object> payloads) {
        super.bindView(viewHolder, payloads);
        Client client = App.database.clientDAO().getOneByCode(ticket.getDEVCodeClient());


        String num = "";
        if(ticket.isSync) num= num+ticket.getDEVNum()+ "";
        else num= num+ticket.getDevCodeM()+ "";

        viewHolder.ticketNumber.setText(String.format(context.getString(R.string.order_number_field),num));
        viewHolder.ticketUser.setText((client != null) ? client.cLINomPren : ticket.getBONLIVNum());
        viewHolder.setIsRecyclable(false);
        //viewHolder.price.setText(String.format("%s %s", StringUtils.priceFormat(Double.parseDouble(ticket.getDEVMntTTC()) - Double.parseDouble(ticket.getDEVRemise())), new PrefUtils(context).getCurrency()));
        viewHolder.price.setText(String.format("%s %s", StringUtils.priceFormat(Double.parseDouble(ticket.getDEVMntTTC()) - Double.parseDouble(ticket.getDEVRemise())), new PrefUtils(context).getCurrency()));

        viewHolder.toolbar.setTag("toolbar_" + ticket.getBONLIVNum());
        viewHolder.itemStatusLabel.setTag("itemStatusLabel_ " + ticket.getBONLIVNum());
        viewHolder.price.setTypeface(viewHolder.price.getTypeface(), Typeface.BOLD);
        viewHolder.price.setTextColor(context.getResources().getColor(R.color.successColor));

        viewHolder.toolbar.inflateMenu(menuId);

      if (Double.parseDouble(ticket.getDEVMntTTC()) > 0) {

            viewHolder.price.setVisibility(View.VISIBLE);
            if (menuId != 0) {

                viewHolder.toolbar.setOnMenuItemClickListener(new MenuItemClickListener<BonCommande>(ticket,
                        menuItemsAction));
                viewHolder.toolbar.getMenu().findItem(R.id.print_item).setIcon(new IconicsDrawable(context)
                        .icon(FontAwesome.Icon.faw_print)
                        .color(context.getResources().getColor(R.color.material_teal700))
                        .sizeDp(20));
            }


            if (!ticket.isSync) {
                viewHolder.dateCreation.setTextColor(context.getResources().getColor(R.color.warningColor));

                viewHolder.dateCreation.setText(ticket.getDEVDDm().replace("Z", "").replace("T", " ").replace(".000",""));
                setTriangleView(viewHolder.itemStatusLabel, 0);
            }
            else{
                viewHolder.dateCreation.setText(ticket.getDEVDate().replace("Z", "").replace("T", " ").replace(".000",""));
                viewHolder.dateCreation.setTextColor(context.getResources().getColor(R.color.successColor));
                setTriangleView(viewHolder.itemStatusLabel, 2);
                viewHolder.toolbar.getMenu().findItem(R.id.edit_item).setVisible(false);
            }



            boolean bcTransfromedToBl;
            if (ticket.getDEVObservation() != null) {
                bcTransfromedToBl= ticket.getDEVObservation().equals("");

            } else bcTransfromedToBl = true;

           viewHolder.toolbar.getMenu().findItem(R.id.bc_to_bl_item).setVisible(
                    ticket.status.equals(Globals.ITEM_STATUS.SELECTED.getStatus())
                    && prefUtils.getBctoBlAuthorization()
                    && bcTransfromedToBl);



            viewHolder.toolbar.getMenu().findItem(R.id.validate_item)
                    .setVisible(ticket.status.equals(Globals.ITEM_STATUS.WAITING.getStatus()));


            if (itemCallback != null) {
                viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        onViewClick(viewHolder);
                    }
                });
                viewHolder.toolbar.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        onViewClick(viewHolder);
                    }
                });
            }
        }
    }

    void onViewClick(CommandeItem.ViewHolder viewHolder) {
        if (ticket != null) {
            if (itemCallback == null) {
                return;
            } else {
                itemCallback.onItemClicked(viewHolder, ticket);
            }
        }
    }

    //reset the view here (this is an optional method, but recommended)
    @Override
    public void unbindView(final CommandeItem.ViewHolder holder) {
        super.unbindView(holder);
        holder.toolbar.setTitle(null);
        holder.toolbar.setSubtitle(null);
    }

    //Init the viewHolder for this Item
    @Override
    public CommandeItem.ViewHolder getViewHolder(View v) {
        return new CommandeItem.ViewHolder(v, menuId, menuItemsAction, ticket);
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        com.asmtunis.procaissemobility.ui.components.TicketView ticketView;
        LinearLayout footerLayout;
        public Toolbar toolbar;
        TextView dateCreation;
        TextView price;
        TextView ticketNumber;
        TextView ticketUser;
        FrameLayout content;


        jp.shts.android.library.TriangleLabelView itemStatusLabel;
        BonCommande ticket;

        public ViewHolder(View view, int menuId, MenuItemsAction menuItemsAction, BonCommande ticket) {
            super(view);
            this.ticket = ticket;
            ticketView = view.findViewById(R.id.layout_ticket);
            footerLayout = view.findViewById(R.id.footer_layout);
            toolbar = view.findViewById(R.id.toolbar);
            price = view.findViewById(R.id.price);
            ticketNumber = view.findViewById(R.id.ticketNumber);
            ticketUser = view.findViewById(R.id.ticketUser);
            dateCreation = view.findViewById(R.id.dateCreation);
            content = view.findViewById(R.id.content_layout);
            itemStatusLabel = view.findViewById(R.id.item_status_label);


        }
    }


    void setTriangleView(jp.shts.android.library.TriangleLabelView labelView, int status) {
        labelView.setVisibility(View.VISIBLE);
        switch (status) {
            case 0:
                labelView.setTriangleBackgroundColorResource(R.color.warningColor);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.notSync);
                labelView.setPrimaryTextColorResource(R.color.md_red_100);

                break;

            case 1:
                labelView.setTriangleBackgroundColorResource(R.color.md_green_800);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.new_label);
                labelView.setPrimaryTextColorResource(R.color.md_green_100);
                break;

            case  2 :
                labelView.setTriangleBackgroundColorResource(R.color.md_green_800);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.Sync);
                labelView.setPrimaryTextColorResource(R.color.md_green_100);
                break;

            default:
                labelView.setVisibility(View.GONE);

                break;
        }

    }

}

