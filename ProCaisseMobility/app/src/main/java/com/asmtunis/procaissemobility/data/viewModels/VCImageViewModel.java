package com.asmtunis.procaissemobility.data.viewModels;

import androidx.fragment.app.Fragment;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelProviders;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.dao.VCImageDAO;
import com.asmtunis.procaissemobility.data.models.VCImage;

import java.util.List;

public class VCImageViewModel extends ViewModel {
  public VCImageDAO vcImageDAO;
  private static VCImageViewModel instance;

  public static VCImageViewModel getInstance(Fragment fragment){
    if(instance==null ){
      instance = new ViewModelProvider(fragment).get(VCImageViewModel.class);
      instance.vcImageDAO= App.database.vcImageDAO();
    }
    return instance;
  }
  public LiveData<List<VCImage>> getImages(String code){
    return App.database.vcImageDAO().getImageByCode(code);
  }

}
