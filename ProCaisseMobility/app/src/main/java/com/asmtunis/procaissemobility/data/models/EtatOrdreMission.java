package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.HashMap;

@Entity
public class EtatOrdreMission {
    @PrimaryKey
    @NonNull
    @SerializedName("CodeEtatOrd")
    @ColumnInfo(name = "CodeEtatOrd")
    @Expose
    public String codeEtatOrd;
    @SerializedName("LibelleEtatOrd")
    @ColumnInfo(name = "LibelleEtatOrd")
    @Expose
    public String libelleEtatOrd;
    @SerializedName("DescriptionEtatOrd")
    @ColumnInfo(name = "DescriptionEtatOrd")
    @Expose
    public String descriptionEtatOrd;
    @SerializedName("export")
    @ColumnInfo(name = "export")
    @Expose
    public Integer export;
    @SerializedName("DDm")
    @ColumnInfo(name = "DDm")
    @Expose
    public String dDm;
    @SerializedName("exportM")
    @ColumnInfo(name = "exportM")
    @Expose
    public Integer exportM;
    @SerializedName("DDmM")
    @ColumnInfo(name = "DDmM")
    @Expose
    public String dDmM;

    @SerializedName("Couleur")
    @ColumnInfo(name = "Couleur")
    @Expose
    public int couleur;

    public EtatOrdreMission(@NonNull String codeEtatOrd, String libelleEtatOrd, String descriptionEtatOrd, Integer export, String dDm, Integer exportM, String dDmM, int couleur) {
        this.codeEtatOrd = codeEtatOrd;
        this.libelleEtatOrd = libelleEtatOrd;
        this.descriptionEtatOrd = descriptionEtatOrd;
        this.export = export;
        this.dDm = dDm;
        this.exportM = exportM;
        this.dDmM = dDmM;
        this.couleur = couleur;
    }

    public int getCouleur() {
        return couleur;
    }

    public String getLibelleEtatOrd() {
        return libelleEtatOrd;
    }

    @NonNull
    public String getCodeEtatOrd() {
        return codeEtatOrd;
    }

    @Override
    public String toString() {
        return "EtatOrdreMission{" +
                "libelleEtatOrd='" + libelleEtatOrd + '\'' +
                '}';
    }
}
