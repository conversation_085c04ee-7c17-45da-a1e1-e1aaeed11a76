package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * Created by Achraf on 14/09/2017.
 */
@Entity(primaryKeys = {"MAR_Code"})

public class Marque extends BaseModel  implements Serializable {

    @ColumnInfo(name = "MAR_Station")
    @SerializedName("MAR_Station")
    @Expose
    public String MAR_Station;

    @ColumnInfo(name = "MAR_User")
    @SerializedName("MAR_User")
    @Expose
    public String MAR_User;

    @NonNull
    @ColumnInfo(name = "MAR_Code")
    @SerializedName("MAR_Code")
    @Expose
    public String MAR_Code="";

    @ColumnInfo(name = "MAR_Designation")
    @SerializedName("MAR_Designation")
    @Expose
    public String MAR_Designation;

    public Marque() {
    }

    public Marque(String MAR_Station, String MAR_User, String MAR_Code, String MAR_Designation) {

        this.MAR_Code = MAR_Code;
        this.MAR_Station = MAR_Station;
        this.MAR_User = MAR_User;
        this.MAR_Designation = MAR_Designation;
    }

    public String getMAR_Station() {
        return MAR_Station;
    }

    public void setMAR_Station(String MAR_Station) {
        this.MAR_Station = MAR_Station;
    }

    public String getMAR_User() {
        return MAR_User;
    }

    public void setMAR_User(String MAR_User) {
        this.MAR_User = MAR_User;
    }

    public String getMAR_Code() {
        return MAR_Code;
    }

    public void setMAR_Code(String MAR_Code) {
        this.MAR_Code = MAR_Code;
    }

    public String getMAR_Designation() {
        return MAR_Designation;
    }

    public void setMAR_Designation(String MAR_Designation) {
        MAR_Designation = MAR_Designation;
    }

    @Override
    public String toString() {
        return "marque{" +
                "MAR_Station=" + MAR_Station +
                ", MAR_User='" + MAR_User + '\'' +
                ", MAR_Code='" + MAR_Code + '\'' +
                ", MAR_Designation='" + MAR_Designation + '\'' +

                '}';
    }

}
