package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.Traking;

import java.util.List;

@Dao
public interface TrakingDAO {
    @Insert()
    void insertOne(Traking traking);

    @Query("SELECT * FROM Traking")
    List<Traking> getTraks();

    @Query("Delete from Traking")
    void deleteAll();

    @Query("select count(*) from Traking")
    LiveData<Integer> count();

    @Delete
    void deleteOne(Traking traking);
}
