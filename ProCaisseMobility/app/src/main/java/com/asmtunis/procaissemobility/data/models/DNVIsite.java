package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

@Entity
public class DNVIsite  extends BaseModel implements Serializable {

    public DNVIsite() {
    }
    public DNVIsite(String VIS_Num, String VIS_Exerc,@NonNull String VIS_Code_M) {
        this.VIS_Num = VIS_Num;
        this.VIS_Exerc = VIS_Exerc;
        this.VIS_Code_M = VIS_Code_M;


    }


    public DNVIsite(String VIS_Num, String VIS_Exerc) {
        this.VIS_Num = VIS_Num;
        this.VIS_Exerc = VIS_Exerc;


    }
    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "VIS_Code_M")
    @SerializedName("VIS_Code_M")
    @Expose
    private String VIS_Code_M;

    @ColumnInfo(name = "VIS_Num")
    @SerializedName("VIS_Num")
    @Expose
    private String VIS_Num;


    @ColumnInfo(name = "VIS_Exerc")
    @SerializedName("VIS_Exerc")
    @Expose
    private String VIS_Exerc;


    @ColumnInfo(name = "VIS_Date")
    @SerializedName("VIS_Date")
    @Expose
    private String VIS_Date;


    @ColumnInfo(name = "VIS_CodeClient")
    @SerializedName("VIS_CodeClient")
    @Expose
    private String VIS_CodeClient;


    @ColumnInfo(name = "VIS_User")
    @SerializedName("VIS_User")
    @Expose
    private String VIS_User;


    @ColumnInfo(name = "VIS_NomMagazin")
    @SerializedName("VIS_NomMagazin")
    @Expose
    private String VIS_NomMagazin;


    @ColumnInfo(name = "VIS_Gouvernorat")
    @SerializedName("VIS_Gouvernorat")
    @Expose
    private String VIS_Gouvernorat;


    @ColumnInfo(name = "VIS_Delegations")
    @SerializedName("VIS_Delegations")
    @Expose
    private String VIS_Delegations;


    @ColumnInfo(name = "VIS_Adresse")
    @SerializedName("VIS_Adresse")
    @Expose
    private String VIS_Adresse;

    @ColumnInfo(name = "VIS_NomGerant")
    @SerializedName("VIS_NomGerant")
    @Expose
    private String VIS_NomGerant;

    @ColumnInfo(name = "VIS_NumTele")
    @SerializedName("VIS_NumTele")
    @Expose
    private String VIS_NumTele;


    @ColumnInfo(name = "VIS_TypePV")
    @SerializedName("VIS_TypePV")
    @Expose
    private String VIS_TypePV;

    @ColumnInfo(name = "VIS_TypeServ")
    @SerializedName("VIS_TypeServ")
    @Expose
    private String VIS_TypeServ;




    @ColumnInfo(name = "VIS_Superf")
    @SerializedName("VIS_Superf")
    @Expose
    private String VIS_Superf;


    @ColumnInfo(name = "VIS_DDM")
    @SerializedName("VIS_DDM")
    @Expose
    private String VIS_DDM;


    @ColumnInfo(name = "VIS_Info1")
    @SerializedName("VIS_Info1")
    @Expose
    private String VIS_Info1;


    public String getVIS_Latitude() {
        return VIS_Latitude;
    }

    public void setVIS_Latitude(String VIS_Latitude) {
        this.VIS_Latitude = VIS_Latitude;
    }

    public String getVIS_Longitude() {
        return VIS_Longitude;
    }

    public void setVIS_Longitude(String VIS_Longitude) {
        this.VIS_Longitude = VIS_Longitude;
    }

    @ColumnInfo(name = "VIS_Latitude")
    @SerializedName("VIS_Latitude")
    @Expose
    private String VIS_Latitude;


    @ColumnInfo(name = "VIS_Longitude")
    @SerializedName("VIS_Longitude")
    @Expose
    private String VIS_Longitude;

    public DNVIsite(String status, boolean isSync, @NonNull String VIS_Code_M, String VIS_Num, String VIS_Exerc,
                    String VIS_Date, String VIS_CodeClient, String VIS_User, String VIS_NomMagazin,
                    String VIS_Gouvernorat, String VIS_Delegations, String VIS_Adresse, String VIS_NomGerant,
                    String VIS_NumTele, String VIS_TypePV, String VIS_TypeServ, String VIS_Superf, String VIS_DDM, String VIS_Info1, String VIS_Info2, String VIS_Info3) {
        super(status, isSync);
        this.VIS_Code_M = VIS_Code_M;
        this.VIS_Num = VIS_Num;
        this.VIS_Exerc = VIS_Exerc;
        this.VIS_Date = VIS_Date;
        this.VIS_CodeClient = VIS_CodeClient;
        this.VIS_User = VIS_User;
        this.VIS_NomMagazin = VIS_NomMagazin;
        this.VIS_Gouvernorat = VIS_Gouvernorat;
        this.VIS_Delegations = VIS_Delegations;
        this.VIS_Adresse = VIS_Adresse;
        this.VIS_NomGerant = VIS_NomGerant;
        this.VIS_NumTele = VIS_NumTele;
        this.VIS_TypePV = VIS_TypePV;
        this.VIS_TypeServ = VIS_TypeServ;
        this.VIS_Superf = VIS_Superf;
        this.VIS_DDM = VIS_DDM;
        this.VIS_Info1 = VIS_Info1;
        this.VIS_Info2 = VIS_Info2;
        this.VIS_Info3 = VIS_Info3;
    }

    public DNVIsite(@NonNull String VIS_Code_M,
                    String VIS_Num,
                    String VIS_Exerc,
                    String VIS_Date,
                    String VIS_CodeClient,
                    String VIS_User,
                    String VIS_NomMagazin,
                    String VIS_Gouvernorat,
                    String VIS_Delegations,
                    String VIS_Adresse,
                    String VIS_NomGerant,
                    String VIS_NumTele,
                    String VIS_TypePV,
                    String VIS_TypeServ,
                    String VIS_Superf,
                    String VIS_DDM,
                    String VIS_Info1,
                    String VIS_Info2,
                    String VIS_Info3,
                    String VIS_Longitude,
                    String VIS_Latitude) {
        this.VIS_Code_M = VIS_Code_M;
        this.VIS_Num = VIS_Num;
        this.VIS_Exerc = VIS_Exerc;
        this.VIS_Date = VIS_Date;
        this.VIS_CodeClient = VIS_CodeClient;
        this.VIS_User = VIS_User;
        this.VIS_NomMagazin = VIS_NomMagazin;
        this.VIS_Gouvernorat = VIS_Gouvernorat;
        this.VIS_Delegations = VIS_Delegations;
        this.VIS_Adresse = VIS_Adresse;
        this.VIS_NomGerant = VIS_NomGerant;
        this.VIS_NumTele = VIS_NumTele;
        this.VIS_TypePV = VIS_TypePV;
        this.VIS_TypeServ = VIS_TypeServ;
        this.VIS_Superf = VIS_Superf;
        this.VIS_DDM = VIS_DDM;
        this.VIS_Info1 = VIS_Info1;
        this.VIS_Info2 = VIS_Info2;
        this.VIS_Info3 = VIS_Info3;
        this.VIS_Longitude = VIS_Longitude;
        this.VIS_Latitude =VIS_Latitude;
    }

    @ColumnInfo(name = "VIS_Info2")
    @SerializedName("VIS_Info2")
    @Expose
    private String VIS_Info2;


    @NonNull
    public String getVIS_Code_M() {
        return VIS_Code_M;
    }

    public void setVIS_Code_M(@NonNull String VIS_Code_M) {
        this.VIS_Code_M = VIS_Code_M;
    }

    public String getVIS_Num() {
        return VIS_Num;
    }

    public void setVIS_Num(String VIS_Num) {
        this.VIS_Num = VIS_Num;
    }

    public String getVIS_Exerc() {
        return VIS_Exerc;
    }

    public void setVIS_Exerc(String VIS_Exerc) {
        this.VIS_Exerc = VIS_Exerc;
    }

    public String getVIS_Date() {
        return VIS_Date;
    }

    public void setVIS_Date(String VIS_Date) {
        this.VIS_Date = VIS_Date;
    }

    public String getVIS_CodeClient() {
        return VIS_CodeClient;
    }

    public void setVIS_CodeClient(String VIS_CodeClient) {
        this.VIS_CodeClient = VIS_CodeClient;
    }

    public String getVIS_User() {
        return VIS_User;
    }

    public void setVIS_User(String VIS_User) {
        this.VIS_User = VIS_User;
    }

    public String getVIS_NomMagazin() {
        return VIS_NomMagazin;
    }

    public void setVIS_NomMagazin(String VIS_NomMagazin) {
        this.VIS_NomMagazin = VIS_NomMagazin;
    }

    public String getVIS_Gouvernorat() {
        return VIS_Gouvernorat;
    }

    public void setVIS_Gouvernorat(String VIS_Gouvernorat) {
        this.VIS_Gouvernorat = VIS_Gouvernorat;
    }

    public String getVIS_Delegations() {
        return VIS_Delegations;
    }

    public void setVIS_Delegations(String VIS_Delegations) {
        this.VIS_Delegations = VIS_Delegations;
    }

    public String getVIS_Adresse() {
        return VIS_Adresse;
    }

    public void setVIS_Adresse(String VIS_Adresse) {
        this.VIS_Adresse = VIS_Adresse;
    }

    public String getVIS_NomGerant() {
        return VIS_NomGerant;
    }

    public void setVIS_NomGerant(String VIS_NomGerant) {
        this.VIS_NomGerant = VIS_NomGerant;
    }

    public String getVIS_NumTele() {
        return VIS_NumTele;
    }

    public void setVIS_NumTele(String VIS_NumTele) {
        this.VIS_NumTele = VIS_NumTele;
    }

    public String getVIS_TypePV() {
        return VIS_TypePV;
    }

    public void setVIS_TypePV(String VIS_TypePV) {
        this.VIS_TypePV = VIS_TypePV;
    }

    public String getVIS_TypeServ() {
        return VIS_TypeServ;
    }

    public void setVIS_TypeServ(String VIS_TypeServ) {
        this.VIS_TypeServ = VIS_TypeServ;
    }

    public String getVIS_Superf() {
        return VIS_Superf;
    }

    public void setVIS_Superf(String VIS_Superf) {
        this.VIS_Superf = VIS_Superf;
    }

    public String getVIS_DDM() {
        return VIS_DDM;
    }

    public void setVIS_DDM(String VIS_DDM) {
        this.VIS_DDM = VIS_DDM;
    }

    public String getVIS_Info1() {
        return VIS_Info1;
    }

    public void setVIS_Info1(String VIS_Info1) {
        this.VIS_Info1 = VIS_Info1;
    }

    public String getVIS_Info2() {
        return VIS_Info2;
    }

    public void setVIS_Info2(String VIS_Info2) {
        this.VIS_Info2 = VIS_Info2;
    }

    public String getVIS_Info3() {
        return VIS_Info3;
    }

    public void setVIS_Info3(String VIS_Info3) {
        this.VIS_Info3 = VIS_Info3;
    }

    @ColumnInfo(name = "VIS_Info3")
    @SerializedName("VIS_Info3")
    @Expose
    private String VIS_Info3;

}
