package com.asmtunis.procaissemobility.data.network.services;

import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Timbre;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

public interface TimbreService {
    @POST("getAll")
    Call<List<Timbre>> getTimbres(@Body GenericObject genericObject);
}
