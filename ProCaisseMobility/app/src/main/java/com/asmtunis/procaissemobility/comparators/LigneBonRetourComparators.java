package com.asmtunis.procaissemobility.comparators;

import com.asmtunis.procaissemobility.data.models.LigneBonRetour;

import java.util.Comparator;

/**
 * Created by me on 10/8/2017.
 */


public final class LigneBonRetourComparators {

    private LigneBonRetourComparators() {
    }
    public static LigneBonRetourQuantityComparator getLigneTicketQuantityComparator() {
        return new LigneBonRetourQuantityComparator();
    }

    public static LigneTicketDiscountComparator getLigneTicketDiscountComparator() {
        return new LigneTicketDiscountComparator();
    }

    public static LigneTicketNameComparator getLigneTicketNameComparator() {
        return new LigneTicketNameComparator();
    }

    public static LigneTicketUnitPriceComparator getLigneTicketUnitPriceComparator() {
        return new LigneTicketUnitPriceComparator();
    }

    public static LigneTicketPriceComparator getLigneTicketPriceComparator() {
        return new LigneTicketPriceComparator();
    }


    private static class LigneTicketNameComparator implements Comparator<LigneBonRetour> {

        @Override
        public int compare(final LigneBonRetour ligneTicket1, final LigneBonRetour ligneTicket2) {
            return ligneTicket1.getArticle().getaRTDesignation().toLowerCase().compareTo(ligneTicket2.getArticle().getaRTDesignation().toLowerCase());
        }
    }


    private static class LigneBonRetourQuantityComparator implements Comparator<LigneBonRetour> {

        @Override
        public int compare(final LigneBonRetour ligneTicket1, final LigneBonRetour ligneTicket2) {
            return (Double.parseDouble(ligneTicket1.getLIGBonEntreeQte()) < Double.parseDouble(
                    ligneTicket2.getLIGBonEntreeQte()))?-1:1;
        }
    }

    private static class LigneTicketPriceComparator implements Comparator<LigneBonRetour> {

        @Override
        public int compare(final LigneBonRetour ligneTicket1, final LigneBonRetour ligneTicket2) {
            return(Double.parseDouble(ligneTicket1.getLIGBonEntreeMntTTC()) < Double.parseDouble(ligneTicket2.getLIGBonEntreeMntTTC()))? -1 : 1;
        }
    }

    private static class LigneTicketDiscountComparator implements Comparator<LigneBonRetour> {

        @Override
        public int compare(final LigneBonRetour ligneTicket1, final LigneBonRetour ligneTicket2) {
            return Double.parseDouble(ligneTicket1.getLIGBonEntreeRemise()) <Double.parseDouble( ligneTicket2.getLIGBonEntreeRemise()) ?-1:1;

        }
    }

    private static class LigneTicketUnitPriceComparator implements Comparator<LigneBonRetour> {

        @Override
        public int compare(final LigneBonRetour ligneTicket1, final LigneBonRetour ligneTicket2) {
            if (ligneTicket1.getArticle().getaRTPrixUnitaireHT() < ligneTicket2.getArticle().getaRTPrixUnitaireHT())
                return -1;
            if (ligneTicket1.getArticle().getaRTPrixUnitaireHT() > ligneTicket2.getArticle().getaRTPrixUnitaireHT())
                return 1;
            return 0;
        }
    }

}