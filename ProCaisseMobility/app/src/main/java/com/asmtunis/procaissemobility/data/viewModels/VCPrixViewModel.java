package com.asmtunis.procaissemobility.data.viewModels;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelProviders;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.dao.VCPricesDAO;
import com.asmtunis.procaissemobility.data.models.VCPrix;

import java.util.List;

public class VCPrixViewModel extends ViewModel {
    public VCPricesDAO vcPricesDAO;
    private static VCPrixViewModel instance;

    public static VCPrixViewModel getInstance(Fragment fragment){
        if (instance==null  ){
            instance = new ViewModelProvider(fragment).get(VCPrixViewModel.class);
            instance.vcPricesDAO= App.database.vcPricesDAO();

        }
        return instance;
    }


     /*  public static DNVisiteViewModel getInstance(Fragment activity) {
        if (instance == null)
            instance = ViewModelProviders.of(activity).get(DNVisiteViewModel.class);
        instance.dnVisiteDAO = App.database.dnVisitesDAO();
        return instance;
    }*/

    public static VCPrixViewModel getInstance(FragmentActivity activity) {
        if (instance == null)
            instance = new ViewModelProvider(activity).get(VCPrixViewModel.class);
        instance.vcPricesDAO = App.database.vcPricesDAO();
        return instance;
    }
    public LiveData<List<VCPrix>> getAll(){
        return vcPricesDAO.getAll();
    }


    public LiveData<Integer> getNoSyncCountMubtale(){
        return vcPricesDAO.getNoSyncCountMubtale();
    }

    public LiveData<Integer> getCountNoSyncedToDeleteMubtale(){
        return vcPricesDAO.getCountNoSyncedToDeleteMubtale();
    }

}
