package com.asmtunis.procaissemobility.adapters.items;

import android.content.Context;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.data.models.Reclamation;
import com.asmtunis.procaissemobility.listener.ItemCallback;
import com.asmtunis.procaissemobility.listener.MenuItemClickListener;
import com.asmtunis.procaissemobility.listener.MenuItemsAction;
import com.mikepenz.fastadapter.items.AbstractItem;
import com.mikepenz.fontawesome_typeface_library.FontAwesome;
import com.mikepenz.iconics.IconicsDrawable;
import com.rengwuxian.materialedittext.MaterialEditText;

import java.util.List;

/**
 * Created by PC on 10/11/2017.
 */

public class ReclamationItem extends AbstractItem<ReclamationItem, ReclamationItem.ViewHolder> {

    private  final int UNSELECTED = -1;
    private  Context context;

 
    public Reclamation reclamation;
    private int selectedItem = UNSELECTED;
    MenuItemsAction menuItemsAction;
    protected ItemCallback itemCallback;
    boolean isOrder;
    int menuId;
    private ViewPager mViewPager;


  //  private NavigationTabStrip mTopNavigationTabStrip;
  //  private NavigationTabStrip mBottomNavigationTabStrip;

    public ReclamationItem(Context context, Reclamation reclamation, int menuId, MenuItemsAction
            menuItemsAction, ItemCallback itemCallback){
        this.reclamation = reclamation;
        this.context = context;
        this.menuItemsAction = menuItemsAction;
        this.menuId = menuId;
        this.itemCallback=itemCallback;

    }

    public ReclamationItem(Context context, Reclamation reclamation){
        this.reclamation = reclamation;
        this.context = context;

    }

    public Reclamation getReclamation() {
        return reclamation;
    }

    public void setReclamation(Reclamation reclamation) {
        this.reclamation = reclamation;
    }


    //The unique ID for this type of item
    @Override
    public int getType() {
        return R.id.fastadapter_reclamation_item_id;
    }

    //The unit_price_dialog to be used for this type of item
    @Override
    public int getLayoutRes() {
        return R.layout.ticket_item;
    }

    //The logic to bind your data to the view
    @Override
    public void bindView(final ViewHolder viewHolder, List<Object> payloads) {
        //call super so the selection is already handled for you
        super.bindView(viewHolder, payloads);
        //bind our data
        //set the text for the name
        viewHolder.setIsRecyclable(false);
        viewHolder.toolbar.setTitle(String.format(context.getString(R.string.reclamation_number_field), reclamation.getRecCode()));
        viewHolder.toolbar.setSubtitle(reclamation.getRecIdClient());
       // viewHolder.price.setText(String.format("%s %s", StringUtils.priceFormat(reclamation.gettIKMtTTC()), new PrefUtils(context).getCurrency()));
       // viewHolder.dateCreation.setText(DateUtils.dateToStr(DateUtils.strToDate(reclamation.gettIKDateHeureTicket(), "yyyy-MM-dd HH:mm"), "dd/MM/yyyy"));

        viewHolder.toolbar.setTag("toolbar_"+reclamation.getRecCode());
        viewHolder.itemStatusLabel.setTag("itemStatusLabel_"+reclamation.getRecCode());
//        viewHolder.expandableLayout.setTag("expandableLayout_"+reclamation.gettIKNumTicket());




        if (menuId!=0)

        {
            viewHolder.toolbar.inflateMenu(menuId);

            viewHolder.toolbar.setOnMenuItemClickListener(new MenuItemClickListener<Reclamation>(reclamation,
                    menuItemsAction));
            viewHolder.toolbar.getMenu().findItem(R.id.print_item).setIcon(new IconicsDrawable(context)
                    .icon(FontAwesome.Icon.faw_print)
                    .color(context.getResources().getColor(R.color.material_teal700))
                    .sizeDp(20));


        }


        if (itemCallback!=null)
        {
            viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onViewClick(viewHolder);
                }
            });
            viewHolder.toolbar.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    onViewClick(viewHolder);

                }
            });        }}



    void onViewClick(ReclamationItem.ViewHolder viewHolder)
    {
        if (reclamation != null) {


            if (itemCallback == null) {
                return;
            } else {
                itemCallback.onItemClicked(viewHolder,reclamation);
            }

        }
    }





    //reset the view here (this is an optional method, but recommended)
    @Override
    public void unbindView(final ViewHolder holder) {
        super.unbindView(holder);
      /*  holder.toolbar.setTitle(null);

        holder.toolbar.setSubtitle(null);
        holder.dateCreation.setText(null);
        holder.price.setText(null);*/
      /*  holder.tel1.setText(null);
        holder.address.setText(null);*/
//        holder.count.setText(item.getDateCreation());

    }

    //Init the viewHolder for this Item
    @NonNull
    @Override
    public ViewHolder getViewHolder(View v) {
        return new ViewHolder(v,menuId,menuItemsAction,reclamation);
    }

    void setText(MaterialEditText editText, String s) {
        try {
            if (s.length() <= 1) {
                editText.setVisibility(View.GONE);
            } else {
                editText.setText(s);
            }
        } catch (NullPointerException e) {
        }
    }


    void setTriangleView(jp.shts.android.library.TriangleLabelView labelView, int status) {
        labelView.setVisibility(View.VISIBLE);
            switch (status) {
                case 0:
                    labelView.setTriangleBackgroundColorResource(R.color.md_red_900);
                    labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                    labelView.setPrimaryText(R.string.cancel);
                    labelView.setPrimaryTextColorResource(R.color.md_red_100);

                    break;

                case 1:
                    labelView.setTriangleBackgroundColorResource(R.color.md_green_800);
                    labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                    labelView.setPrimaryText(R.string.new_label);
                    labelView.setPrimaryTextColorResource(R.color.md_green_100);
                    break;
                    default:
                        labelView.setVisibility(View.GONE);

                        break;
            }

    }



    //The viewHolder used for this item. This viewHolder is always reused by the RecyclerView so scrolling is blazing fast
    public static class ViewHolder extends RecyclerView.ViewHolder {

    //    @BindView(R.id.expandable_layout)
    com.asmtunis.procaissemobility.ui.components.TicketView  reclamationView;
    LinearLayout footerLayout;
    //public ExpandableLayout expandableLayout;
       // @BindView(R.id.toolbar)
       public Toolbar toolbar;
       // @BindView(R.id.dateCreation)
        TextView dateCreation;
      //  @BindView(R.id.price)
        TextView price;
      //  @BindView(R.id.content_layout)
        FrameLayout content;


        jp.shts.android.library.TriangleLabelView itemStatusLabel;
                Reclamation reclamation;

        public ViewHolder(View view,int menuId,MenuItemsAction menuItemsAction, Reclamation reclamation) {
            super(view);
            this.reclamation=reclamation;
     //       ButterKnife.bind(this, view);

            reclamationView = view.findViewById(R.id.layout_ticket);
            footerLayout = view.findViewById(R.id.footer_layout);
        //    expandableLayout = view.findViewById(R.id.expandable_layout);
            toolbar = view.findViewById(R.id.toolbar);
            price = view.findViewById(R.id.price);
            dateCreation = view.findViewById(R.id.dateCreation);
            content = view.findViewById(R.id.content_layout);
            itemStatusLabel = view.findViewById(R.id.item_status_label);

/*
            if (reclamation.gettIKAnnuler()>0)
            {
                price.setPaintFlags(price.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG);
                dateCreation.setPaintFlags(dateCreation.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG);
            }
*/

        /*    toolbar.getMenu().findItem(R.id.edit_item).setIcon(new IconicsDrawable(context)
                    .icon(FontAwesome.Icon.faw_pencil)
                    .color(context.getColor(R.color.material_teal700))
                    .sizeDp(20));*/
        }
    }

    //        holder.count.setText(item.getDateCreation());


}
