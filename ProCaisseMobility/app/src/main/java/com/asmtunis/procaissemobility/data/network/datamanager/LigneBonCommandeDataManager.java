package com.asmtunis.procaissemobility.data.network.datamanager;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.LigneBonCommande;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.LigneBonCommandeService;

import java.util.List;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

public class LigneBonCommandeDataManager {
    private static LigneBonCommandeDataManager sInstance;
    private final LigneBonCommandeService ligneBonCommandeService;

    public LigneBonCommandeDataManager() {

        ligneBonCommandeService = new ServiceFactory<>(LigneBonCommandeService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"LigneCommande")).makeService();
    }

    public static LigneBonCommandeDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new LigneBonCommandeDataManager();
        }
        return sInstance;
    }

    public void getLigneBonCommande(GenericObject genericObject,
                                    RemoteCallback<List<LigneBonCommande>> listener) {
        ligneBonCommandeService.getLigneBonCommande(genericObject)
                .enqueue(listener);
    }

    public void addBatchLigneBonCommande(GenericObject commandes,
                                    RemoteCallback<List<LigneBonCommande>> listener) {
        ligneBonCommandeService.addBatchLigneCommande(commandes)
                .enqueue(listener);
    }
}

