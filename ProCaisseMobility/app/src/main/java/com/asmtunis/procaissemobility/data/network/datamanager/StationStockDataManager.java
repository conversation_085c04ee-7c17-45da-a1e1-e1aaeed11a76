package com.asmtunis.procaissemobility.data.network.datamanager;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.StationStock;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.StationStockService;

import java.util.List;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

public class StationStockDataManager {
    private static StationStockDataManager sInstance;
    private final StationStockService mStationStockService;

    public StationStockDataManager() {

        mStationStockService = new ServiceFactory<>(StationStockService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Station")).makeService();
    }

    public static StationStockDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new StationStockDataManager();
        }
        return sInstance;
    }



    public void getStockByStation(GenericObject genericObject,
                                  RemoteCallback<List<StationStock>> listener) {
        mStationStockService.getStockByStation(genericObject)
                .enqueue(listener);
    }
    public void getstockArticle(GenericObject genericObject,
                                  RemoteCallback<List<StationStock>> listener) {
        mStationStockService.getstockArticle(genericObject)
                .enqueue(listener);
    }

}
