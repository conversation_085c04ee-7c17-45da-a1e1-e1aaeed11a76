package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.Article;
import com.asmtunis.procaissemobility.data.models.StationStock;

import java.util.List;

@Dao
public interface StationStockDAO {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<StationStock> items);
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertOne(StationStock item);
    @Query("SELECT * FROM stationstock WHERE SART_CodeArt =:code and SART_CodeSatation =:station ")
    StationStock getOneByCode(String code,String station);

    @Query("SELECT * FROM stationstock WHERE SART_CodeSatation =:station and SART_Qte > 0")
    List<StationStock> getByCodeStation(String station);




    @Query("SELECT * FROM stationstock WHERE SART_CodeSatation =:station ")
    LiveData<List<StationStock>> getByCodeStationMutable(String station);

    @Query("SELECT * FROM stationstock WHERE SART_CodeSatation =:station and CAST(SART_Qte AS FLOAT) > 0  ")
    LiveData<List<StationStock>> getByCodeStationNonNullQuantityMutable(String station);

    @Query("SELECT * FROM stationstock WHERE SART_CodeSatation =:station and CAST(SART_Qte AS FLOAT) > 0 ")
    List<StationStock> getByCodeStationNonNullQuantity(String station);

    @Query("SELECT * FROM stationstock")
    List<StationStock> getAll();
    @Query("DELETE FROM stationstock ")
    void deleteAll();
}
