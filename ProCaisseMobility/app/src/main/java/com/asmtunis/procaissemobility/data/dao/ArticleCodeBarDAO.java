package com.asmtunis.procaissemobility.data.dao;


import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.ArticleCodeBar;

import java.util.List;

@Dao
public interface ArticleCodeBarDAO {
    @Query("SELECT * FROM ArticleCodeBar")
    List<ArticleCodeBar> getAll();

    @Query("SELECT * FROM ArticleCodeBar WHERE isSync=0 and  (Status=:status) ")
    List<ArticleCodeBar> getByStatus(String status);

    @Query("SELECT * FROM ArticleCodeBar WHERE (Status=:status) ")
    List<ArticleCodeBar> getByStatusForced(String status);

    @Query("SELECT * FROM ArticleCodeBar where Parent_CodeBar = :code and cod_Qte > 0")
    ArticleCodeBar getParent(String code);

   // @Query("SELECT * FROM ArticleCodeBar where Fils_CodeBar = :code and cod_Qte > 0")
   // @Query("SELECT * FROM ArticleCodeBar where Fils_CodeBar = :code")
    @Query("SELECT * FROM ArticleCodeBar where Fils_CodeBar LIKE :code")
    ArticleCodeBar getFils(String code);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<ArticleCodeBar> items);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(ArticleCodeBar item);

    @Query("UPDATE ArticleCodeBar SET isSync=1 , Status='SELECTED'")
    void updateStatus();

    @Query("DELETE FROM ArticleCodeBar")
    void deleteAll();
}
