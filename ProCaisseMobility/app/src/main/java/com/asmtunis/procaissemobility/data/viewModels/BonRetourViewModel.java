package com.asmtunis.procaissemobility.data.viewModels;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelProviders;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.dao.BonRetourDAO;
import com.asmtunis.procaissemobility.data.models.BonRetour;

import java.util.List;

public class BonRetourViewModel extends ViewModel {

    public BonRetourDAO dao;
    private static BonRetourViewModel instance;


    public static BonRetourViewModel getInstance(Fragment activity) {
        if (instance == null)
        instance = new ViewModelProvider(activity).get(BonRetourViewModel.class);

        instance.dao = App.database.bonRetourDAO();

        return instance;
    }

    public static BonRetourViewModel getInstance(FragmentActivity activity) {
        if (instance == null)
            instance = new ViewModelProvider(activity).get(BonRetourViewModel.class);
        instance.dao = App.database.bonRetourDAO();
        return instance;
    }

    public LiveData<Integer> getNoSyncCount() {
        return dao.getNoSyncCountMutable();
    }

    public Integer getNoSyncCountNonMutable() {
        return dao.getNoSyncCountNonMutable();
    }


    public LiveData<List<BonRetour>> getBystation(String  station,String caisseId) {
            return dao.getByStationMutable(station);
    }

    public LiveData<Integer> getAllCountBySessionMutable(String station) {
        return dao.getAllCountBySessionMutable(station);
    }
}
