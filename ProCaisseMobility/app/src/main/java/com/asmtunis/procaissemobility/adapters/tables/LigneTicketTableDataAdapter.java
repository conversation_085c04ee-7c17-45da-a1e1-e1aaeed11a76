package com.asmtunis.procaissemobility.adapters.tables;

import android.content.Context;
import android.graphics.Typeface;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.data.models.LigneTicket;
import com.asmtunis.procaissemobility.helper.Globals;
import com.asmtunis.procaissemobility.helper.utils.Calculator;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.ui.components.SortableLigneTicketTableView;

import java.text.NumberFormat;
import java.util.List;

import de.codecrafters.tableview.toolkit.LongPressAwareTableDataAdapter;

/**
 * ²
 */

public class LigneTicketTableDataAdapter extends LongPressAwareTableDataAdapter<LigneTicket> {

    private static final int TEXT_SIZE = 14;
    private static final NumberFormat PRICE_FORMATTER = NumberFormat.getNumberInstance();
    private PrefUtils prefUtils;

    public LigneTicketTableDataAdapter(final Context context, final List<LigneTicket> data, final SortableLigneTicketTableView tableView) {
        super(context, data, tableView);
        prefUtils = new PrefUtils(context);
    }

    @Override
    public View getDefaultCellView(int rowIndex, int columnIndex, ViewGroup parentView) {
        final LigneTicket ligneTicket = getRowData(rowIndex);
        switch (columnIndex) {
            case 0:
                return renderQuantity(ligneTicket);
            case 1:
                return renderLigneTicketName(ligneTicket);
            case 2:
                return renderUnitPrice(ligneTicket);
            case 3:
                return renderDiscount(ligneTicket);
            case 4:
                return renderPriceWithDiscount(ligneTicket);
        }
        return new View(getContext());
    }

    @Override
    public View getLongPressCellView(int rowIndex, int columnIndex, ViewGroup parentView) {
        return getDefaultCellView(rowIndex, columnIndex, parentView);
    }

    private View renderLigneTicketName(final LigneTicket ligneTicket) {
        final TextView textView = new TextView(getContext());

        if(ligneTicket.getArticle()!=null){
            final String ligneTicketNameString = StringUtils.isEmptyString(ligneTicket.getArticle().getaRTDesignation()) ? ligneTicket.getArticle().getmARDesignation() : ligneTicket.getArticle().getaRTDesignation();
            textView.setText(ligneTicketNameString);
            textView.setPadding(20, 10, 20, 10);
            textView.setTextSize(TEXT_SIZE);
            textView.setHeight(160);
            textView.setGravity(Gravity.CENTER_VERTICAL);
        }


        return textView;
    }

    private View renderQuantity(final LigneTicket ligneTicket) {
        final TextView textView = new TextView(getContext());
        final String quantityString = String.valueOf(StringUtils.decimalFormat(ligneTicket.getlTQte()));
        textView.setText(quantityString);
        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        textView.setHeight(140);
        textView.setGravity(Gravity.CENTER_VERTICAL);
        return textView;
    }

    private View renderUnitPrice(final LigneTicket ligneTicket) {
        final TextView textView = new TextView(getContext());
        if(ligneTicket.getArticle()!=null){
          //  final String priceString = StringUtils.priceFormat(ligneTicket.getArticle().getPvttc());
            final String priceString = StringUtils.priceFormat(ligneTicket.getlTPrixVente());



            textView.setText(priceString);
            textView.setPadding(20, 10, 20, 10);
            textView.setTextSize(TEXT_SIZE);
            textView.setHeight(140);
            textView.setGravity(Gravity.CENTER_VERTICAL);
            if (ligneTicket.getlTMtTTC() > 50000) {
                textView.setTextColor(ContextCompat.getColor(getContext(), R.color.material_green700));
            } else if (ligneTicket.getlTMtTTC() > 100000) {
                textView.setTextColor(ContextCompat.getColor(getContext(), R.color.material_red600));
            }

        }

        return textView;
    }


    private View renderPriceWithDiscount(final LigneTicket ligneTicket) {
        final TextView textView = new TextView(getContext());
        if(ligneTicket.getArticle()!=null){
            //ligneTicket.setlTMtTTC(ligneTicket.getlTQte() * Calculator.calculateAmountTTCNet(ligneTicket.getArticle().getPvttc(), ligneTicket.getlTTauxRemise()));
          //  ligneTicket.setlTMtTTC(Calculator.calculateAmountTTCNet(ligneTicket.getlTMtTTC(), ligneTicket.getlTTauxRemise()));
            ligneTicket.setlTMtTTC(ligneTicket.getlTMtTTC());


            final String priceString = StringUtils.priceFormat(ligneTicket.getlTMtTTC());
            textView.setText(priceString);
            textView.setPadding(20, 10, 20, 10);
            textView.setTextSize(TEXT_SIZE);
            textView.setTypeface(textView.getTypeface(), Typeface.BOLD);
            textView.setHeight(140);
            textView.setGravity(Gravity.CENTER_VERTICAL);
            if (ligneTicket.getlTTauxRemise() > 0) {
                textView.setTextColor(ContextCompat.getColor(getContext(), R.color.md_blue_grey_800));

            }
        }

        return textView;
    }


    private View renderDiscount(final LigneTicket ligneTicket) {
        final TextView textView = new TextView(getContext());
        final String priceString = PRICE_FORMATTER.format(ligneTicket.getlTTauxRemise()) + "% ";
        textView.setText(priceString);
        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        textView.setHeight(140);
        textView.setGravity(Gravity.CENTER_VERTICAL);

        if (ligneTicket.getlTMtTTC() >= 0) {
            if (prefUtils.getDiscountAuthorization().equalsIgnoreCase(Globals.WDND)) {
                double rem = StringUtils.parseDouble(ligneTicket.getArticle().getArtMaxTRemise(), 100);
                double tr = ligneTicket.getlTTauxRemise();
                System.out.println(rem + " " + tr);
                if (tr <= rem) {
                    textView.setTextColor(ContextCompat.getColor(getContext(), R.color.material_green600));
                } else if (tr > 100) {
                    ligneTicket.authorizedDiscount = false;
                    textView.setTextColor(ContextCompat.getColor(getContext(), R.color.material_red600));
                } else if (tr > rem) {
                    textView.setText(priceString + "(" + PRICE_FORMATTER.format(StringUtils.parseDouble(ligneTicket.getArticle().getArtMaxTRemise(), 100)) + "%)");
                    textView.setTextColor(ContextCompat.getColor(getContext(), R.color.Colororange));
                    ligneTicket.setlTTauxRemise(Double.parseDouble(ligneTicket.getArticle().getArtMaxTRemise()));
                    ligneTicket.authorizedDiscount = true;
                }
            } else if (prefUtils.getDiscountAuthorization().equalsIgnoreCase(Globals.WDWD)) {
                textView.setTextColor(ContextCompat.getColor(getContext(), R.color.material_green600));
                if (ligneTicket.getlTTauxRemise() > 100) {
                    ligneTicket.authorizedDiscount = false;
                    textView.setTextColor(ContextCompat.getColor(getContext(), R.color.material_red600));
                }
            } else if (StringUtils.isNumeric(prefUtils.getDiscountAuthorization())) {
                textView.setTextColor(ContextCompat.getColor(getContext(), R.color.material_green600));
                if (ligneTicket.getlTTauxRemise() > Double.parseDouble(prefUtils.getDiscountAuthorization())) {
                    ligneTicket.authorizedDiscount = false;
                    textView.setTextColor(ContextCompat.getColor(getContext(), R.color.material_red600));
                }
            }
        }
        return textView;
    }


    private View renderString(final String value) {
        final TextView textView = new TextView(getContext());
        textView.setText(value);
        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        textView.setHeight(140);
        textView.setGravity(Gravity.CENTER_VERTICAL);
        return textView;
    }

    private static class LigneTicketNameUpdater implements TextWatcher {

        private LigneTicket ligneTicketToUpdate;

        public LigneTicketNameUpdater(LigneTicket ligneTicketToUpdate) {
            this.ligneTicketToUpdate = ligneTicketToUpdate;
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            // no used
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            // not used
        }

        @Override
        public void afterTextChanged(Editable s) {
            //    ligneTicketToUpdate.setName(s.toString());
        }
    }


}
