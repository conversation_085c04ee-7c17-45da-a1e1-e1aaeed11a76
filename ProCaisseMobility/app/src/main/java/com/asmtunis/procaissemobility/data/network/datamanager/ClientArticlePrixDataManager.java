package com.asmtunis.procaissemobility.data.network.datamanager;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.data.models.ClientArticlePrix;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.ClientArticlePrixService;
import com.asmtunis.procaissemobility.data.network.services.ClientService;

import java.util.List;

/**
 * Created by Oussama AZIZI on 3/9/22.
 */

public class ClientArticlePrixDataManager {
    private static ClientArticlePrixDataManager sInstance;

    private final ClientArticlePrixService mClientArticlePrixService;

    public ClientArticlePrixDataManager() {
        mClientArticlePrixService = new ServiceFactory<>(ClientArticlePrixService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Article")).makeService();
    }

    public static ClientArticlePrixDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new ClientArticlePrixDataManager();
        }
        return sInstance;
    }

    public void getClientArticlePrix(GenericObject genericObject,
                           RemoteCallback<List<ClientArticlePrix>> listener) {
        String ddm = App.database.clientDAO().getDDM();
        mClientArticlePrixService.getClientArticlePrix(genericObject)
                .enqueue(listener);
    }

}
