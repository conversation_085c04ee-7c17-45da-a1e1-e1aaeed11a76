package com.asmtunis.procaissemobility.data.network.base;

import android.util.Log;

import com.asmtunis.procaissemobility.BuildConfig;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.converters.BooleanSerializer;
import com.asmtunis.procaissemobility.data.converters.Exclusion;
import com.blankj.utilcode.util.AppUtils;
import com.google.gson.GsonBuilder;

import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

/**
 * Created by Achraf on 25/09/2017.
 */

public class ServiceFactory<T> {

    private static final int HTTP_READ_TIMEOUT = 6000;
    private static final int HTTP_CONNECT_TIMEOUT = 6000;
    private final Class<T> clazz;
    private final String BASE_URL;


    public ServiceFactory(Class<T> clazz, String BASE_URL) {
        this.clazz = clazz;
        this.BASE_URL = BASE_URL;
    }

    private static OkHttpClient makeOkHttpClient() {
        OkHttpClient.Builder httpClientBuilder = new OkHttpClient().newBuilder();
        httpClientBuilder.connectTimeout(HTTP_CONNECT_TIMEOUT, TimeUnit.SECONDS);
        httpClientBuilder.readTimeout(HTTP_READ_TIMEOUT, TimeUnit.SECONDS);
        httpClientBuilder.addInterceptor(chain -> {
            Request original = chain.request();

            Request.Builder builder = original.newBuilder();
            Request.Builder headers = builder
                    .removeHeader("Authorization").removeHeader("Key").removeHeader("Baseconfig");
            headers.addHeader("Language", "fr");
            headers.addHeader("Application-name", AppUtils.getAppName());
           // headers.addHeader("Application-name", "ProCaisseMobility");
            headers.addHeader("Version-code", String.valueOf(AppUtils.getAppVersionCode()));
            headers.addHeader("Version-name", AppUtils.getAppVersionName());

            if (App.prefUtils.isConnected()) {
                if(App.prefUtils.getUserAccount()!=null)
                builder.addHeader("user", App.prefUtils.getUserAccount().getCodeUt());
            }

            Request request = headers.build();
            return chain.proceed(request);
        });

        if (BuildConfig.DEBUG) {
            httpClientBuilder.addInterceptor(makeLoggingInterceptor());
        }
        // if (BuildConfig.DEBUG) {
        //httpClientBuilder.addInterceptor(makeLoggingInterceptor());
        //}

/*
        httpClientBuilder.addInterceptor(makeLoggingInterceptor());
        */
        return httpClientBuilder.build();
    }


    private static HttpLoggingInterceptor makeLoggingInterceptor() {
        HttpLoggingInterceptor logging = new HttpLoggingInterceptor();
        logging.setLevel(BuildConfig.DEBUG ? HttpLoggingInterceptor.Level.BODY
                : HttpLoggingInterceptor.Level.NONE);
        return logging;
    }

    public T makeService() {
        return makeService(makeOkHttpClient());
    }

    private T makeService(OkHttpClient okHttpClient) {
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(BASE_URL)
                .client(okHttpClient)
                .addConverterFactory(new NullOnEmptyConverterFactory())
                .addConverterFactory(GsonConverterFactory.create(new GsonBuilder().registerTypeAdapter(boolean.class, new BooleanSerializer())
                        .registerTypeAdapter(Boolean.class, new BooleanSerializer()).setExclusionStrategies(new Exclusion()).setLenient().create()))

                .build();

        return retrofit.create(clazz);
    }




}
