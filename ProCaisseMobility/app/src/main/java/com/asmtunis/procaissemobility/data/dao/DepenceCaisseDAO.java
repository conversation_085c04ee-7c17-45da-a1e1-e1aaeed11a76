package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.DepenceCaisse;
import com.asmtunis.procaissemobility.data.models.Ticket;

import java.util.List;

/**
 * Created by WAEL on 29/08/2022.
 */
@Dao
public interface DepenceCaisseDAO {

    @Query("SELECT * FROM DepenceCaisse")
    LiveData<List<DepenceCaisse>> getAllMutable();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(DepenceCaisse item);

    @Query("SELECT * FROM DepenceCaisse where DEP_SC_IdSCession = :session order by strftime('%Y-%m-%d %H-%M',DEP_DDm) desc")
    LiveData<List<DepenceCaisse>> getAllDepenceCaisseBySessionMutable(String session);

    @Query("SELECT count(*) FROM DepenceCaisse where DEP_SC_IdSCession = :session")
    Integer getAllCountBySession(String session);

    @Query("SELECT count(*) FROM DepenceCaisse where DEP_SC_IdSCession = :session")
    LiveData<Integer> getAllCountBySessionMutable(String session);

    @Query("SELECT * FROM DepenceCaisse WHERE  isSync=0 and (Status='INSERTED'  or Status='UPDATED') order by strftime('%Y-%m-%d %H-%M',DEP_DDm) desc")
    List<DepenceCaisse> getNonSync();

    @Query("UPDATE DepenceCaisse SET IsSync=1, Status='SELECTED' where DEP_Code =:depCode")
    void updateDepenseCaisseStatus(String depCode);

    @Query("UPDATE DepenceCaisse SET DEP_Montant=:amount, DEP_DDm=:date, DDm=:date, DEP_descrip=:description where DEP_Code_M =:depCodeM")
    void updateDepenseCaisse(String amount, String date, String description, String depCodeM);


    @Query("UPDATE DepenceCaisse SET DEP_Code = :code where DEP_Code = :oldCode")
    void updateCodeM(String code, String oldCode);
    /*
    @Query("SELECT * FROM DEPENCE WHERE DEPENCE = :devise ")
    Devise getOneByDevise(String devise);

    @Query("SELECT * FROM Devise WHERE Activite = '1' ")
    Devise getActiveOne();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(Devise item);
    */

    @Query("DELETE FROM DepenceCaisse where DEP_Code_M= :depCodeM")
    void deleteById(String depCodeM);

    @Query("SELECT count(*) FROM DepenceCaisse where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    int getNoSyncCount();

    @Query("SELECT count(*) FROM DepenceCaisse where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    LiveData<Integer> getNoSyncCountMutable();

    @Query("DELETE FROM DepenceCaisse")
    void deleteAll();

    @Query("DELETE FROM DepenceCaisse where DEP_Code =:depCode")
    void deleteByCode(String depCode);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<DepenceCaisse> items);

    @Query("SELECT strftime('%Y-%m-%d', DDm) FROM DepenceCaisse order by strftime('%Y-%m-%d %H-%M',DDm) desc limit 1")
    String getDDM();

}