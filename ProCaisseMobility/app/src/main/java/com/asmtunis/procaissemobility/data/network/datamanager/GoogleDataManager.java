package com.asmtunis.procaissemobility.data.network.datamanager;

import com.asmtunis.procaissemobility.data.models.google.DistanceResponse;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.GoogleService;

import java.util.Map;

public class GoogleDataManager {
    private static GoogleDataManager sInstance;
    private final GoogleService googleService;

    public GoogleDataManager() {
        googleService = new ServiceFactory<>(GoogleService.class,"https://maps.googleapis.com").makeService();
    }

    public static GoogleDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new GoogleDataManager();
        }
        return sInstance;
    }

    public synchronized void getDistance(Map<String, String> parameters, RemoteCallback<DistanceResponse> listener) {
        googleService.getDistanceInfo(parameters)
                .enqueue(listener);
    }
}
