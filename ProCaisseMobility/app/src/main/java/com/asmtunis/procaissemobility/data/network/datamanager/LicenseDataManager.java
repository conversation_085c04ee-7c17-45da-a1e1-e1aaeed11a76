package com.asmtunis.procaissemobility.data.network.datamanager;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.BonCommandResponse;
import com.asmtunis.procaissemobility.data.models.BonCommande;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.InvPatBatchResponse;
import com.asmtunis.procaissemobility.data.models.LicenseResponse;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.LicenseService;
import com.asmtunis.procaissemobility.helper.utils.Utils;

import java.util.List;






public class LicenseDataManager {
    private static LicenseDataManager sInstance;
    private final LicenseService licenseService;

    public LicenseDataManager() {

        licenseService = new ServiceFactory<>(LicenseService.class, String.format(Utils.validateBaseUrl(), App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"License")).makeService();
    }

    public static LicenseDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new LicenseDataManager();
        }
        return sInstance;
    }

    public void getLicenseUrl(GenericObject genericObject,
                               RemoteCallback<LicenseResponse> listener) {
        licenseService.getLicensesUrl(genericObject)
                .enqueue(listener);
    }



}
