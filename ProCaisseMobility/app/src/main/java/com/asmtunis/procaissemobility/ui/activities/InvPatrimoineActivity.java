package com.asmtunis.procaissemobility.ui.activities;

import static android.view.View.GONE;
import static com.asmtunis.procaissemobility.App.prefUtils;
import static com.asmtunis.procaissemobility.helper.Globals.CLIENT_INTENT_ID_KEY;
import static com.asmtunis.procaissemobility.helper.utils.UIUtils.getIconicsDrawable;
import static com.asmtunis.procaissemobility.helper.utils.UIUtils.setDefaultAutoScanColor;
import static com.asmtunis.procaissemobility.helper.utils.Utils.indexOfClient;
import static com.asmtunis.procaissemobility.ui.activities.CommandeActivity.REQUEST_TICKET_CODE;
import static com.honeywell.aidc.BarcodeReader.PROPERTY_CODE_128_ENABLED;
import static com.honeywell.aidc.BarcodeReader.PROPERTY_EAN_13_CHECK_DIGIT_TRANSMIT_ENABLED;
import static com.honeywell.aidc.BarcodeReader.PROPERTY_EAN_13_ENABLED;

import android.app.Activity;
import android.app.ProgressDialog;
import android.app.SearchManager;
import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.Gravity;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.RequiresApi;
import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.appcompat.widget.SearchView;
import androidx.appcompat.widget.Toolbar;
import androidx.core.content.res.ResourcesCompat;
import androidx.fragment.app.FragmentActivity;

import com.afollestad.materialdialogs.MaterialDialog;
import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.adapters.ImmobilisationSpinnerAdapter;
import com.asmtunis.procaissemobility.adapters.spinners.ClientSpinnerAdapter;
import com.asmtunis.procaissemobility.adapters.tables.LigneInvTicketTableDataAdapter;
import com.asmtunis.procaissemobility.data.models.Article;
import com.asmtunis.procaissemobility.data.models.BonCommandResponse;
import com.asmtunis.procaissemobility.data.models.BonCommande;
import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.data.models.ControleInventaire;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Immobilisation;
import com.asmtunis.procaissemobility.data.models.LigneBonCommande;
import com.asmtunis.procaissemobility.data.models.LigneTicket;
import com.asmtunis.procaissemobility.data.models.Marque;
import com.asmtunis.procaissemobility.data.models.Ticket;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.datamanager.BonCommandeDataManager;
import com.asmtunis.procaissemobility.data.viewModels.ArticleViewModel;
import com.asmtunis.procaissemobility.data.viewModels.BonComandeViewModel;
import com.asmtunis.procaissemobility.data.viewModels.ClientViewModel;
import com.asmtunis.procaissemobility.helper.BluetoothService;
import com.asmtunis.procaissemobility.helper.GPSTracker;
import com.asmtunis.procaissemobility.helper.Globals;
import com.asmtunis.procaissemobility.helper.utils.DateUtils;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.helper.utils.UIUtils;
import com.asmtunis.procaissemobility.helper.utils.Utils;
import com.asmtunis.procaissemobility.listener.DialogClickInterface;
import com.asmtunis.procaissemobility.ui.components.SortableLigneInventaireTableView;
import com.asmtunis.procaissemobility.ui.components.SortableLigneTicketTableView;
import com.asmtunis.procaissemobility.ui.dialogs.ArticleDialog;
import com.asmtunis.procaissemobility.ui.dialogs.ArticleListDialog;
import com.asmtunis.procaissemobility.ui.dialogs.LigneTicketDialog;
import com.asmtunis.procaissemobility.ui.dialogs.NumSerieInputDialog;
import com.blankj.utilcode.util.ObjectUtils;
import com.example.barecodereader.barcode.BarCodeReaderManager;
import com.example.barecodereader.barcode.listener.BarcodeListener;
import com.example.barecodereader.barcode.readers.EdaReader;
import com.example.barecodereader.barcode.readers.PM80Reader;
import com.google.android.material.snackbar.Snackbar;
import com.mikepenz.fontawesome_typeface_library.FontAwesome;
import com.mikepenz.google_material_typeface_library.GoogleMaterial;
import com.mikepenz.iconics.IconicsDrawable;
import com.mikepenz.iconics.view.IconicsButton;
import com.mobsandgeeks.saripaar.ValidationError;
import com.mobsandgeeks.saripaar.Validator;
import com.rengwuxian.materialedittext.MaterialEditText;
import com.transitionseverywhere.Slide;
import com.transitionseverywhere.TransitionManager;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;
import es.dmoral.toasty.Toasty;
import searchablespinner.interfaces.OnItemSelectedListener;

public class InvPatrimoineActivity extends BaseActivity implements DialogClickInterface {
    SearchView searchView;
    private static final int REQUEST_CLIENT_CODE = 101;
    public static final int REQUEST_CONNECT_DEVICE = 845;
    public static final int REQUEST_ENABLE_BT = 875;
    public static BluetoothDevice con_dev = null;
    public BluetoothService mService = null;
    String bonCmdNumber;
    int index_prod_intable = 0;

    int nbrProduct = 0;
    ArticleDialog articleDialog;
    NumSerieInputDialog numSerieInputDialog;




    ImmobilisationSpinnerAdapter societeSpinnerAdapter;
    searchablespinner.SearchableSpinner searchableSocieteSpinner;


    ImmobilisationSpinnerAdapter siteSpinnerAdapter;
    searchablespinner.SearchableSpinner searchableSiteSpinner;


    ImmobilisationSpinnerAdapter blocSpinnerAdapter;
    searchablespinner.SearchableSpinner searchableBlocSpinner;


    ImmobilisationSpinnerAdapter batimementAdapter;
    searchablespinner.SearchableSpinner searchableBatimentSpinner;


    FrameLayout sites;
    FrameLayout societes;
    FrameLayout blocs;
    FrameLayout batiments;


    TextView site;
    TextView societe;
    TextView bloc;
    TextView batiment;

    public Immobilisation selectedSociete;
    public Immobilisation selectedSite;
    public Immobilisation selectedBloc;
    public Immobilisation selectedBatiment;

    List<Article> artiicles;
    static Validator.ValidationListener validationListener;

    Ticket ticket;
    BonCommande bonCommande;
    List<LigneBonCommande> ligneBonCommandes;
    List<LigneBonCommande> listLgBcProduitAffecteAuClt = new ArrayList<>();
    List<LigneBonCommande> listLgBcProduitScanneAffecteAuClt = new ArrayList<>();
    static EditText amountInputField;
    static TextView title0;


    static EditText amountwithDiscountInputField;
    TextView title2;

    static EditText discountInputField;

    IconicsButton scanBut;
    @BindView(R.id.addItemButton)
    com.mikepenz.iconics.view.IconicsButton addItemButton;

    SortableLigneInventaireTableView tableView;
    SortableLigneTicketTableView tabView;
    ScrollView scrollView;

  @BindView(R.id.SearchableClientSpinner)
    searchablespinner.SearchableSpinner searchableSpinner;
    /*  @BindView(R.id.DateInputField)
    TextView dateInputField;*/

    @BindView(R.id.buttons_layout)
    LinearLayoutCompat linearLayoutCompat;

    @BindView(R.id.autoscan)
    com.mikepenz.iconics.view.IconicsButton autoScanButton;

    @BindView(R.id.produitTxtVw)
    TextView  produitTxtVw;

    @BindView(R.id.scan)
    com.mikepenz.iconics.view.IconicsButton scanArt;

    static View footerTicketView;

    static
    View dataTableView;

    @BindView(R.id.headerTicketView)
     View headerTicketView;




    @BindView(R.id.dividerView1)
    View dividerView1;
    @BindView(R.id.dividerView2)
    View dividerView2;

    @BindView(R.id.discountLayout)
    RelativeLayout discountLayout;

    @BindView(R.id.AmountwithDiscountLayout)
    RelativeLayout AmountwithDiscountLayout;

    @BindView(R.id.AmountLayout)
    RelativeLayout AmountLayout;


    @BindView(R.id.addClientButton)
    com.mikepenz.iconics.view.IconicsButton addClientButton;

    MenuItem save, scan;

    static ViewGroup content;

    ClientSpinnerAdapter simpleListAdapter;

    TextView immoTxVw;
    Client client = null;

    static boolean dialogShown = false;
    static LigneTicketDialog ligneTicketDialog;

    Bundle savedInstanceState;
    static ArticleListDialog articleListDialog;
    static LigneInvTicketTableDataAdapter tableDataAdapter = null;

    ArrayList<LigneTicket> selectedArticles;
    static double amount;
    static double amountWithDiscount;
    Date date;

    @BindView(R.id.toolbar)
    Toolbar toolbar;
    static FragmentActivity context;
    Intent intent;
    Unbinder unbinder;
    GPSTracker gPSTracker;
    double entreLongitude, entreAltitude;

    BarCodeReaderManager barCodeReaderManager;
    ProgressDialog mprogress;

    private Boolean isUpdate = false;

    private String patFrom = "";
    String devInfo3="";

    String ddm = "";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        unbinder = ButterKnife.bind(this);
        initComponents();
        autoScanButton.setDrawableBottom(getIconicsDrawable(GoogleMaterial.Icon.gmd_wb_auto, R.color.material_drawer_accent, this));
        setDefaultAutoScanColor(autoScanButton, this);
        autoScanButton.setVisibility(GONE);
        scanArt.setDrawableBottom(getIconicsDrawable(GoogleMaterial.Icon.gmd_flip, R.color.material_drawer_accent, this));
        mprogress = new ProgressDialog(this);
        isUpdate = getIntent().getBooleanExtra(Globals.UPDATE_TICKET, false);




        patFrom = getIntent().getStringExtra(Globals.PAT_FROM);

        if (patFrom.equals(getString(R.string.affectation_title)))
            devInfo3 = Globals.TYPE_PATRIMOINE.AFFECTATION.getTypePat();
        if (patFrom.equals(getString(R.string.patrimoineInventaire_title)))
            devInfo3 = Globals.TYPE_PATRIMOINE.INVENTAIRE.getTypePat();
        if (patFrom.equals(getString(R.string.patrimoine_deplacement_out_title)))
            devInfo3 = Globals.TYPE_PATRIMOINE.SORTIE.getTypePat();
        if (patFrom.equals(getString(R.string.patrimoine_deplacement_in_title)))
            devInfo3 = Globals.TYPE_PATRIMOINE.ENTREE.getTypePat();

        bonCommande = (BonCommande) getIntent().getSerializableExtra(Globals.TICKET_TO_UPDATE);
        if (isUpdate) {
            ddm = bonCommande.getDEVDDm();


        }

        this.savedInstanceState = savedInstanceState;
        setSupportActionBar(toolbar);
        Objects.requireNonNull(getSupportActionBar()).setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setDisplayShowCustomEnabled(true);
        getSupportActionBar().setDisplayUseLogoEnabled(false);
        context = this;
        tableDataAdapter = null;
        gPSTracker = new GPSTracker(context);
        entreLongitude = gPSTracker.getLongitude();
        entreAltitude = gPSTracker.getLatitude();
        discountLayout.setVisibility(View.INVISIBLE);
        dividerView1.setVisibility(GONE);
        dividerView2.setVisibility(GONE);

        AmountwithDiscountLayout.setVisibility(View.VISIBLE);
        AmountwithDiscountLayout.setVisibility(View.VISIBLE);
        AmountLayout.setVisibility(View.VISIBLE);


        if ((gPSTracker.getLatitude() > 0) && (gPSTracker.getLongitude() > 0)) {
            getSupportActionBar().setSubtitle(String.format(context.getResources().getString(R.string.lat_log), gPSTracker.getLatitude() + "", gPSTracker.getLongitude() + ""));
        }



        getData();

        validationListener = new Validator.ValidationListener() {
            @Override
            public void onValidationSucceeded() {
            }

            @Override
            public void onValidationFailed(List<ValidationError> errors) {
                for (ValidationError error : errors) {
                    View view = error.getView();
                    String message = error.getCollatedErrorMessage(context);

                    if (view instanceof EditText) {
                        ((MaterialEditText) view).setError(message);
                    } else {
                        Toast.makeText(context, message, Toast.LENGTH_LONG).show();
                    }
                }
            }
        };
        addClientButton.setVisibility(GONE);

        if (isUpdate) {
            bonCmdNumber = bonCommande.getDEVNum();
            client = App.database.clientDAO().getOneByCode(bonCommande.getDEVCodeClient());
        } else {
            bonCmdNumber = String.valueOf(App.database.bonCommandeDAO().getCount() + 1);
            client = App.database.clientDAO().getOneByCode(getIntent().getStringExtra(Globals.CLT_CODE));
        }
        getSupportActionBar().setTitle(String.format(getString(R.string.invpat_number_field), String.format("%s", bonCmdNumber)));




        startBarcode();

        immoTxVw.setOnClickListener(view -> {

            if(societe.getVisibility()== View.VISIBLE) {
                hideBatiment();
                hideBlocs();
                hideSite();
                hideSociete();


                String code;

                if(selectedBatiment!=null) code = selectedBatiment.cLICode;
                else if(selectedBloc!=null) code = selectedBloc.cLICode;
                else if(selectedSite!=null) code = selectedSite.cLICode;
                else if(selectedSociete!=null) code = selectedSociete.cLICode;
                else code = "";

                if(!code.isEmpty())
                showTable(true, code);
                else showSociete();
            }
            else {
                TransitionManager.beginDelayedTransition(content, new Slide(Gravity.TOP));
                dataTableView.setVisibility(GONE);

                if(selectedSociete != null){
                    showSociete();



                //    setSocieteSearchSpinner(App.database.clientImooDAO().getAllSociete());

                    List<Immobilisation> listSites = App.database.clientImooDAO().getAllSite(selectedSociete.cLICode);
                    if(!listSites.isEmpty()){
                        showSite();
                        setSiteSearchSpinner(listSites);
                    }

                }
                if(selectedSite != null) {
                    showBlocs();
                    setBlocSearchSpinner();

                }
                if(selectedBloc != null) {
                    showBatiment();
                    setBatimementSearchSpinner();
                }
               // if(selectedBatiment != null)


            }
        });

    }

    void startBarcode() {
        barCodeReaderManager = new BarCodeReaderManager();
        Map<String, Object> properties = new HashMap();
        properties.put(PROPERTY_CODE_128_ENABLED, true);
        properties.put(PROPERTY_EAN_13_ENABLED, true);
        properties.put(PROPERTY_EAN_13_CHECK_DIGIT_TRANSMIT_ENABLED, prefUtils.getEan13CheckDigitEnabled());
        EdaReader edaReader = new EdaReader(this, new BarcodeListener() {
            @Override
            public void onSuccess(String event) {
                Log.d("loplknv","k " +event);
                if (patFrom.equals(getString(R.string.patrimoine_deplacement_out_title)) || patFrom.equals(getString(R.string.patrimoine_deplacement_in_title)))
                    verifyPatOnline(event,"scan");
                else if(patFrom.equals(getString(R.string.patrimoineInventaire_title))) {
                    checkPat(event,"scan");




                }
                else Toasty.error(context, "Il faut choisir un article avant de scanner code bare").show();
            }

            @Override
            public void onFail(String event) {
                Toasty.info(context, R.string.error_read_codebare).show();
            }
        }, properties);
        barCodeReaderManager.addReader(edaReader).addReader(new PM80Reader(new BarcodeListener() {
            @Override
            public void onSuccess(String event) {
                Log.d("loplknv","k " +event);
                if (patFrom.equals(getString(R.string.patrimoine_deplacement_out_title)) || patFrom.equals(getString(R.string.patrimoine_deplacement_in_title)))
                    verifyPatOnline(event,"scan");
                else if(patFrom.equals(getString(R.string.patrimoineInventaire_title))) {
                    checkPat(event,"scan");
                }
                else Toasty.error(context, "Il faut choisir un article avant de scanner code bare").show();
            }

            @Override
            public void onFail(String event) {
                Toasty.info(context, R.string.error_read_codebare).show();
            }
        }));
        barCodeReaderManager.startListener();


    }
    /**
     * load view from xml
     */
    private void initComponents() {
        amountInputField = findViewById(R.id.AmountInputField);
        title0 = findViewById(R.id.title0);


        amountwithDiscountInputField = findViewById(R.id.AmountwithDiscountInputField);
        title2 = findViewById(R.id.title2);
        discountInputField = findViewById(R.id.DiscountInputField);
        amountInputField.setVisibility(View.INVISIBLE);
        title0.setVisibility(View.INVISIBLE);



        amountwithDiscountInputField.setVisibility(View.VISIBLE);
        discountInputField.setVisibility(GONE);
        dataTableView = findViewById(R.id.dataTableView);
        tableView = findViewById(R.id.tableViewLigneInv);
        tabView = findViewById(R.id.tableView);
        scrollView = findViewById(R.id.scrollView);

        immoTxVw = findViewById(R.id.immoTxVw);

        tableView.setVisibility(View.VISIBLE);
        tabView.setVisibility(GONE);
        footerTicketView = findViewById(R.id.footerTicketView);
        linearLayoutCompat.setVisibility(GONE);
        content = findViewById(android.R.id.content);
        scanBut = findViewById(R.id.scan);
        scanBut.setVisibility(GONE);


        searchableSocieteSpinner = findViewById(R.id.searchableSocieteSpinner);
        searchableSiteSpinner = findViewById(R.id.searchableSiteSpinner);
        searchableBlocSpinner = findViewById(R.id.searchableBlocSpinner);
        searchableBatimentSpinner = findViewById(R.id.searchablebatimentSpinner);


        sites = findViewById(R.id.sites);
        societes = findViewById(R.id.societes);
        blocs = findViewById(R.id.blocs);
        batiments = findViewById(R.id.batiments);


        site = findViewById(R.id.site);
        societe = findViewById(R.id.societee);
        bloc = findViewById(R.id.bloc);
        batiment = findViewById(R.id.batiment);




        hideSite();

        hideBlocs();

        hideBatiment();
    }

    void hideSociete(){
        societe.setVisibility(View.GONE);
        societes.setVisibility(View.GONE);
        searchableSocieteSpinner.setVisibility(View.GONE);
    }
    void hideSite(){
        sites.setVisibility(View.GONE);
        site.setVisibility(View.GONE);
        searchableSiteSpinner.setVisibility(View.GONE);
    }

    void hideBlocs(){
        blocs.setVisibility(View.GONE);
        bloc.setVisibility(View.GONE);
        searchableBlocSpinner.setVisibility(View.GONE);
    }

    void hideBatiment(){
        batiments.setVisibility(View.GONE);
        batiment.setVisibility(View.GONE);
        searchableBatimentSpinner.setVisibility(View.GONE);
    }


    void showSociete(){
        societe.setVisibility(View.VISIBLE);
        societes.setVisibility(View.VISIBLE);
        searchableSocieteSpinner.setVisibility(View.VISIBLE);
    }
    void showSite(){
        sites.setVisibility(View.VISIBLE);
        site.setVisibility(View.VISIBLE);
        searchableSiteSpinner.setVisibility(View.VISIBLE);
    }

    void showBlocs(){
        blocs.setVisibility(View.VISIBLE);
        bloc.setVisibility(View.VISIBLE);
        searchableBlocSpinner.setVisibility(View.VISIBLE);
    }

    void showBatiment(){
        batiments.setVisibility(View.VISIBLE);
        batiment.setVisibility(View.VISIBLE);
        searchableBatimentSpinner.setVisibility(View.VISIBLE);
    }
    void setSocieteSearchSpinner(List<Immobilisation> listImmo) {


        societeSpinnerAdapter = new ImmobilisationSpinnerAdapter(this, (ArrayList<Immobilisation>) listImmo);
        searchableSocieteSpinner.setAdapter(societeSpinnerAdapter);


        searchableSocieteSpinner.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(View view, int position, long id) {
                if(position>0){
                    selectedSociete = societeSpinnerAdapter.getItem(position);

                    showSite();

                    immoTxVw.setText(selectedSociete.cLINomPren);
                    setSiteSearchSpinner(App.database.clientImooDAO().getAllSite(selectedSociete.cLICode));

                } else {
                    immoTxVw.setText("");

                    hideSite();
                    hideBlocs();
                    hideBatiment();



                    selectedBloc = null;
                    selectedBatiment = null;
                    selectedSite = null;
                    selectedSociete = null;

                    searchableSiteSpinner.setSelectedItem(0);
                    searchableBlocSpinner.setSelectedItem(0);
                    searchableBatimentSpinner.setSelectedItem(0);
                }
            }

            @Override
            public void onNothingSelected() {

            }
        });

    }


    void setSiteSearchSpinner(List<Immobilisation> listSites) {

         if(listSites.isEmpty()) {
             hideSociete();
             hideSite();
             hideBlocs();
             hideBatiment();
             showTable(true, selectedSociete.cLICode);
             return;
         }
        siteSpinnerAdapter = new ImmobilisationSpinnerAdapter(this, (ArrayList<Immobilisation>) listSites);
        searchableSiteSpinner.setAdapter(siteSpinnerAdapter);


        searchableSiteSpinner.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(View view, int position, long id) {
                if(position>0){

                    selectedSite = siteSpinnerAdapter.getItem(position);
                    showBlocs();
                    setBlocSearchSpinner();

                } else {



                    hideBlocs();
                    hideBatiment();


                    selectedBloc = null;
                    selectedBatiment = null;
                    selectedSite = null;

                    searchableBlocSpinner.setSelectedItem(0);
                    searchableBatimentSpinner.setSelectedItem(0);
                }
                immoTxVw.setText(immoText());
            }

            @Override
            public void onNothingSelected() {

            }
        });

    }

    void setBlocSearchSpinner() {
        List<Immobilisation> listBlocs = App.database.clientImooDAO().getAllBloc(selectedSite.cLICode);
        if(listBlocs == null) {
            hideSociete();
            hideSite();
            hideBlocs();
            hideBatiment();
            showTable(true, selectedSite.cLICode);
            return;
        }
        blocSpinnerAdapter = new ImmobilisationSpinnerAdapter(this, (ArrayList<Immobilisation>) listBlocs);
        searchableBlocSpinner.setAdapter(blocSpinnerAdapter);


        searchableBlocSpinner.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(View view, int position, long id) {

                if(position>0){
                    selectedBloc = blocSpinnerAdapter.getItem(position);
                    showBatiment();

                    setBatimementSearchSpinner();

                } else {

                    selectedBloc = null;
                    selectedBatiment = null;


                   hideBatiment();
                }

                immoTxVw.setText(immoText());
            }

            @Override
            public void onNothingSelected() {

            }
        });

    }



    void setBatimementSearchSpinner() {
        batimementAdapter = new ImmobilisationSpinnerAdapter(this, (ArrayList<Immobilisation>) App.database.clientImooDAO().getAllBatiment(selectedBloc.cLICode));
        searchableBatimentSpinner.setAdapter(batimementAdapter);


        searchableBatimentSpinner.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(View view, int position, long id) {
                if(position>0){
                    selectedBatiment = batimementAdapter.getItem(position);
                    showTable(true, selectedBloc.cLICode);

                    hideSociete();
                    hideSite();
                    hideBlocs();
                    hideBatiment();



                }else {
                    selectedBatiment = null;
                }
                immoTxVw.setText(immoText());
            }

            @Override
            public void onNothingSelected() {

            }
        });

    }

    String immoText() {

        String immo = "";

        if(selectedSociete!=null){
            immo = selectedSociete.cLINomPren;
            if(selectedSite!=null) {
                immo = selectedSociete.cLINomPren + " -> " + selectedSite.cLINomPren;

                if(selectedBloc!=null){
                    immo = selectedSociete.cLINomPren + " -> " + selectedSite.cLINomPren+ " -> " + selectedBloc.cLINomPren;
                    if(selectedBatiment!=null){
                        immo = selectedSociete.cLINomPren + " -> " + selectedSite.cLINomPren+ " -> " + selectedBloc.cLINomPren + " -> " +selectedBatiment.cLINomPren;
                    }
                }

            }

        }


        return immo;

    }
    void showTable(Boolean beginAnimation, String code) {
        if (beginAnimation){
            TransitionManager.beginDelayedTransition(content, new Slide(Gravity.BOTTOM));
            dataTableView.setVisibility(View.VISIBLE);
        }


        setArticlesListView(artiicles, code);

        setNbrArtclAffecter(code);
    }

    void setupView(List<Article> articles) {

        artiicles= articles;
        //  setArticlesListView(articles);
        setTicketFooter();
        if(App.database.clientImooDAO().getAllSociete()!=null) {
            Log.d("plmknggv","eeeer");
            headerTicketView.setVisibility(GONE);
            setSocieteSearchSpinner(App.database.clientImooDAO().getAllSociete());
        }else {
            Snackbar snackbar = Snackbar.make(linearLayoutCompat, "La liste des Immobilisation est vide !", Snackbar.LENGTH_INDEFINITE)
                    .setAction("quitter", v -> {

                        finish();

                    });
            snackbar.show();
        }

        /*else{
            Log.d("plmknggv","tttttttttttttt");
            headerTicketView.setVisibility(View.VISIBLE);
            setTicketHeader();
        }*/

    }


    /**
     * get articles list form local database
     */
    private void getData() {
        ArticleViewModel.getInstance(this).getAllPat().observe(this, articles -> {
            if (articles.size() > 0) {
                date = new Date();
                setupView(articles);
            } else {
                Snackbar snackbar = Snackbar.make(linearLayoutCompat, "La liste des patrimoines est vide !", Snackbar.LENGTH_INDEFINITE)
                        .setAction("quitter", v -> {

                            finish();

                        });
                snackbar.show();
            }
        });
    }

    /**
     * send articles list to dialog
     */

    void setArticlesListView(List<Article> articles, String code) {


        BonCommande bonCommand;
        //  String cltCode ="";
        // if(client!=null)
        //  cltCode =client.getcLICode();
        bonCommand = new BonCommande(code,devInfo3);
        //  }
        // else  bonCommand = new BonCommande("",devInfo3);
        articleListDialog = new ArticleListDialog(articles, articles, false, 3, false,
                true, false,bonCommand);


    }


    /**
     * load the spinner with clients list
     */
    void setSearchableSpinner(List<Client> clients) {
        simpleListAdapter = new ClientSpinnerAdapter(this, (ArrayList<Client>) clients);
        searchableSpinner.setAdapter(simpleListAdapter);


        Log.d("fdccv", String.valueOf(isUpdate));
        if(client!=null) {
            searchableSpinner.setSelectedItem(indexOfClient((ArrayList<Client>) clients, client) + 1);

            showTable(true, client.getcLICode());
        }
        if (isUpdate) {


            List<LigneBonCommande> ligneBonCommandes = App.database.ligneBonCommandeDAO().getByBCCode(bonCommande.getDEVNum());
            for (LigneBonCommande ld : ligneBonCommandes) {
                Article article = App.database.articleDAO().getOneByCode(ld.getLGDEVCodeArt());
                article.setPhotoPath(ld.lGDevNumSerie);
                articleListDialog.getList().put(article, Double.parseDouble(ld.getLGDEVQte()), Double.parseDouble(ld.getLGDEVRemise()), false);
            }
            setTicketDataTable(true);
            setFooter();
        }

        searchableSpinner.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(View view, int position, long id) {
                TransitionManager.beginDelayedTransition(content, new Slide(Gravity.BOTTOM));
                dataTableView.setVisibility(position > 0 ? View.VISIBLE : GONE);
                client = simpleListAdapter.getItem(position);

                if(client != null){
                    showTable(false, client.getcLICode());


                }
            }

            @Override
            public void onNothingSelected() {

            }
        });

    }


    void setNbrArtclAffecter(String code){
        produitTxtVw.setText("");
        if(Objects.equals(Globals.TYPE_PATRIMOINE.INVENTAIRE.getTypePat(), devInfo3))
            BonComandeViewModel.getInstance(context).getByCodeClientandPatEtat(code,
                    Globals.TYPE_PATRIMOINE.AFFECTATION.getTypePat()).observe(context, bonCommandes -> {
                nbrProduct = 0;
                if (!bonCommandes.isEmpty()) {
                    for (int i = 0; i < bonCommandes.size(); i++) {

                        if(bonCommandes.get(i).isSync) listLgBcProduitAffecteAuClt = App.database.ligneBonCommandeDAO().getByBCCode(bonCommandes.get(i).getDEVNum());
                        else listLgBcProduitAffecteAuClt = App.database.ligneBonCommandeDAO().getByBCCode(bonCommandes.get(i).getDevCodeM());



                        nbrProduct=nbrProduct + listLgBcProduitAffecteAuClt.size();

                        produitTxtVw.setText(nbrProduct  + " Article Affecté");
                    }
                } else {
                    produitTxtVw.setText(" Pas d'Article Affecté");

                }
            });
    }
    void showArticlesListDialog(int pDialogIdentifier) {
        articleListDialog.showConfirmDialog(this, pDialogIdentifier, savedInstanceState);
        if (tableDataAdapter != null && !tableDataAdapter.getData().isEmpty()) {
            for (LigneTicket ligneTicket : tableDataAdapter.getData()) {
                updateListArticleQuantity(ligneTicket.getArticle(), ligneTicket.lTQte);
            }
        }
        articleListDialog.generateView();

    }

    private void updateListArticleQuantity(Article article, double qty) {
        for (Article article1 : articleListDialog.articles)
            if (article.aRTCode.equals(article1.aRTCode) && article.getaRTDesignation().equals(article1.getaRTDesignation()))
                article1.setCount(qty);
        if (!articleListDialog.getList().isEmpty())
            for (Article article1 : articleListDialog.getList().keySet())
                if (article.aRTCode.equals(article1.aRTCode) && article.getaRTDesignation().equals(article1.getaRTDesignation()))
                    article1.setCount(qty);
        if (!articleListDialog.getCurrenSelection().isEmpty())
            for (Article article1 : articleListDialog.getCurrenSelection().keySet())
                if (article.aRTCode.equals(article1.aRTCode) && article.getaRTDesignation().equals(article1.getaRTDesignation()))
                    article1.setCount(qty);
    }


    void setTicketHeader() {
       // dateInputField.setText(DateUtils.dateToStr(date, "EEEE, dd MMMM yyyy HH:mm"));
        ClientViewModel.getInstance(this).getByStation(prefUtils.getFiltreCltAuthorization(), prefUtils.getUserStationId()).observe(this, clients -> {



            if (clients != null)  setSearchableSpinner(clients);



        });
    }

    void setTicketFooter() {
        discountInputField.addTextChangedListener(new TextWatcher() {
            @Override
            public void onTextChanged(CharSequence cs, int arg1, int arg2, int arg3) {
            }

            @Override
            public void beforeTextChanged(CharSequence s, int arg1, int arg2, int arg3) {
            }

            @Override
            public void afterTextChanged(Editable arg0) {
                amountWithDiscount = articleListDialog.setList(articleListDialog.getList().setDiscount(discountInputField.getText().toString())).getAmountWithDiscount();
                setTicketDataTable(true);
                setFooter();
            }
        });


    }

    /**
     * load table adapter with tickets
     */
    void setTicketDataTable(boolean changeFromGlobal) {
        tableView.setSwipeToRefreshEnabled(false);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            tableView.setElevation(10);
        }
        tableView.setHeaderBackground(R.color.material_teal500);
        if (changeFromGlobal) {
            amount = articleListDialog.setList(articleListDialog.getList().setDiscount(discountInputField.getText().toString())).getAmount();
            amountWithDiscount = articleListDialog.setList(articleListDialog.getList().setDiscount(discountInputField.getText().toString())).getAmountWithDiscount();
        } else {
            amount = articleListDialog.getList().getAmount();
            amountWithDiscount = articleListDialog.getList().getAmountWithDiscount();
        }
        tableDataAdapter = new LigneInvTicketTableDataAdapter(context, articleListDialog.getList().getLigneTickets(), tableView);
        tableDataAdapter.setNotifyOnChange(true);
        tableView.setDataAdapter(tableDataAdapter);

        tableView.addDataLongClickListener((rowIndex, clickedData) -> {
            if (!dialogShown) {
                dialogShown = true;
                new MaterialDialog.Builder(context)
                        .title(R.string.confirmation)
                        .content(R.string.delete_confirmation_msg)
                        .positiveText(R.string.yes)
                        .negativeText(R.string.no)
                        .onPositive((dialog, which) -> {
                                    try {
                                        if (tableDataAdapter.getData() != null) {
                                            tableDataAdapter.getData().get(rowIndex).getArticle().setCount(0);
                                            articleListDialog.getList().remove(tableDataAdapter.getData().get(rowIndex).getArticle());
                                            tableDataAdapter.getData().remove(rowIndex);
                                            tableDataAdapter.notifyDataSetChanged();
                                            amount = articleListDialog.setList(articleListDialog.getList().setDiscount(discountInputField.getText().toString())).getAmount();
                                            amountWithDiscount = articleListDialog.setList(articleListDialog.getList().setDiscount(discountInputField.getText().toString())).getAmountWithDiscount();
                                            setFooter();
                                            TransitionManager.beginDelayedTransition(content, new Slide(Gravity.BOTTOM));
                                            footerTicketView.setVisibility(!tableDataAdapter.getData().isEmpty() ? View.VISIBLE : GONE);
                                        }
                                    } catch (NullPointerException ignored) {
                                    }
                                    dialogShown = false;
                                }

                        ).onNegative((dialog, which) -> dialogShown = false)
                        .show();
            }
            return false;
        });

        TransitionManager.beginDelayedTransition(content, new Slide(Gravity.BOTTOM));
        footerTicketView.setVisibility(!tableDataAdapter.getData().isEmpty() ? View.VISIBLE : GONE);
        tableView.addDataClickListener((rowIndex, clickedData) -> {
           /* if (!dialogShown) {
                dialogShown = true;
                openModifyArticleDialog(clickedData);
            }*/
        });
    }



  /*  private void openModifyArticleDialog(LigneTicket clickedData, Boolean scanned) {
        if (prefUtils.getIsAutoScan() && scanned) {
            addArticleToTable("scan", clickedData, -11.1);
        } else {
            ligneTicketDialog = new LigneTicketDialog(context, scanned, clickedData, 3, false,
                    (dialog, which) -> ligneTicketDialog.validator.validate(), (dialog, which) -> {
                dialog.dismiss();
                dialogShown = false;
            }, new Validator.ValidationListener() {
                @Override
                public void onValidationSucceeded() {
                    if (ligneTicketDialog.validate()) {
                        if (!ArticleListDialog.passagerBlocked) {
                            double quantity = StringUtils.parseDouble(ligneTicketDialog.getQuantityInputField().getText().toString(), 0);
                            double discount1 = StringUtils.parseDouble(ligneTicketDialog.getDiscountInputField().getText().toString(), 0);
                            double price = StringUtils.parseDouble(ligneTicketDialog.getUnitPriceInputField().getText().toString(), 0);
                            if (tableDataAdapter != null) {
                                List<LigneTicket> ligneTickets = tableDataAdapter.getData();


                                if (ligneTickets != null) {
                                    for (int rowIndex = 0; rowIndex < ligneTickets.size(); rowIndex++) {
                                        if (ligneTickets.get(rowIndex).getArticle().getaRTCodeBar().equals(clickedData.article.getaRTCodeBar())) {

                                            if (scanned)
                                                quantity += ligneTickets.get(rowIndex).article.getCount(); // if from scan increment quantity else if from modify then replace with user input quantity

                                            articleListDialog.getList().remove(tableDataAdapter.getData().get(rowIndex).getArticle());
                                            articleListDialog.getCurrenSelection().remove(tableDataAdapter.getData().get(rowIndex).getArticle());
                                            tableDataAdapter.getData().remove(rowIndex);
                                            tableDataAdapter.notifyDataSetChanged();
                                        }
                                    }
                                }
                            }

                            clickedData.getArticle().setPvttc(price);
                            clickedData.getArticle().setCount(quantity);
                            articleListDialog.getList().put(clickedData.getArticle(), quantity, discount1, false);
                            setTicketDataTable(false);
                            setFooter();
                            ligneTicketDialog.dismiss();
                            dialogShown = false;
                        }
                    }

                }

                @Override
                public void onValidationFailed(List<ValidationError> errors) {
                    for (ValidationError error : errors) {
                        View view = error.getView();
                        String message = error.getCollatedErrorMessage(context);

                        if (view instanceof EditText) {
                            ((MaterialEditText) view).setError(message);
                        } else {
                            Toast.makeText(context, message, Toast.LENGTH_LONG).show();
                        }
                    }
                }
            }, client);

            ligneTicketDialog.show(context.getFragmentManager(), StringUtils.upper);


        }
    }
*/


    /**
     * add new client button
     */
    @OnClick(R.id.addClientButton)
    void addClient() {
        addClientButton.setEnabled(false);
        addClientButton.setClickable(false);
        intent = new Intent(context, AddClientActivity.class);
        startActivityForResult(intent, REQUEST_CLIENT_CODE);
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        // Check which request we're responding to
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case REQUEST_ENABLE_BT:
                if (resultCode == Activity.RESULT_OK) {
                    Toast.makeText(context, "Bluetooth open successful", Toast.LENGTH_LONG).show();
                } else {
                    Toast.makeText(context, "Bluetooth failed to connect", Toast.LENGTH_LONG).show();
                }
                break;
            case REQUEST_CONNECT_DEVICE:
                if (resultCode == Activity.RESULT_OK) {
                    String address = data.getExtras()
                            .getString(DeviceListActivity.EXTRA_DEVICE_ADDRESS);
                    try {
                        con_dev = mService.getDevByMac(address);
                        mService.connect(con_dev);
                    } catch (Exception e) {
                        Log.d("erro", e.getMessage());
                    }
                }
            case Activity.RESULT_CANCELED:
                finish();
                break;
        }
        if (requestCode == REQUEST_CLIENT_CODE) {
            if (resultCode == RESULT_OK) {
                final Client client1 = (Client) data.getSerializableExtra(CLIENT_INTENT_ID_KEY);
                if (client1 != null) {
                    simpleListAdapter.addItem(client1);
                    searchableSpinner.setSelectedItem(simpleListAdapter.getCount() - 1);
                }
            }
            addClientButton.setEnabled(true);
            addClientButton.setClickable(true);
        }

    }


    @OnClick(R.id.addItemButton)
    void addItems() {

      /*  if(patFrom.equals(getString(R.string.patrimoine_deplacement_out_title))
                || patFrom.equals(getString(R.string.patrimoine_deplacement_in_title))
        ){
            scanArt.setEnabled(false);
           showScanner();
        //   showArticlesListDialog(2);

        } else*/ if(patFrom.equals(getString(R.string.patrimoineInventaire_title))||
                patFrom.equals(getString(R.string.patrimoine_deplacement_out_title))
                || patFrom.equals(getString(R.string.patrimoine_deplacement_in_title))){

            numSerieInputDialog = new NumSerieInputDialog(

                    context,
                    patFrom,
                    (dialog, which) -> {

                        if (numSerieInputDialog.validate()) {

                            verifyPatOnline(numSerieInputDialog.getBarcode().getText().toString(),"");
                            //   checkPat(numSerieInputDialog.getBarcode().getText().toString(),"3");
                            numSerieInputDialog.dismiss();
                        }


                    }, (dialog, which) -> {
                dialog.dismiss();
                dialogShown = false;
            },
                    validationListener,
                    client);

            numSerieInputDialog.show(context.getSupportFragmentManager(), StringUtils.digits);

        }

        else showArticlesListDialog(1);


    }




    private void verifyPatOnline(String result, String from){
        Log.d("oolknjb","result");
        ControleInventaire controleInventaire    = new ControleInventaire(result,
                client.getcLICode(), devInfo3);
        BonCommandeDataManager.getInstance().controlInventair(new GenericObject(prefUtils.getBaseConfig(), controleInventaire),
                new RemoteCallback<BonCommandResponse>(context, true, false) {
                    @RequiresApi(api = Build.VERSION_CODES.O)
                    @Override
                    public void onSuccess(BonCommandResponse response) {
                        if (response != null) {
                            if (response.getCode() != null) {
                                if (response.getCode().equals("10200")) {
                                    List<LigneBonCommande> lgBnCommande = App.database.ligneBonCommandeDAO().getByNumSerieList(result);

                                    if (!lgBnCommande.isEmpty()){
                                        listLgBcProduitScanneAffecteAuClt.add(lgBnCommande.get(0));
                                        showArticleFromScan(lgBnCommande.get(0), result, "", from);
                                    }

                                    else Toasty.error(context, response.getMessage()).show();
                                } else if (response.getCode().equals("10704")) {
                                    Toasty.error(context, response.getMessage()).show();
                                } else if (response.getCode().equals("10705")) {

                                    Toasty.error(context, response.getMessage()).show();
                                } else if (response.getCode().equals("10706")) {

                                    Toasty.error(context, response.getMessage()).show();
                                } else if (response.getCode().equals("10707")) {

                                    Toasty.error(context, response.getMessage()).show();
                                }
                            }
                        }


                    }

                    @Override
                    public void onUnauthorized() {
                        checkPat(result,"");
                    }

                    @Override
                    public void onFailed(Throwable throwable) {
                        checkPat(result,"");
                    }
                });


    }


    private void checkPat(String result, String from){

       /* if (devInfo3.equals(Globals.TYPE_PATRIMOINE.AFFECTATION.getTypePat())){
            if(App.database.ligneBonCommandeDAO().getByNumSerie(result)!=null) {
                Toasty.error(context,getString(R.string.pat_deja_affecter)).show();
            }
            else{
                showArticleFromScan("",result,"");
            }
        }*/


        if ((devInfo3.equals(Globals.TYPE_PATRIMOINE.AFFECTATION.getTypePat()))) {
            LigneBonCommande lg = App.database.ligneBonCommandeDAO().getByNumSerie(result);
            if ( lg!= null) {
                List<BonCommande> bnCommande =    App.database.bonCommandeDAO().getByNumOrderedBydate(lg.getLGDEVNumBon());
                if(bnCommande.get(0).getDEV_info3().equals(Globals.TYPE_PATRIMOINE.AFFECTATION.getTypePat()))
                    Toasty.error(context,getString(R.string.pat_deja_affecter)).show();
                else  showArticleFromScan(lg,result,"",from);
            } else  showArticleFromScan(lg,result,"",from);
        }



        else if (devInfo3.equals(Globals.TYPE_PATRIMOINE.INVENTAIRE.getTypePat())){
            String remarque = "";
            if(App.database.ligneBonCommandeDAO().getByNumSerie(result)!=null) {
                List<LigneBonCommande> lgBnCommande = App.database.ligneBonCommandeDAO().getByNumSerieList(result);
                if(!lgBnCommande.isEmpty()){
                    List<BonCommande> bnCommande =    App.database.bonCommandeDAO().getByNumOrderedBydate(lgBnCommande.get(0).getLGDEVNumBon());
                    // BonCommande bnCommande =    App.database.bonCommandeDAO().getByCodeClient(((BonCommande) extraInfo).getDEVCodeClient(),App.prefUtils.getExercice());

                    if(!bnCommande.isEmpty()){
                        Client clt = App.database.clientDAO().getOneByCode(bnCommande.get(0).getDEVCodeClient());

                        if(bnCommande.get(0).getDEV_info3()!=null){
                            if(bnCommande.get(0).getDEV_info3().equals(Globals.TYPE_PATRIMOINE.AFFECTATION.getTypePat())  ){


                                if(!client.getcLICode().equals(bnCommande.get(0).getDEVCodeClient())){
                                    remarque =   " ( Affecté à : "+clt.cLINomPren+" )";
                                    Toasty.error(context,"Article affecter à " +clt.cLINomPren).show();
                                } //else
                                showArticleFromScan(lgBnCommande.get(0),result,remarque,from);
                                listLgBcProduitScanneAffecteAuClt.add(lgBnCommande.get(0));
                            }
                            else   if(bnCommande.get(0).getDEV_info3().equals(Globals.TYPE_PATRIMOINE.SORTIE.getTypePat()) ){

                                if(!client.getcLICode().equals(bnCommande.get(0).getDEVCodeClient())){
                                    Toasty.error(context,"Dernier opération Sortie à " +clt.cLINomPren).show();
                                } else  Toasty.error(context,"Dernier opération Sortie ").show();
                            }
                            else if(bnCommande.get(0).getDEV_info3().equals(Globals.TYPE_PATRIMOINE.ENTREE.getTypePat())){
                                if(!client.getcLICode().equals(bnCommande.get(0).getDEVCodeClient())){
                                    remarque =   " ( Dernière opération Déplacement In à : "+clt.cLINomPren+" )";
                                    Toasty.error(context,"Dernière opération Déplacement In Client: " +clt.cLINomPren).show();
                                } //else
                                showArticleFromScan(lgBnCommande.get(0),result,remarque,from);
                                listLgBcProduitScanneAffecteAuClt.add(lgBnCommande.get(0));
                            }
                            else if(bnCommande.get(0).getDEV_info3().equals(Globals.TYPE_PATRIMOINE.INVENTAIRE.getTypePat())){
                                Toasty.error(context,"Dernier opération : " +Globals.TYPE_PATRIMOINE.INVENTAIRE.getTypePat() + ", Client : "+clt.cLINomPren).show();

                                remarque =   " (Dernier opération " +Globals.TYPE_PATRIMOINE.INVENTAIRE.getTypePat()+ " : " +clt.cLINomPren+" )";

                                showArticleFromScan(lgBnCommande.get(0),result,remarque,from);
                                listLgBcProduitScanneAffecteAuClt.add(lgBnCommande.get(0));
                            }
                            /*else Toasty.error(context,"Dernier opération diffère de : " +Globals.TYPE_PATRIMOINE.AFFECTATION.getTypePat()+" / "+
                                        Globals.TYPE_PATRIMOINE.SORTIE.getTypePat()).show();*/


                        }  else  {
                            openDialogueAffectation();
                        }
                    } else{
                        openDialogueAffectation();
                    }

                } else {
                    openDialogueAffectation();
                }

            }
            else{

                openDialogueAffectation();
            }

        }




        else  if (devInfo3.equals(Globals.TYPE_PATRIMOINE.SORTIE.getTypePat())){
            if(App.database.ligneBonCommandeDAO().getByNumSerie(result)!=null) {
                List<LigneBonCommande> lgBnCommande = App.database.ligneBonCommandeDAO().getByNumSerieList(result);
                if(!lgBnCommande.isEmpty()){
                    List<BonCommande> bnCommande =    App.database.bonCommandeDAO().getByNumOrderedBydate(lgBnCommande.get(0).getLGDEVNumBon());
                    if(!bnCommande.isEmpty()){
                        if(bnCommande.get(0).getDEV_info3()!=null){
                            if(bnCommande.get(0).getDEV_info3().equals(Globals.TYPE_PATRIMOINE.AFFECTATION.getTypePat())||
                                    bnCommande.get(0).getDEV_info3().equals(Globals.TYPE_PATRIMOINE.ENTREE.getTypePat()) ||
                                    bnCommande.get(0).getDEV_info3().equals(Globals.TYPE_PATRIMOINE.INVENTAIRE.getTypePat())){

                                if(!client.getcLICode().equals(bnCommande.get(0).getDEVCodeClient())){
                                    Toasty.error(context,"Article affecter a un autre client").show();
                                } else showArticleFromScan(lgBnCommande.get(0),result,"",from);
                            }
                            else if(bnCommande.get(0).getDEV_info3().equals(Globals.TYPE_PATRIMOINE.SORTIE.getTypePat())){
                                Toasty.error(context,"Dernier opération : " +Globals.TYPE_PATRIMOINE.SORTIE.getTypePat()).show();
                            }
                          /*  else if(bnCommande.get(0).getDEV_info3().equals(Globals.TYPE_PATRIMOINE.INVENTAIRE.getTypePat())){
                                Toasty.error(context,"Dernier opération : " +Globals.TYPE_PATRIMOINE.INVENTAIRE.getTypePat()).show();
                            }*/
                            else Toasty.error(context,"Dernier opération diffère de : " +Globals.TYPE_PATRIMOINE.AFFECTATION.getTypePat()+" / "+
                                        Globals.TYPE_PATRIMOINE.ENTREE.getTypePat()+" / "+Globals.TYPE_PATRIMOINE.INVENTAIRE.getTypePat()).show();
                        }  else   Toasty.error(context,getString(R.string.pat_pas_affecter)).show();
                    } else Toasty.error(context,getString(R.string.pat_pas_affecter)).show();

                } else Toasty.error(context,getString(R.string.pat_pas_affecter)).show();
            }
            else Toasty.error(context,getString(R.string.pat_pas_affecter)).show();




        }

        else   if (devInfo3.equals(Globals.TYPE_PATRIMOINE.ENTREE.getTypePat())){
            if(App.database.ligneBonCommandeDAO().getByNumSerie(result)!=null) {

                List<LigneBonCommande> lgBnCommande = App.database.ligneBonCommandeDAO().getByNumSerieList(result);
                if(!lgBnCommande.isEmpty()){
                    List<BonCommande> bnCommande =    App.database.bonCommandeDAO().getByNumOrderedBydate(lgBnCommande.get(0).getLGDEVNumBon());
                    if(!bnCommande.isEmpty()){
                        if(bnCommande.get(0).getDEV_info3()!=null){
                            if(bnCommande.get(0).getDEV_info3().equals(Globals.TYPE_PATRIMOINE.SORTIE.getTypePat()) ){
                                showArticleFromScan(lgBnCommande.get(0),result,"",from);
                            }
                            else if(bnCommande.get(0).getDEV_info3().equals(Globals.TYPE_PATRIMOINE.ENTREE.getTypePat())){
                                Toasty.error(context,"Dernier opération : " +Globals.TYPE_PATRIMOINE.ENTREE.getTypePat()).show();
                            }
                            else if(bnCommande.get(0).getDEV_info3().equals(Globals.TYPE_PATRIMOINE.INVENTAIRE.getTypePat())){
                                Toasty.error(context,"Dernier opération : " +Globals.TYPE_PATRIMOINE.INVENTAIRE.getTypePat()).show();
                            }

                            else if(bnCommande.get(0).getDEV_info3().equals(Globals.TYPE_PATRIMOINE.AFFECTATION.getTypePat())){
                                Toasty.error(context,"Dernier opération : " +Globals.TYPE_PATRIMOINE.AFFECTATION.getTypePat()).show();
                            } else  Toasty.error(context,"Dernier opération differe de : " +Globals.TYPE_PATRIMOINE.SORTIE.getTypePat()).show();
                        }  else Toasty.error(context,getString(R.string.pat_pas_affecter)).show();
                    } else   Toasty.error(context,getString(R.string.pat_pas_affecter)).show();

                } else   Toasty.error(context,getString(R.string.pat_pas_affecter)).show();

            } else   Toasty.error(context,getString(R.string.pat_pas_affecter)).show();

        }


    }




    @Override
    protected int setContentView() {
        return R.layout.activity_inv_patrimoine;
    }

    /*  @Override
     public void onClickPositiveButton(DialogInterface pDialog) {

         for (Article article : articleListDialog.getCurrenSelection().keySet()) {
             Log.d("llopl", String.valueOf(article));
             articleListDialog.getList().remove(article);

         }
         articleListDialog.getList().putAll(articleListDialog.getCurrentSelection());
         articleListDialog.getCurrentSelection().clear();
         articleListDialog.generateView();
         selectedArticles = articleListDialog.getList().getLigneTickets();
         setTicketDataTable(false);
         setFooter();
         pDialog.dismiss();
     }
 */
    @Override
    public void onClickPositiveButton(DialogInterface pDialog) {
        index_prod_intable = 0;
        boolean prodexist = false;
        Article articl = null;
        if (tableDataAdapter == null) {
            tableDataAdapter = new LigneInvTicketTableDataAdapter(context, articleListDialog.getList().getLigneTickets(), tableView);

        }
        else {
            tableDataAdapter.clear();
            tableDataAdapter.addAll(articleListDialog.getList().getLigneTickets());
        }

        List<LigneTicket> ligneTickets = tableDataAdapter.getData();


        //for (Article article : articleListDialog.getCurrenSelection().keySet()) {
        for (Article article : articleListDialog.getCurrenSelection().keySet()) {
            if (isExistArticle(article, ligneTickets)) {
                articl = article;
                prodexist = true;
                break;
            }
            //  articleListDialog.getList().remove(article);
        }
        Log.d("llmlnn", String.valueOf(prodexist));
        if (!prodexist) {

            articleListDialog.getList().putAll(articleListDialog.getCurrentSelection());
            articleListDialog.getCurrentSelection().clear();
            articleListDialog.generateView();
            selectedArticles = articleListDialog.getList().getLigneTickets();
            setTicketDataTable(false);
            setFooter();
        } else {
            //  addArticleToTable(Objects.requireNonNull(articleListDialog.getCurrenSelection().get(articl)), ligneTickets.get(index_prod_intable).lTQte);
            Toasty.info(context,getString(R.string.pat_deja_affecter)).show();
        }


        pDialog.dismiss();

    }


    private boolean isExistArticle(Article selectedArticle, List<LigneTicket> ligneTickets) {
        boolean exist = false;
        for (LigneTicket ligneTicket : ligneTickets) {

            if (ligneTicket.getArticle().getPhotoPath().equals(selectedArticle.getPhotoPath())
                    && ligneTicket.getArticle().getaRTDesignation().equals(selectedArticle.getaRTDesignation())) {
                exist = true;
                break;
            }
            index_prod_intable++;
        }
        return exist;
    }



    void setFooter() {
        setText(amountInputField, StringUtils.priceFormat(amount));
        setText(amountwithDiscountInputField, String.valueOf(tableDataAdapter.getData().size()));
        amountwithDiscountInputField.setEnabled(false);
        amountwithDiscountInputField.setFocusable(false);
        title2.setText("Total Patrimoines : ");
    }

    @Override
    public void onClickNegativeButton(DialogInterface pDialog) {
        articleListDialog.getCurrentSelection().clear();
        if (tableDataAdapter != null && !tableDataAdapter.getData().isEmpty()) {
            for (LigneTicket ligneTicket : tableDataAdapter.getData()) {
                updateListArticleQuantity(ligneTicket.getArticle(), ligneTicket.lTQte);
            }
        }
        //  articleListDialog.generateView();
        pDialog.dismiss();
    }

    @Override
    public void onBackPressed() {
        new MaterialDialog.Builder(this).onPositive((dialog, which) -> finish()).onNegative((dialog, which) -> {
                }).title(R.string.confirmation)
                .content("Êtes-vous sûr de vouloir quitter?")
                .positiveText(R.string.yes)
                .negativeText(R.string.no)
                .show();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem menuItem) {
        switch (menuItem.getItemId()) {
            case android.R.id.home:
                onBackPressed();
                break;
        }

        return super.onOptionsItemSelected(menuItem);
    }


    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.add_ticket_menu, menu);
        save = menu.findItem(R.id.save);
        scan = menu.findItem(R.id.scan);
        scan.setVisible(patFrom.equals(getString(R.string.patrimoineInventaire_title)));

        scan.setIcon(new IconicsDrawable(context).icon(FontAwesome.Icon.faw_save)
                .color(Color.WHITE).sizeDp(20));

        scan.setOnMenuItemClickListener(item -> {


            if  ((tableView.getVisibility() == View.VISIBLE)
                    && (searchableSpinner.getSelectedPosition() > 0
                    && !immoCode().isEmpty()
                    && footerTicketView.getVisibility() == View.VISIBLE)){

                if(!articleListDialog.getList().isEmpty())  {
                    sendData(false);
                    articleListDialog.getList().clear();

                    finish();



                    //        Bundle bundle = new Bundle();
                    //     bundle.putString(Globals.INV_FROM,patFrom);
                } else Toasty.info(context,"Ajouter des Patrimoines").show();


            }else Toasty.info(context,"Selectioner un client, puis Ajouter des Patrimoines").show();

            return false;

        });
        save.setIcon(new IconicsDrawable(context).icon(GoogleMaterial.Icon.gmd_send)
                .color(ResourcesCompat.getColor(getResources(), R.color.material_white, null))
                .sizeDp(24));
        // save.setEnabled(false);
        save.setOnMenuItemClickListener(item -> {

            if ((tableView.getVisibility() == View.VISIBLE)
                   // && (searchableSpinner.getSelectedPosition() > 0
                    && (selectedSociete!=null
                    && footerTicketView.getVisibility() == View.VISIBLE  )) {

                if(articleListDialog.getList().size()==nbrProduct || nbrProduct==0)
                    new MaterialDialog.Builder(this)
                            .title(R.string.confirmation)
                            .content(getString(R.string.sauv_label))
                            .negativeText(R.string.cancel)
                            .positiveText(R.string.dialog_ok)
                            .cancelable(false)
                            .onPositive((dialog, which) -> sendData(true))
                            .onNegative((dialog, which) -> save.setEnabled(true))
                            .show();
                else  new MaterialDialog.Builder(this)
                        .title(articleListDialog.getList().size()+" Produit scanné, "+nbrProduct+" Produit affecté au client ")
                        .content("Inventaire incomplet ! Appuyer sur :"+getString(R.string.cancel) +
                                " pour continuer l'inventaire ou sur "+getString(R.string.dialog_ok)+" pour sauvegarder" )
                        .negativeText(R.string.cancel)
                        .positiveText(R.string.dialog_ok)
                        .cancelable(false)
                        .onPositive((dialog, which) -> sendData(true))
                        .onNegative((dialog, which) -> save.setEnabled(true))
                        .show();
            } else {
                UIUtils.showDialog(context, R.string.fields_error);
                save.setEnabled(true);

            }
            return false;
        });




        // Associate searchable configuration with the SearchView
        SearchManager searchManager =
                (SearchManager) context.getSystemService(Context.SEARCH_SERVICE);
        searchView =
                (SearchView) menu.findItem(R.id.search).getActionView();
        searchView.setSearchableInfo(
                searchManager.getSearchableInfo(context.getComponentName()));

        searchView.setOnQueryTextListener(new SearchView.OnQueryTextListener() {
            @Override
            public boolean onQueryTextSubmit(String s) {
                return true;

            }


            @Override
            public boolean onQueryTextChange(String s) {

                // Create a copy of the original LinkedHashMap
                Map<Article, LigneTicket> originalMap = new LinkedHashMap<>(articleListDialog.getList());

                // Filter the copy based on the search query
                Map<Article, LigneTicket> filteredMap = null;
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                    filteredMap = originalMap.entrySet()
                            .stream()
                            .filter(entry -> entry.getKey().getaRTDesignation().toLowerCase().contains(s.toLowerCase()) ||
                                    entry.getKey().getPhotoPath().toLowerCase().contains(s.toLowerCase()) )
                            .collect(LinkedHashMap::new, (m, e) -> m.put(e.getKey(), e.getValue()), LinkedHashMap::putAll);
                }

                // Update the tableDataAdapter with the filtered values
                tableDataAdapter.getData().clear();
                assert filteredMap != null;
                tableDataAdapter.addAll(filteredMap.values());
                tableDataAdapter.notifyDataSetChanged();

                setFooter();
                return true;
            }
        });
        return super.onCreateOptionsMenu(menu);
    }

    public void showArticle(LigneBonCommande lgBonCommande, String numSerie, String remarque) {
        Article selectedArticle = App.database.articleDAO().getByCodeBar(lgBonCommande.getLGDEVCodeArt());
        // selectedArticle.setSelectedMarque(numSerieInputDialog.selectedMarque);


        if(numSerieInputDialog!=null)
            selectedArticle.setSelectedMarque(numSerieInputDialog.selectedMarque);
        LigneTicket ligneTicketToSet = new LigneTicket();
        ligneTicketToSet.setSync(false);
        ligneTicketToSet.setStatus(Globals.ITEM_STATUS.INSERTED.getStatus());
        ligneTicketToSet.setArticle(selectedArticle);


        setTicketDataTable(false);
        if (selectedArticle != null && !isDestroyed()) {
            dataTableView.setVisibility(View.VISIBLE);
            if (tableDataAdapter != null) {
                List<LigneTicket> ligneTickets = tableDataAdapter.getData();
                if (ligneTickets != null) {
                    boolean exist = false;
                    for (LigneTicket ligneTicket : ligneTickets) {
                        //    if (ligneTicket.getArticle().getPhotoPath().equals(selectedArticle.getPhotoPath())) {
                        if (ligneTicket.getArticle().getPhotoPath().equals(numSerie)) {
                            // openModifyArticleDialog(ligneTicket);
                            exist = true;
                            break;
                        }
                    }
                    if (!exist){
                        // openModifyArticleDialog(ligneTicketToSet);
                        selectedArticle.setPhotoPath(numSerie);
                        selectedArticle.setaRTDesignation(selectedArticle.getaRTDesignation() + remarque);


                        Marque ligneTicketmarque;
                        if(lgBonCommande.getLGDEVCMarq()!=null){
                            Marque marque = App.database.marqueDAO().getByCode(lgBonCommande.getLGDEVCMarq());


                            if(marque!=null) ligneTicketmarque = marque;
                            else ligneTicketmarque = new Marque();
                        }

                        else ligneTicketmarque = new Marque();

                        selectedArticle.setSelectedMarque(ligneTicketmarque);
                        articleListDialog.getList().put(selectedArticle, 1, 0, false);
                        setTicketDataTable(false);
                        setFooter();
                    }
                    else{

                        new MaterialDialog.Builder(context)
                                .title(R.string.Article_ajouter)
                                //   .content(R.string.delete_confirmation_msg)
                                .positiveText(R.string.quitter)
                                // .negativeText(R.string.no)
                                .onPositive((dialog, whic) -> {
                                            dialog.dismiss();
                                        }

                                )
                                //.onNegative((dialog, which) -> )
                                .show();
                        Toasty.info(context,"Article Déja Ajouter").show();
                    }

                } else  {
                    Toasty.error(context,"Error 1").show();
                }

            } else{
                Toasty.error(context,"Error 2").show();
            }
        } else Toasty.error(context,"Article introuvable").show();



        startBarcode();
    }


    public void showArticleFromScan(LigneBonCommande lgBonCommande, String numSerie, String remarque, String from) {
        if (from.equals("scan")){
            showArticle(lgBonCommande, numSerie, remarque);
              /*  numSerieInputDialog = new NumSerieInputDialog(

                        context,
                        FROM_INV_SCAN,
                        (dialog, which) -> {

                            if (numSerieInputDialog.validate()) {
                                showArticle(codeArt, numSerie, remarque);
                                numSerieInputDialog.dismiss();
                            } else Toasty.error(context, "Erreur ").show();


                        }, (dialog, which) -> {
                    dialog.dismiss();
                    dialogShown = false;
                },
                        validationListener,
                        client);

            numSerieInputDialog.show(context.getSupportFragmentManager(), StringUtils.digits);*/
        }
        else showArticle(lgBonCommande, numSerie, remarque);

    }



    void sendData(Boolean saveAndSync) {
        ArrayList<LigneTicket> ligneTickets = articleListDialog.getList().getLigneTickets();

        Log.d("jjnhbh", String.valueOf(ligneTickets.size()));
        for (int i = 0; i < ligneTickets.size(); i++) {
            LigneTicket p1 = ligneTickets.get(i);
            for (int j = i + 1; j < ligneTickets.size(); j++) {
                LigneTicket p2 = ligneTickets.get(j);
                if (p1.getArticle().photoPath.equals(p2.getArticle().photoPath)) {
                    ligneTickets.remove(j);
                    j--;
                }
            }
        }
        Log.d("jjnhbh","dd"+ String.valueOf(ligneTickets.size()));
        createBc(saveAndSync, ligneTickets);
        if(saveAndSync) patValide();
    }





    private void patValide() {
        if (articleListDialog.getList().getLigneTickets().isEmpty()) {
            bonCommande = null;
            ticket = null;
            save.setEnabled(true);
            Toasty.info(context, R.string.no_article_selected).show();
            return;
        }
        finish();
    }

    String immoCode() {

     String immo = "";

        if(selectedSociete!=null){
            immo = selectedSociete.cLICode;
            if(selectedSite!=null) {
                immo = selectedSite.cLICode;

                if(selectedBloc!=null){
                    immo = selectedBloc.cLICode;
                    if(selectedBatiment!=null){
                        immo = selectedBatiment.cLICode;
                    }
                }

            }

        }

        return immo;
    }
    private void createBc(Boolean saveAndSync,ArrayList<LigneTicket> ligneTickets) {


        String numBonCommande = "";
        String numBonCommandeM = "";


        if (ObjectUtils.isEmpty(bonCommande)) {
            numBonCommande = "PM" + "_" + Utils.generateCommonCode(new Date(), String.valueOf((int) Math.random()), String.valueOf((int) Math.random())) + "_"
                    + App.database.bonCommandeDAO().getNewCode(App.database.prefixeDAO().getOneById("Devis").getpREPrefixe());
            numBonCommandeM = Utils.generateMobileDevisCode(prefUtils.getUserStationId(), numBonCommande);
        } else {
            numBonCommande = bonCmdNumber;
            numBonCommandeM = bonCommande.devCodeM;
        }


        ligneBonCommandes = new ArrayList<>();
        bonCommande = new BonCommande();
        bonCommande.setDEVNum(numBonCommandeM);
        bonCommande.setDevCodeM(numBonCommandeM);
        bonCommande.setDEVEtat("patrimoine");
        bonCommande.setBONLIVExerc(App.prefUtils.getExercice());
        bonCommande.setDEVExerc(App.prefUtils.getExercice());
        bonCommande.setDEVCodeClient(immoCode());
        bonCommande.setDEVStationOrigine(App.prefUtils.getUserStationId());
        bonCommande.setDEVStation(App.prefUtils.getUserStationId());
        bonCommande.setDEVMntFodec("0.0");
        bonCommande.setDEVMntDC("0.0");
        bonCommande.setDEVEtatBon("1");
        bonCommande.setDEVExonoration("0");
        bonCommande.setDEVTimbre("0.0");
        bonCommande.setDEVUser(App.prefUtils.getUserId());
        bonCommande.setDEVMntTTC(String.valueOf(articleListDialog.getList().getAmountWithDiscount()));
        bonCommande.setDEVMntTva(String.valueOf(articleListDialog.getList().getVATAmount()));
        bonCommande.setDEVMntht(String.valueOf(articleListDialog.getList().getAmountHT()));
        bonCommande.setDEVMntNetHt(String.valueOf(articleListDialog.getList().getAmountHT()));


        if(saveAndSync) bonCommande.setStatus(Globals.ITEM_STATUS.INSERTED.getStatus());
        else bonCommande.setStatus(Globals.ITEM_STATUS.WAITING.getStatus());

        bonCommande.setDEVDate(DateUtils.dateToStr(DateUtils.strToDate(new Date().toString(), "EEEE, dd MMMM yyyy HH:mm"), Globals.DATE_PATTERN));
        bonCommande.setDEVDDm(DateUtils.dateToStr(DateUtils.strToDate(new Date().toString(), "EEEE, dd MMMM yyyy HH:mm"), Globals.DATE_PATTERN));
        bonCommande.isSync = false;
        bonCommande.setDEVDDm(isUpdate ? ddm : "empty");


        if (patFrom.equals(getString(R.string.affectation_title))){
            bonCommande.setDEV_info3(Globals.TYPE_PATRIMOINE.AFFECTATION.getTypePat());
        }
        if (patFrom.equals(getString(R.string.patrimoineInventaire_title))){
            bonCommande.setDEV_info3(Globals.TYPE_PATRIMOINE.INVENTAIRE.getTypePat());

        }
        if (patFrom.equals(getString(R.string.patrimoine_deplacement_out_title))){
            bonCommande.setDEV_info3(Globals.TYPE_PATRIMOINE.SORTIE.getTypePat());

        }
        if (patFrom.equals(getString(R.string.patrimoine_deplacement_in_title))){
            bonCommande.setDEV_info3(Globals.TYPE_PATRIMOINE.ENTREE.getTypePat());

        }

        App.database.bonCommandeDAO().insert(bonCommande);


        for (int i = 0, ligneTicketsSize = ligneTickets.size(); i < ligneTicketsSize; i++) {
            LigneTicket ligneTicket = ligneTickets.get(i);
            LigneBonCommande ligneBonCommande = new LigneBonCommande();
            //ligneBonCommande.setLGDEVNumBon(numBonCommande);
            ligneBonCommande.setLGDEVNumBon(numBonCommandeM);
            ligneBonCommande.setLGDEVDDm(DateUtils.dateToStr(DateUtils.strToDate(new Date().toString(), "EEEE, dd MMMM yyyy HH:mm"), Globals.DATE_PATTERN));
            ligneBonCommande.setLGDEVExerc(prefUtils.getExercice());
            ligneBonCommande.setLGDEVUnite("Pièce");
            ligneBonCommande.setLGDEVStation(ligneTicket.getArticle().sARTCodeSatation);
            ligneBonCommande.setLGDEVCodeArt(ligneTicket.getlTCodArt());
            ligneBonCommande.setLGDEVMntHT(String.valueOf(ligneTicket.getlTMtHT()));
            ligneBonCommande.setLGDEVMntBrutHT(String.valueOf(ligneTicket.getlTMtHT()));
            ligneBonCommande.setLGDEVMntTTC(String.valueOf(ligneTicket.getlTMtTTC()));
            ligneBonCommande.setLGDEVPUTTC(String.valueOf(ligneTicket.lTPrixVente));
            ligneBonCommande.setLGDEVPUHT(String.valueOf(ligneTicket.lTPACHAT));
            ligneBonCommande.setLGDEVMntTva(String.valueOf(bonCommande.getDEVMntTva()));
            ligneBonCommande.setLGDEVQte(String.valueOf(ligneTicket.getlTQte()));
            ligneBonCommande.setLGDEVRemise(String.valueOf(ligneTicket.getlTTauxRemise()));
            ligneBonCommande.lGDevNumSerie = ligneTicket.getArticle().photoPath;
            ligneBonCommande.isSync = false;
            ligneBonCommande.lGDEVNumOrdre = ligneTickets.get(i).lTNumOrdre;
            ligneBonCommande.setLGDEVCMarq(ligneTicket.getArticle().selectedMarque.MAR_Code);


            if(saveAndSync) ligneBonCommande.setStatus(Globals.ITEM_STATUS.INSERTED.getStatus());
            else ligneBonCommande.setStatus(Globals.ITEM_STATUS.WAITING.getStatus());


            //  ligneBonCommande.lGDEVCodeM = Utils.generateMobileLigneDevisCode(prefUtils.getUserStationId(), numBonCommande,String.valueOf(i),String.valueOf(ligneTickets.get(i).lTNumOrdre));
            ligneBonCommande.lGDEVCodeM = numBonCommandeM + "_" + ligneTickets.get(i).lTNumOrdre;

            ligneBonCommandes.add(ligneBonCommande);
        }
        App.database.ligneBonCommandeDAO().deleteByCmd(numBonCommandeM);
        App.database.ligneBonCommandeDAO().insertAll(ligneBonCommandes);
    }


    static void setText(EditText text, String value) {
        text.setText(value);
    }


    @Override
    protected void onResume() {


        //barCodeReaderManager.resume();
        super.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
    }

    @Override
    protected void onDestroy() {

        if(barCodeReaderManager!=null)    barCodeReaderManager.destroy();
        super.onDestroy();
    }


    @Override
    protected void onStop() {
        super.onStop();
        //   if(barCodeReaderManager!=null) barCodeReaderManager.destroy();
    }

    @Override
    protected void onPostResume() {
        super.onPostResume();
        //   if(barCodeReaderManager!=null) barCodeReaderManager.resume();
    }


    /**
     * show the dialog which contains all the articles to chose from
     */
/*    private void showArticleDialog(Article selectedArticle) {
        if (prefUtils.getIsAutoScan()) addPurchaseLineFromAutoScan(selectedArticle);
        else {
            boolean isScanSource = true;
            articleDialog = new ArticleDialog(context, false, selectedArticle, 3, true, isScanSource, false, (dialog, which) -> {
                articleDialog.validator.validate();
                selectedArticle.setSync(false);
                selectedArticle.setStatus(Globals.ITEM_STATUS.INSERTED.getStatus());
                selectedArticle.setCount(StringUtils.parseDouble(articleDialog.getQuantityInputField().getText().toString(), 0));
                selectedArticle.photoPath = (articleDialog.getBarcode().getText().toString());
                articleListDialog.getList().put(selectedArticle, Double.parseDouble(articleDialog.getQuantityInputField().getText().toString()), 0, false);
                setTicketDataTable(false);
                setFooter();
                articleDialog.dismiss();
            }, (dialog, which) -> {
                if (ArticleDialog.article != null)
                    ArticleDialog.article.setPvttc(App.database.articleDAO().getOneByCodeAndStation(ArticleDialog.article.getaRTCode(), ArticleDialog.article.getsARTCodeSatation()).getPvttc());
                dialog.dismiss();

            }, validationListener);
            articleDialog.show(context.getSupportFragmentManager(), StringUtils.digits);
        }
    }*/
    private void addPurchaseLineFromAutoScan(Article selectedArticle) {
        selectedArticle.setSync(false);
        selectedArticle.setStatus(Globals.ITEM_STATUS.INSERTED.getStatus());
        selectedArticle.setCount(StringUtils.parseDouble("1", 1));
        articleListDialog.getList().put(selectedArticle, Double.parseDouble("1"), 0, false);
        setTicketDataTable(false);
        setFooter();
        App.database.articleDAO().insert(ArticleDialog.article);
    }


    void openDialogueAffectation(){
        new MaterialDialog.Builder(this)
                .title(getString(R.string.pat_pas_affecter))
                .content("Voulez vous l'affecter?" )
                .negativeText(R.string.cancel)
                .positiveText(R.string.dialog_ok)
                .cancelable(false)
                .onPositive((dialog, which) ->{
                    if(!articleListDialog.getList().isEmpty())   sendData(false);
                    articleListDialog.getList().clear();

                    finish();
                    dialog.dismiss();
                    intent = new Intent(this, InvPatrimoineActivity.class);
                    //   intent.putExtra(Globals.TICKET_TO_UPDATE, item);
                    intent.putExtra(Globals.UPDATE_TICKET, false);
                    intent.putExtra(Globals.CLT_CODE, client.getcLICode());

                    intent.putExtra(Globals.PAT_FROM, getString(R.string.affectation_title));

                    MainActivity.instance.startActivityForResult(intent, REQUEST_TICKET_CODE);
                } )
                .onNegative((dialog, which) -> save.setEnabled(true))
                .show();
        Toasty.error(context,getString(R.string.pat_pas_affecter)).show();
    }
}



