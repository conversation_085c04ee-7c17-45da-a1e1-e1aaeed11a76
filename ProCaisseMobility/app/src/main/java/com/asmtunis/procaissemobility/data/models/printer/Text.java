package com.asmtunis.procaissemobility.data.models.printer;

/**
 * 文本配置参数
 *
 * <AUTHOR>
 */
public class Text {

    // print the content type
    private int type;
    // Alignment left, center, right
    private int format;
    // The number of empty lines
    private int line;
    // print the text content
    private String text;
    // text font size
    private int size;
    // whether the text is bold
    private boolean bold;
    // The text is underlined
    private boolean underline;

    public Text(int type, int format, int line, String text, int size, boolean bold, boolean underline) {
        this.type = type;
        this.format = format;
        this.line = line;
        this.text = text;
        this.size = size;
        this.bold = bold;
        this.underline = underline;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getFormat() {
        return format;
    }

    public void setFormat(int format) {
        this.format = format;
    }

    public int getLine() {
        return line;
    }

    public void setLine(int line) {
        this.line = line;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public boolean isBold() {
        return bold;
    }

    public void setBold(boolean bold) {
        this.bold = bold;
    }

    public boolean isUnderline() {
        return underline;
    }

    public void setUnderline(boolean underline) {
        this.underline = underline;
    }
}
