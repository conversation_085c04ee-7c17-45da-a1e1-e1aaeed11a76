package com.asmtunis.procaissemobility.adapters.items;

import android.content.Context;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.RecyclerView;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.data.models.EtatOrdreMission;
import com.asmtunis.procaissemobility.data.models.OrdreMission;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.listener.ItemCallback;
import com.asmtunis.procaissemobility.listener.MenuItemsAction;
import com.blankj.utilcode.util.ObjectUtils;
import com.mikepenz.fastadapter.items.AbstractItem;

import java.util.List;



public class OrdreMissionItem extends AbstractItem<OrdreMissionItem, OrdreMissionItem.ViewHolder> {

    private final Context context;
    private OrdreMission ordreMission;
    protected ItemCallback itemCallback;
    MenuItemsAction menuItemsAction;

    public OrdreMissionItem(Context context, OrdreMission ordreMission, ItemCallback itemCallback, MenuItemsAction menuItemsAction) {
        this.ordreMission = ordreMission;
        this.context = context;
        this.menuItemsAction = menuItemsAction;
        this.itemCallback = itemCallback;
    }

   public void setItemCallback(ItemCallback itemCallback) {
        this.itemCallback = itemCallback;
    }


    public OrdreMission getLigneOrdreMission() {
        return ordreMission;
    }


    @Override
    public int getType() {
        return R.id.fastadapter_ticket_item_id;
    }

    @Override
    public int getLayoutRes() {
        return R.layout.ticket_item;
    }


    void setText(AppCompatTextView editText, String value) {
        if (!StringUtils.isEmptyString(value)) {
            editText.setVisibility(View.VISIBLE);
            editText.setText(value);
        } else {
            editText.setVisibility(View.GONE);
        }
    }

    @Override
    public void bindView(@NonNull final ViewHolder viewHolder, @NonNull List<Object> payloads) {
        super.bindView(viewHolder, payloads);

        if (itemCallback != null) {
            //   holder.itemView.setOnClickListener(v -> onViewClick(holder));

            viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onViewClick(viewHolder);
                }
            });
            viewHolder.toolbar.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onViewClick(viewHolder);
                }
            });
        }

        viewHolder.code.setText("Code : " +ordreMission.oRDCode);
        viewHolder.date.setText(ordreMission.oRDDate);

        viewHolder.tiketuserImV.setVisibility(View.GONE);
        viewHolder.articleConcurrent.setVisibility(View.GONE);
        viewHolder.typeCommunication.setVisibility(View.INVISIBLE);
    }

   /* void onViewClick(OrdreMissionItem.ViewHolder viewHolder) {
        if (itemCallback != null) {
            itemCallback.onItemClicked(viewHolder);
        }
    }*/


    void onViewClick(OrdreMissionItem.ViewHolder viewHolder) {
        if (ordreMission != null) {
            if (itemCallback == null) {
                return;
            } else {
                itemCallback.onItemClicked(viewHolder, ordreMission);
            }

        }
    }

    @Override
    public void unbindView(@NonNull final ViewHolder holder) {
        super.unbindView(holder);
    }

    // Init the viewHolder for this Item
    @NonNull
    @Override
    public ViewHolder getViewHolder(@NonNull View v) {
        return new ViewHolder(v);
    }

    void setTriangleView(jp.shts.android.library.TriangleLabelView labelView, EtatOrdreMission status) {
        labelView.setVisibility(View.VISIBLE);
        if (ObjectUtils.isNotEmpty(status)) {
            if (ObjectUtils.isNotEmpty(status.getCouleur())) {
                labelView.setTriangleBackgroundColor(status.getCouleur());
            }
            labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
            if (ObjectUtils.isNotEmpty(status.getLibelleEtatOrd())) {
                labelView.setPrimaryText(status.getLibelleEtatOrd());
            }
            labelView.setPrimaryTextColorResource(R.color.md_red_100);
        }
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        TextView code;
        TextView articleConcurrent;
        TextView article;
        TextView typeCommunication;
        TextView prixConcurrent;
        TextView date;
        ImageView tiketuserImV;

        LinearLayout footerLayout;
        public Toolbar toolbar;
        FrameLayout content;
        com.asmtunis.procaissemobility.ui.components.TicketView  ticketView;
        jp.shts.android.library.TriangleLabelView itemStatusLabel;


        public ViewHolder(View view) {
            super(view);
            code = itemView.findViewById(R.id.code_prix);
            articleConcurrent= itemView.findViewById(R.id.article_concurrent_prix);
            article= itemView.findViewById(R.id.article_prix);
            typeCommunication =  itemView.findViewById(R.id.type_communication_prix);
            prixConcurrent =  itemView.findViewById(R.id.price_concurrent_prix);
            date   =  itemView.findViewById(R.id.date_promo_prix);




            ticketView = itemView.findViewById(R.id.layout_ticket);
            footerLayout = itemView.findViewById(R.id.footer_layout);
            toolbar = itemView.findViewById(R.id.toolbar);
            typeCommunication = itemView.findViewById(R.id.price);
            code = itemView.findViewById(R.id.ticketNumber);
            articleConcurrent = itemView.findViewById(R.id.ticketUser);
            date = itemView.findViewById(R.id.dateCreation);
            content = itemView.findViewById(R.id.content_layout);
            itemStatusLabel = itemView.findViewById(R.id.item_status_label);
            tiketuserImV = itemView.findViewById(R.id.tiketuserImV);
        }
    }
}









