package com.asmtunis.procaissemobility.data.models;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;
import androidx.annotation.NonNull;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * Created by Achraf on 29/09/2017.
 */
@Entity
public class Caisse  extends BaseModel{
    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "CAI_IdCaisse")
    @SerializedName("CAI_IdCaisse")
    @Expose
    public String cAIIdCaisse;
    @NonNull
    @ColumnInfo(name = "CAI_DesCaisse")
    @SerializedName("CAI_DesCaisse")
    @Expose
    public String cAIDesCaisse;
    @NonNull
    @ColumnInfo(name = "CAI_Station")
    @SerializedName("CAI_Station")
    @Expose
    public String cAIStation;
    @NonNull
    @ColumnInfo(name = "CAI_UserStation")
    @SerializedName("CAI_UserStation")
    @Expose
    public String cAIUserStation;
    @NonNull
    @ColumnInfo(name = "CAI_User")
    @SerializedName("CAI_User")
    @Expose
    public String cAIUser;
    @NonNull
    @ColumnInfo(name = "CAI_export")
    @SerializedName("CAI_export")
    @Expose
    public int cAIExport;
    @NonNull
    @ColumnInfo(name = "CAI_DDm")
    @SerializedName("CAI_DDm")
    @Expose
    public String cAIDDm;

    public Caisse() {
    }

    @Ignore
    public Caisse(String cAIIdCaisse, String cAIDesCaisse, String cAIStation, String cAIUserStation, String cAIUser, int cAIExport, String cAIDDm) {
        this.cAIIdCaisse = cAIIdCaisse;
        this.cAIDesCaisse = cAIDesCaisse;
        this.cAIStation = cAIStation;
        this.cAIUserStation = cAIUserStation;
        this.cAIUser = cAIUser;
        this.cAIExport = cAIExport;
        this.cAIDDm = cAIDDm;
    }

    public String getcAIIdCaisse() {
        return cAIIdCaisse;
    }

    public void setcAIIdCaisse(String cAIIdCaisse) {
        this.cAIIdCaisse = cAIIdCaisse;
    }

    public String getcAIDesCaisse() {
        return cAIDesCaisse;
    }

    public void setcAIDesCaisse(String cAIDesCaisse) {
        this.cAIDesCaisse = cAIDesCaisse;
    }

    public String getcAIStation() {
        return cAIStation;
    }

    public void setcAIStation(String cAIStation) {
        this.cAIStation = cAIStation;
    }

    public String getcAIUserStation() {
        return cAIUserStation;
    }

    public void setcAIUserStation(String cAIUserStation) {
        this.cAIUserStation = cAIUserStation;
    }

    public String getcAIUser() {
        return cAIUser;
    }

    public void setcAIUser(String cAIUser) {
        this.cAIUser = cAIUser;
    }

    public int getcAIExport() {
        return cAIExport;
    }

    public void setcAIExport(int cAIExport) {
        this.cAIExport = cAIExport;
    }

    public String getcAIDDm() {
        return cAIDDm;
    }

    public void setcAIDDm(String cAIDDm) {
        this.cAIDDm = cAIDDm;
    }

    @Override
    public String toString() {
        return "Caisse{" +
                "cAIIdCaisse='" + cAIIdCaisse + '\'' +
                ", cAIDesCaisse='" + cAIDesCaisse + '\'' +
                ", cAIStation='" + cAIStation + '\'' +
                ", cAIUserStation='" + cAIUserStation + '\'' +
                ", cAIUser='" + cAIUser + '\'' +
                ", cAIExport='" + cAIExport + '\'' +
                ", cAIDDm=" + cAIDDm +
                '}';
    }
}
