package com.asmtunis.procaissemobility.data.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.Marque;

import java.util.List;


@Dao
public interface MarqueDAO {

    @Query("SELECT * FROM Marque")
    List<Marque> getAll();


    @Query("SELECT * FROM Marque where MAR_Code =:code ")
    Marque getByCode(String code);
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<Marque> items);


    @Query("DELETE FROM Marque")
    void deleteAll();
}
