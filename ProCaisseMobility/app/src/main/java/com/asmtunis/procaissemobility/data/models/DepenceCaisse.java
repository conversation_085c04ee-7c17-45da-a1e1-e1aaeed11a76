package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

@Entity
public class DepenceCaisse extends BaseModel implements Serializable {
    @ColumnInfo(name = "DEP_Code")
    @SerializedName("DEP_Code")
    @Expose
    private String depCode;
    @ColumnInfo(name = "DEP_SC_IdSCession")
    @SerializedName("DEP_SC_IdSCession")
    @Expose
    private String depSCIdSCession;
    @ColumnInfo(name = "DEP_SC_Caisse")
    @SerializedName("DEP_SC_Caisse")
    @Expose
    private String depSCCaisse;
    @ColumnInfo(name = "DEP_User")
    @SerializedName("DEP_User")
    @Expose
    private String depUser;
    @ColumnInfo(name = "DEP_Montant")
    @SerializedName("DEP_Montant")
    @Expose
    private String depMontant;
    @ColumnInfo(name = "DEP_descrip")
    @SerializedName("DEP_descrip")
    @Expose
    private String depDescrip;
    @ColumnInfo(name = "DEP_export")
    @SerializedName("DEP_export")
    @Expose
    private String depExport;
    @ColumnInfo(name = "DEP_DDm")
    @SerializedName("DEP_DDm")
    @Expose
    private String depDDm;
    @ColumnInfo(name = "DDm")
    @SerializedName("DDm")
    @Expose
    private String dDm;
    @ColumnInfo(name = "export")
    @SerializedName("export")
    @Expose
    private boolean export;
    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "DEP_Code_M")
    @SerializedName("DEP_Code_M")
    @Expose
    private String depCodeM;

    public DepenceCaisse() {
    }

    public DepenceCaisse(String depSCCaisse, String dDm) {
        this.depSCCaisse = depSCCaisse;
        this.dDm = dDm;
    }

    public DepenceCaisse(String depCode, String depSCIdSCession, String depSCCaisse, String depUser, String depMontant, String depDescrip, String depExport, String depDDm, String dDm, boolean export,@NonNull String depCodeM,boolean isSync, String status) {
        this.depCode = depCode;
        this.depSCIdSCession = depSCIdSCession;
        this.depSCCaisse = depSCCaisse;
        this.depUser = depUser;
        this.depMontant = depMontant;
        this.depDescrip = depDescrip;
        this.depExport = depExport;
        this.depDDm = depDDm;
        this.dDm = dDm;
        this.export = export;
        this.depCodeM=depCodeM;
        this.isSync = isSync;
        this.status = status;
    }

    @NonNull
    public String getDepCodeM() {
        return depCodeM;
    }

    public void setDepCodeM(@NonNull String depCodeM) {
        this.depCodeM = depCodeM;
    }

    public String getDepCode() {
        return depCode;
    }

    public void setDepCode(String depCode) {
        this.depCode = depCode;
    }

    public String getDepSCIdSCession() {
        return depSCIdSCession;
    }

    public void setDepSCIdSCession(String depSCIdSCession) {
        this.depSCIdSCession = depSCIdSCession;
    }

    public String getDepSCCaisse() {
        return depSCCaisse;
    }

    public void setDepSCCaisse(String depSCCaisse) {
        this.depSCCaisse = depSCCaisse;
    }

    public String getDepUser() {
        return depUser;
    }

    public void setDepUser(String depUser) {
        this.depUser = depUser;
    }

    public String getDepMontant() {
        return depMontant;
    }

    public void setDepMontant(String depMontant) {
        this.depMontant = depMontant;
    }

    public String getDepDescrip() {
        return depDescrip;
    }

    public void setDepDescrip(String depDescrip) {
        this.depDescrip = depDescrip;
    }

    public String getDepExport() {
        return depExport;
    }

    public void setDepExport(String depExport) {
        this.depExport = depExport;
    }


    public String getDepDDm() {
        return depDDm;
    }

    public void setDepDDm(String depDDm) {
        this.depDDm = depDDm;
    }

    public String getDDm() {
        return dDm;
    }

    public void setDDm(String dDm) {
        this.dDm = dDm;
    }

    public boolean isExport() {
        return export;
    }

    public void setExport(boolean export) {
        this.export = export;
    }

    /*
        public int getDepCode1() {
            return depCode1;
        }

        public void setDepCode1(int depCode1) {
            this.depCode1 = depCode1;
        }
    */
    @Override
    public String toString() {
        return "DepenceCaisse{" +
                "depCode='" + depCode + '\'' +
                ", depSCIdSCession='" + depSCIdSCession + '\'' +
                ", depSCCaisse='" + depSCCaisse + '\'' +
                ", depUser='" + depUser + '\'' +
                ", depMontant='" + depMontant + '\'' +
                ", depDescrip='" + depDescrip + '\'' +
                ", depExport='" + depExport + '\'' +
                ", depDDm=" + depDDm +
                ", issync=" + isSync +
                ", status=" + status +
            //    ", dDm=" + dDm +
                ", export=" + export +
            //    ", depCode1=" + depCode1 +
                '}';
    }
}