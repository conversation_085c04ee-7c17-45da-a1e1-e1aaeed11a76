package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.VCAutre;
import com.asmtunis.procaissemobility.data.models.VCNewProduct;
import com.asmtunis.procaissemobility.data.models.VCPrix;

import java.util.List;

/**
 * Created by Oussama AZIZI on 6/24/22.
 */

@Dao
public interface VCAutreDAO {
    @Query("SELECT * FROM VCAutre where Status !='DELETED' order by strftime('%Y-%m-%d %H-%M-%S',DateOp) desc")
    LiveData<List<VCAutre>> getAll();

    @Query("SELECT * FROM VCAutre WHERE Code_Mob = :code")
    VCAutre getByCodeM(String code);

    @Query("SELECT * FROM VCAutre WHERE CodeAutre = :code")
    VCAutre getByCode(String code);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<VCAutre> vcAutres);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(VCAutre vcAutres);



    @Query("SELECT * FROM VCAutre where isSync=0 and  (Status='INSERTED'  or Status='UPDATED') ")
    List<VCAutre> getNoSyncedToAddOrUpdate();

    @Query("SELECT * FROM VCAutre where isSync=0 and Status='DELETED' ")
    List<VCAutre> getNoSyncedToDelete();


    @Query("SELECT count(*) FROM VCAutre where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    int getCountNonSync();

    @Query("SELECT count(*) FROM VCAutre where isSync=0 and Status='DELETED' ")
    int getCountNoSyncedToDelete();

    @Query("SELECT count(*) FROM VCAutre where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    LiveData<Integer> getNoSyncCountMubtale();

    @Query("SELECT count(*) FROM VCAutre where isSync=0 and Status='DELETED' ")
    LiveData<Integer> getCountNoSyncedToDeleteMubtale();

    @Query("delete from VCAutre")
    void deleteAll();

    @Query("DELETE FROM VCAutre where CodeAutre=:CodeAutre")
    void deleteById(String CodeAutre);

    @Query("DELETE FROM VCAutre where CodeAutre=:CodeAutre or Code_Mob = :CodeMobile")
    void deleteByIdAndCodeM(String CodeAutre, String CodeMobile);

    @Query("UPDATE VCAutre SET CodeAutre = :code_procaiss where Code_Mob = :CodeMobile")
  void updateCloudCode(String code_procaiss, String CodeMobile);
}
