package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Transaction;
import androidx.room.Update;

import com.asmtunis.procaissemobility.data.models.LigneOrdreMission;
import com.asmtunis.procaissemobility.data.models.OrdreWithLines;

import java.util.List;

@Dao
public interface OrdreWithLinesDAO {
    @Transaction
    @Query("select * from OrdreMission where ORD_Code not like 'ORD_LOCAL_MOBILE'")
    LiveData<List<OrdreWithLines>> getOrdresWithLinesMutable();

    @Transaction
    @Query("select * from OrdreMission")
    List<OrdreWithLines> getOrdresWithLines();

    @Transaction
    @Query("select * from OrdreMission where ORD_Code= :code")
    OrdreWithLines getOrdreWithLines(String code);

    @Query("select * from LigneOrdreMission where LIGOR_Code= :ordCode and LIGOR_Clt= :cliCode")
    LigneOrdreMission getLineWithOrdreAndClient(String ordCode, String cliCode);

    @Query("select * from LigneOrdreMission where LIGOR_Code= :ordCode")
    List<LigneOrdreMission> getLinesWithOrdre(String ordCode);

}
