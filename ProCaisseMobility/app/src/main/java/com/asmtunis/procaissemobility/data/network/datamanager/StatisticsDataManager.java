package com.asmtunis.procaissemobility.data.network.datamanager;


import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Statistics;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.StatisticService;

import java.util.List;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

/**
 * Created by PC on 10/4/2017.
 */

public class StatisticsDataManager {
    private static StatisticsDataManager sInstance;

    private final StatisticService mStatisticService;

    public StatisticsDataManager() {
        mStatisticService = new ServiceFactory<>(StatisticService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Statistics")).makeService();

    }

    public static StatisticsDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new StatisticsDataManager();
        }
        return sInstance;
    }

    public void getVenteStatistics(GenericObject genericObject,
                                                 RemoteCallback<Statistics> listener) {
        mStatisticService.getVenteStatistics(genericObject)
                .enqueue(listener);
    }

    public void getReglementStatistics(GenericObject genericObject,
                              RemoteCallback<Statistics> listener) {
        mStatisticService.getReglementStatistics(genericObject)
                .enqueue(listener);
    }


    public void getTopNClients(GenericObject genericObject,
                                       RemoteCallback<List<Client>> listener) {
        mStatisticService.getTopNClients(genericObject)
                .enqueue(listener);
    }




}

