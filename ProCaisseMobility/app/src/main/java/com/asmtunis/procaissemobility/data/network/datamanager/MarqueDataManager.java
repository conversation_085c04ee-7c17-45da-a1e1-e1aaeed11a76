package com.asmtunis.procaissemobility.data.network.datamanager;

import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Marque;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.MarqueService;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;
import com.asmtunis.procaissemobility.helper.utils.Utils;

import java.util.List;


public class MarqueDataManager {

    private static MarqueDataManager sInstance;

    private final MarqueService marqueDataManager;

    public MarqueDataManager() {
        marqueDataManager = new ServiceFactory<>(MarqueService.class, String.format(Utils.validateBaseUrl(), PrefUtils.getServerIPAddress(),PrefUtils.getServerPort(),"Marque")).makeService();


    }
    public static MarqueDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new MarqueDataManager();
        }
        return sInstance;
    }

    public void getMarques(GenericObject genericObject,
                                         RemoteCallback<List<Marque>> listener) {
        marqueDataManager.getMarques(genericObject)
                .enqueue(listener);
    }


}
