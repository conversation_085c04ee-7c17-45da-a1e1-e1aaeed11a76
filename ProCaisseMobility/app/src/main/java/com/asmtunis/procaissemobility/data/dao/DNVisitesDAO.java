package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.ChequeCaisse;
import com.asmtunis.procaissemobility.data.models.DNTypeServices;
import com.asmtunis.procaissemobility.data.models.DNVIsite;
import com.asmtunis.procaissemobility.data.models.VCNewProduct;
import com.asmtunis.procaissemobility.data.models.VCPrix;

import java.util.List;

@Dao
public interface DNVisitesDAO {

    @Query("SELECT * FROM DNVIsite where Status!='DELETED' order by strftime('%Y-%m-%d %H-%M-%S',VIS_DDM) desc")
        // List<DNVIsite> getAll();
    LiveData<List<DNVIsite>> getAll();


    @Query("SELECT * FROM DNVIsite where VIS_Num=:VIS_Num")
    DNVIsite getByNumViste(String VIS_Num);


    @Query("SELECT * FROM DNVIsite where VIS_Num=:VIS_Num and VIS_CodeClient=:codeclient")
    DNVIsite getByVisNumAndCodeClient(String VIS_Num, String codeclient);


    @Query("SELECT * FROM DNVIsite where VIS_CodeClient=:codeclient")
    List< DNVIsite> getByCodeClient(String codeclient);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<DNVIsite> dnVisites);

    @Query("SELECT * FROM DNVIsite WHERE VIS_Code_M = :code")
    DNVIsite getByCodeM(String code);


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(DNVIsite dnVisite);



    @Query("SELECT * FROM DNVIsite where isSync=0 and  (Status='INSERTED'  or Status='UPDATED') ")
    List<DNVIsite> getNoSyncedToAddOrUpdate();

    @Query("SELECT * FROM DNVIsite where isSync=0 and Status='DELETED' ")
    List<DNVIsite> getNoSyncedToDelete();

    @Query("SELECT count(*) FROM DNVIsite where isSync=0 and  (Status='INSERTED'  or Status='UPDATED' or Status='DELETED')")
    int getCountNonSync();


    @Query("SELECT count(*) FROM DNVIsite where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    LiveData<Integer> getNoSyncCountMubtale();

    @Query("SELECT count(*) FROM DNVIsite where isSync=0 and Status='DELETED'")
    LiveData<Integer> getNoSynctoDeleteCountMubtale();


    @Query("delete from DNVIsite")
    void deleteAll();

    @Query("DELETE FROM DNVIsite where VIS_Code_M=:VIS_Code_M")
    void deleteById(String VIS_Code_M);



    @Query("SELECT * FROM DNVIsite where VIS_CodeClient=:clientCode and Status!='DELETED' order by strftime('%Y-%m-%d %H-%M-%S',VIS_DDM) desc")
    List<DNVIsite> getByClientCode(String clientCode);



    @Query("UPDATE DNVIsite SET VIS_CodeClient = :code_client where VIS_CodeClient = :oldCodeClient")
    void updateCodeClient(String code_client, String oldCodeClient);
}
