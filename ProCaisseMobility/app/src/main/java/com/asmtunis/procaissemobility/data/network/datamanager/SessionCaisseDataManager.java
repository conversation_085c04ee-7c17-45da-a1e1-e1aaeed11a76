package com.asmtunis.procaissemobility.data.network.datamanager;


import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.SessionCaisse;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.SessionCaisseService;

import java.util.List;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

/**
 * Created by Achraf on 29/09/2017.
 */

public class SessionCaisseDataManager  {

    private static SessionCaisseDataManager sInstance;

    private final SessionCaisseService mSessionCaisseService;

    public SessionCaisseDataManager() {
        mSessionCaisseService = new ServiceFactory<>(SessionCaisseService.class,  String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"SessionCaisse")).makeService();

    }

    public static SessionCaisseDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new SessionCaisseDataManager();
        }
        return sInstance;
    }

    public void getSessionCaisses(
            GenericObject genericObject,
            RemoteCallback<List<SessionCaisse>> listener) {
        mSessionCaisseService.getSessionCaisses(genericObject)
                .enqueue(listener);
    }

    public void addSessionCaisse(SessionCaisse sessionCaisse,
                                 RemoteCallback<String> listener) {
        mSessionCaisseService.addSessionCaisse(sessionCaisse)
                .enqueue(listener);
    }


    public void addSessionVendeur(GenericObject genericObject,
                                  RemoteCallback<SessionCaisse> listener) {
        mSessionCaisseService.addSessionVendeur(genericObject)
                .enqueue(listener);
    }

    public void closeSessionVendeur(GenericObject genericObject,
                                  RemoteCallback<Boolean> listener) {
        mSessionCaisseService.closeSessionVendeur(genericObject, true)
                .enqueue(listener);
    }

    public void getSessionCaisseByUser(GenericObject genericObject,
                                       RemoteCallback<SessionCaisse> listener) {
        mSessionCaisseService.getSessionCaisseByUser(genericObject)
                .enqueue(listener);
    }

}
