package com.asmtunis.procaissemobility.data.network.datamanager;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Facture;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Ticket;
import com.asmtunis.procaissemobility.data.models.TicketUpdate;
import com.asmtunis.procaissemobility.data.models.TicketWithLinesAndPayments;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.FactureService;
import com.asmtunis.procaissemobility.data.network.services.TicketService;

import java.util.List;

/**
 * Created by Oussama AZIZI on 3/11/22.
 */

public class FactureDataManager {
    private static FactureDataManager sInstance;

    private final FactureService mFactureService;

    public FactureDataManager() {
        mFactureService = new ServiceFactory<>(FactureService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"facture")).makeService();
    }

    public static FactureDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new FactureDataManager();
        }
        return sInstance;
    }


    public void addBatchFactureWithLines(GenericObject ticketWithLinesAndPayments,
                                                         RemoteCallback<List<TicketUpdate>> listener) {

        mFactureService.addBatchFactureWithLines(ticketWithLinesAndPayments).enqueue(listener);}


    public void getFacture(GenericObject genericObject, RemoteCallback<List<Facture>> listener) {

        mFactureService.getFacture(genericObject).enqueue(listener);}


}
