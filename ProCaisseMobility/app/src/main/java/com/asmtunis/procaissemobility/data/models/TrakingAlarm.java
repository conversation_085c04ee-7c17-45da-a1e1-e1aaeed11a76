package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;


@Entity
public class TrakingAlarm {

    @PrimaryKey(autoGenerate = true)
    @NonNull
    @ColumnInfo(name = "CodeAlarm")
    public Integer codeAlarm;

    @ColumnInfo(name = "startIDAlarm")
    public String startAlarmID;

    @ColumnInfo(name = "endIDAlarm")
    public String endAlarmID;

    @ColumnInfo(name = "CodeOrdreMission")
    public String codeOrdreMission;

    @ColumnInfo(name = "StartDate")
    public String startDate;

    @ColumnInfo(name = "EndDate")
    public String endDate;

    @ColumnInfo(name = "Started")
    public Boolean started;

    @ColumnInfo(name = "Ended")
    public Boolean ended;

    @ColumnInfo(name = "Setted")
    public Boolean setted;

    public TrakingAlarm(String codeOrdreMission, String startDate, String endDate, Boolean started, <PERSON><PERSON><PERSON> ended, <PERSON><PERSON><PERSON> setted) {
        this.codeOrdreMission = codeOrdreMission;
        this.startDate = startDate;
        this.endDate = endDate;
        this.started = started;
        this.ended = ended;
        this.setted = setted;
    }
}
