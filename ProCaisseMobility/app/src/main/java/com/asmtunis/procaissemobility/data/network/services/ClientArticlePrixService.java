package com.asmtunis.procaissemobility.data.network.services;

import com.asmtunis.procaissemobility.data.models.ClientArticlePrix;
import com.asmtunis.procaissemobility.data.models.GenericObject;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * Created by Oussama AZIZI on 3/9/22.
 */

public interface ClientArticlePrixService {
    @POST("getArticleClientPrix")
    Call<List<ClientArticlePrix>> getClientArticlePrix(@Body GenericObject genericObject);
}
