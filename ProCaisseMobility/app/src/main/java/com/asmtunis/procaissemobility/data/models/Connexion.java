package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.asmtunis.procaissemobility.App;
import com.blankj.utilcode.util.DeviceUtils;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * Created by PC on 7/20/2017.
 */

@Entity(primaryKeys = {"id_base_config", "password"})
public class Connexion implements Serializable {

    @NonNull
    @ColumnInfo(name = "id_base_config")
    @SerializedName("id_base_config")
    @Expose
    private String idBaseConfig;
    @ColumnInfo(name = "key_base")
    @SerializedName("key_base")
    @Expose
    private String keyBase;
    @ColumnInfo(name = "dbName")
    @SerializedName("dbName")
    @Expose
    private String dbName;
    @ColumnInfo(name = "dbIpAddress")
    @SerializedName("dbIpAddress")
    @Expose
    private String dbIpAddress;
    @ColumnInfo(name = "adresse_ip")
    @SerializedName("adresse_ip")
    @Expose
    private String adresseIp;
    @ColumnInfo(name = "port")
    @SerializedName("port")
    @Expose
    private String port;
    @ColumnInfo(name = "username")
    @SerializedName("username")
    @Expose
    private String username;


    @NonNull
    @ColumnInfo(name = "password")
    @SerializedName("password")
    @Expose
    private String password;
    @ColumnInfo(name = "designation_base")
    @SerializedName("designation_base")
    @Expose
    private String designationBase;
    @ColumnInfo(name = "produit")
    @SerializedName("produit")
    @Expose
    private String produit;
    @ColumnInfo(name = "id_entreprise")
    @SerializedName("id_entreprise")
    @Expose
    private String idEntreprise;
    @ColumnInfo(name = "date_creation")
    @SerializedName("date_creation")
    @Expose
    private String dateCreation;

    public String getDeviceID() {
        return deviceID;
    }

    public void setDeviceID(String deviceID) {
        this.deviceID = deviceID;
    }

    @ColumnInfo(name = "device_id")
    @SerializedName("device_id")
    @Expose
    private String deviceID = DeviceUtils.getAndroidID();

    public Connexion(){
    }

    public Connexion(String idBaseConfig, String keyBase, String dbName, String dbIpAddress, String adresseIp, String port, String username, String password, String designationBase, String produit, String idEntreprise, String dateCreation) {
        this.idBaseConfig = idBaseConfig;
        this.keyBase = keyBase;
        this.dbName = dbName;
        this.dbIpAddress = dbIpAddress;
        this.adresseIp = adresseIp;
        this.port = port;
        this.username = username;
        this.password = password;
        this.designationBase = designationBase;
        this.produit = produit;
        this.idEntreprise = idEntreprise;
        this.dateCreation = dateCreation;
    }

    public Connexion(String idBaseConfig, String designationBase, String keyBase, String dbName, String adresseIp, String port, String dbIpAddress, String username, String password) {
        this.idBaseConfig = idBaseConfig;
        this.keyBase = keyBase;
        this.dbName = dbName;
        this.dbIpAddress = dbIpAddress;
        this.adresseIp = adresseIp;
        this.port = port;
        this.username = username;
        this.password = password;
        this.designationBase = designationBase;
    }

    public String getIdBaseConfig() {
        return idBaseConfig;
    }

    public void setIdBaseConfig(String idBaseConfig) {
        this.idBaseConfig = idBaseConfig;
    }

    public String getKeyBase() {
        return keyBase;
    }

    public void setKeyBase(String keyBase) {
        this.keyBase = keyBase;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getDbIpAddress() {
        return dbIpAddress;
    }

    public void setDbIpAddress(String dbIpAddress) {
        this.dbIpAddress = dbIpAddress;
    }

    public String getAdresseIp() {
        return adresseIp;
    }

    public void setAdresseIp(String adresseIp) {
        this.adresseIp = adresseIp;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getDesignationBase() {
        return designationBase;
    }

    public void setDesignationBase(String designationBase) {
        this.designationBase = designationBase;
    }

    public String getProduit() {
        return produit;
    }

    public void setProduit(String produit) {
        this.produit = produit;
    }

    public String getIdEntreprise() {
        return idEntreprise;
    }

    public void setIdEntreprise(String idEntreprise) {
        this.idEntreprise = idEntreprise;
    }

    public String getDateCreation() {
        return dateCreation;
    }

    public void setDateCreation(String dateCreation) {
        this.dateCreation = dateCreation;
    }

    @Override
    public String toString() {
        return "Connexion{" +
                "idBaseConfig='" + idBaseConfig + '\'' +
                ", keyBase='" + keyBase + '\'' +
                ", dbName='" + dbName + '\'' +
                ", dbIpAddress='" + dbIpAddress + '\'' +
                ", adresseIp='" + adresseIp + '\'' +
                ", port='" + port + '\'' +
                ", username='" + username + '\'' +
                ", password='" + password + '\'' +
                ", designationBase='" + designationBase + '\'' +
                ", produit='" + produit + '\'' +
                ", idEntreprise='" + idEntreprise + '\'' +
                ", dateCreation='" + dateCreation + '\'' +
                '}';
    }
}