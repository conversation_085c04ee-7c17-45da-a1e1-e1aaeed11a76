package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.Article;
import com.asmtunis.procaissemobility.data.models.ReglementCaisse;
import com.asmtunis.procaissemobility.data.models.custom.Client;

import java.util.List;

/**
 * Created by PC on 11/18/2017.
 */
@Dao
public interface StatisticsDAO {

    @Query("SELECT   SUM(TIK_MtTTC) AS CA FROM Ticket\n" + "WHERE (TIK_IdSCaisse = :idSCaisse) AND (ifnull(TIK_Annuler, 'False') <> 'True')")
    double getCA(String idSCaisse);

    @Query("SELECT COUNT(TIK_NumTicket) AS NbreTicket FROM Ticket WHERE (TIK_IdSCaisse = :idSCaisse) AND (ifnull(TIK_Annuler, 'False') <> 'True')")
    String getTicketNumber(String idSCaisse);

    @Query("SELECT SUM(SART_Qte) AS ValeurStock FROM StationStock where SART_CodeSatation=:station and SART_Qte > 0")
    Double getValeurStockByStation(String station);

    @Query("SELECT SUM(SART_Qte) AS ValeurStock FROM Article where SART_CodeSatation=:station and SART_Qte > 0")
    Double getValeurStockByStationFromArtTable(String station);


    @Query("SELECT * FROM Article where SART_CodeSatation=:station and SART_Qte > 0")
    List<Article> getListArtByStationFromArtTable(String station);

    @Query("SELECT SUM(ifnull(REGC_MntEspece,0))  FROM ReglementCaisse where REGC_IdStation= :station and REGC_IdSCaisse= :caisse")
    String getMntEspece(String station,String caisse);

    @Query("SELECT SUM(ifnull(REGC_MntCheque,0))  FROM ReglementCaisse where REGC_IdStation= :station and REGC_IdSCaisse= :caisse")
    String getMntCheque(String station,String caisse);

    @Query("SELECT SUM(ifnull(REGC_MntTraite,0)) FROM ReglementCaisse where REGC_IdStation= :station and REGC_IdSCaisse= :caisse")
    String getMntTraite(String station,String caisse);

    @Query(" select ifnull(SUM(TIK_MtTTC)*(-1),0) as Mnt_Credit from Ticket where TIK_Etat='Credit' and TIK_IdSCaisse= :caisse")
    String getMntCredit(String caisse);

    @Query("SELECT SUM(ifnull(REGC_Montant,0)) FROM ReglementCaisse where REGC_IdSCaisse= :session and REGC_Station= :station  and REGC_Remarque= :regRemarque")
    String getbyRegRemarque(String session, String station, String regRemarque);




    @Query("SELECT   SUM(TIK_MtTTC) AS CA, TIK_CodClt as CLI_Code,TIK_NomClient as CLI_NomPren from Ticket WHERE    (TIK_IdSCaisse = :idSCaisse) AND   (ifnull(TIK_Annuler, 'False') <> 'True') group by TIK_CodClt,TIK_NomClient order by SUM(TIK_MtTTC) desc LIMIT :number")
    List<Client> getNClients(String idSCaisse, int number);


}
