package com.asmtunis.procaissemobility.adapters.items;

import android.content.Context;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.Toolbar;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelStoreOwner;
import androidx.recyclerview.widget.RecyclerView;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.data.models.VCNewProduct;
import com.asmtunis.procaissemobility.data.viewModels.VCListConcurrentViewModel;
import com.asmtunis.procaissemobility.data.viewModels.VCTypeCommunicationViewModel;
import com.asmtunis.procaissemobility.helper.utils.DateUtils;
import com.asmtunis.procaissemobility.listener.ItemCallback;
import com.asmtunis.procaissemobility.listener.MenuItemsAction;
import com.mikepenz.fastadapter.items.AbstractItem;

import java.util.List;

public class VCNouveauProduitAdapter extends AbstractItem<VCNouveauProduitAdapter, VCNouveauProduitAdapter.VCViewHolder> {

    private Context context;
    public VCNewProduct vcNewProduct;
    protected ItemCallback itemCallback;
    MenuItemsAction menuItemsAction;
    VCListConcurrentViewModel vcListConcurrentViewModel;
    VCTypeCommunicationViewModel vcTypeCommunicationViewModel;

    public VCNouveauProduitAdapter(Context context, VCNewProduct vcNewProduct, ViewModelStoreOwner viewModelStoreOwner, ItemCallback itemCallback, MenuItemsAction menuItemsAction) {
        this.context = context;
        this.vcNewProduct = vcNewProduct;
        this.itemCallback = itemCallback;
        this.menuItemsAction=menuItemsAction;
        vcListConcurrentViewModel=new ViewModelProvider(viewModelStoreOwner).get(VCListConcurrentViewModel.class);
        vcTypeCommunicationViewModel=new ViewModelProvider(viewModelStoreOwner).get(VCTypeCommunicationViewModel.class);

    }

    public VCNewProduct getVcNewProduct() {
        return vcNewProduct;
    }

    public void setVcNewProduct(VCNewProduct vcNewProduct) {
        this.vcNewProduct = vcNewProduct;
    }

    @NonNull
    @Override
    public VCViewHolder getViewHolder(View v) {
        return new VCViewHolder(v);
    }


    @Override
    public int getType() {
        return R.id.fastadapter_ticket_item_id;
    }

    @Override
    public int getLayoutRes() {
        return R.layout.ticket_item;
    }

    @Override
    public void bindView(VCViewHolder holder, List<Object> payloads) {
        super.bindView(holder, payloads);
        holder.codeTV.setText(vcNewProduct.getCodeVCLanP());
      //  holder.productTV.setText(vcNewProduct.getProduitLanP());
        holder.tiketuserImV.setVisibility(View.INVISIBLE);
       holder.concurrentTV.setText("Article Concurrent : "+vcListConcurrentViewModel.getConcurrent(vcNewProduct.getCodeConcur()));
        holder.typeCommunicationTV.setText(vcTypeCommunicationViewModel.getTypeCommunicationByCode(vcNewProduct.getCodeTypeCom()));
//        holder.priceTV.setText(String.valueOf(vcNewProduct.getPrixLanP()));
      //  holder.totalTV.setText(String.valueOf(vcNewProduct.getTauxPromo()));
        holder.dateTV.setText(vcNewProduct.getDateOp().replace(".000",""));

        if (itemCallback != null) {
        //    holder.itemView.setOnClickListener(v -> onViewClick(holder));

            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onViewClick(holder);
                }
            });
            holder.toolbar.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onViewClick(holder);
                }
            });
        }


        if(!vcNewProduct.isSync) {
            holder.dateTV.setTextColor(context.getResources().getColor(R.color.warningColor));
            setTriangleView(holder.itemStatusLabel, 0);
        }
        else  {
            setTriangleView(holder.itemStatusLabel, -1);

            holder.dateTV.setTextColor(context.getResources().getColor(R.color.successColor));
        }
    }


    public class VCViewHolder extends RecyclerView.ViewHolder {
        TextView codeTV;
        TextView productTV;
        TextView concurrentTV;
        TextView typeCommunicationTV;
        TextView priceTV;
        TextView totalTV;
        TextView dateTV;
        ImageView tiketuserImV;

        LinearLayout footerLayout;
        public Toolbar toolbar;
        FrameLayout content;
        com.asmtunis.procaissemobility.ui.components.TicketView  ticketView;
        jp.shts.android.library.TriangleLabelView itemStatusLabel;

        public VCViewHolder(@NonNull View itemView) {
            super(itemView);
      /*      codeTV = itemView.findViewById(R.id.code);
            productTV = itemView.findViewById(R.id.product);
            concurrentTV = itemView.findViewById(R.id.concurrent);
            typeCommunicationTV=itemView.findViewById(R.id.type_communication_np);
            priceTV = itemView.findViewById(R.id.price);
            totalTV = itemView.findViewById(R.id.total);
            dateTV = itemView.findViewById(R.id.date);
*/



            ticketView = itemView.findViewById(R.id.layout_ticket);
            footerLayout = itemView.findViewById(R.id.footer_layout);
            toolbar = itemView.findViewById(R.id.toolbar);
            typeCommunicationTV = itemView.findViewById(R.id.price);
            codeTV = itemView.findViewById(R.id.ticketNumber);
            concurrentTV = itemView.findViewById(R.id.ticketUser);
            dateTV = itemView.findViewById(R.id.dateCreation);
            content = itemView.findViewById(R.id.content_layout);
            itemStatusLabel = itemView.findViewById(R.id.item_status_label);
            tiketuserImV = itemView.findViewById(R.id.tiketuserImV);
        }
    }

    void onViewClick(VCViewHolder viewHolder) {
        if (vcNewProduct != null) {
            if (itemCallback == null) {
                return;
            } else {
                 itemCallback.onItemClicked(viewHolder, vcNewProduct);

            }

        }
    }



    void setTriangleView(jp.shts.android.library.TriangleLabelView labelView, int status) {
        labelView.setVisibility(View.VISIBLE);
        switch (status) {
            case 0:
                labelView.setTriangleBackgroundColorResource(R.color.warningColor);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.notSync);
                labelView.setPrimaryTextColorResource(R.color.md_red_100);

                break;

            case 1:
                labelView.setTriangleBackgroundColorResource(R.color.md_green_800);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.new_label);
                labelView.setPrimaryTextColorResource(R.color.md_green_100);
                break;
            default:
                labelView.setVisibility(View.GONE);

                break;
        }

    }

}
