package com.asmtunis.procaissemobility.data.network.datamanager;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Traking;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.TrakingService;
import java.util.List;

public class TrakingDataManager {
    private static TrakingDataManager sInstance;
    private final TrakingService mTrakingService;

    public TrakingDataManager() {
        this.mTrakingService = new ServiceFactory<>(TrakingService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Traking")).makeService();
    }

    public static TrakingDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new TrakingDataManager();
        }
        return sInstance;
    }

    public void addTraking(GenericObject genericObject,
                           RemoteCallback<Boolean> listener) {
        mTrakingService.insertTraking(genericObject)
                .enqueue(listener);
    }
}
