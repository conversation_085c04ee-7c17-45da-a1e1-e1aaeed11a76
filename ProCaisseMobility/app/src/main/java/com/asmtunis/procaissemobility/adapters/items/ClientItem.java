package com.asmtunis.procaissemobility.adapters.items;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageSwitcher;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.RequiresApi;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.helper.ClientHelper;
import com.asmtunis.procaissemobility.helper.GPSTracker;
import com.asmtunis.procaissemobility.helper.Globals;
import com.asmtunis.procaissemobility.helper.utils.DateUtils;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.helper.utils.Utils;
import com.asmtunis.procaissemobility.listener.ItemCallback;
import com.asmtunis.procaissemobility.listener.MenuItemClickListener;
import com.asmtunis.procaissemobility.listener.MenuItemsAction;
import com.mikepenz.fastadapter.items.AbstractItem;
import com.mikepenz.google_material_typeface_library.GoogleMaterial;
import com.rengwuxian.materialedittext.MaterialEditText;

import net.cachapa.expandablelayout.ExpandableLayout;

import java.util.List;
import java.util.Objects;

import static android.content.Context.LAYOUT_INFLATER_SERVICE;
import static com.asmtunis.procaissemobility.App.prefUtils;
import static com.asmtunis.procaissemobility.helper.utils.UIUtils.getIconicsDrawable;

import butterknife.BindView;

/**
 * Created by PC on 10/11/2017.
 */


public class ClientItem extends AbstractItem<ClientItem, ClientItem.ViewHolder> {

    private static Context context;
    public Client client;
    MenuItemsAction menuItemsAction;
    protected ItemCallback itemCallback;

    static int menuId;
    static ImageView mapImageView;

    static RelativeLayout mapView;

    static MaterialEditText taxRegistrationNumberInputField;

    static MaterialEditText entitledInputField;

    static MaterialEditText addressInputField;

    static MaterialEditText emailInputField;

    static MaterialEditText professionInputField;

    static MaterialEditText companyInputField;

    static MaterialEditText phone1InputField;

    static MaterialEditText phone2InputField;


    public ClientItem(Context context, Client client) {
        this.client = client;
        this.context = context;
    }

    public ClientItem(Context context, Client client, int menuId, MenuItemsAction
            menuItemsAction, ItemCallback itemCallback) {
        this.client = client;
        this.context = context;
        this.menuItemsAction = menuItemsAction;
        this.menuId = menuId;
        this.itemCallback = itemCallback;
    }

    public ClientItem(Context context, Client client, ItemCallback itemCallback) {
        this.client = client;
        ClientItem.context = context;
        this.itemCallback = itemCallback;
    }

    public Client getClient() {
        return client;
    }

    public void setClient(Client client) {
        this.client = client;
    }

    @Override
    public int getType() {
        return R.id.adapter_client_item_id;
    }

    @Override
    public int getLayoutRes() {
        return R.layout.client_item_adapter;
    }

    static String setText(MaterialEditText editText, String value) {
        if (!StringUtils.isEmptyString(value)) {
            editText.setTag(editText.getId() + "_" + value);
            editText.setVisibility(View.VISIBLE);
            editText.setText(value);
            editText.setHideUnderline(true);
        }
        return value;
    }

    //The logic to bind your data to the view
    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    @Override
    public void bindView(final ViewHolder viewHolder, List<Object> payloads) {
        //call super so the selection is already handled for you
        super.bindView(viewHolder, payloads);
        viewHolder.toolbar.setTitle(client.getcLINomPren());
        viewHolder.toolbar.setSubtitle(client.cLICode );
        if (client.cLIDateCre != null)

        if (!checkFields()) {
            viewHolder.toolbar.setTag("toolbar_" + client.getcLICode());
            viewHolder.expandableLayout.setTag("expandableLayout_" + client.getcLICode());
            viewHolder.itemStatusLabel.setTag("itemStatusLabel_" + client.getcLICode());

            viewHolder.price.setTag("price_" + client.getcLICode());
            viewHolder.price.setText(String.format("%s: %s", context.getString(R.string.credit_label), client.getCliCredit()));
            viewHolder.priceCardView.setVisibility(View.GONE);

            viewHolder.total.setText(String.format("%s", ClientHelper.calculateSold(client, prefUtils.getExercice())));
            viewHolder.totalContainer.setBackground(context.getDrawable(ClientHelper.calculateSold(client, prefUtils.getExercice()) >= 0 ? R.drawable.green_rect : R.drawable.red_rect));

            viewHolder.toReturnPrice.setText(String.format("%s: %s", context.getString(R.string.received_field_title), client.getDebit()));
            viewHolder.toReturnPriceCardView.setVisibility(View.GONE);


            //    viewHolder.cliSold.setText(String.format("%s: %s", context.getString(R.string.sold),  Utils.round(client.getcLISolde(),3)));

               viewHolder.cliSold.setText(String.format("%s: %s", context.getString(R.string.sold),  Utils.round(client.getSolde(),3)));
            // viewHolder.cliSoldCardView.setBackground(context.getDrawable(Utils.round(client.getcLISolde(),3) >=0 ? R.drawable.green_rect : R.drawable.red_rect));
            viewHolder.cliSoldCardView.setBackground(context.getDrawable(Utils.round(client.getSolde(),3) >=0 ? R.drawable.green_rect : R.drawable.red_rect));

            String cltinfo = "N/A";
            if(!Objects.equals(client.getCltInfo1(), null)) cltinfo=client.getCltInfo1();

            viewHolder.clt_info1Txvw.setText("Type Client : "+ cltinfo);

          //  if(client.getcLISolde()==0.0) viewHolder.cliSoldCardView.setVisibility(View.GONE);
          //  else viewHolder.cliSoldCardView.setVisibility(View.VISIBLE);


            viewHolder.total.setVisibility(View.GONE); // NORMALLLLLLYYY == VISIBLE
            viewHolder.clt_info1Txvw.setVisibility(View.VISIBLE);
            if (client.getStatus().equalsIgnoreCase(Globals.ITEM_STATUS.INSERTED.getStatus())) {
                setTriangleView(viewHolder.itemStatusLabel, 1);
            }
            else setTriangleView(viewHolder.itemStatusLabel, -1);

            if (menuId != 0) {
                viewHolder.toolbar.inflateMenu(menuId);
                viewHolder.toolbar.setOnMenuItemClickListener(new MenuItemClickListener<>(client,
                        menuItemsAction));
            }

            if (itemCallback != null) {
                viewHolder.InfoConatainer.setOnClickListener(v -> onViewClick(viewHolder));
                viewHolder.toolbar.setOnClickListener(v -> onViewClick(viewHolder));

                viewHolder.totalContainer.setOnClickListener(v -> {

                    if (client != null) {

                        if (itemCallback == null) {
                            return;
                        } else {
                            itemCallback.onItemClicked(client);
                        }
                    }

                });
            }

            if(client.getLatitude() == null || client.getLongitude() == null) {
                viewHolder.updateLocalisation.setDrawableBottom(getIconicsDrawable(GoogleMaterial.Icon.gmd_location_off, R.color.red_btn_bg_pressed_color, context));
                viewHolder.updateLocalisation.setVisibility(View.VISIBLE);
            }
            else {
                if(client.getLatitude() == 0.0 || client.getLongitude() == 0.0) {
                    viewHolder.updateLocalisation.setDrawableBottom(getIconicsDrawable(GoogleMaterial.Icon.gmd_location_off, R.color.red_btn_bg_pressed_color, context));
                    viewHolder.updateLocalisation.setVisibility(View.VISIBLE);
                } else {
                    viewHolder.updateLocalisation.setDrawableBottom(getIconicsDrawable(GoogleMaterial.Icon.gmd_location_on, R.color.material_blue_grey_80, context));
                    viewHolder.updateLocalisation.setVisibility(View.VISIBLE);
                }
            }
        }
    }

    void onViewClick(ViewHolder viewHolder) {
        if (client != null) {

            if (itemCallback == null) {
                return;
            } else {
                itemCallback.onItemClicked(viewHolder, client);
            }

        }
    }


    boolean checkFields() {
        return StringUtils.isEmptyString(client.getcLINomPren()) &&

                StringUtils.isEmptyString(client.getCLI_MatFisc())
                && StringUtils.isEmptyString(client.getcLIAdresse())
                && StringUtils.isEmptyString(client.getcLIMail() + "")
                && StringUtils.isEmptyString(client.getcLISociete())
                && StringUtils.isEmptyString(client.getcLITel1())
                && StringUtils.isEmptyString(client.getcLITel2());

    }


    @Override
    public void unbindView(final ViewHolder holder) {
        super.unbindView(holder);
        holder.toolbar.setTitle(null);
        holder.toolbar.setSubtitle(null);
    }

    //Init the viewHolder for this Item
    @Override
    public ViewHolder getViewHolder(View v) {
        return new ViewHolder(v);
    }


    void setTriangleView(jp.shts.android.library.TriangleLabelView labelView, int status) {
        labelView.setVisibility(View.VISIBLE);
        switch (status) {
            case 0:
                labelView.setTriangleBackgroundColorResource(R.color.md_red_900);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.cancel);
                labelView.setPrimaryTextColorResource(R.color.md_red_100);

                break;

            case 1:
                labelView.setTriangleBackgroundColorResource(R.color.color_yellow);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.new_label);
                labelView.setPrimaryTextColorResource(R.color.md_red_100);
                break;
            default:
                labelView.setVisibility(View.GONE);
                break;
        }

    }


    public static void setView(ViewHolder viewHolder) {
        LayoutInflater inflater = (LayoutInflater) context.getSystemService(LAYOUT_INFLATER_SERVICE);
        viewHolder.content.addView(inflater.inflate(R.layout.client_item_view, null));
        taxRegistrationNumberInputField = viewHolder.content.findViewById(R.id.taxRegistrationNumberInputField);
        entitledInputField = viewHolder.content.findViewById(R.id.entitledInputField);
        addressInputField = viewHolder.content.findViewById(R.id.addressInputField);
        emailInputField = viewHolder.content.findViewById(R.id.emailInputField);
        professionInputField = viewHolder.content.findViewById(R.id.professionInputField);
        companyInputField = viewHolder.content.findViewById(R.id.companyInputField);
        phone1InputField = viewHolder.content.findViewById(R.id.phone1InputField);
        phone2InputField = viewHolder.content.findViewById(R.id.phone2InputField);
        mapImageView = viewHolder.content.findViewById(R.id.map_image_view);
        mapView = viewHolder.content.findViewById(R.id.map_view);
    }


    public static class ViewHolder extends RecyclerView.ViewHolder {

        public ExpandableLayout expandableLayout;
        public Toolbar toolbar;
        TextView price;
        TextView total;
        TextView toReturnPrice;
        CardView cliSoldCardView;
        CardView toReturnPriceCardView;
        CardView priceCardView;
        TextView cliSold;
        TextView clt_info1Txvw;
        LinearLayout totalContainer;
        LinearLayout InfoConatainer;
        public FrameLayout content;
        com.mikepenz.iconics.view.IconicsButton updateLocalisation;
        jp.shts.android.library.TriangleLabelView itemStatusLabel;
        public ViewHolder(View view) {
            super(view);
            //setIsRecyclable(false);
            expandableLayout = view.findViewById(R.id.expandable_layout);
            toolbar = view.findViewById(R.id.toolbar);
            price = view.findViewById(R.id.price);
            price.setVisibility(View.VISIBLE);
            content = view.findViewById(R.id.content_layout);
            total = view.findViewById(R.id.total);
            toReturnPrice = view.findViewById(R.id.toReturnPrice);
            priceCardView = view.findViewById(R.id.priceCardView);
            toReturnPriceCardView = view.findViewById(R.id.toReturnPriceCardView);
            cliSoldCardView = view.findViewById(R.id.cliSoldCardView);
            cliSold= view.findViewById(R.id.cliSold);
            clt_info1Txvw = view.findViewById(R.id.clt_info1Txvw);
            totalContainer = view.findViewById(R.id.totalContainer);
            InfoConatainer = view.findViewById(R.id.InfoConatainer);
           // toReturnPrice.setVisibility(View.GONE);
         //   total.setVisibility(View.GONE);
            itemStatusLabel = view.findViewById(R.id.item_status_label);
            updateLocalisation = view.findViewById(R.id.updateLocalisation);
        }
    }

}
