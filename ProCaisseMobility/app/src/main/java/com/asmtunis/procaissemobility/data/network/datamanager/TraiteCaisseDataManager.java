package com.asmtunis.procaissemobility.data.network.datamanager;


import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.TraiteCaisse;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.TraiteCaisseService;

import java.util.List;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

/**
 * Created by PC on 10/4/2017.
 */

public class TraiteCaisseDataManager {
    private static TraiteCaisseDataManager sInstance;

    private final TraiteCaisseService mTraiteCaisseService;

    public TraiteCaisseDataManager( ) {
        mTraiteCaisseService = new ServiceFactory<>(TraiteCaisseService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"TraiteCaisse")).makeService();

    }

    public static TraiteCaisseDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new TraiteCaisseDataManager();
        }
        return sInstance;
    }
/*
    public void getTraiteCaisse(GenericObject genericObject,
                                                 RemoteCallback<List<LigneTicket>> listener) {
        mTraiteCaisseService.getTraiteCaisse(genericObject)
                .enqueue(listener);
    }
*/

    public void getTraiteCaisseByReglement(GenericObject genericObject,
                                RemoteCallback<List<TraiteCaisse>> listener) {
        mTraiteCaisseService.getTraiteCaisseByReglement(genericObject)
                .enqueue(listener);
    }
    public void getTraiteCaisseByReglements(GenericObject genericObject,
                                       RemoteCallback<List<List<TraiteCaisse>>> listener) {
        mTraiteCaisseService.getTraiteCaisseByReglements(genericObject)
                .enqueue(listener);
    }
}

