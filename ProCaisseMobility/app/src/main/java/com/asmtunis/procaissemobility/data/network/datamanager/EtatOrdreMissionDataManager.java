package com.asmtunis.procaissemobility.data.network.datamanager;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Etablisement;
import com.asmtunis.procaissemobility.data.models.EtatOrdreMission;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.EtatOrdreMissionService;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

import java.util.List;

public class EtatOrdreMissionDataManager {

    private static EtatOrdreMissionDataManager sInstance;
    private final EtatOrdreMissionService mEtatOrdreMissionService;

    public EtatOrdreMissionDataManager() {

        mEtatOrdreMissionService = new ServiceFactory<>(EtatOrdreMissionService.class, String.format(BASE_URL, PrefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"OrdreMission")).makeService();
    }

    public static EtatOrdreMissionDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new EtatOrdreMissionDataManager();
        }
        return sInstance;
    }

    public void getEtatOrdreMission(GenericObject genericObject,
                                 RemoteCallback<List<EtatOrdreMission>> listener) {
        mEtatOrdreMissionService.getEtatOrdreMission(genericObject)
                .enqueue(listener);
    }
}

