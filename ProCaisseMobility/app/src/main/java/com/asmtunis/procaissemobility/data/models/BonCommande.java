package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
@Entity
public class BonCommande extends BaseModel{
    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "DEV_Num")
    @SerializedName("DEV_Num")
    @Expose
    private String dEVNum;

    @ColumnInfo(name = "DEV_Exerc")
    @SerializedName("DEV_Exerc")
    @Expose
    private String dEVExerc;
    @ColumnInfo(name = "DEV_Date")
    @SerializedName("DEV_Date")
    @Expose
    private String dEVDate;
    @ColumnInfo(name = "DEV_CodeClient")
    @SerializedName("DEV_CodeClient")
    @Expose
    private String dEVCodeClient;
    @ColumnInfo(name = "DEV_StationOrigine")
    @SerializedName("DEV_StationOrigine")
    @Expose
    private String dEVStationOrigine;
    @ColumnInfo(name = "DEV_Etat")
    @SerializedName("DEV_Etat")
    @Expose
    private String dEVEtat;
    @ColumnInfo(name = "DEV_Station")
    @SerializedName("DEV_Station")
    @Expose
    private String dEVStation;
    @ColumnInfo(name = "DEV_User")
    @SerializedName("DEV_User")
    @Expose
    private String dEVUser;
    @ColumnInfo(name = "DEV_Mntht")
    @SerializedName("DEV_Mntht")
    @Expose
    private String dEVMntht;
    @ColumnInfo(name = "DEV_MntNetHt")
    @SerializedName("DEV_MntNetHt")
    @Expose
    private String dEVMntNetHt;
    @ColumnInfo(name = "DEV_MntTva")
    @SerializedName("DEV_MntTva")
    @Expose
    private String dEVMntTva;
    @ColumnInfo(name = "DEV_MntTTC")
    @SerializedName("DEV_MntTTC")
    @Expose
    private String dEVMntTTC;
    @ColumnInfo(name = "DEV_TauxRemise")
    @SerializedName("DEV_TauxRemise")
    @Expose
    private String dEVTauxRemise;
    @ColumnInfo(name = "DEV_Remise")
    @SerializedName("DEV_Remise")
    @Expose
    private String dEVRemise;
    @ColumnInfo(name = "DEV_Regler")
    @SerializedName("DEV_Regler")
    @Expose
    private String dEVRegler;
    @ColumnInfo(name = "DEV_export")
    @SerializedName("DEV_export")
    @Expose
    private String dEVExport;
    @ColumnInfo(name = "DEV_DDm")
    @SerializedName("DEV_DDm")
    @Expose
    private String dEVDDm;
    @ColumnInfo(name = "DEV_ExoNum")
    @SerializedName("DEV_ExoNum")
    @Expose
    private String dEVExoNum;
    @ColumnInfo(name = "DEV_ExoVal")
    @SerializedName("DEV_ExoVal")
    @Expose
    private String dEVExoVal;
    @ColumnInfo(name = "DEV_Timbre")
    @SerializedName("DEV_Timbre")
    @Expose
    private String dEVTimbre;
    @ColumnInfo(name = "DEV_Exonoration")
    @SerializedName("DEV_Exonoration")
    @Expose
    private String dEVExonoration;
    @ColumnInfo(name = "DEV_Chauffeur")
    @SerializedName("DEV_Chauffeur")
    @Expose
    private String dEVChauffeur;
    @ColumnInfo(name = "DEV_vehicule")
    @SerializedName("DEV_vehicule")
    @Expose
    private String dEVVehicule;
    @ColumnInfo(name = "DEV_Observation")
    @SerializedName("DEV_Observation")
    @Expose
    private String dEVObservation;
    @ColumnInfo(name = "DEV_Client")
    @SerializedName("DEV_Client")
    @Expose
    private String dEVClient;
    @ColumnInfo(name = "DEV_MntFodec")
    @SerializedName("DEV_MntFodec")
    @Expose
    private String dEVMntFodec;
    @ColumnInfo(name = "DEV_MntDC")
    @SerializedName("DEV_MntDC")
    @Expose
    private String dEVMntDC;
    @ColumnInfo(name = "DEV_EtatBon")
    @SerializedName("DEV_EtatBon")
    @Expose
    private String dEVEtatBon;
    @ColumnInfo(name = "BON_LIV_Num")
    @SerializedName("BON_LIV_Num")
    @Expose
    private String bONLIVNum;
    @ColumnInfo(name = "BON_LIV_Exerc")
    @SerializedName("BON_LIV_Exerc")
    @Expose
    private String bONLIVExerc;


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    @SerializedName("code")
    @ColumnInfo(name = "code")
    private transient String code;


    @SerializedName("msg")
    @ColumnInfo(name = "msg")
    private transient String msg;


    public BonCommande(){
    }

    public BonCommande(String dEVCodeClient, String DEV_info3){
        this.dEVCodeClient = dEVCodeClient;
        this.DEV_info3 = DEV_info3;
    }

    public String getDEV_info3() {
        return DEV_info3;
    }

    public void setDEV_info3(String DEV_info3) {
        this.DEV_info3 = DEV_info3;
    }

    @ColumnInfo(name = "DEV_info3")
    @SerializedName("DEV_info3")
    @Expose
    private String DEV_info3;



    public String getDevCodeM() {
        return devCodeM;
    }

    public void setDevCodeM(String devCodeM) {
        this.devCodeM = devCodeM;
    }

    @ColumnInfo(name = "DEV_Code_M")
    @SerializedName("DEV_Code_M")
    @Expose
    public String devCodeM;
    @Ignore
    private  Article article;

    public Article getArticle() {
        return article;
    }

    public void setArticle(Article article) {
        this.article = article;
    }

    public String getDEVNum() {
        return dEVNum;
    }

    public void setDEVNum(String dEVNum) {
        this.dEVNum = dEVNum;
    }

    public String getDEVExerc() {
        return dEVExerc;
    }

    public void setDEVExerc(String dEVExerc) {
        this.dEVExerc = dEVExerc;
    }

    public String getDEVDate() {
        return dEVDate;
    }

    public void setDEVDate(String dEVDate) {
        this.dEVDate = dEVDate;
    }

    public String getDEVCodeClient() {
        return dEVCodeClient;
    }

    public void setDEVCodeClient(String dEVCodeClient) {
        this.dEVCodeClient = dEVCodeClient;
    }

    public String getDEVStationOrigine() {
        return dEVStationOrigine;
    }

    public void setDEVStationOrigine(String dEVStationOrigine) {
        this.dEVStationOrigine = dEVStationOrigine;
    }

    public String getDEVEtat() {
        return dEVEtat;
    }

    public void setDEVEtat(String dEVEtat) {
        this.dEVEtat = dEVEtat;
    }

    public String getDEVStation() {
        return dEVStation;
    }

    public void setDEVStation(String dEVStation) {
        this.dEVStation = dEVStation;
    }

    public String getDEVUser() {
        return dEVUser;
    }

    public void setDEVUser(String dEVUser) {
        this.dEVUser = dEVUser;
    }

    public String getDEVMntht() {
        return dEVMntht;
    }

    public void setDEVMntht(String dEVMntht) {
        this.dEVMntht = dEVMntht;
    }

    public String getDEVMntNetHt() {
        return dEVMntNetHt;
    }

    public void setDEVMntNetHt(String dEVMntNetHt) {
        this.dEVMntNetHt = dEVMntNetHt;
    }

    public String getDEVMntTva() {
        return dEVMntTva;
    }

    public void setDEVMntTva(String dEVMntTva) {
        this.dEVMntTva = dEVMntTva;
    }

    public String getDEVMntTTC() {
        return dEVMntTTC;
    }

    public void setDEVMntTTC(String dEVMntTTC) {
        this.dEVMntTTC = dEVMntTTC;
    }

    public String getDEVTauxRemise() {
        return dEVTauxRemise;
    }

    public void setDEVTauxRemise(String dEVTauxRemise) {
        this.dEVTauxRemise = dEVTauxRemise;
    }

    public String getDEVRemise() {
        return dEVRemise;
    }

    public void setDEVRemise(String dEVRemise) {
        this.dEVRemise = dEVRemise;
    }

    public String getDEVRegler() {
        return dEVRegler;
    }

    public void setDEVRegler(String dEVRegler) {
        this.dEVRegler = dEVRegler;
    }

    public String getDEVExport() {
        return dEVExport;
    }

    public void setDEVExport(String dEVExport) {
        this.dEVExport = dEVExport;
    }

    public String getDEVDDm() {
        return dEVDDm;
    }

    public void setDEVDDm(String dEVDDm) {
        this.dEVDDm = dEVDDm;
    }

    public String getDEVExoNum() {
        return dEVExoNum;
    }

    public void setDEVExoNum(String dEVExoNum) {
        this.dEVExoNum = dEVExoNum;
    }

    public String getDEVExoVal() {
        return dEVExoVal;
    }

    public void setDEVExoVal(String dEVExoVal) {
        this.dEVExoVal = dEVExoVal;
    }

    public String getDEVTimbre() {
        return dEVTimbre;
    }

    public void setDEVTimbre(String dEVTimbre) {
        this.dEVTimbre = dEVTimbre;
    }

    public String getDEVExonoration() {
        return dEVExonoration;
    }

    public void setDEVExonoration(String dEVExonoration) {
        this.dEVExonoration = dEVExonoration;
    }

    public String getDEVChauffeur() {
        return dEVChauffeur;
    }

    public void setDEVChauffeur(String dEVChauffeur) {
        this.dEVChauffeur = dEVChauffeur;
    }

    public String getDEVVehicule() {
        return dEVVehicule;
    }

    public void setDEVVehicule(String dEVVehicule) {
        this.dEVVehicule = dEVVehicule;
    }

    public String getDEVObservation() {
        return dEVObservation;
    }

    public void setDEVObservation(String dEVObservation) {
        this.dEVObservation = dEVObservation;
    }

    public String getDEVClient() {
        return dEVClient;
    }

    public void setDEVClient(String dEVClient) {
        this.dEVClient = dEVClient;
    }

    public String getDEVMntFodec() {
        return dEVMntFodec;
    }

    public void setDEVMntFodec(String dEVMntFodec) {
        this.dEVMntFodec = dEVMntFodec;
    }

    public String getDEVMntDC() {
        return dEVMntDC;
    }

    public void setDEVMntDC(String dEVMntDC) {
        this.dEVMntDC = dEVMntDC;
    }

    public String getDEVEtatBon() {
        return dEVEtatBon;
    }

    public void setDEVEtatBon(String dEVEtatBon) {
        this.dEVEtatBon = dEVEtatBon;
    }

    public String getBONLIVNum() {
        return bONLIVNum;
    }

    public void setBONLIVNum(String bONLIVNum) {
        this.bONLIVNum = bONLIVNum;
    }

    public String getBONLIVExerc() {
        return bONLIVExerc;
    }

    public void setBONLIVExerc(String bONLIVExerc) {
        this.bONLIVExerc = bONLIVExerc;
    }

    @Override
    public String toString() {
        return "BonCommande{" +
                "dEVNum='" + dEVNum + '\'' +
                ", dEVExerc='" + dEVExerc + '\'' +
                ", dEVDate='" + dEVDate + '\'' +
                ", dEVCodeClient='" + dEVCodeClient + '\'' +
                ", dEVStationOrigine='" + dEVStationOrigine + '\'' +
                ", dEVEtat='" + dEVEtat + '\'' +
                ", dEVStation='" + dEVStation + '\'' +
                ", dEVUser='" + dEVUser + '\'' +
                ", dEVMntht='" + dEVMntht + '\'' +
                ", dEVMntNetHt='" + dEVMntNetHt + '\'' +
                ", dEVMntTva='" + dEVMntTva + '\'' +
                ", dEVMntTTC='" + dEVMntTTC + '\'' +
                ", dEVTauxRemise='" + dEVTauxRemise + '\'' +
                ", dEVRemise='" + dEVRemise + '\'' +
                ", dEVRegler='" + dEVRegler + '\'' +
                ", dEVExport='" + dEVExport + '\'' +
                ", dEVDDm='" + dEVDDm + '\'' +
                ", dEVExoNum='" + dEVExoNum + '\'' +
                ", dEVExoVal='" + dEVExoVal + '\'' +
                ", dEVTimbre='" + dEVTimbre + '\'' +
                ", dEVExonoration='" + dEVExonoration + '\'' +
                ", dEVChauffeur='" + dEVChauffeur + '\'' +
                ", dEVVehicule='" + dEVVehicule + '\'' +
                ", dEVObservation='" + dEVObservation + '\'' +
                ", dEVClient='" + dEVClient + '\'' +
                ", dEVMntFodec='" + dEVMntFodec + '\'' +
                ", dEVMntDC='" + dEVMntDC + '\'' +
                ", dEVEtatBon='" + dEVEtatBon + '\'' +
                ", bONLIVNum='" + bONLIVNum + '\'' +
                ", bONLIVExerc='" + bONLIVExerc + '\'' +
                ", DEV_info3='" + DEV_info3 + '\'' +
                '}';
    }
}
