package com.asmtunis.procaissemobility.data.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.Immobilisation;

import java.util.List;

@Dao
public interface  ClientImmoDAO {
    @Query("SELECT * FROM Immobilisation order by CLI_NomPren asc")
    List<Immobilisation> getAll();


    @Query("SELECT * FROM Immobilisation where TyEmpImNom= 'Societe' and Clt_Immo =1 order by CLI_NomPren asc")
    List<Immobilisation> getAllSociete();



    @Query("SELECT * FROM Immobilisation where TyEmpImNom= 'Site' and Clt_Immo =1 and Clt_ImoCodeP = :codeSociete order by CLI_NomPren asc")
    List<Immobilisation> getAllSite(String codeSociete);


    @Query("SELECT * FROM Immobilisation where TyEmpImNom= 'Bloc' and Clt_Immo =1 and Clt_ImoCodeP = :codeSite order by CLI_NomPren asc")
    List<Immobilisation> getAllBloc(String codeSite);

    @Query("SELECT * FROM Immobilisation where TyEmpImNom= 'Batiment' and Clt_Immo =1 and Clt_ImoCodeP = :codeBatiment order by CLI_NomPren asc")
    List<Immobilisation> getAllBatiment(String codeBatiment);


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(Immobilisation item);


    @Insert(onConflict = OnConflictStrategy.REPLACE)
  void insertAll(List<Immobilisation> items);

    @Query("DELETE FROM Immobilisation")
    void deleteAll();
}
