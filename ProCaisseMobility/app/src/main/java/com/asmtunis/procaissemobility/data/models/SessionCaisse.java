package com.asmtunis.procaissemobility.data.models;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;
import androidx.annotation.NonNull;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * Created by Achraf on 29/09/2017.
 */
@Entity
public class SessionCaisse extends BaseModel{

    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "SC_IdSCaisse")
    @SerializedName("SC_IdSCaisse")
    @Expose
    public String sCIdSCaisse;
    @ColumnInfo(name = "SC_Caisse")
    @SerializedName("SC_Caisse")
    @Expose
    public String sCCaisse;
    @ColumnInfo(name = "SC_CodeUtilisateur")
    @SerializedName("SC_CodeUtilisateur")
    @Expose
    public int sCCodeUtilisateur;
    @ColumnInfo(name = "SC_DateHeureOuv")
    @SerializedName("SC_DateHeureOuv")
    @Expose
    public String sCDateHeureOuv;
    @ColumnInfo(name = "SC_ClotCaisse")
    @SerializedName("SC_ClotCaisse")
    @Expose
    public int sCClotCaisse;
    @ColumnInfo(name = "SC_DateHeureClot")
    @SerializedName("SC_DateHeureClot")
    @Expose
    public String sCDateHeureClot;
    @ColumnInfo(name = "SC_FondCaisse")
    @SerializedName("SC_FondCaisse")
    @Expose
    public String sCFondCaisse;
    @ColumnInfo(name = "SC_TotRemise")
    @SerializedName("SC_TotRemise")
    @Expose
    public String sCTotRemise;
    @ColumnInfo(name = "SC_BenificeTVA")
    @SerializedName("SC_BenificeTVA")
    @Expose
    public double sCBenificeTVA;
    @ColumnInfo(name = "SC_BenificeSansTVA")
    @SerializedName("SC_BenificeSansTVA")
    @Expose
    public double sCBenificeSansTVA;
    @SerializedName("SC_TotBenifice")
    @ColumnInfo(name = "SC_TotBenifice")
    @Expose
    public double sCTotBenifice;
    @ColumnInfo(name = "SC_TotalRecette")
    @SerializedName("SC_TotalRecette")
    @Expose
    public double sCTotalRecette;
    @ColumnInfo(name = "SC_TotalCaisse")
    @SerializedName("SC_TotalCaisse")
    @Expose
    public double sCTotalCaisse;
    @SerializedName("SC_IdCarnet")
    @ColumnInfo(name = "SC_IdCarnet")
    @Expose
    public String sCIdCarnet;
    @SerializedName("SC_Etat")
    @ColumnInfo(name = "SC_Etat")
    @Expose
    public int sCEtat;
    @ColumnInfo(name = "SC_Facturer")
    @SerializedName("SC_Facturer")
    @Expose
    public int sCFacturer;
    @ColumnInfo(name = "SC_NumFact")
    @SerializedName("SC_NumFact")
    @Expose
    public String sCNumFact;
    @ColumnInfo(name = "SC_Station")
    @SerializedName("SC_Station")
    @Expose
    public String sCStation;
    @ColumnInfo(name = "SC_User")
    @SerializedName("SC_User")
    @Expose
    public String sCUser;
    @ColumnInfo(name = "SC_DateHeureCrea")
    @SerializedName("SC_DateHeureCrea")
    @Expose
    public String sCDateHeureCrea;
    @ColumnInfo(name = "SC_TotDepense")
    @SerializedName("SC_TotDepense")
    @Expose
    public double sCTotDepense;
    @SerializedName("SC_export")
    @ColumnInfo(name = "SC_export")
    @Expose
    public String sCExport;
    @SerializedName("SC_DDm")
    @ColumnInfo(name = "SC_DDm")
    @Expose
    public String sCDDm;
    @ColumnInfo(name = "SC_TotBenificeRes")
    @SerializedName("SC_TotBenificeRes")
    @Expose
    public String sCTotBenificeRes;
    @ColumnInfo(name = "SC_TotalRecetteManuel")
    @SerializedName("SC_TotalRecetteManuel")
    @Expose
    public double sCTotalRecetteManuel;
    @ColumnInfo(name = "ddm")
    @SerializedName("ddm")
    @Expose
    public String ddm;
    @ColumnInfo(name = "export")
    @SerializedName("export")
    @Expose
    public int export;

    public SessionCaisse() {
    }


    @Ignore

    public SessionCaisse(String sCIdSCaisse, String sCCaisse, int sCCodeUtilisateur, String sCDateHeureOuv, int sCClotCaisse, String sCDateHeureClot, String sCFondCaisse, String sCTotRemise, double sCBenificeTVA, double sCBenificeSansTVA, double sCTotBenifice, double sCTotalRecette, double sCTotalCaisse, String sCIdCarnet, int sCEtat, int sCFacturer, String sCNumFact, String sCStation, String sCUser, String sCDateHeureCrea, double sCTotDepense, String sCExport, String sCDDm, String sCTotBenificeRes, double sCTotalRecetteManuel, String ddm, int export) {
        this.sCIdSCaisse = sCIdSCaisse;
        this.sCCaisse = sCCaisse;
        this.sCCodeUtilisateur = sCCodeUtilisateur;
        this.sCDateHeureOuv = sCDateHeureOuv;
        this.sCClotCaisse = sCClotCaisse;
        this.sCDateHeureClot = sCDateHeureClot;
        this.sCFondCaisse = sCFondCaisse;
        this.sCTotRemise = sCTotRemise;
        this.sCBenificeTVA = sCBenificeTVA;
        this.sCBenificeSansTVA = sCBenificeSansTVA;
        this.sCTotBenifice = sCTotBenifice;
        this.sCTotalRecette = sCTotalRecette;
        this.sCTotalCaisse = sCTotalCaisse;
        this.sCIdCarnet = sCIdCarnet;
        this.sCEtat = sCEtat;
        this.sCFacturer = sCFacturer;
        this.sCNumFact = sCNumFact;
        this.sCStation = sCStation;
        this.sCUser = sCUser;
        this.sCDateHeureCrea = sCDateHeureCrea;
        this.sCTotDepense = sCTotDepense;
        this.sCExport = sCExport;
        this.sCDDm = sCDDm;
        this.sCTotBenificeRes = sCTotBenificeRes;
        this.sCTotalRecetteManuel = sCTotalRecetteManuel;
        this.ddm = ddm;
        this.export = export;
    }

    public String getsCIdSCaisse() {
        return sCIdSCaisse;
    }

    public void setsCIdSCaisse(String sCIdSCaisse) {
        this.sCIdSCaisse = sCIdSCaisse;
    }

    public String getsCCaisse() {
        return sCCaisse;
    }

    public void setsCCaisse(String sCCaisse) {
        this.sCCaisse = sCCaisse;
    }

    public int getsCCodeUtilisateur() {
        return sCCodeUtilisateur;
    }

    public void setsCCodeUtilisateur(int sCCodeUtilisateur) {
        this.sCCodeUtilisateur = sCCodeUtilisateur;
    }

    public String getsCDateHeureOuv() {
        return sCDateHeureOuv;
    }

    public void setsCDateHeureOuv(String sCDateHeureOuv) {
        this.sCDateHeureOuv = sCDateHeureOuv;
    }

    public int getsCClotCaisse() {
        return sCClotCaisse;
    }

    public void setsCClotCaisse(int sCClotCaisse) {
        this.sCClotCaisse = sCClotCaisse;
    }

    public String getsCDateHeureClot() {
        return sCDateHeureClot;
    }

    public void setsCDateHeureClot(String sCDateHeureClot) {
        this.sCDateHeureClot = sCDateHeureClot;
    }

    public String getsCFondCaisse() {
        return sCFondCaisse;
    }

    public void setsCFondCaisse(String sCFondCaisse) {
        this.sCFondCaisse = sCFondCaisse;
    }

    public String getsCTotRemise() {
        return sCTotRemise;
    }

    public void setsCTotRemise(String sCTotRemise) {
        this.sCTotRemise = sCTotRemise;
    }

    public double getsCBenificeTVA() {
        return sCBenificeTVA;
    }

    public void setsCBenificeTVA(double sCBenificeTVA) {
        this.sCBenificeTVA = sCBenificeTVA;
    }

    public double getsCBenificeSansTVA() {
        return sCBenificeSansTVA;
    }

    public void setsCBenificeSansTVA(double sCBenificeSansTVA) {
        this.sCBenificeSansTVA = sCBenificeSansTVA;
    }

    public double getsCTotBenifice() {
        return sCTotBenifice;
    }

    public void setsCTotBenifice(double sCTotBenifice) {
        this.sCTotBenifice = sCTotBenifice;
    }

    public double getsCTotalRecette() {
        return sCTotalRecette;
    }

    public void setsCTotalRecette(double sCTotalRecette) {
        this.sCTotalRecette = sCTotalRecette;
    }

    public double getsCTotalCaisse() {
        return sCTotalCaisse;
    }

    public void setsCTotalCaisse(double sCTotalCaisse) {
        this.sCTotalCaisse = sCTotalCaisse;
    }

    public String getsCIdCarnet() {
        return sCIdCarnet;
    }

    public void setsCIdCarnet(String sCIdCarnet) {
        this.sCIdCarnet = sCIdCarnet;
    }

    public int getsCEtat() {
        return sCEtat;
    }

    public void setsCEtat(int sCEtat) {
        this.sCEtat = sCEtat;
    }

    public int getsCFacturer() {
        return sCFacturer;
    }

    public void setsCFacturer(int sCFacturer) {
        this.sCFacturer = sCFacturer;
    }

    public String getsCNumFact() {
        return sCNumFact;
    }

    public void setsCNumFact(String sCNumFact) {
        this.sCNumFact = sCNumFact;
    }

    public String getsCStation() {
        return sCStation;
    }

    public void setsCStation(String sCStation) {
        this.sCStation = sCStation;
    }

    public String getsCUser() {
        return sCUser;
    }

    public void setsCUser(String sCUser) {
        this.sCUser = sCUser;
    }

    public String getsCDateHeureCrea() {
        return sCDateHeureCrea;
    }

    public void setsCDateHeureCrea(String sCDateHeureCrea) {
        this.sCDateHeureCrea = sCDateHeureCrea;
    }

    public double getsCTotDepense() {
        return sCTotDepense;
    }

    public void setsCTotDepense(double sCTotDepense) {
        this.sCTotDepense = sCTotDepense;
    }

    public String getsCExport() {
        return sCExport;
    }

    public void setsCExport(String sCExport) {
        this.sCExport = sCExport;
    }

    public String getsCDDm() {
        return sCDDm;
    }

    public void setsCDDm(String sCDDm) {
        this.sCDDm = sCDDm;
    }

    public String getsCTotBenificeRes() {
        return sCTotBenificeRes;
    }

    public void setsCTotBenificeRes(String sCTotBenificeRes) {
        this.sCTotBenificeRes = sCTotBenificeRes;
    }

    public double getsCTotalRecetteManuel() {
        return sCTotalRecetteManuel;
    }

    public void setsCTotalRecetteManuel(double sCTotalRecetteManuel) {
        this.sCTotalRecetteManuel = sCTotalRecetteManuel;
    }

    public String getDdm() {
        return ddm;
    }

    public void setDdm(String ddm) {
        this.ddm = ddm;
    }

    public int getExport() {
        return export;
    }

    public void setExport(int export) {
        this.export = export;
    }

    @Override
    public String toString() {
        return sCIdSCaisse + "  " + sCDateHeureCrea;
    }
}
