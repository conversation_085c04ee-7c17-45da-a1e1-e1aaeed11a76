package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.ReglementCaisse;

import java.util.List;

/**
 * Created by PC on 11/18/2017.
 */
@Dao
public interface ReglementCaisseDAO {

    @Query("SELECT * FROM ReglementCaisse order by  CAST(REGC_DateReg AS DATE )  desc ")
    List<ReglementCaisse> getAll();

    @Query("SELECT * FROM ReglementCaisse order by  CAST(REGC_DateReg AS DATE )  desc ")
    LiveData<List<ReglementCaisse>> getAllMutable();

   // @Query("SELECT * FROM ReglementCaisse where REGC_IdSCaisse= :session order by  CAST(REGC_DateReg AS DATE )  desc ")
   @Query("SELECT * FROM ReglementCaisse where REGC_IdSCaisse= :session order by strftime('%Y-%m-%d %H-%M-%S',REGC_DateReg) desc")
    LiveData<List<ReglementCaisse>> getAllMutableBySession(String session);



    @Query("SELECT * FROM ReglementCaisse where REGC_NumTicketPart= :numTicketPart")
    ReglementCaisse getbyNumTicketPart(String numTicketPart);

   // @Query("SELECT * FROM ReglementCaisse where REGC_IdSCaisse= :session and REGC_Station= :station  and REGC_Remarque= :regRemarque")
   // LiveData<List<ReglementCaisse>> getbyRegRemarque(String session, String station, String regRemarque);



    @Query("SELECT ifnull(MAX(cast(substr(REGC_Code,length(:prefix) + 1 ,length('REGC_Code'))as integer)),0)+1 FROM   ReglementCaisse WHERE substr(REGC_Code, 0 ,length(:prefix)+1) = :prefix")
    String getNewCode(String prefix);

    @Query("SELECT * FROM ReglementCaisse WHERE REGC_MntEspece > 0 and REGC_NumTicket =:rEGCNumTicket")
    ReglementCaisse getByCash(int rEGCNumTicket);

    @Query("SELECT * FROM ReglementCaisse WHERE REGC_Code_M =:rEGC_Code_M")
    ReglementCaisse getByREGCM(String rEGC_Code_M);

    @Query("SELECT * FROM ReglementCaisse WHERE   REGC_NumTicket =:rEGCNumTicket")
    List<ReglementCaisse> getAllByTicket(int rEGCNumTicket);

    @Query("SELECT * FROM ReglementCaisse WHERE  REGC_NumTicket =:rEGCNumTicket and isSync= 0 and  (Status='INSERTED'  or Status='UPDATED')")
    List<ReglementCaisse> getAllByTicketNotSynced(int rEGCNumTicket);

    @Query("SELECT * FROM ReglementCaisse WHERE   isSync= 0 and  Status='INSERTED_REG_FROM_REGLEMENT'")
    List<ReglementCaisse> getAllNotSynced();

    @Query("SELECT * FROM ReglementCaisse WHERE   REGC_NumTicket =:rEGCNumTicket")
    ReglementCaisse getByTicket(int rEGCNumTicket);

    @Query("SELECT * FROM ReglementCaisse WHERE   REGC_Code_M =:rEGCNumTicket")
    ReglementCaisse getByTicketM(String rEGCNumTicket);

    @Query("SELECT sum(REGC_Montant) FROM ReglementCaisse WHERE   REGC_CodeClient=:codeC and REGC_Exercice =:exercice")
    double getSumMnttcByClient(String codeC,String exercice);

    @Query("SELECT SUM(REGC_Montant) FROM ReglementCaisse WHERE   REGC_NumTicket = 0")
    double getSumReglementCredit();


    @Query("SELECT * FROM ReglementCaisse WHERE   REGC_NumTicket =:rEGCNumTicket and isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    ReglementCaisse getByTicketNotSynced(int rEGCNumTicket);

    @Query("Update  ReglementCaisse set  isSync=1 where isSync=0 ")
    void updateAllReglementCaisse();

    @Query("Update  ReglementCaisse set  REGC_NumTicket=:newCode where REGC_NumTicket=:oldCode and REGC_IdCaisse=:caisse and REGC_Exercice=:REGC_Exercice")
    void updateNumTicket(int oldCode, int newCode,String caisse,String REGC_Exercice);

    @Query("SELECT count(*) FROM ReglementCaisse where  isSync=0 and  (Status='INSERTED'  or Status='UPDATED' or Status='INSERTED_REG_FROM_REGLEMENT')")
    int getNoSyncCount();

    @Query("SELECT count(*) FROM ReglementCaisse where  isSync=0 and  (Status='INSERTED'  or Status='UPDATED' or Status='INSERTED_REG_FROM_REGLEMENT')")
    LiveData<Integer> getNoSyncCountMuable();

    @Query("SELECT count(*) FROM ReglementCaisse where  isSync=0 and  (Status='INSERTED'  or Status='UPDATED' or Status='INSERTED_REG_FROM_REGLEMENT')")
    Integer getNoSyncCountNonMutable();

    @Query("SELECT * FROM ReglementCaisse WHERE REGC_MntCarteBancaire > 0 and REGC_NumTicket =:rEGCNumTicket")
    List<ReglementCaisse> getByCheck(int rEGCNumTicket);

    @Query("SELECT * FROM ReglementCaisse WHERE REGC_MntTraite > 0 and REGC_NumTicket =:rEGCNumTicket")
    List<ReglementCaisse> getByTicketResto(int rEGCNumTicket);

    @Query("SELECT * FROM ReglementCaisse")
    List<ReglementCaisse> getAllByType();

    @Query("SELECT * FROM ReglementCaisse WHERE REGC_Code = :code ")
    ReglementCaisse getOneByCode(String code);

    @Query("UPDATE ReglementCaisse SET REGC_Code=:rEGC_Code where REGC_Code_M = :tik_num_ticket_M")
    void updateregCode( String rEGC_Code,String tik_num_ticket_M);


 @Query("SELECT * FROM ReglementCaisse WHERE REGC_Code_M = :code ")
 ReglementCaisse getOneByCodeM(String code);

    @Query("SELECT * FROM ReglementCaisse LIMIT 1")
    ReglementCaisse getOne();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(ReglementCaisse item);

    @Query("UPDATE ReglementCaisse SET REGC_CodeClient = :code_client where REGC_CodeClient = :oldCodeClient")
    void updateCodeClient(String code_client, String oldCodeClient);


    @Query("select * from ReglementCaisse where REGC_CodeClient = :oldCodeClient")
    List<ReglementCaisse> getByCodeClient(String oldCodeClient);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<ReglementCaisse> items);

    @Query("DELETE FROM ReglementCaisse where Status='SELECTED'")
    void deleteAllExeptNotSync();

    @Query("DELETE FROM ReglementCaisse")
    void deleteAll();

    @Query("DELETE FROM ReglementCaisse where (REGC_Code=:code or REGC_Code_M=:code) and REGC_Exercice=:exercice and REGC_IdSCaisse=:idCaisse")
    void deleteByCode(String code, String exercice, String idCaisse);
}
