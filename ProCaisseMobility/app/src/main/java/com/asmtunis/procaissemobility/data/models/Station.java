package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.yalantis.filter.model.FilterModel;

import org.jetbrains.annotations.NotNull;

@Entity
public class Station implements FilterModel {
    @NonNull
    @PrimaryKey
    @SerializedName("STAT_Code")
    @Expose
    private String sTATCode;
    @SerializedName("STAT_Desg")
    @Expose
    private String sTATDesg;
    @SerializedName("STAT_Adresse")
    @Expose
    private String sTATAdresse;
    @SerializedName("STAT_Tel1")
    @Expose
    private String sTATTel1;
    @SerializedName("STAT_Tel2")
    @Expose
    private String sTATTel2;
    @SerializedName("STAT_Fax")
    @Expose
    private String sTATFax;
    @SerializedName("STAT_Surface")
    @Expose
    private String sTATSurface;
    @SerializedName("STAT_Emplacement")
    @Expose
    private String sTATEmplacement;
    @SerializedName("STAT_Type")
    @Expose
    private String sTATType;
    @SerializedName("STAT_Etat")
    @Expose
    private String sTATEtat;
    @SerializedName("STAT_station")
    @Expose
    private String sTATStation;
    @SerializedName("STAT_user")
    @Expose
    private String sTATUser;
    @SerializedName("STAT_export")
    @Expose
    private String sTATExport;
    @SerializedName("STAT_DDm")
    @Expose
    private String sTATDDm;
    @SerializedName("STAT_SoldeDD")
    @Expose
    private String sTATSoldeDD;
    @SerializedName("STAT_SoldeDF")
    @Expose
    private String sTATSoldeDF;
    @SerializedName("STAT_Solde")
    @Expose
    private String sTATSolde;
    @SerializedName("STAT_Arabe")
    @Expose
    private String sTATArabe;
    @SerializedName("Adresse_Arabe")
    @Expose
    private String adresseArabe;
    @SerializedName("MAtriculeF")
    @Expose
    private String mAtriculeF;
    @SerializedName("TVA")
    @Expose
    private String tVA;
    @SerializedName("Activite")
    @Expose
    private String activite;
    @SerializedName("Stat_Prefixe")
    @Expose
    private String statPrefixe;
    @SerializedName("STAT_Fidelite")
    @Expose
    private String sTATFidelite;
    @SerializedName("STAT_CA")
    @Expose
    private String sTATCA;
    @SerializedName("STAT_BA")
    @Expose
    private String sTATBA;
    @SerializedName("ddm")
    @Expose
    private String ddm;
    @SerializedName("export")
    @Expose
    private String export;
    @SerializedName("STAT_Et")
    @Expose
    private String sTATEt;
    @SerializedName("Is_Prix_Station")
    @Expose
    private String isPrixStation;
    @SerializedName("STAT_Latitude")
    @Expose
    private String sTATLatitude;
    @SerializedName("STAT_Longitude")
    @Expose
    private String sTATLongitude;
    @SerializedName("CLI_Code1")
    @Expose
    private String cLICode1;
    @SerializedName("Code_Ut1")
    @Expose
    private String codeUt1;
    @SerializedName("Stat_Info1")
    @Expose
    private String statInfo1;
    @SerializedName("Stat_Info2")
    @Expose
    private String statInfo2;
    @SerializedName("Stat_Info3")
    @Expose
    private String statInfo3;
    @SerializedName("Stat_Info4")
    @Expose
    private String statInfo4;
    @SerializedName("Stat_Info5")
    @Expose
    private String statInfo5;
    @SerializedName("Type_Prix")
    @Expose
    private String typePrix;
    @SerializedName("Type_PrixSite")
    @Expose
    private String typePrixSite;

    @SerializedName("STATMetrageM")
    @Expose
    private double statMetrageM;
    @SerializedName("STATSecondeM")
    @Expose
    private double statSecondeM;

    public double getStatMetrageM() {
        return statMetrageM;
    }

    public double getStatSecondeM() {
        return statSecondeM;
    }

    public void setStatMetrageM(double statMetrageM) {
        this.statMetrageM = statMetrageM;
    }

    public void setStatSecondeM(double statSecondeM) {
        this.statSecondeM = statSecondeM;
    }

    @Ignore
    private int colorCode=0;

    public int getColorCode() {
        return colorCode;
    }

    public void setColorCode(int colorCode) {
        this.colorCode = colorCode;
    }

    public String getSTATCode() {
        return sTATCode;
    }

    public void setSTATCode(String sTATCode) {
        this.sTATCode = sTATCode;
    }

    public String getSTATDesg() {
        return sTATDesg;
    }

    public void setSTATDesg(String sTATDesg) {
        this.sTATDesg = sTATDesg;
    }

    public String getSTATAdresse() {
        return sTATAdresse;
    }

    public void setSTATAdresse(String sTATAdresse) {
        this.sTATAdresse = sTATAdresse;
    }

    public String getSTATTel1() {
        return sTATTel1;
    }

    public void setSTATTel1(String sTATTel1) {
        this.sTATTel1 = sTATTel1;
    }

    public String getSTATTel2() {
        return sTATTel2;
    }

    public void setSTATTel2(String sTATTel2) {
        this.sTATTel2 = sTATTel2;
    }

    public String getSTATFax() {
        return sTATFax;
    }

    public void setSTATFax(String sTATFax) {
        this.sTATFax = sTATFax;
    }

    public String getSTATSurface() {
        return sTATSurface;
    }

    public void setSTATSurface(String sTATSurface) {
        this.sTATSurface = sTATSurface;
    }

    public String getSTATEmplacement() {
        return sTATEmplacement;
    }

    public void setSTATEmplacement(String sTATEmplacement) {
        this.sTATEmplacement = sTATEmplacement;
    }

    public String getSTATType() {
        return sTATType;
    }

    public void setSTATType(String sTATType) {
        this.sTATType = sTATType;
    }

    public String getSTATEtat() {
        return sTATEtat;
    }

    public void setSTATEtat(String sTATEtat) {
        this.sTATEtat = sTATEtat;
    }

    public String getSTATStation() {
        return sTATStation;
    }

    public void setSTATStation(String sTATStation) {
        this.sTATStation = sTATStation;
    }

    public String getSTATUser() {
        return sTATUser;
    }

    public void setSTATUser(String sTATUser) {
        this.sTATUser = sTATUser;
    }

    public String getSTATExport() {
        return sTATExport;
    }

    public void setSTATExport(String sTATExport) {
        this.sTATExport = sTATExport;
    }

    public String getSTATDDm() {
        return sTATDDm;
    }

    public void setSTATDDm(String sTATDDm) {
        this.sTATDDm = sTATDDm;
    }

    public String getSTATSoldeDD() {
        return sTATSoldeDD;
    }

    public void setSTATSoldeDD(String sTATSoldeDD) {
        this.sTATSoldeDD = sTATSoldeDD;
    }

    public String getSTATSoldeDF() {
        return sTATSoldeDF;
    }

    public void setSTATSoldeDF(String sTATSoldeDF) {
        this.sTATSoldeDF = sTATSoldeDF;
    }

    public String getSTATSolde() {
        return sTATSolde;
    }

    public void setSTATSolde(String sTATSolde) {
        this.sTATSolde = sTATSolde;
    }

    public String getSTATArabe() {
        return sTATArabe;
    }

    public void setSTATArabe(String sTATArabe) {
        this.sTATArabe = sTATArabe;
    }

    public String getAdresseArabe() {
        return adresseArabe;
    }

    public void setAdresseArabe(String adresseArabe) {
        this.adresseArabe = adresseArabe;
    }

    public String getMAtriculeF() {
        return mAtriculeF;
    }

    public void setMAtriculeF(String mAtriculeF) {
        this.mAtriculeF = mAtriculeF;
    }

    public String getTVA() {
        return tVA;
    }

    public void setTVA(String tVA) {
        this.tVA = tVA;
    }

    public String getActivite() {
        return activite;
    }

    public void setActivite(String activite) {
        this.activite = activite;
    }

    public String getStatPrefixe() {
        return statPrefixe;
    }

    public void setStatPrefixe(String statPrefixe) {
        this.statPrefixe = statPrefixe;
    }

    public String getSTATFidelite() {
        return sTATFidelite;
    }

    public void setSTATFidelite(String sTATFidelite) {
        this.sTATFidelite = sTATFidelite;
    }

    public String getSTATCA() {
        return sTATCA;
    }

    public void setSTATCA(String sTATCA) {
        this.sTATCA = sTATCA;
    }

    public String getSTATBA() {
        return sTATBA;
    }

    public void setSTATBA(String sTATBA) {
        this.sTATBA = sTATBA;
    }

    public String getDdm() {
        return ddm;
    }

    public void setDdm(String ddm) {
        this.ddm = ddm;
    }

    public String getExport() {
        return export;
    }

    public void setExport(String export) {
        this.export = export;
    }

    public String getSTATEt() {
        return sTATEt;
    }

    public void setSTATEt(String sTATEt) {
        this.sTATEt = sTATEt;
    }

    public String getIsPrixStation() {
        return isPrixStation;
    }

    public void setIsPrixStation(String isPrixStation) {
        this.isPrixStation = isPrixStation;
    }

    public String getSTATLatitude() {
        return sTATLatitude;
    }

    public void setSTATLatitude(String sTATLatitude) {
        this.sTATLatitude = sTATLatitude;
    }

    public String getSTATLongitude() {
        return sTATLongitude;
    }

    public void setSTATLongitude(String sTATLongitude) {
        this.sTATLongitude = sTATLongitude;
    }

    public String getCLICode1() {
        return cLICode1;
    }

    public void setCLICode1(String cLICode1) {
        this.cLICode1 = cLICode1;
    }

    public String getCodeUt1() {
        return codeUt1;
    }

    public void setCodeUt1(String codeUt1) {
        this.codeUt1 = codeUt1;
    }

    public String getStatInfo1() {
        return statInfo1;
    }

    public void setStatInfo1(String statInfo1) {
        this.statInfo1 = statInfo1;
    }

    public String getStatInfo2() {
        return statInfo2;
    }

    public void setStatInfo2(String statInfo2) {
        this.statInfo2 = statInfo2;
    }

    public String getStatInfo3() {
        return statInfo3;
    }

    public void setStatInfo3(String statInfo3) {
        this.statInfo3 = statInfo3;
    }

    public String getStatInfo4() {
        return statInfo4;
    }

    public void setStatInfo4(String statInfo4) {
        this.statInfo4 = statInfo4;
    }

    public String getStatInfo5() {
        return statInfo5;
    }

    public void setStatInfo5(String statInfo5) {
        this.statInfo5 = statInfo5;
    }

    public String getTypePrix() {
        return typePrix;
    }

    public void setTypePrix(String typePrix) {
        this.typePrix = typePrix;
    }

    public String getTypePrixSite() {
        return typePrixSite;
    }

    public void setTypePrixSite(String typePrixSite) {
        this.typePrixSite = typePrixSite;
    }

    @Override
    public String toString() {
        return "Station{" +
                "sTATCode='" + sTATCode + '\'' +
                ", sTATDesg='" + sTATDesg + '\'' +
                ", sTATAdresse='" + sTATAdresse + '\'' +
                ", sTATTel1='" + sTATTel1 + '\'' +
                ", sTATTel2='" + sTATTel2 + '\'' +
                ", sTATFax='" + sTATFax + '\'' +
                ", sTATSurface='" + sTATSurface + '\'' +
                ", sTATEmplacement='" + sTATEmplacement + '\'' +
                ", sTATType='" + sTATType + '\'' +
                ", sTATEtat='" + sTATEtat + '\'' +
                ", sTATStation='" + sTATStation + '\'' +
                ", sTATUser='" + sTATUser + '\'' +
                ", sTATExport='" + sTATExport + '\'' +
                ", sTATDDm='" + sTATDDm + '\'' +
                ", sTATSoldeDD='" + sTATSoldeDD + '\'' +
                ", sTATSoldeDF='" + sTATSoldeDF + '\'' +
                ", sTATSolde='" + sTATSolde + '\'' +
                ", sTATArabe='" + sTATArabe + '\'' +
                ", adresseArabe='" + adresseArabe + '\'' +
                ", mAtriculeF='" + mAtriculeF + '\'' +
                ", tVA='" + tVA + '\'' +
                ", activite='" + activite + '\'' +
                ", statPrefixe='" + statPrefixe + '\'' +
                ", sTATFidelite='" + sTATFidelite + '\'' +
                ", sTATCA='" + sTATCA + '\'' +
                ", sTATBA='" + sTATBA + '\'' +
                ", ddm='" + ddm + '\'' +
                ", export='" + export + '\'' +
                ", sTATEt='" + sTATEt + '\'' +
                ", isPrixStation='" + isPrixStation + '\'' +
                ", sTATLatitude='" + sTATLatitude + '\'' +
                ", sTATLongitude='" + sTATLongitude + '\'' +
                ", cLICode1='" + cLICode1 + '\'' +
                ", codeUt1='" + codeUt1 + '\'' +
                ", statInfo1='" + statInfo1 + '\'' +
                ", statInfo2='" + statInfo2 + '\'' +
                ", statInfo3='" + statInfo3 + '\'' +
                ", statInfo4='" + statInfo4 + '\'' +
                ", statInfo5='" + statInfo5 + '\'' +
                ", typePrix='" + typePrix + '\'' +
                ", typePrixSite='" + typePrixSite + '\'' +
                '}';
    }

    @NotNull
    @Override
    public String getText() {
        return "Code: " + sTATCode + "\n"
                + (sTATDesg.length() > 16 ? sTATDesg.substring(0, 13) + "..." : sTATDesg);
    }
}
