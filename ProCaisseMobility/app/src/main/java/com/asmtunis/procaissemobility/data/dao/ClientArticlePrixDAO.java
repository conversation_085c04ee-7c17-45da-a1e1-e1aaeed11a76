package com.asmtunis.procaissemobility.data.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.Article;
import com.asmtunis.procaissemobility.data.models.ClientArticlePrix;

import java.util.List;

/**
 * Created by Oussama AZIZI on 3/9/22.
 */

@Dao
public interface ClientArticlePrixDAO {
    @Query("SELECT * FROM ClientArticlePrix")
    List<ClientArticlePrix> getAll();

    @Query("SELECT * FROM ClientArticlePrix WHERE ART_CLI_CodeArt = :artCliCodeArt AND ART_CLI_CodeCli = :artCliCodeCli")
    ClientArticlePrix getByClientAndProduct(String artCliCodeArt, String artCliCodeCli);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(ClientArticlePrix item);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<ClientArticlePrix> items);

    @Query("DELETE FROM ClientArticlePrix")
    void deleteAll();
}
