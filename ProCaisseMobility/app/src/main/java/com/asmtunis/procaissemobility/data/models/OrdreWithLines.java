package com.asmtunis.procaissemobility.data.models;

import androidx.room.Embedded;
import androidx.room.Relation;

import java.util.List;

public class OrdreWithLines {
    @Embedded public OrdreMission ordreMission;
    @Relation(
            parentColumn = "ORD_Code",
            entityColumn = "LIGOR_Code"
    )
    public List<LigneOrdreMission> ligneOrdreMission;

    public OrdreWithLines(OrdreMission ordreMission, List<LigneOrdreMission> ligneOrdreMission) {
        this.ordreMission = ordreMission;
        this.ligneOrdreMission = ligneOrdreMission;
    }

    @Override
    public String toString() {
        return  ordreMission.oRDDate;
    }
}
