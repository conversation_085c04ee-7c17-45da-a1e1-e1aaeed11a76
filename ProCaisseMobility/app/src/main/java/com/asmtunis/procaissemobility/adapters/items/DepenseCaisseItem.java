package com.asmtunis.procaissemobility.adapters.items;

import android.content.Context;
import android.graphics.Paint;
import android.graphics.Typeface;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.RecyclerView;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.data.models.DepenceType;
import com.asmtunis.procaissemobility.data.models.DepenceCaisse;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.listener.ItemCallback;
import com.asmtunis.procaissemobility.listener.MenuItemClickListener;
import com.asmtunis.procaissemobility.listener.MenuItemsAction;
import com.mikepenz.fastadapter.items.AbstractItem;
import com.mikepenz.fontawesome_typeface_library.FontAwesome;
import com.mikepenz.iconics.IconicsDrawable;

import java.util.List;

/**
 * Created by Oussama AZIZI on 9/1/22.
 */

public class DepenseCaisseItem extends AbstractItem<DepenseCaisseItem, DepenseCaisseItem.ViewHolder> {

    private final int UNSELECTED = -1;
    private final Context context;
    public DepenceCaisse depenceCaisse;
    private final int selectedItem = UNSELECTED;
    MenuItemsAction menuItemsAction;
    protected ItemCallback itemCallback;
    boolean isOrder;
    boolean invoiced = false;
    int menuId;

    public DepenseCaisseItem(Context context, boolean isOrder, DepenceCaisse depenceCaisse, int menuId, MenuItemsAction
            menuItemsAction, ItemCallback itemCallback) {
        this.depenceCaisse = depenceCaisse;
        this.context = context;
        this.menuItemsAction = menuItemsAction;
        this.menuId = menuId;
        this.itemCallback = itemCallback;
        this.isOrder = isOrder;
    }

    public DepenceCaisse getDepenceCaisse() {
        return depenceCaisse;
    }

    public void setDepenceCaisse(DepenceCaisse depenceCaisse) {
        this.depenceCaisse = depenceCaisse;
    }

    //The unique ID for this type of item
    @Override
    public int getType() {
        return R.id.fastadapter_expense_item_id;
    }

    //The unit_price_dialog to be used for this type of item
    @Override
    public int getLayoutRes() {
        return R.layout.ticket_item;
    }

    //The logic to bind your data to the view
    @Override
    public void bindView(final ViewHolder viewHolder, List<Object> payloads) {
        //call super so the selection is already handled for you
        super.bindView(viewHolder, payloads);
        String numFact = "";
        viewHolder.setIsRecyclable(false);
       /* if (depenceCaisse.isSync) {
            menuId = 0;
        }

        if (menuId != 0) {*/
            viewHolder.toolbar.inflateMenu(menuId);
        //}

        viewHolder.tiketuserImV.setVisibility(View.INVISIBLE);
        // DepenceType selectedDepenceType = App.database.depenceTypeDAO().getDepenseDetails(depenceCaisse.getDepCode());
        DepenceType selectedDepenceType = App.database.depenceTypeDAO().getOneByCodeM(depenceCaisse.getDepCode());

        if(selectedDepenceType !=null){
            viewHolder.ticketUser.setText(selectedDepenceType.getDepLib());
            viewHolder.ticketNumber.setText(String.format(context.getString(R.string.depense_number_field), selectedDepenceType.getDepCode()));

        }

        else {
            viewHolder.ticketUser.setText("Code Type Dépence : " +depenceCaisse.getDepCode());
            viewHolder.ticketNumber.setText(String.format(context.getString(R.string.depense_number_field), depenceCaisse.getDepCode()));

        }

        viewHolder.price.setText(String.format("%s %s", StringUtils.priceFormat(Double.parseDouble(depenceCaisse.getDepMontant())),
                new PrefUtils(context).getCurrency()));


        viewHolder.dateCreation.setText(depenceCaisse.getDepDDm());
        viewHolder.toolbar.setTag("toolbar_" + depenceCaisse.getDepCode());
        viewHolder.itemStatusLabel.setTag("itemStatusLabel_" + depenceCaisse.getDepCode());

        viewHolder.price.setVisibility(View.VISIBLE);
        viewHolder.price.setTypeface(viewHolder.price.getTypeface(), Typeface.BOLD);
        viewHolder.price.setTextColor(context.getResources().getColor(R.color.successColor));

//        for (LigneTicket ligneTicket : ligneTickets) {
//            System.out.println(ligneTicket);
//            if (ligneTicket.lTnumFacture != null) {
//                invoiced = true;
//                numFact = ligneTicket.lTnumFacture;
//            }
//        }

        /* if (ticket.tIKNumeroBL!=null){
            invoiced = true;
            numFact = ticket.tIKNumeroBL;
        }*/

        /*
        if (ticket.tIKAnnuler == 1) {
            viewHolder.state.setText("A");
            viewHolder.price.setPaintFlags(viewHolder.price.getPaintFlags() & (~Paint.STRIKE_THRU_TEXT_FLAG));
            viewHolder.state.setBackground(context.getResources().getDrawable(R.drawable.round_corner_a));
            viewHolder.price.setTextColor(context.getResources().getColor(R.color.errorColor));
        }
*/

    //    if (menuId != 0) {
            if (!depenceCaisse.isSync) {
                setTriangleView(viewHolder.itemStatusLabel, 0);
                viewHolder.state.setText("NOT SYNC");
                viewHolder.price.setPaintFlags(viewHolder.price.getPaintFlags() & (~Paint.STRIKE_THRU_TEXT_FLAG));
                viewHolder.state.setBackground(context.getResources().getDrawable(R.drawable.round_corner_c));
                viewHolder.price.setTextColor(context.getResources().getColor(R.color.warningColor));
                viewHolder.toolbar.getMenu().findItem(R.id.edit_item).setVisible(true);
                viewHolder.toolbar.getMenu().findItem(R.id.delete_item).setVisible(true);
            } else {
                setTriangleView(viewHolder.itemStatusLabel, 1);
                viewHolder.toolbar.getMenu().findItem(R.id.edit_item).setVisible(false);
                viewHolder.toolbar.getMenu().findItem(R.id.delete_item).setVisible(false);
            }

            viewHolder.toolbar.setOnMenuItemClickListener(new MenuItemClickListener<>(depenceCaisse,
                    menuItemsAction));
            viewHolder.toolbar.getMenu().findItem(R.id.edit_item).setIcon(new IconicsDrawable(context)
                    .icon(FontAwesome.Icon.faw_eye)
                    .color(context.getResources().getColor(R.color.material_teal700))
                    .sizeDp(20));

            viewHolder.toolbar.getMenu().findItem(R.id.delete_item).setIcon(new IconicsDrawable(context)
                    .icon(FontAwesome.Icon.faw_delicious)
                    .color(context.getResources().getColor(R.color.material_teal700))
                    .sizeDp(20));

          /*  //if(ticket.tIK_Source == null) viewHolder.toolbar.getMenu().findItem(R.id.edit_item).setVisible(false);
            if (!invoiced) {
                //viewHolder.toolbar.getMenu().findItem(R.id.edit_item).setVisible(true);
                viewHolder.toolbar.getMenu().findItem(R.id.invoice_item).setVisible(true);

            } else {
                viewHolder.toolbar.getMenu().findItem(R.id.edit_item).setVisible(false);
                viewHolder.toolbar.getMenu().findItem(R.id.invoice_item).setVisible(false);
                viewHolder.ticketNumber.setText(String.format(context.getString(R.string.fact), numFact));
                setTriangleView(viewHolder.itemStatusLabel, 0);
            }*/

    //    }

        if (itemCallback != null) {
            viewHolder.itemView.setOnClickListener(v -> onViewClick(viewHolder));
            viewHolder.toolbar.setOnClickListener(v -> onViewClick(viewHolder));
        }

    }

    void onViewClick(DepenseCaisseItem.ViewHolder viewHolder) {
        if (depenceCaisse != null) {
            if (itemCallback == null) {
                return;
            } else {
                itemCallback.onItemClicked(viewHolder, depenceCaisse);
            }

        }
    }

    //reset the view here (this is an optional method, but recommended)
    @Override
    public void unbindView(final ViewHolder holder) {
        super.unbindView(holder);
        holder.toolbar.setTitle(null);

        holder.toolbar.setSubtitle(null);
        holder.dateCreation.setText(null);
        holder.price.setText(null);
      /*  holder.tel1.setText(null);
        holder.address.setText(null);*/
//        holder.count.setText(item.getDateCreation());

    }

    //Init the viewHolder for this Item
    @Override
    public ViewHolder getViewHolder(View v) {
        return new ViewHolder(v);
    }

    void setTriangleView(jp.shts.android.library.TriangleLabelView labelView, int status) {
        labelView.setVisibility(View.VISIBLE);
        switch (status) {
            case 0:
                labelView.setTriangleBackgroundColorResource(R.color.material_deeporange300);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.notSync);
                labelView.setPrimaryTextColorResource(R.color.md_red_100);

                break;

            case 1:
                labelView.setTriangleBackgroundColorResource(R.color.md_green_800);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.Sync);
                labelView.setPrimaryTextColorResource(R.color.md_green_100);
                break;
            default:
                labelView.setVisibility(View.GONE);

                break;
        }

    }

    //The viewHolder used for this item. This viewHolder is always reused by the RecyclerView so scrolling is blazing fast
    public static class ViewHolder extends RecyclerView.ViewHolder {

        //    @BindView(R.id.expandable_layout)
        com.asmtunis.procaissemobility.ui.components.TicketView ticketView;
        LinearLayout footerLayout;
        //public ExpandableLayout expandableLayout;
        // @BindView(R.id.toolbar)
        public Toolbar toolbar;
        // @BindView(R.id.dateCreation)
        TextView dateCreation;
        //  @BindView(R.id.price)
        TextView price;
        //  @BindView(R.id.content_layout)
        FrameLayout content;
        TextView state;

        TextView ticketNumber;
        TextView ticketUser;
        jp.shts.android.library.TriangleLabelView itemStatusLabel;
        DepenceCaisse depenceCaisse;
        LinearLayout linearLayout;
        LinearLayout llTicketUser;
        ImageView tiketuserImV;

        public ViewHolder(View view) {
            super(view);
            //       ButterKnife.bind(this, view);

            ticketView = view.findViewById(R.id.layout_ticket);
            footerLayout = view.findViewById(R.id.footer_layout);
            //    expandableLayout = view.findViewById(R.id.expandable_layout);
            toolbar = view.findViewById(R.id.toolbar);
            price = view.findViewById(R.id.price);
            dateCreation = view.findViewById(R.id.dateCreation);
            content = view.findViewById(R.id.content_layout);
            itemStatusLabel = view.findViewById(R.id.item_status_label);
            llTicketUser = view.findViewById(R.id.ticketUserLL);
            ticketNumber = view.findViewById(R.id.ticketNumber);
            ticketUser = view.findViewById(R.id.ticketUser);
            state = view.findViewById(R.id.state);
            linearLayout = view.findViewById(R.id.linearLayout);
            tiketuserImV = view.findViewById(R.id.tiketuserImV);

        }
    }
}

