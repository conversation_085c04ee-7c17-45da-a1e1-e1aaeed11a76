package com.asmtunis.procaissemobility.data.network.datamanager;


import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.ClientService;

import java.util.List;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

import android.util.Log;

/**
 * Created by Achraf on 26/09/2017.
 */

public class ClientDataManager  {

    private static ClientDataManager sInstance;

    private final ClientService mClientService;

    public ClientDataManager( ) {
        mClientService = new ServiceFactory<>(ClientService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Client")).makeService();
    }

    public static ClientDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new ClientDataManager();
        }
        return sInstance;
    }

    public void getClients(GenericObject genericObject,
                           RemoteCallback<List<Client>> listener, String clientEquiv) {
        System.out.println("myobj"+genericObject);
        Log.d("myclient","clientEquiv "+ clientEquiv);

        mClientService.getClients(genericObject, clientEquiv)
                .enqueue(listener);
    }

    public void addClient(GenericObject client,
                          RemoteCallback<Client> listener) {
        mClientService.addClient(client)
                .enqueue(listener);
    }

    public void addBatchClient(GenericObject client,
                          RemoteCallback<List<Client>> listener) {
        mClientService.addBatchClient(client)
                .enqueue(listener);
    }


    public void updatClient(GenericObject client,
                               RemoteCallback<Boolean> listener) {
        mClientService.updateClient(client)
                .enqueue(listener);
    }


}