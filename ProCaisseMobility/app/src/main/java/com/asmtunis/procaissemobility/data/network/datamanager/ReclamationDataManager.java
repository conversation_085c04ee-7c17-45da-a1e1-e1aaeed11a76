package com.asmtunis.procaissemobility.data.network.datamanager;

import android.content.Context;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Reclamation;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.ReclamationService;

import java.util.List;

import license.model.ServerResponse;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

//import com.blankj.utilcode.util.FileUtils;

/**
 * Created by PC on 10/4/2017.
 */

public class ReclamationDataManager  {
    private static ReclamationDataManager sInstance;
    private final ReclamationService mReclamationService;
    Context context;
    public ReclamationDataManager() {
        this.context= context;
        mReclamationService = new ServiceFactory<>(ReclamationService.class, String.format(BASE_URL,App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Reclamation")).makeService();
    }

    public static ReclamationDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new ReclamationDataManager();
        }
        return sInstance;
    }

    public void getReclamations(GenericObject genericObject,
            RemoteCallback<List<Reclamation>> listener) {
        mReclamationService.getReclamations(genericObject)
                .enqueue(listener);
    }

  public void addBatchReclamation(GenericObject genericObject,
            RemoteCallback<List<Reclamation>> listener) {


      mReclamationService.addBatchReclamation(genericObject)
                .enqueue(listener);
    }


    public void uploadFile(RemoteCallback<ServerResponse> listener) {
      /*  File file = FileUtils.getFileByPath(Globals.IMAGE_DIRECTORY_PATH+"/files.zip");
        RequestBody requestFile =
                RequestBody.create(MediaType.parse("multipart/form-data"), file);

// MultipartBody.Part is used to send also the actual file name
        MultipartBody.Part body =
                MultipartBody.Part.createFormData("files", file.getName(), requestFile);

        mReclamationService.uploadFile(requestFile,body)
                .enqueue(listener);*/
    }


    public void uploadMultipart(/*GenericObject genericObject*/)   {


    /*    new ZipFilesTask(App.getInstance().getApplicationContext(), new AsyncResponse<Boolean>() {
            @Override
            public void processFinish(Boolean output){
                Log.d("dfsfd",String.format(BASE_URL,App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Reclamation")+"uploadReclamationFile");
                String uploadId = UUID.randomUUID().toString() + System.currentTimeMillis();
                //Creating a multi part request
                try {
                    if (FileUtils.isFileExists(Globals.IMAGE_DIRECTORY_PATH+"/files.zip"))
                    new MultipartUploadRequest(context, uploadId, String.format(BASE_URL,App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Reclamation")+"uploadReclamationFile")
                            .addFileToUpload(FileUtils.getFileByPath(Globals.IMAGE_DIRECTORY_PATH+"/files.zip").getAbsolutePath(), "files").setNotificationConfig(new UploadNotificationConfig()) //Adding file
                            //   .addParameter("name", "files")
                            //Adding text parameter to the request
                            *//* .addParameter("Reclamation",   new GsonBuilder().create().toJson(genericObject)) //Adding text parameter to the request*//*
                            .setMaxRetries(2).setAutoDeleteFilesAfterSuccessfulUpload(true)
                            .startUpload(); //Starting the upload
                } catch (MalformedURLException e) {
                    Log.d("sdfsdf",e.toString());
                    e.printStackTrace();
                } catch (FileNotFoundException e) {
                    e.printStackTrace();
                    Log.d("sdfsdf",e.toString());
                }
            }
        }).execute();*/




    }




}

