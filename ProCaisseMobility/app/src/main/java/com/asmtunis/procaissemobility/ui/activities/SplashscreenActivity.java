package com.asmtunis.procaissemobility.ui.activities;

import static android.text.InputType.TYPE_CLASS_NUMBER;
import static android.text.InputType.TYPE_NUMBER_FLAG_DECIMAL;
import static com.asmtunis.procaissemobility.helper.Globals.CHECK_LICENCE_BASEURL;
import static com.asmtunis.procaissemobility.helper.Globals.CHECK_LICENCE_URL;
import static com.asmtunis.procaissemobility.helper.Globals.DEFAULT_VALUE;
import static com.asmtunis.procaissemobility.helper.Globals.GET_BASE_CONFIG_BASEURL;
import static com.asmtunis.procaissemobility.helper.Globals.GET_BASE_CONFIG_URL;
import static com.asmtunis.procaissemobility.helper.Globals.STATISTICS_DB_KEY;
import static com.asmtunis.procaissemobility.helper.Globals.getApplicationVerion;
import static com.asmtunis.procaissemobility.helper.utils.DateUtils.getCurrentDate;
import static com.asmtunis.procaissemobility.helper.utils.StringUtils.decimalFormat;

import android.Manifest;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.Paint;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.text.Html;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatSpinner;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.text.HtmlCompat;

import com.afollestad.materialdialogs.MaterialDialog;
import com.arasthel.asyncjob.AsyncJob;
import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.AppProperties;
import com.asmtunis.procaissemobility.data.models.Article;
import com.asmtunis.procaissemobility.data.models.ArticleCodeBar;
import com.asmtunis.procaissemobility.data.models.Banque;
import com.asmtunis.procaissemobility.data.models.BonCommande;
import com.asmtunis.procaissemobility.data.models.BonRetour;
import com.asmtunis.procaissemobility.data.models.CarteResto;
import com.asmtunis.procaissemobility.data.models.ChequeCaisse;
import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.data.models.ClientArticlePrix;
import com.asmtunis.procaissemobility.data.models.Facture;
import com.asmtunis.procaissemobility.data.models.Immobilisation;
import com.asmtunis.procaissemobility.data.models.Connexion;
import com.asmtunis.procaissemobility.data.models.DNFamille;
import com.asmtunis.procaissemobility.data.models.DNSuperficie;
import com.asmtunis.procaissemobility.data.models.DNTypePVente;
import com.asmtunis.procaissemobility.data.models.DNTypeServices;
import com.asmtunis.procaissemobility.data.models.DNVIsite;
import com.asmtunis.procaissemobility.data.models.DN_LigneVisite;
import com.asmtunis.procaissemobility.data.models.DepenceType;
import com.asmtunis.procaissemobility.data.models.DepenceCaisse;
import com.asmtunis.procaissemobility.data.models.Devise;
import com.asmtunis.procaissemobility.data.models.Etablisement;
import com.asmtunis.procaissemobility.data.models.EtatOrdreMission;
import com.asmtunis.procaissemobility.data.models.Exercice;
import com.asmtunis.procaissemobility.data.models.Famille;
import com.asmtunis.procaissemobility.data.models.Fournisseur;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.LicenseResponse;
import com.asmtunis.procaissemobility.data.models.LicenseResponseItem;
import com.asmtunis.procaissemobility.data.models.LigneBonCommande;
import com.asmtunis.procaissemobility.data.models.LigneBonRetour;
import com.asmtunis.procaissemobility.data.models.LigneTicket;
import com.asmtunis.procaissemobility.data.models.Marque;
import com.asmtunis.procaissemobility.data.models.OrdreMission;
import com.asmtunis.procaissemobility.data.models.OrdreWithLines;
import com.asmtunis.procaissemobility.data.models.Parametrages;
import com.asmtunis.procaissemobility.data.models.Prefixe;
import com.asmtunis.procaissemobility.data.models.PricePerStation;
import com.asmtunis.procaissemobility.data.models.Reclamation;
import com.asmtunis.procaissemobility.data.models.ReglementCaisse;
import com.asmtunis.procaissemobility.data.models.SessionCaisse;
import com.asmtunis.procaissemobility.data.models.Station;
import com.asmtunis.procaissemobility.data.models.StationStock;
import com.asmtunis.procaissemobility.data.models.Statistics;
import com.asmtunis.procaissemobility.data.models.Ticket;
import com.asmtunis.procaissemobility.data.models.Timbre;
import com.asmtunis.procaissemobility.data.models.TraiteCaisse;
import com.asmtunis.procaissemobility.data.models.TrakingAlarm;
import com.asmtunis.procaissemobility.data.models.Utilisateur;
import com.asmtunis.procaissemobility.data.models.VCAutre;
import com.asmtunis.procaissemobility.data.models.VCImage;
import com.asmtunis.procaissemobility.data.models.VCListeConcurrent;
import com.asmtunis.procaissemobility.data.models.VCNewProduct;
import com.asmtunis.procaissemobility.data.models.VCPrix;
import com.asmtunis.procaissemobility.data.models.VCPromo;
import com.asmtunis.procaissemobility.data.models.VCTypeCommunication;
import com.asmtunis.procaissemobility.data.models.Vendeur;
import com.asmtunis.procaissemobility.data.models.Ville;
import com.asmtunis.procaissemobility.data.models.custom.SessionActivation;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.datamanager.ActivationDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.ArticleDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.BanqueDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.BaseConfigDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.BonCommandeDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.BonRetourDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.CarteRestoDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.ChequeCaisseDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.ClientArticlePrixDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.ClientDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.CodeBareDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.DepenceCaisseDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.DepenceDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.DeviseDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.DistributionNumeriqueDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.EtablisementDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.EtatOrdreMissionDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.ExerciceDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.FactureDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.FamilleDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.FournisseurDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.ImmobilisationDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.LicenseDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.LigneBonCommandeDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.LigneBonRetourDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.LigneTicketDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.MarqueDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.MiscDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.OrdreMissionDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.ParametragesDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.PrefixeDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.PricePerStationDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.ReclamationDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.ReglementCaisseDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.SessionCaisseDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.StationDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.StationStockDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.TicketDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.TimbreDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.TraiteCaisseDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.UtilisateurDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.VeuilleConcurrentielleDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.VilleDataManager;
import com.asmtunis.procaissemobility.helper.AppHelper;
import com.asmtunis.procaissemobility.helper.Globals;
import com.asmtunis.procaissemobility.helper.utils.DateUtils;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.helper.utils.UIUtils;
import com.asmtunis.procaissemobility.helper.utils.Utils;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.DeviceUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ObjectUtils;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.mikepenz.fontawesome_typeface_library.FontAwesome;
import com.mikepenz.iconics.IconicsDrawable;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;
import es.dmoral.toasty.Toasty;
import io.paperdb.Paper;
import license.model.Activation;
import license.model.Licence;


public class SplashscreenActivity extends BaseActivity {



    boolean doubleBackToExitPressedOnce = false;
    private static final int REQUEST_CODE = 0x11;


    @BindView(R.id.progressLayout)
    com.vlonjatg.progressactivity.ProgressFrameLayout progressLayout;

    @BindView(R.id.defaultLayout)
    FrameLayout defaultLayout;

    @BindView(R.id.loadingView)
    FrameLayout loadingView;

    @BindView(R.id.RetryButton)
    androidx.appcompat.widget.AppCompatButton RetryButton;

    @BindView(R.id.applicationVesion)
    TextView applicationVesion;

    @BindView(R.id.linkChangeKey)
    TextView linkChangeKey;

    @BindView(R.id.serialKeyField1)
    AppCompatSpinner serialKeyField;


    @BindView(R.id.submitButton)
    AppCompatButton submitButton;

    @BindView(R.id.licenceKeyView)
    LinearLayout licenceKeyView;

    @BindView(R.id.loading)
    TextView loading;

    @BindView(R.id.deivceID)
    TextView deivceID;

    TextView baseConfigName;

    Unbinder unbinder;
    Activity context;

    List<Connexion> listConfig = new ArrayList<>();
    PrefUtils prefUtils;
    Boolean newSession = false;

    com.akexorcist.roundcornerprogressbar.RoundCornerProgressBar progress;

    LinearLayout progressLayout2;

    String actionFrom = "";

    String lastLoaded = "";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        context = this;
        Utils.validateBaseUrl();
        FirebaseCrashlytics.getInstance().setUserId(DeviceUtils.getAndroidID());

        prefUtils = new PrefUtils(context);
        unbinder = ButterKnife.bind(this);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        applicationVesion.setText(getApplicationVerion());
        deivceID.setText(DeviceUtils.getAndroidID());


        Log.d("plrvdssccdcs", AppUtils.getAppName());
        getBackUpUrlLicenceFromLocalDB();



        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M){


            String[] permissions;
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                permissions =  new String[]{
                        "android.permission.CAMERA",
                        "android.permission.READ_EXTERNAL_STORAGE",
                        "android.permission.WRITE_EXTERNAL_STORAGE",
                        "android.hardware.camera",
                        Manifest.permission.ACCESS_COARSE_LOCATION,
                        Manifest.permission.ACCESS_FINE_LOCATION,
                        Manifest.permission.BLUETOOTH_SCAN,
                        Manifest.permission.BLUETOOTH_CONNECT,
                        Manifest.permission.SCHEDULE_EXACT_ALARM,
                };
            }
            else
                permissions = new String[]{
                    "android.permission.CAMERA",
                    "android.permission.READ_EXTERNAL_STORAGE",
                    "android.permission.WRITE_EXTERNAL_STORAGE",
                    "android.hardware.camera",
                    Manifest.permission.ACCESS_COARSE_LOCATION,
                    Manifest.permission.ACCESS_FINE_LOCATION,

            };
            requestPermissions(permissions, REQUEST_CODE); // without sdk version check
        }

        else showContent();


        RetryButton.setOnClickListener(v -> {
            handleRetryCases();
        });


        baseConfigName = findViewById(R.id.baseConfigName);
        baseConfigName.setText(App.prefUtils.getBaseConfigName());
        baseConfigName.setText(Build.VERSION.SDK_INT >= Build.VERSION_CODES.N ? Html.fromHtml(App.prefUtils.getBaseConfigName(), Html.FROM_HTML_MODE_COMPACT) : Html.fromHtml(App.prefUtils.getBaseConfigName()));
    }


    List<String> getGrantedPermissions() {
        List<String> granted = new ArrayList<String>();
        try {
            PackageInfo pi = getPackageManager().getPackageInfo(getApplicationContext().getPackageName(), PackageManager.GET_PERMISSIONS);
            for (int i = 0; i < pi.requestedPermissions.length; i++) {
                if ((pi.requestedPermissionsFlags[i] & PackageInfo.REQUESTED_PERMISSION_GRANTED) != 0) {
                    granted.add(pi.requestedPermissions[i]);
                }
            }
        } catch (Exception e) {
        }
        return granted;
    }




    private void handleRetryCases() {
        switch (lastLoaded) {
            case "":
                showContent();
                break;



            case "getFacture":
                getFacture();
                break;
            case "getCaisses":
                getCaisses();
                break;
            case "getSessionCaisseByUser":
                getSessionCaisseByUser();
                break;
            case "getSessionCaisses":
                getSessionCaisses();
                break;
            case "getBanques":
                getBanques();
                break;
            case "getClients":
                getClients();
                break;
            case "getArticleCodeBare":
                getArticleCodeBare();
                break;
            case "getArticles":
                getArticles();
                break;
            case "getArticlesStock":
                getArticlesStock();
                break;
            case "getReclamations":
                getReclamations();
                break;
            case "getBonRetour":
                getBonRetour();
                break;
            case "getLigneBonRetour":
                getLigneBonRetour();
                break;
            case "getBonCommande":
                getBonCommande();
                break;
            case "getLigneBonCommande":
                getLigneBonCommande();
                break;
            case "getFamilles":
                getFamilles();
                break;
            case "getTimbres":
                getTimbres();
                break;
            case "getEtatOrdreMission":
                getEtatOrdreMission();
                break;
            case "getOrdreWithLines":
                getOrdreWithLines();
                break;
            case "getVilles":
                getVilles();
                break;
            case "getTickets":
                getTickets();
                break;
            //else if(lastLoaded.equals("getLigneTickets")) getLigneTickets();
            case "getReglementsCaisse":
                getReglementsCaisse();
                break;
            // else if(lastLoaded.equals("getChequesCaisse")) getChequesCaisse();
            // else if(lastLoaded.equals("getTraitesCaisse")) getTraitesCaisse();
            case "getAllTypeServices":
                getAllTypeServices();
                break;
            case "getAllSuperficieService":
                getAllSuperficieService();
                break;
            case "getAllTypePVentes":
                getAllTypePVentes();
                break;
            case "getAllDNVIsites":
                getAllDNVIsites();
                break;
            case "getAllLignevisitebyUser":
                getAllLignevisitebyUser();
                break;
            case "getallDNFamille":
                getallDNFamille();
                break;
            case "getDevises":
                getDevises();
                break;
            case "getMaxTikNum":
                getMaxTikNum();
                break;
            case "getPrefixes":
                getPrefixes();
                break;


            case "getParametrages":
                getParametrages();
                break;

            case "getImmobilisation":
                getImmobilisation();
                break;

            case "getPrices":
                getPrices();
                break;
            case "getStations":
                getStations();
                break;
            case "getFornisseurs":
                getFornisseurs();
                break;
            case "getEtablisements":
                getEtablisements();
                break;
            case "getStatistics":
                getStatistics();
                break;
            case "getCartesResto":
                getCartesResto();
                break;
            case "getClientsArticlePrix":
                getClientsArticlePrix();
                break;
            case "getVCNewProducts":
                getVCNewProducts();
                break;
            case "getVCPromos":
                getVCPromos();
                break;
            case "getVCPrix":
                getVCPrix();
                break;
            case "getVCAutre":
                getVCAutre();
                break;
            case "getVCTypeCommunication":
                getVCTypeCommunication();
                break;
            case "getVCListConcurrent":
                getVCListConcurrent();
                break;
            case "getVCImages":
                getVCImages();
                break;
            case "LoadBackUpLicensesUrl":
                LoadBackUpLicensesUrl();
                break;

            case "getExpenses":
                getExpenses();
                break;

            case "getDepenceCaisseByCaisseId":
                getDepenceCaisseByCaisseId();
                break;

            case "getMarque":
                getMarque();
                break;
        }

        licenceKeyView.setVisibility(View.GONE);
        loadingView.setVisibility(View.VISIBLE);
        RetryButton.setVisibility(View.GONE);
        progressLayout2.setVisibility(View.VISIBLE);
        linkChangeKey.setVisibility(View.GONE);
        defaultLayout.setVisibility(View.VISIBLE);
    }


    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_CODE) {
            if(grantResults.length == 0) {
                Toast.makeText(context, "PERMISSION_DENIED", Toast.LENGTH_SHORT).show();
            }
            else {
                if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    showContent();
                }
                else {
                    Toast.makeText(context, "PERMISSION_DENIED", Toast.LENGTH_SHORT).show();
                    if (ContextCompat.checkSelfPermission(context,Manifest.permission.ACCESS_FINE_LOCATION)!= PackageManager.PERMISSION_GRANTED) {
                        if (ActivityCompat.shouldShowRequestPermissionRationale(context,
                                Manifest.permission.ACCESS_FINE_LOCATION) ||ActivityCompat.shouldShowRequestPermissionRationale(context,
                                Manifest.permission.ACCESS_COARSE_LOCATION)  ) {
                            // Show an expanation to the user

                            new MaterialDialog.Builder(context)
                                    // .title("Info :")
                                    .content(context.getString(R.string.disclaimer_permission))
                                    .positiveText("Oui")
                                    .negativeText("Non")
                                    .onPositive((dialog, which) -> {
                                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                                            requestPermissions(permissions, REQUEST_CODE); // without sdk version check
                                        }
                                    })

                                    .onNegative((dialog, which) -> {
                                        finish();
                                    })
                                    .show();

                        }
                    }


                }
            }

        }
    }

    void showContent() {
        progressLayout2 = findViewById(R.id.progressLayout2);
        if (progress == null) progress = findViewById(R.id.progress);
         runOnUiThread(() -> {
            if (isMissingConfigData()) {
                 App.database.appPropertiesDAO().insert(new AppProperties());
                if (NetworkUtils.isConnected()) {

                    checkLicense();
                } else {
                    if (!App.database.baseConfigDAO().getAll().isEmpty()) {
                        if (progress != null) progress.setVisibility(View.INVISIBLE);
                        if (loading != null) loading.setVisibility(View.INVISIBLE);


                       // List<Connexion> response = App.database.baseConfigDAO().getAll();
                       // setListBaseConfig(response);
                    } else progressLayout.showError(new IconicsDrawable(context)
                                    .icon(FontAwesome.Icon.faw_database)
                                    .color(getResources().getColor(R.color.material_white))
                                    .sizeDp(512), getString(R.string.missing_data),
                            getString(R.string.no_connection_and_no_confing_data),
                            getString(R.string.retry), v -> {
                                finish();
                                startActivity(SplashscreenActivity.class);
                            });
                }
            } else {
                if (isExpiredLicense()) {
                    App.prefUtils.clearSharedPreferences();
                    progressLayout.showError(new IconicsDrawable(context)
                                    .icon(FontAwesome.Icon.faw_user_alt_slash)
                                    .color(getResources().getColor(R.color.material_white))
                                    .sizeDp(512), getString(R.string.expired_license_title),
                            getString(R.string.expired_license_msg) + "\n" + getString(R.string.your_divice_id) + DeviceUtils.getAndroidID(),
                            getString(R.string.try_again), v -> {
                                finish();
                                startActivity(SplashscreenActivity.class);
                            });
                } else {
                    if (isNonConnectedUser()) {

                        if (actionFrom != null)
                           LoadSpinner("isNonConnectedUser","1");
                         else
                             startActivity(LoginActivity.class);

                    } else {
                        loadingView.setVisibility(View.VISIBLE);
                        if (prefUtils.getLoadData().equals("")) {

                            if (prefUtils.getBlAuthorization()) getCaisses();
                            else getExercice();
                        } else {

                            /**
                             * when install new apk version if db empty the reload
                             */

                            if (App.database.authorizationDAO().getAll().isEmpty()) {
                                getExercice();
                            }

                            /**
                             * to reload auth into db
                             */

                            else{
                                LoadSpinner("is_ConnectedUser","2");
                            }
                        }
                    }
                }
            }
        });

    }

    private boolean isNonConnectedUser() {
        return ObjectUtils.isEmpty(prefUtils.getUserAccount());
    }

    private boolean isExpiredLicense() {

        return !StringUtils.isEmptyString(App.prefUtils.getExpirationDate()) && DateUtils.getRemainingTime(App.prefUtils.getExpirationDate()) <= 0;
    }

    private void checkLicense() {
        progressLayout.showContent();
        if (loadingView != null) loadingView.setVisibility(View.VISIBLE);
        ActivationDataManager.getInstance().checkActivation(new Activation(context), new RemoteCallback<Licence>(context, false) {
            @Override
            public void onSuccess(Licence output) {
                if (isValidLicense(output)) {
                    if (DateUtils.getRemainingTime(output.getDatef()) <= 0) {
                        App.prefUtils.clearSharedPreferences();
                        progressLayout.showError(new IconicsDrawable(context)
                                        .icon(FontAwesome.Icon.faw_user_alt_slash)
                                        .color(getResources().getColor(R.color.material_white))
                                        .sizeDp(512), getString(R.string.expired_license_title),
                                getString(R.string.expired_license_msg) + "\n" + getString(R.string.your_divice_id) + DeviceUtils.getAndroidID(),
                                getString(R.string.try_again), v -> {
                                    finish();
                                    startActivity(SplashscreenActivity.class);
                                });
                    } else {
                        if (output.getNbjr() < 30 && output.getNbjr() > 0 && output.getDemo().equals("true")) {
                            Toast.makeText(getApplicationContext(), "Licence expire dans " + output.getNbjr() + " Jours", Toast.LENGTH_LONG).show();
                        }
                        setupView();
                    }
                    App.prefUtils.setIsActivated(output.getActivated());
                    App.prefUtils.setExpirationDte(output.getDatef());
                    App.prefUtils.setEntrepriseAdresse(output.getActivated());

                    if (App.prefUtils.getIsActivated().equals("true")) {
                        if (StringUtils.isEmptyString(new PrefUtils(context).getSerialKey())) {
                            LoadSpinner("checkLicense","3");
                        } else {

                            // if(!Objects.equals(App.prefUtils.getUserType(), "Commercial") && !Objects.equals(App.prefUtils.getUserType(), "Operateur patrimoine"))
                            if (prefUtils.getBlAuthorization()) getCaisses();
                            else getExercice();
                        }
                    }
                    else {
                        progressLayout.showError(new IconicsDrawable(context)
                                        .icon(FontAwesome.Icon.faw_user_alt_slash)
                                        .color(getResources().getColor(R.color.material_white))
                                        .sizeDp(512), getString(R.string.expired_license_title),
                                getString(R.string.expired_license_msg),
                                getString(R.string.try_again), v -> {
                                    finish();
                                    startActivity(SplashscreenActivity.class);
                                });
                    }
                } else {
                    startActivity(DemoActivity.class);
//                    startActivity(DemandeLicenceActivity.class);
                }
            }




            @Override
            public void onUnauthorized() {
                showRetryButton();
            }

            @Override
            public void onFailed(Throwable throwable) {
            /*    loadingView.setVisibility(View.GONE);
                progress.setVisibility(View.GONE);
              //  Local

                if (StringUtils.isEmptyString(new PrefUtils(context).getSerialKey())) {
                    licenceKeyView.setVisibility(View.VISIBLE);
                    loadingView.setVisibility(View.GONE);
                    LoadSpinner("checkLicense");
                } else {

                    // if(!Objects.equals(App.prefUtils.getUserType(), "Commercial") && !Objects.equals(App.prefUtils.getUserType(), "Operateur patrimoine"))
                    if (prefUtils.getBlAuthorization()) getCaisses();
                    else getExercice();
                }*/

             /*    prefUtils.setIsActivated("true");
                    prefUtils.setExpirationDte("2200-10-12");
                    Connexion connexion = GsonUtils.fromJson(ResourceUtils
                            .readAssets2String("config/baseConfig.json"), Connexion.class);
                    prefUtils.setBaseConfig(connexion);
                    prefUtils.setServerIPAddress(connexion.getAdresseIp());
                    prefUtils.setServerPort(Integer.parseInt(connexion.getPort()));
                    prefUtils.setSerialKey(connexion.getKeyBase());
                  //  setAppConfig();
*/


                showRetryButton();

            }
        });
    }
    private boolean isValidLicense(Licence output) {
        return output != null && (output.getId() != 0 && output.getIdDevice() != null && !output.getIdDevice().isEmpty()
                && !output.getIdDevice().equals(null));
    }
    private boolean isMissingConfigData() {
        //return true;
        return prefUtils.getSerialKey() == null ||
                prefUtils.getBaseConfig() == null ||
                prefUtils.getBaseConfig().getAdresseIp() == null ||
                (prefUtils.getBaseConfig().getAdresseIp() != null &&
                        prefUtils.getBaseConfig().getAdresseIp().equalsIgnoreCase(DEFAULT_VALUE)) ||
                ((prefUtils.getSerialKey() != null &&
                        prefUtils.getSerialKey().length() <= 0)
                        && (prefUtils.getUserAccount() != null
                        && prefUtils.getUserAccount().getLogin().equalsIgnoreCase(DEFAULT_VALUE)));


    }


    @Override
    public void onStateNotSaved() {
        super.onStateNotSaved();
    }


    @Override
    protected void onResume() {
        super.onResume();


    }

    @Override
    protected int setContentView() {
        return R.layout.activity_splashscreen;
    }


    void checkSerialKey() {
        setupView();
    }

    /**
     * clicking on the button to chose the server config
     */
    @OnClick(R.id.submitButton)
    void submitSerialKey() {
        if (listConfig.size() != 0) {
            int i = serialKeyField.getSelectedItemPosition();
            Connexion connexion = listConfig.get(i);
            submitButton.setEnabled(false);
            actionFrom = null;
            App.prefUtils.setBaseconfigName(connexion.getDesignationBase());

            setBaseConfig(connexion);


            //   if(progress != null) progress.setVisibility(View.INVISIBLE);
            //    if (loading != null) loading.setVisibility(View.INVISIBLE);
            submitButton.setEnabled(true);
        }
    }

    /**
     * save base config in local database
     */

    void setBaseConfig(Connexion response) {
        prefUtils.setBaseConfig(response);

        prefUtils.setServerPort(Integer.parseInt(response.getPort()));
        prefUtils.setSerialKey(response.getKeyBase());
        showContent();
    }


    /**
     * click on change config button
     */
    @OnClick(R.id.linkChangeKey)
    public void changeKey() {
        // TODO submit data to server...
        // App.prefUtils.clearSharedPreferences(); TO NOT DELETE base config
        prefUtils.deleteUserAccount();
        prefUtils.setLoadData("");
        Paper.book().destroy();
        App.database.appPropertiesDAO().deleteAll();
        AppHelper.doRestart(this);
    }


    void setupView() {
        if (!StringUtils.isEmptyString(new PrefUtils(context).getSerialKey())) {
            licenceKeyView.setVisibility(View.GONE);
            loadingView.setVisibility(View.VISIBLE);
            RetryButton.setVisibility(View.GONE);
            progressLayout2.setVisibility(View.VISIBLE);
            linkChangeKey.setVisibility(View.GONE);
            defaultLayout.setVisibility(View.VISIBLE);
        } else {
            if (licenceKeyView != null) {
                progressLayout2.setVisibility(View.GONE);
                licenceKeyView.setVisibility(View.VISIBLE);
            }

        }
    }

    /**
     * load the list of config from server into a spinner
     */
    private void LoadSpinner(String from, String c) {
        if (progress != null)  progress.setVisibility(View.INVISIBLE);
        if (loading != null) {
            loading.setVisibility(View.VISIBLE);
            loading.setText("Vérification Base Config. ...");
        }
        progressLayout2.setVisibility(View.VISIBLE);
        licenceKeyView.setVisibility(View.INVISIBLE);
        loadingView.setVisibility(View.VISIBLE);
        submitButton.setVisibility(View.INVISIBLE);

        RetryButton.setVisibility(View.GONE);
        linkChangeKey.setVisibility(View.GONE);
        defaultLayout.setVisibility(View.VISIBLE);
        if (NetworkUtils.isConnected()) {
            BaseConfigDataManager.getInstance().getBaseConfig(new Activation(context), new RemoteCallback<List<Connexion>>(this, false) {
                @Override
                public void onSuccess(List<Connexion> response) {
                    if (response != null) {
                        App.database.baseConfigDAO().deleteAll();
                        App.database.baseConfigDAO().insertAll(response);



                        if (!isMissingConfigData()) {
                            int i = 0;
                            for (Connexion connexion : response) {
                                i=i+1;

                                if (baseconfigExist(connexion)) {

                                    if (from.equals("checkLicense") || from.equals("is_ConnectedUser")){
                                        setUserAccountAndAuthorisations();
                                        setListBaseConfig(response);
                                        startActivity(MainActivity.class);
                                        Log.d("jfhd", "aaa");
                                    }
                                    else if (from.equals("isNonConnectedUser")) {
                                        submitButton.setVisibility(View.VISIBLE);
                                        startActivity(LoginActivity.class);
                                    }
                                    Log.d("jfhd", from);

                                    break;
                                }
                                else if(i==response.size()){
                                    Log.d("jfhd", "!basconfigExist");
                                    prefUtils.clearSharedPreferences();
                                    prefUtils.setIsConnected(false);
                                    Paper.book().destroy();
                                    App.database.appPropertiesDAO().deleteAll();
                                    purgeData();
                                    if (!isMissingConfigData())   AppHelper.doRestart(SplashscreenActivity.this);

                                    finish();
                                    startActivity(SplashscreenActivity.class);
                                }

                            }
                        } else {
                            submitButton.setVisibility(View.VISIBLE);
                            setListBaseConfig(response);
                        }

                        //  if (!basconfigExist) {
                        //   Log.d("jfhd", "!basconfigExist");
                        //  prefUtils.clearSharedPreferences();
                        //  prefUtils.setIsConnected(false);
                        //  Paper.book().destroy();
                        //  App.database.appPropertiesDAO().deleteAll();
                        //  purgeData();
                        // if (!isMissingConfigData())   AppHelper.doRestart(SplashscreenActivity.this);

                        //   finish();
                        //   startActivity(SplashscreenActivity.class);
                        //  } else {





                         /*   if(!isExpiredLicense()){
                                if (isNonConnectedUser()) {
                                    startActivity(LoginActivity.class);
                                } else {
                                    loadingView.setVisibility(View.VISIBLE);
                                    if (prefUtils.getLoadData().equals("")) {
                                        getCaisses();

                                    } else {//to reload auth into db

                                        setUserAccountAndAuthorisations();

                                    }
                                }
                            }*/

                        //    }

                    }
                }

                @Override
                public void onUnauthorized() {
                    submitButton.setEnabled(true);

                }

                @Override
                public void onFailed(Throwable throwable) {
                    if (!App.database.baseConfigDAO().getAll().isEmpty()) {
                        if (progress != null) progress.setVisibility(View.INVISIBLE);
                        if (loading != null) loading.setVisibility(View.INVISIBLE);


                        //   List<Connexion> response = App.database.baseConfigDAO().getAll();
                        //   setListBaseConfig(response);
                    }
                    //   Toasty.error(context, "Local").show();
            /*    loadingView.setVisibility(View.GONE);
                progress.setVisibility(View.GONE);
                loading.setVisibility(View.GONE);
                List<String> showlist = new ArrayList<>();
               Connexion response = new Connexion(
                        "1035",
                 "D0Q-D16-JYG",
                 "ed8ad57eb2273cae5733c088f62b4589",
                        "e38523a9681f8a51b003a28604d6c90e",
                 "197.14.1.179",
                 "8020",
                 "2148ed972ff6ca8e0bf0484a9bcdc179",
                 "cd2de8662a02152aa28c9eaa39370dd6",
                 "Chahia-externe",
                 "procaisse mobility",
                "offline licence mode",
                "01-10-2022" );


                //for (Connexion connexion : response) {
                    showlist.add(androidx.core.text.HtmlCompat.fromHtml(response.getDesignationBase(), HtmlCompat.FROM_HTML_MODE_LEGACY).toString());
                    listConfig.add(response);
              //  }
                ArrayAdapter<String> adapter = new ArrayAdapter<>(SplashscreenActivity.this, android.R.layout.simple_spinner_item, showlist);
                adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
                if (serialKeyField == null) {
                    serialKeyField = SplashscreenActivity.this.findViewById(R.id.serialKeyField1);

                }
                if (serialKeyField != null) serialKeyField.setAdapter(adapter);

                if (!(StringUtils.isEmptyString(prefUtils.getSerialKey()))) {
                    checkSerialKey();
                }
*/
      /*     Connexion response = new Connexion(
                        "1035",
                        "D0Q-D16-JYG",
                        "ed8ad57eb2273cae5733c088f62b4589",
                        "6845728aa947732909031c16701de252",
                        "procaisse.asmtechtn.com",
                        "443",
                        "2148ed972ff6ca8e0bf0484a9bcdc179",
                        "efe0914c9807cf450762d44b2f34ce6c",
                        "Pro-Caisse Mobile DEV",
                        "procaisse mobility",
                        "offline licence mode",
                        "01-10-2022" );*/
                    submitButton.setEnabled(true);
                }
            });

        }
        else {
            if (from.equals("checkLicense") || from.equals("is_ConnectedUser")){
                // setListBaseConfig(response);
                startActivity(MainActivity.class);

            }

            else if (from.equals("checkBaseConfig")) {

                //  setUserAccountAndAuthorisations();
            } else if (from.equals("isNonConnectedUser")) {
                submitButton.setVisibility(View.VISIBLE);
                startActivity(LoginActivity.class);
            }
            if (!App.database.baseConfigDAO().getAll().isEmpty()) {
                if (progress != null) progress.setVisibility(View.INVISIBLE);
                if (loading != null) loading.setVisibility(View.INVISIBLE);


                //   List<Connexion> response = App.database.baseConfigDAO().getAll();
                //   setListBaseConfig(response);
            }
        }




    }


    /**
     * load the list of  BackUp Licenses Url
     */
    public void LoadBackUpLicensesUrl() {
        if(progress != null)  progress.setProgress(01);
        LicenseDataManager.getInstance().getLicenseUrl(new GenericObject(App.prefUtils.getBaseConfig()),
                new RemoteCallback<LicenseResponse>(context, false) {
                    @Override
                    public void onSuccess(LicenseResponse response) {

                        lastLoaded = "LoadBackUpLicensesUrl";

                        App.database.backUpLicenseUrlDAO().insertAll(response);

                        getParametrages();

                    }

                    @Override
                    public void onUnauthorized() {
                        showRetryButton();
                    }

                    @Override
                    public void onFailed(Throwable throwable) {
                        showRetryButton();
                    }
                });

    }

    void getParametrages() {
        if (progress != null) progress.setProgress(02);
        if (loading != null) loading.setText("Chargement logos. ...");
        if (!App.prefUtils.getUserId().equals(DEFAULT_VALUE)) {
            ParametragesDataManager.getInstance().getParametrage(new GenericObject(App.prefUtils.getBaseConfig()),
                    new RemoteCallback<Parametrages>(context, false) {
                        @Override
                        public void onSuccess(Parametrages response) {
                            if (response != null) {
                                //App.database.familleDAO().insertAll(response);

                                App.prefUtils.setEntrepriseIcon(response.getPARAM_Logo());
                                lastLoaded = "getParametrages";

                            }

                            getPrefixes();
                        }

                        @Override
                        public void onUnauthorized() {

                        }

                        @Override
                        public void onFailed(Throwable throwable) {

                        }
                    });
        }
    }


    private void setListBaseConfig(List<Connexion> response) {
        if (listConfig != null) if (!listConfig.isEmpty()) listConfig.clear();
        List<String> showlist = new ArrayList<>();
        for (Connexion connexion : response) {
            showlist.add(androidx.core.text.HtmlCompat.fromHtml(connexion.getDesignationBase(), HtmlCompat.FROM_HTML_MODE_LEGACY).toString());
            listConfig.add(connexion);
        }
        ArrayAdapter<String> adapter = new ArrayAdapter<>(SplashscreenActivity.this, android.R.layout.simple_spinner_item, showlist);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        if (serialKeyField == null) {
            serialKeyField = SplashscreenActivity.this.findViewById(R.id.serialKeyField1);

        }
        if (serialKeyField != null) {
            serialKeyField.setAdapter(adapter);
        }

        if (!(StringUtils.isEmptyString(prefUtils.getSerialKey())) || !App.database.baseConfigDAO().getAll().isEmpty()) {
            checkSerialKey();
            loadingView.setVisibility(View.GONE);
        }
    }


    public boolean baseconfigExist(Connexion connection) {

//to remove

      String AdresseIp = prefUtils.getBaseConfig().getAdresseIp();
        if (AdresseIp != null) {
            AdresseIp = AdresseIp.replace("https://", "");
            AdresseIp = AdresseIp.replace("http://", "");
        } else AdresseIp = "null";

 /*
        if (prefUtils.getBaseConfig() == null) Log.d("fvccc", " nulll ");
        if (prefUtils.getBaseConfig() != null) {
            if (prefUtils.getBaseConfig().getDateCreation() == null)
                Log.d("fvccc", " getDateCreation nulll ");
            else Log.d("fvccc", " getDateCreation ");
        }


        Boolean result = true;

        if (prefUtils.getBaseConfig() == null) result = false;
        else {
            if (prefUtils.getBaseConfig().getIdBaseConfig() == null
                    || prefUtils.getBaseConfig().getKeyBase() == null
                    || prefUtils.getBaseConfig().getDbName() == null
                    || prefUtils.getBaseConfig().getDbIpAddress() == null
                    || AdresseIp.equals("null")
                   // || prefUtils.getBaseConfig().getDateCreation()== null
                    || prefUtils.getBaseConfig().getIdEntreprise() == null
                    || prefUtils.getBaseConfig().getProduit() == null
                    || prefUtils.getBaseConfig().getPort() == null
                    || prefUtils.getBaseConfig().getDesignationBase() == null
                    || prefUtils.getBaseConfig().getUsername() == null
                    || prefUtils.getBaseConfig().getPassword() == null) {
                result = false;
            }
            else {
                result = prefUtils.getBaseConfig().getIdBaseConfig().equals(connection.getIdBaseConfig())
                        && prefUtils.getBaseConfig().getKeyBase().equals(connection.getKeyBase())
                        && prefUtils.getBaseConfig().getDbName().equals(connection.getDbName())
                        && prefUtils.getBaseConfig().getDbIpAddress().equals(connection.getDbIpAddress())
                        && AdresseIp.equals(connection.getAdresseIp())


                        && prefUtils.getBaseConfig().getDateCreation().equals(connection.getDateCreation())//prefUtils.getBaseConfig().getDateCreation() MIGHT BE NULL §§
                        && prefUtils.getBaseConfig().getIdEntreprise().equals(connection.getIdEntreprise())//prefUtils.getBaseConfig().getIdEntreprise() MIGHT BE NULL §§
                        && prefUtils.getBaseConfig().getProduit().equals(connection.getProduit()) //prefUtils.getBaseConfig().getProduit() MIGHT BE NULL §§

                        && prefUtils.getBaseConfig().getPort().equals(connection.getPort())
                        && prefUtils.getBaseConfig().getDesignationBase().equals(connection.getDesignationBase())
                        && prefUtils.getBaseConfig().getUsername().equals(connection.getUsername())
                        && prefUtils.getBaseConfig().getPassword().equals(connection.getPassword());
            }
        }*/
        Log.d("fvccc", prefUtils.getBaseConfig().getDesignationBase());
        Log.d("fvccc", connection.getDesignationBase());
         return prefUtils.getBaseConfig().getIdBaseConfig().equals(connection.getIdBaseConfig())
                && prefUtils.getBaseConfig().getKeyBase().equals(connection.getKeyBase())
                && prefUtils.getBaseConfig().getDbName().equals(connection.getDbName())
                && prefUtils.getBaseConfig().getDbIpAddress().equals(connection.getDbIpAddress())
                && AdresseIp.equals(connection.getAdresseIp())


              // && prefUtils.getBaseConfig().getDateCreation().equals(connection.getDateCreation())//prefUtils.getBaseConfig().getDateCreation() MIGHT BE NULL §§
             //   && prefUtils.getBaseConfig().getIdEntreprise().equals(connection.getIdEntreprise())//prefUtils.getBaseConfig().getIdEntreprise() MIGHT BE NULL §§
//                && prefUtils.getBaseConfig().getProduit().equals(connection.getProduit()) //prefUtils.getBaseConfig().getProduit() MIGHT BE NULL §§

                && prefUtils.getBaseConfig().getPort().equals(connection.getPort())
                && prefUtils.getBaseConfig().getDesignationBase().equals(connection.getDesignationBase())
                && prefUtils.getBaseConfig().getUsername().equals(connection.getUsername())
                && prefUtils.getBaseConfig().getPassword().equals(connection.getPassword());

       // return result;
    }

    /**
     * get caisse data from server
     */
    public void getCaisses() {
        if (!App.prefUtils.getUserId().equals(DEFAULT_VALUE)) {
            SessionCaisseDataManager.getInstance().getSessionCaisseByUser(new GenericObject(App.prefUtils.getBaseConfig(),
                            App.prefUtils.getUserId()),
                    new RemoteCallback<SessionCaisse>(context, false) {
                        @Override
                        public void onSuccess(SessionCaisse response) {

                            lastLoaded = "getCaisses";
                            if (response.sCCaisse != null && response.getsCIdCarnet() != null) {

                                App.prefUtils.setSessionCaisse(response);
                                getExercice();
                                //prefUtils.getBrAuthorization()
                                if (!App.prefUtils.getUserId().equals(DEFAULT_VALUE)) {

                                    // Log.d("kiolJJe", response.getsCIdCarnet() + " sCCaisse " + response.sCCaisse);

                                    if (!App.prefUtils.getUserId().equals(DEFAULT_VALUE))
                                        getSessionCaisseByUser();
                                }
                            } else {
                                if (!App.prefUtils.getUserId().equals(DEFAULT_VALUE)) {
                                    //   getSessionCaisseByUser();
                                    getExercice();
                                } else {
                                    if (!StringUtils.isEmptyString(new PrefUtils(context).getSerialKey())) {
                                        startActivity(LoginActivity.class);
                                    }
                                }
                            }
                        }

                        @Override
                        public void onUnauthorized() {
                            showRetryButton();
                        }

                        @Override
                        public void onFailed(Throwable throwable) {
                            showRetryButton();
                        }
                    });
        } else {
            startActivity(LoginActivity.class);

        }
    }

    /**
     * get session caisse data by user id from server
     */
    void getSessionCaisseByUser() {
        Utilisateur utilisateur = prefUtils.getUserAccount();
        if (utilisateur == null) {
            startActivity(LoginActivity.class);
        } else {
            SessionCaisseDataManager.getInstance().getSessionCaisseByUser(new GenericObject(App.prefUtils.getBaseConfig(),
                    utilisateur.getCodeUt()), new RemoteCallback<SessionCaisse>(context, false) {
                @Override
                public void onSuccess(SessionCaisse response) {
                    lastLoaded = "getSessionCaisseByUser";
                    if (response != null && response.getsCIdSCaisse() != null) {
                        App.prefUtils.setSessionCaisse(response);
                        getExercice();
                    } else {

                        UIUtils.showDialog2(context, R.string.inactive_session_error, R.string.inactive_session_error_request,
                                (dialog, which) -> {
                                    // Set an EditText view to get user fond caisse input
                                    final EditText input = new EditText(context);
                                    input.setInputType(TYPE_CLASS_NUMBER | TYPE_NUMBER_FLAG_DECIMAL);
                                    AlertDialog dialogFondCaisse = new AlertDialog.Builder(context)
                                            .setTitle(R.string.cash_fund)
                                            .setMessage(R.string.cash_fund_request)
                                            .setView(input)
                                            .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                                @Override
                                                public void onClick(DialogInterface dialogInterface, int i) {
                                                    String editTextInput = input.getText().toString();
                                                    //   Log.d("onclick","editext value is: "+ editTextInput);



                                                    if(editTextInput.isBlank() || editTextInput.isEmpty()){
                                                        Toasty.error(context, "Please enter a valid number").show();
                                                        return;
                                                    }


                                                            if (Double.parseDouble(editTextInput) >= 0) {
                                                                SessionCaisseDataManager.getInstance()
                                                                        .addSessionVendeur(
                                                                                new GenericObject(App.prefUtils.getBaseConfig(),
                                                                                        new SessionActivation(
                                                                                                utilisateur.getCodeCaisse(),
                                                                                                utilisateur.getCodeUt(),
                                                                                                utilisateur.getCodeCarnet(),
                                                                                                utilisateur.getStation(),
                                                                                                editTextInput,
                                                                                                DeviceUtils.getUniqueDeviceId())),
                                                                                new RemoteCallback<SessionCaisse>(context, true) {
                                                                                    @RequiresApi(api = Build.VERSION_CODES.N)
                                                                                    @Override
                                                                                    public void onSuccess(SessionCaisse response) {

                                                                                        if (response != null) {
                                                                                            if (response.getsCIdSCaisse() != null) {
                                                                                                newSession = true;
                                                                                                App.prefUtils.setSessionCaisse(response);
                                                                                                Toasty.success(context, "Nouvelle session crée avec succés : " + prefUtils.getSessionCaisseId() +" Fond Caisse : "+ decimalFormat(Double.parseDouble(response.sCFondCaisse))+ "DT").show();

                                                                                                App.database.sessionCaisseDAO().insert(response);

                                                                                            }
                                                                                        }

                                                                                        else {
                                                                                            Toasty.error(context, "Cannot create a session").show();
                                                                                        }
                                                                                    }

                                                                                    @Override
                                                                                    public void onUnauthorized() {

                                                                                    }

                                                                                    @Override
                                                                                    public void onFailed(Throwable throwable) {
                                                                                        Toasty.error(context, "Carnet invalide, veuillez contacter votre administrateur").show();
                                                                                    }
                                                                                });
                                                            } else {
                                                                Toasty.error(context, "Please enter a valid number").show();
                                                            }

                                                }
                                            })
                                            .setNegativeButton("Cancel", null)
                                            .create();
                                    dialogFondCaisse.show();


                                  /*  new MaterialDialog.Builder(context)
                                            .title(R.string.cash_fund)
                                            .content(R.string.cash_fund_request)
                                            .inputType(InputType.TYPE_NUMBER_FLAG_DECIMAL | InputType.TYPE_CLASS_NUMBER)
                                            .input("", "",
                                                    (dialog1, input) -> {
                                                        try {
                                                            if (input != null && Double.parseDouble(input.toString()) >= 0) {
                                                                SessionCaisseDataManager.getInstance()
                                                                        .addSessionVendeur(
                                                                                new GenericObject(App.prefUtils.getBaseConfig(),
                                                                                        new SessionActivation(
                                                                                                utilisateur.getCodeCaisse(),
                                                                                                utilisateur.getCodeUt(),
                                                                                                utilisateur.getCodeCarnet(),
                                                                                                utilisateur.getStation(),
                                                                                                input.toString(),
                                                                                                DeviceUtils.getUniqueDeviceId())),
                                                                                new RemoteCallback<SessionCaisse>(context, true) {
                                                                                    @RequiresApi(api = Build.VERSION_CODES.N)
                                                                                    @Override
                                                                                    public void onSuccess(SessionCaisse response) {

                                                                                        if (response != null) {
                                                                                            if (response.getsCIdSCaisse() != null) {
                                                                                                newSession = true;
                                                                                                App.prefUtils.setSessionCaisse(response);
                                                                                                Toasty.success(context, "Nouvelle session crée avec succés : " + prefUtils.getSessionCaisseId()).show();

                                                                                                App.database.sessionCaisseDAO().insert(response);

                                                                                            }
                                                                                        }

                                                                                        else {
                                                                                            Toasty.error(context, "Cannot create a session").show();
                                                                                        }
                                                                                    }

                                                                                    @Override
                                                                                    public void onUnauthorized() {

                                                                                    }

                                                                                    @Override
                                                                                    public void onFailed(Throwable throwable) {
                                                                                        Toasty.error(context, "Carnet invalide, veuillez contacter votre administrateur").show();
                                                                                    }
                                                                                });
                                                            } else {
                                                                Toasty.error(context, "Please enter a valid number").show();
                                                            }
                                                        } catch (NumberFormatException ignored) {
                                                            Toasty.error(context, "Please enter a valid number").show();
                                                        }
                                                    }).show();*/

                                }, (dialog, which) -> {
                                    prefUtils.deleteUserAccount();
                                    ActivityUtils.finishAllActivities();
                                }).setCancelable(false);



                    }
                }

                @Override
                public void onUnauthorized() {
                    showRetryButton();
                }

                @Override
                public void onFailed(Throwable throwable) {
                    showRetryButton();

                }
            });
        }
    }

    /**
     * add ordre mission
     */
    void addOrdreMission() {
        List<OrdreMission> ordreMissionList = new ArrayList<>();
        OrdreMission ordreMission = new OrdreMission(
                "",
                prefUtils.getExercice(),
                "",
                "",
                "",
                "",
                prefUtils.getUserId(),
                "",
                "",
                prefUtils.getUserStationId(),
                0,
                "",
                0,
                "",
                "",
                "",
                "",
                prefUtils.getSessionCaisseId()
        );
        ordreMissionList.add(ordreMission);
        OrdreMissionDataManager.getInstance().addOrdreMission(new GenericObject(App.prefUtils.getBaseConfig(), ordreMissionList),
                new RemoteCallback<Boolean>(context, false) {
                    @Override
                    public void onSuccess(Boolean response) {
                        getData();
                    }

                    @Override
                    public void onUnauthorized() {

                    }

                    @Override
                    public void onFailed(Throwable throwable) {

                    }
                });
    }

    /**
     * get all caisse sessions
     */
    void getSessionCaisses() {
        if (progress != null) progress.setProgress(10);
        if (loading != null) loading.setText("Chargement des données Session Caisses ...");

        SessionCaisseDataManager.getInstance().getSessionCaisses(new GenericObject(App.prefUtils.getBaseConfig()), new RemoteCallback<List<SessionCaisse>>(context, false) {
            @Override
            public void onSuccess(List<SessionCaisse> response) {
                if (response != null) {
                    App.database.sessionCaisseDAO().insertAll(response);
                }
                lastLoaded = "getSessionCaisses";
                getPrices();

            }

            @Override
            public void onUnauthorized() {
                showRetryButton();

            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();

            }
        });
    }

    void setUserAccountAndAuthorisations() {

        UtilisateurDataManager.getInstance().authentification(new GenericObject(App.prefUtils.getBaseConfig(), new Utilisateur(PrefUtils.getUserLogin(), App.prefUtils.getUserPassword())),
                new RemoteCallback<Utilisateur>(context, false, false) {
                    @Override
                    public void onSuccess(Utilisateur response) {
                        if (response != null) {

                            try {
                                App.prefUtils.setUserAccount(response);
                              //  startActivity(MainActivity.class);
                            } catch (Exception e) {
                                e.printStackTrace();
                                UIUtils.showDialogWithoutChoice(context, context.getString(R.string.error), context.getString(R.string.aut_error), (dialog, which) -> {
                                    dialog.dismiss();
                                    startActivity(LoginActivity.class);
                                });


                                //if error then only add auth to db
                                // if (ObjectUtils.isNotEmpty(response.getAutorisationUser())) {
                                //     App.database.authorizationDAO().deleteAll();
                                //     App.database.authorizationDAO().insertAll(response.getAutorisationUser());
                                // }
                            }
                            // if (ObjectUtils.isNotEmpty(response.getAutorisationUser())) {
                            App.database.authorizationDAO().deleteAll();
                            App.database.authorizationDAO().insertAll(response.getAutorisationUser());
                            //  }

                        } else {
                            startActivity(LoginActivity.class);
                        }

                    }

                    @Override
                    public void onUnauthorized() {
                        //     showRetryButton();
                        startActivity(MainActivity.class);
                    }

                    @Override
                    public void onFailed(Throwable throwable) {
                        // showRetryButton();
                        startActivity(MainActivity.class);

                    }
                });
    }

    /**
     * get connected user data from server
     */
    void getUser() {
        UtilisateurDataManager.getInstance().authentification(new GenericObject(App.prefUtils.getBaseConfig(), new Utilisateur(App.prefUtils.getUserLogin(), App.prefUtils.getUserPassword())),
                new RemoteCallback<Utilisateur>(context, false) {
                    @Override
                    public void onSuccess(Utilisateur response) {
                        if (response != null) {
                            try {
                                App.prefUtils.setUserAccount(response);
                                if (App.prefUtils.getCrtourAuto() && newSession) {
                                    addOrdreMission();
                                } else {
                                    getData();
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                                UIUtils.showDialogWithoutChoice(context, context.getString(R.string.error), context.getString(R.string.aut_error), (dialog, which) -> {
                                    dialog.dismiss();
                                    startActivity(LoginActivity.class);
                                });
                            }

                        } else {
                            startActivity(LoginActivity.class);
                        }

                    }

                    @Override
                    public void onUnauthorized() {
                        showRetryButton();

                    }

                    @Override
                    public void onFailed(Throwable throwable) {
                        showRetryButton();

                    }
                });
    }

    /**
     * get exercice data from server
     */
    void getExercice() {
        ExerciceDataManager.getInstance().getExercice(new GenericObject(App.prefUtils.getBaseConfig()), new RemoteCallback<Exercice>(context, false) {
            @Override
            public void onSuccess(Exercice response) {
                if (response.getExerciceCode() != null) {
                    App.prefUtils.setExercice(response.getExerciceCode());
                    getUser();
                } else {
                    UIUtils.showDialog(context, getString(R.string.error), getString(R.string.exrcice_not_found), (dialog, which) -> finish());
                }

            }

            @Override
            public void onUnauthorized() {
                showRetryButton();

            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();
            }
        });
    }

    /**
     * open a new activity
     */
    void startActivity(Class aClass) {
        Intent i = new Intent(this, aClass);

        i.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
//intent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP);
        //intent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP);
        startActivity(i);
        //  finish();
    }

    @Override
    public void onBackPressed() {
        if (doubleBackToExitPressedOnce) {
            finishAffinity();
            super.onBackPressed();
            return;
        }

        this.doubleBackToExitPressedOnce = true;
        Toasty.info(this, R.string.press_back, Toast.LENGTH_SHORT).show();
        new Handler().postDelayed(() -> doubleBackToExitPressedOnce = false, 2000);
    }

    /**
     * show the retry button when connecting has failed
     */
    void showRetryButton() {
        if (loadingView != null) {
            prefUtils.setSerialKey(null);
            loadingView.setVisibility(View.GONE);
            RetryButton.setVisibility(View.VISIBLE);
            linkChangeKey.setVisibility(View.VISIBLE);
            linkChangeKey.setPaintFlags(linkChangeKey.getPaintFlags() | Paint.UNDERLINE_TEXT_FLAG);
        }
    }

    /**
     * retry button click detection
     */
    @OnClick(R.id.RetryButton)
    void retry() {
        setupView();
    }

    /**
     * get banqes data from server
     */
    void getBanques() {
        if (progress != null) progress.setProgress(34);
        if (loading != null) loading.setText("Chargement des données banque ...");
        BanqueDataManager.getInstance().getBanques(new GenericObject(App.prefUtils.getBaseConfig()), new RemoteCallback<ArrayList<Banque>>(context, false) {
            @Override
            public void onSuccess(ArrayList<Banque> response) {
                App.database.banqueDAO().insertAll(response);

                lastLoaded = "getBanques";
                getClients();
            }

            @Override
            public void onUnauthorized() {
                showRetryButton();

            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();

            }
        });
    }

    /**
     * get clients data from server
     */

    void getClients() {
        if (progress != null) progress.setProgress(37);
        if (loading != null) loading.setText("Chargement des données clients ...");

        List<String> zones = prefUtils.getUserAccount().getZone();

        ClientDataManager.getInstance().getClients(new GenericObject(prefUtils.getBaseConfig(), null), new RemoteCallback<List<Client>>(context, false) {
            @Override
            public void onSuccess(List<Client> response) throws NoSuchFieldException, InstantiationException, IllegalAccessException {
                System.out.println("myclient" + response.size());
                 App.database.clientDAO().insertAll(response);


                lastLoaded = "getClients";
                // getImmobilisation();
               getArticles();
            }

            @Override
            public void onUnauthorized() {
                showRetryButton();
            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();
            }
        }, prefUtils.getUserAccount().getTypeUser().equals("Client") ? prefUtils.getUserAccount().getCltEquivalent() : null);


    }


    /**
     * get Immobilisation data from server
     */

    void getImmobilisation() {
        if (progress != null) progress.setProgress(37);
        if (loading != null) loading.setText("Chargement des données immobilisation ...");

        List<String> zones = prefUtils.getUserAccount().getZone();

        ImmobilisationDataManager.getInstance().getImmobilisation(new GenericObject(prefUtils.getBaseConfig(), null), new RemoteCallback<List<Immobilisation>>(context, false) {
            @Override
            public void onSuccess(List<Immobilisation> response) throws NoSuchFieldException, InstantiationException, IllegalAccessException {


                for (Immobilisation immobilisation : response) {
                    Immobilisation im = new Immobilisation(
                            immobilisation.getcLICode(),
                            immobilisation.cLINomPren,
                            immobilisation.getCliImmo(),
                            immobilisation.getCliImoCodeP(),
                            immobilisation.getCliImoTypEmp(),
                            immobilisation.getCliImoCB(),
                            immobilisation.getTyEmpImNom().replace(" ",""));




                    App.database.clientImooDAO().insert(im);
                }



                lastLoaded = "getImmobilisation";
                getArticles();
            }

            @Override
            public void onUnauthorized() {
                showRetryButton();
            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();
            }
        }
        );
    }

    private void getArticleCodeBare() {
        if (progress != null) progress.setProgress(42);
        if (loading != null) loading.setText("Chargement des données code à barres ...");
        CodeBareDataManager.getInstance().getCodeBares(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<ArticleCodeBar>>(context, false) {

            @Override
            public void onSuccess(List<ArticleCodeBar> response) {

                if (ObjectUtils.isNotEmpty(response)) {
                    App.database.articleCodeBarDAO().insertAll(response);
                }
                lastLoaded = "getArticleCodeBare";
                getArticlesStock();

            }

            @Override
            public void onUnauthorized() {
                showRetryButton();
            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();
                //Toasty.info(context, throwable.getMessage()).show();
            }
        });
    }


    /**
     * get all articles data from server
     */
    void getArticles() {
        if (progress != null) progress.setProgress(40);
        if (loading != null) loading.setText("Chargement des données article ...");
        ArticleDataManager.getInstance().getArticles(new GenericObject(App.prefUtils.getBaseConfig(),null), new RemoteCallback<List<Article>>(context, false) {
            @Override
            public void onSuccess(List<Article> response) {
                lastLoaded = "getArticles";
                if (ObjectUtils.isNotEmpty(response)) {
                    Log.d("jfndjd", String.valueOf(response.get(0)));
                    App.database.articleDAO().deleteAll();
                    App.database.articleDAO().insertAll(response);
                    getArticleCodeBare();
                } else {
                    UIUtils.showDialog(context, "erreur", "0 produit trouvé", (dialog, which) -> finish());
                }
            }

            @Override
            public void onUnauthorized() {
                // Toasty.error(context, " getArticles onUnauthorized").show();
                showRetryButton();

            }

            @Override
            public void onFailed(Throwable throwable) {
                //Toasty.error(context, " getArticles : " + throwable.getMessage()).show();
                showRetryButton();

            }
        }/*, prefUtils.getInvPAuthorization()*/);
    }

    /**
     * get article stock numbers data from server
     */
    void getArticlesStock() {
        if (progress != null) progress.setProgress(46);
        if (loading != null) loading.setText("Chargement des données stock ...");
        StationStockDataManager.getInstance().getstockArticle(new GenericObject(App.prefUtils.getBaseConfig(), App.prefUtils.getCaisseStationId()), new RemoteCallback<List<StationStock>>(context, false) {
            @Override
            public void onSuccess(List<StationStock> response) {
                lastLoaded = "getArticlesStock";
                if (response != null) {
                    App.database.stationStockDAO().deleteAll();
                    App.database.stationStockDAO().insertAll(response);
                    for (StationStock stationStock : response) {
                        if (stationStock.getSARTCodeSatation().equals(App.prefUtils.getCaisseStationId())) {
                            Article article = App.database.articleDAO().getOneByCode(stationStock.getSARTCodeArt());
                            if (article != null) {
                                try {
                                    article.setaRTQteStock((stationStock.getSARTQte()));
                                    article.setsARTCodeSatation(stationStock.getSARTCodeSatation());
                                } catch (Exception e) {
                                    Log.d("ssd", e.getMessage());
                                }
                                App.database.articleDAO().insert(article);
                            }
                        }

                    }
                    if (App.prefUtils.getVeilleConcurentielle())
                        getVCNewProducts();
                    else getReclamations();

                    //    getReclamations();
                } else {
                    UIUtils.showDialog(context, "erreur", "0 produit trouvé", (dialog, which) -> finish());
                }
            }

            @Override
            public void onUnauthorized() {
                Toasty.error(context, " getArticlesStock onUnauthorized").show();

                showRetryButton();
            }

            @Override
            public void onFailed(Throwable throwable) {
                Toasty.error(context, throwable.getMessage()).show();
                showRetryButton();
            }
        });
    }

    /**
     * get all reclamations data from server
     */
    void getReclamations() {
        if (progress != null) progress.setProgress(73);
        if (loading != null) loading.setText("Chargement des données reclamations ...");

        ReclamationDataManager.getInstance().getReclamations(new GenericObject(App.prefUtils.getBaseConfig(), App.prefUtils.getCaisseId()), new RemoteCallback<List<Reclamation>>(context, false) {
            @Override
            public void onSuccess(List<Reclamation> response) {
                List<Reclamation> reclamations = new ArrayList<>();
                if (response != null || !response.isEmpty()) {

                    if (!reclamations.isEmpty()) {
                        App.database.reclamationDAO().insertAll(reclamations);
                    }


                }
                lastLoaded = "getReclamations";
                getBonRetour();

            }

            @Override
            public void onUnauthorized() {
                showRetryButton();

            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();
            }
        });
    }


    void getBonRetour() {
        if (progress != null) progress.setProgress(75);
        if (App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.br))) {
            if (loading != null)
                loading.setText("Chargement des données Bon retour ...");
            BonRetourDataManager.getInstance().getBonRetours(new GenericObject(App.prefUtils.getBaseConfig(), App.prefUtils.getCaisseId()), new RemoteCallback<List<BonRetour>>(context, false) {
                @Override
                public void onSuccess(List<BonRetour> response) {
                    if (response != null || !response.isEmpty()) {

                        if (!response.isEmpty()) {
                            for (BonRetour bonRetour : response) {
                                bonRetour.isSync = true;
                                bonRetour.setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());
                            }
                            App.database.bonRetourDAO().insertAll(response);
                        }
                    }
                    lastLoaded = "getBonRetour";

                    getLigneBonRetour();

                }

                @Override
                public void onUnauthorized() {
                    showRetryButton();

                }

                @Override
                public void onFailed(Throwable throwable) {
                    showRetryButton();

                }
            });
        } else {
            getBonCommande();
        }
    }

    void getLigneBonRetour() {
        if (progress != null) progress.setProgress(78);
        if (loading != null) loading.setText("Chargement des données lignes bon retour ...");

        LigneBonRetourDataManager.getInstance().getLigneBonRetours(new GenericObject(App.prefUtils.getBaseConfig(), App.prefUtils.getCaisseId()), new RemoteCallback<List<LigneBonRetour>>(context, false) {
            @Override
            public void onSuccess(List<LigneBonRetour> response) {
                if (response != null || !response.isEmpty()) {

                    if (!response.isEmpty()) {
                        for (LigneBonRetour bonRetour : response) {
                            bonRetour.isSync = true;
                            bonRetour.setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());
                        }
                        App.database.ligneBonRetourDAO().insertAll(response);
                    }


                }
                lastLoaded = "getLigneBonRetour";
                getBonCommande();

            }

            @Override
            public void onUnauthorized() {
                showRetryButton();

            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();

            }
        });
    }


    void getBonCommande() {
        if (progress != null) progress.setProgress(80);
        if (prefUtils.getBcAuthorization() || prefUtils.getInvPAuthorization()) {
            loading.setText("Chargement des données lignes bon commande ...");
            BonCommandeDataManager.getInstance().getBonCommande(new GenericObject(App.prefUtils.getBaseConfig(), App.prefUtils.getCaisseId()), new RemoteCallback<List<BonCommande>>(context, false) {
                @Override
                public void onSuccess(List<BonCommande> response) {
                    if (response != null || !response.isEmpty()) {
                        if (!response.isEmpty()) {
                            for (BonCommande bonRetour : response) {
                                bonRetour.isSync = true;
                                bonRetour.setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());
                            }
                            App.database.bonCommandeDAO().insertAll(response);
                        }
                    }
                    lastLoaded = "getBonCommande";
                    getLigneBonCommande();
                }

                @Override
                public void onUnauthorized() {
                    showRetryButton();

                }

                @Override
                public void onFailed(Throwable throwable) {
                    showRetryButton();

                }
            });
        } else {
            getEtatOrdreMission();
        }
    }
    /**
     * get Marques data from server
     */
    void getMarque() {
        App.database.marqueDAO().deleteAll();
        if (progress != null) progress.setProgress(96);
        if (loading != null) loading.setText("Chargement des données Marque. ...");        MarqueDataManager.getInstance().getMarques(new GenericObject(App.prefUtils.getBaseConfig(), null), new RemoteCallback<List<Marque>>(context, false) {
            @Override
            public void onSuccess(List<Marque> response) {
                if (!response.isEmpty()) {
                    App.database.marqueDAO().insertAll(response);
                }

                lastLoaded = "getMarque";
                getTickets();
            }

            @Override
            public void onUnauthorized() {
                showRetryButton();
            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();
            }
        });
    }

    void getLigneBonCommande() {
        if (progress != null) progress.setProgress(83);
        if (loading != null) loading.setText("Chargement des données Bon commande ...");
        LigneBonCommandeDataManager.getInstance().getLigneBonCommande(new GenericObject(App.prefUtils.getBaseConfig(), App.prefUtils.getCaisseId()), new RemoteCallback<List<LigneBonCommande>>(context, false) {
            @Override
            public void onSuccess(List<LigneBonCommande> response) {
                if (response != null && !response.isEmpty()) {
                    for (LigneBonCommande bonRetour : response) {
                        bonRetour.isSync = true;
                        bonRetour.setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());
                    }
                    App.database.ligneBonCommandeDAO().insertAll(response);
                }
                lastLoaded = "getLigneBonCommande";
                getEtatOrdreMission();
            }

            @Override
            public void onUnauthorized() {
                showRetryButton();
            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();
            }
        });
    }

    /**
     * get timbre data from server
     */
    void getFamilles() {
        if (progress != null) progress.setProgress(93);
        if (loading != null) loading.setText("Chargement des données Familles. ...");
        if (!App.prefUtils.getUserId().equals(DEFAULT_VALUE)) {
            FamilleDataManager.getInstance().getFamilles(new GenericObject(App.prefUtils.getBaseConfig(),
                            App.prefUtils.getUserId()),
                    new RemoteCallback<List<Famille>>(context, false) {
                        @Override
                        public void onSuccess(List<Famille> response) {
                            if (response != null) {
                                App.database.familleDAO().insertAll(response);
                            }

                            lastLoaded = "getFamilles";
                           getFacture();
                           // getClientsArticlePrix();
                        }

                        @Override
                        public void onUnauthorized() {

                        }

                        @Override
                        public void onFailed(Throwable throwable) {

                        }
                    });
        }
    }


    /**
     * get facture data from server
     */
    void getFacture() {
        if (progress != null) progress.setProgress(94);
        if (loading != null) loading.setText("Chargement des données Facture. ...");
        if (!App.prefUtils.getUserId().equals(DEFAULT_VALUE)) {
            FactureDataManager.getInstance().getFacture(new GenericObject(App.prefUtils.getBaseConfig(), null),
                    new RemoteCallback<List<Facture>>(context, false) {
                        @Override
                        public void onSuccess(List<Facture> response) {
                            if (response != null) {
                                App.database.factureDAO().insertAll(response);
                            }

                            lastLoaded = "getFacture";
                            getClientsArticlePrix();
                        }

                        @Override
                        public void onUnauthorized() {

                        }

                        @Override
                        public void onFailed(Throwable throwable) {

                        }
                    });
        }
    }

    /**
     * get timbre data from server
     */
    void getTimbres() {
        if (progress != null) progress.setProgress(91);
        if (loading != null) loading.setText("Chargement des données Timbres. ...");
        if (!App.prefUtils.getUserId().equals(DEFAULT_VALUE)) {
            TimbreDataManager.getInstance().getTimbres(new GenericObject(App.prefUtils.getBaseConfig(), App.prefUtils.getUserId()),
                    new RemoteCallback<List<Timbre>>(context, false) {
                        @Override
                        public void onSuccess(List<Timbre> response) {
                            if (response.size() != 0) {
                                App.database.timbreDAO().insertAll(response);
                            }

                            lastLoaded = "getTimbres";
                            getFamilles();
                        }

                        @Override
                        public void onUnauthorized() {
                            showRetryButton();
                        }

                        @Override
                        public void onFailed(Throwable throwable) {
                            showRetryButton();
                        }
                    });
        }
    }

    /**
     * get EtatOrdreMission data from server
     */
    void getEtatOrdreMission() {
        if (progress != null) progress.setProgress(86);
        if (prefUtils.getTourAuthorization()) {
            if (loading != null)

                loading.setText("Chargement des données Etat Ordre Missions. ...");
            EtatOrdreMissionDataManager.getInstance().getEtatOrdreMission(
                    new GenericObject(App.prefUtils.getBaseConfig(), null),
                    new RemoteCallback<List<EtatOrdreMission>>(context, false) {
                        @Override
                        public void onSuccess(List<EtatOrdreMission> response) {
                            if (!response.isEmpty() && response != null) {
                                App.database.etatOrdreMissionDAO().insertAll(response);
                            }
                            lastLoaded = "getEtatOrdreMission";
                            getOrdreWithLines();
                        }


                        @Override
                        public void onUnauthorized() {
                            showRetryButton();
                        }

                        @Override
                        public void onFailed(Throwable throwable) {
                            showRetryButton();
                        }
                    }
            );
        } else {
            if (App.prefUtils.getBlAuthorization()) getTimbres();
            else getFamilles();
        }
    }


    /**
     * /**
     * get OrdreWithLines data from server
     */
    void getOrdreWithLines() {
        if (progress != null) progress.setProgress(90);

        Vendeur vendeur = new Vendeur(App.prefUtils.getUserId(),getCurrentDate());
        if (loading != null) loading.setText("Chargement des données Ordre Missions. ...");
        OrdreMissionDataManager.getInstance().getOrdreMessionWithLines(
                new GenericObject(App.prefUtils.getBaseConfig(), vendeur),
                new RemoteCallback<List<OrdreWithLines>>(context, false) {
                    @Override
                    public void onSuccess(List<OrdreWithLines> response) {
                        if (!response.isEmpty() && response != null) {
                            App.database.ordreMissionDAO().deleteAll();
                            App.database.ligneOrdreMissionDAO().deleteAll();
                            App.database.trakingAlarmDAO().deleteAll();
                            for (OrdreWithLines ordreWithLines : response) {
                                App.database.ordreMissionDAO().insertOne(ordreWithLines.ordreMission);
                                App.database.ligneOrdreMissionDAO().insertAll(ordreWithLines.ligneOrdreMission);
                                TrakingAlarm trakingAlarm = App.database.trakingAlarmDAO().getWithOrdreMission(ordreWithLines.ordreMission.oRDCode);
                                if (!(ordreWithLines.ordreMission.oRD_dateDebut == null) && !(ordreWithLines.ordreMission.oRD_dateFin == null) && trakingAlarm == null) {
                                    trakingAlarm
                                            = new TrakingAlarm(
                                            ordreWithLines.ordreMission.oRDCode,
                                            ordreWithLines.ordreMission.oRD_dateDebut,
                                            ordreWithLines.ordreMission.oRD_dateFin,
                                            false,
                                            false,
                                            false
                                    );
                                    App.database.trakingAlarmDAO().insertTrAlarm(trakingAlarm);
                                }
                            }
                        }
                        lastLoaded = "getOrdreWithLines";
                        if (App.prefUtils.getBlAuthorization()) getTimbres();
                        else getFamilles();
                    }

                    @Override
                    public void onUnauthorized() {
                        showRetryButton();
                    }

                    @Override
                    public void onFailed(Throwable throwable) {
                        showRetryButton();
                    }
                }
        );
    }


    /**
     * get villes data from server
     */
    void getVilles() {
        if (progress != null) progress.setProgress(96);
        if (loading != null) loading.setText("Chargement des données Villes. ...");
        VilleDataManager.getInstance().getVilles(new GenericObject(App.prefUtils.getBaseConfig(), App.prefUtils.getCaisseId()), new RemoteCallback<List<Ville>>(context, false) {
            @Override
            public void onSuccess(List<Ville> response) {
                if (!response.isEmpty()) {
                    App.database.villeDAO().insertAll(response);
                    prefUtils.setMaxPassager(response.get(0));
                }
                lastLoaded = "getVilles";
                if(prefUtils.getInvPAuthorization())
                    getMarque();

                else
                    getTickets();
            }

            @Override
            public void onUnauthorized() {
                showRetryButton();
            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();
            }
        });
    }

    /**
     * get tickets data from server
     */
    void getTickets() {
        if (progress != null) progress.setProgress(97);
        if (prefUtils.getBlAuthorization()) {
            if (loading != null) loading.setText("Chargement des données B.L. ...");

            TicketDataManager.getInstance().getTicketsByCaisseId(new GenericObject(App.prefUtils.getBaseConfig(), prefUtils.getCaisseId()), new RemoteCallback<List<Ticket>>(context, false) {
                @Override
                public void onSuccess(List<Ticket> response) {
                    List<Ticket> tickets = new ArrayList<>();
                    if (response != null || !response.isEmpty()) {
                        for (int i = 0; i < response.size(); i++) {
                              if (response.get(i).gettIKMtTTC() > 0) {
                            if (response.get(i).tikNumTicketM == null) {
                                response.get(i).setTikNumTicketM(String.valueOf(response.get(i).gettIKNumTicket()));

                            }
                            App.database.ticketDAO().insert(response.get(i));
                            tickets.add(response.get(i));

                             }
                        }

                        lastLoaded = "getTickets";
                        if (!tickets.isEmpty()) {

                               App.database.ticketDAO().insertAll(tickets);
                           getLigneTickets(tickets);
                        } else {
                            prefUtils.setLoadData("full");
                            startActivity(MainActivity.class);
                        }


                    }
                }

                @Override
                public void onUnauthorized() {
                    showRetryButton();
                }

                @Override
                public void onFailed(Throwable throwable) {
                    showRetryButton();
                }
            }, false, true);
        } else {
            App.prefUtils.setIsInitiated(true);
            prefUtils.setLoadData("full");
            startActivity(MainActivity.class);
            App.database.appPropertiesDAO().insert(new AppProperties(new Date().getTime(), 0L));
        }
    }

    /**
     * get tickets lines data from server
     */
    void getLigneTickets(List<Ticket> tickets) {


        if (progress != null) progress.setProgress(98);
        if (loading != null) loading.setText("Chargement des données lignes des B.L. ...");
        LigneTicketDataManager.getInstance().getLigneTicketByTickets(new GenericObject(App.prefUtils.getBaseConfig(), tickets), new RemoteCallback<List<List<LigneTicket>>>(context, false) {
            @Override
            public void onSuccess(List<List<LigneTicket>> response) {

                List<LigneTicket> finalList = new ArrayList<>();

                if (response.get(0) != null) {
                    for (List<LigneTicket> list : response) {
                        finalList.addAll(list);
                    }

                    //   lastLoaded = "getLigneTickets";
                    lastLoaded = "getTickets";
                    if (!finalList.isEmpty()) {
                        App.database.ligneTicketDAO().insertAll(finalList);
                        getReglementsCaisse();
                    } else {
                        App.prefUtils.setIsInitiated(false);
                        prefUtils.setLoadData("full");
                        startActivity(MainActivity.class);
                        App.database.appPropertiesDAO().insert(new AppProperties(new Date().getTime(), 0L));

                    }

                }

            }

            @Override
            public void onUnauthorized() {
                showRetryButton();

            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();

            }
        });

    }


    /**
     * get payments data from server
     */
    void getReglementsCaisse() {
        if (progress != null) progress.setProgress(99);
        if (loading != null) loading.setText("Chargement des données réglements (1)...");
        ReglementCaisseDataManager.getInstance().getReglementCaisseBySession(new GenericObject(App.prefUtils.getBaseConfig(), App.prefUtils.getCaisseId()), new RemoteCallback<List<ReglementCaisse>>(context, false) {
            @Override
            public void onSuccess(List<ReglementCaisse> response) {

                if (response != null) {
                    if (!response.isEmpty()) {

                        for (int i = 0; i < response.size(); i++) {
                            if (response.get(i).getrEGCCode_M() == null) {
                                response.get(i).setrEGCCode_M(response.get(i).rEGCCode);
                            }

                            response.get(i).setrEGCDateReg(DateUtils.dateToStr(
                                    DateUtils.strToDate(response.get(i).getrEGCDateReg().replaceAll("/", "-"),
                                            "yyyy-MM-dd HH:mm:ss"), "yyyy-MM-dd HH:mm:ss"));
                        }

                    }
                    App.database.reglementCaisseDAO().insertAll(response);

                    lastLoaded = "getReglementsCaisse";
                   getChequesCaisse(response);
                } else {
                    App.prefUtils.setIsInitiated(false);
                    prefUtils.setLoadData("full");
                    startActivity(MainActivity.class);
                    App.database.appPropertiesDAO().insert(new AppProperties(new Date().getTime(), 0L));
                }
            }


            @Override
            public void onUnauthorized() {
                showRetryButton();

            }

            @Override
            public void onFailed(Throwable throwable) {
                Toasty.info(context, throwable.getMessage()).show();
                showRetryButton();

            }
        }, false);

    }

    /**
     * get payments with type cheque data from server
     */
    void getChequesCaisse(List<ReglementCaisse> reglementsCaisse) {
        if (progress != null) progress.setProgress(99);
        if (loading != null) loading.setText("Chargement des données réglements (2)...");

        if (!reglementsCaisse.isEmpty()) {
            ChequeCaisseDataManager.getInstance().getChequeCaisseByReglements(new GenericObject(App.prefUtils.getBaseConfig(), reglementsCaisse), new RemoteCallback<List<List<ChequeCaisse>>>(context, false) {
                @Override
                public void onSuccess(List<List<ChequeCaisse>> response) {
                    if (response != null) {

                        App.database.chequeCaisseDAO().insertAll(response.get(0));

                        //  lastLoaded = "getChequesCaisse";
                        lastLoaded = "getReglementsCaisse";
                        getTraitesCaisse(reglementsCaisse);
                    } else {
                        App.prefUtils.setIsInitiated(false);
                        prefUtils.setLoadData("full");
                        startActivity(MainActivity.class);
                        App.database.appPropertiesDAO().insert(new AppProperties(new Date().getTime(), 0L));
                    }

                }

                @Override
                public void onUnauthorized() {
                    showRetryButton();

                }

                @Override
                public void onFailed(Throwable throwable) {
                    showRetryButton();

                }
            });
        } else {
            App.prefUtils.setIsInitiated(false);
            prefUtils.setLoadData("full");
            startActivity(MainActivity.class);
            App.database.appPropertiesDAO().insert(new AppProperties(new Date().getTime(), 0L));
        }
    }

    /**
     * get payments with type traites data from server
     */
    void getTraitesCaisse(List<ReglementCaisse> reglementsCaisse) {
        if (progress != null) progress.setProgress(100);
        if (loading != null) loading.setText("Chargement des données réglements (3)...");
        TraiteCaisseDataManager.getInstance().getTraiteCaisseByReglements(new GenericObject(App.prefUtils.getBaseConfig(), reglementsCaisse), new RemoteCallback<List<List<TraiteCaisse>>>(context, false) {
            @Override
            public void onSuccess(List<List<TraiteCaisse>> response) {
                if (!response.isEmpty()) {
                    App.database.traiteCaisseDAO().insertAll(response.get(0));
                    App.prefUtils.setIsInitiated(false);
                    prefUtils.setLoadData("full");
                    startActivity(MainActivity.class);
                    App.database.appPropertiesDAO().insert(new AppProperties(new Date().getTime(), 0L));
                }

            }

            @Override
            public void onUnauthorized() {
                showRetryButton();

            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();
                //    getAllTypeServices();

            }
        });

    }

    /**
     * get distribution numérique AllTypeServices data from server
     */
    private void getAllTypeServices() {
        if (progress != null) progress.setProgress(71);
        if (loading != null) loading.setText("Chargement des données distribution numérique 1 ...");
        DistributionNumeriqueDataManager.getInstance().getAllTypeService(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<DNTypeServices>>(context, false) {
            @Override
            public void onSuccess(List<DNTypeServices> response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    App.database.dnTypeServicesDAO().insertAll(response);
                }
                lastLoaded = "getAllTypeServices";
                getAllSuperficieService();
            }

            @Override
            public void onUnauthorized() {
                showRetryButton();
            }

            @Override
            public void onFailed(Throwable throwable) {
                Toasty.info(context, throwable.getMessage()).show();
                showRetryButton();
                //  getAllTypeService();
            }
        });
    }

    /**
     * get distribution numérique AllTypeServices data from server
     */
    private void getAllSuperficieService() {
        if (progress != null) progress.setProgress(72);
        if (loading != null) loading.setText("Chargement des données distribution numérique 2 ...");
        DistributionNumeriqueDataManager.getInstance().getAllSuperficies(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<DNSuperficie>>(context, false) {
            @Override
            public void onSuccess(List<DNSuperficie> response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    App.database.dnSuperficieDAO().insertAll(response);
                }
                lastLoaded = "getAllSuperficieService";
                getAllTypePVentes();

            }

            @Override
            public void onUnauthorized() {
                showRetryButton();
            }

            @Override
            public void onFailed(Throwable throwable) {
                //  Toasty.info(context, throwable.getMessage()).show();
                showRetryButton();
                //  getAllTypePVentes();
            }
        });
    }


    /**
     * get distribution numérique  TypePVentes()  data from server
     */
    private void getAllTypePVentes() {
        if (progress != null) progress.setProgress(72);
        if (loading != null) loading.setText("Chargement des données distribution numérique 3 ...");
        DistributionNumeriqueDataManager.getInstance().getAllTypePVentes(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<DNTypePVente>>(context, false) {
            @Override
            public void onSuccess(List<DNTypePVente> response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    App.database.dnTypePVenteSDAO().insertAll(response);
                }
                lastLoaded = "getAllTypePVentes";
                getAllDNVIsites();
            }

            @Override
            public void onUnauthorized() {
                showRetryButton();
            }

            @Override
            public void onFailed(Throwable throwable) {
                //   Toasty.info(context, throwable.getMessage()).show();
                showRetryButton();
                // getAllDNVIsites();
            }
        });
    }

    /**
     * get distribution numérique  DNVIsiteS  data from server
     */
    private void getAllDNVIsites() {
        if (progress != null) progress.setProgress(72);
        if (loading != null) loading.setText("Chargement des données distribution numérique 4 ...");
        DistributionNumeriqueDataManager.getInstance().getDNVIsitesByuser(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<DNVIsite>>(context, false) {
            @Override
            public void onSuccess(List<DNVIsite> response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    int i = 0;
                    for (DNVIsite dNVIsite : response) {

                        if (response.get(i).getVIS_Code_M() == null) {
                            dNVIsite.setVIS_Code_M(response.get(i).getVIS_Num());
                        }
                        App.database.dnVisitesDAO().insert(dNVIsite);
                        i++;

                    }

                }
                lastLoaded = "getAllDNVIsites";
                getAllLignevisitebyUser();
            }

            @Override
            public void onUnauthorized() {
                showRetryButton();
            }

            @Override
            public void onFailed(Throwable throwable) {
                // Toasty.info(context, throwable.getMessage()).show();
                showRetryButton();
                // getallDNFamille();
            }
        });
    }


    /**
     * get distribution numérique  DNLigne VIsiteS  by user data from server
     */
    private void getAllLignevisitebyUser() {
        if (progress != null) progress.setProgress(72);
        if (loading != null) loading.setText("Chargement des données distribution numérique 5 ...");
        DistributionNumeriqueDataManager.getInstance().getAllLignevisiteByUser(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<DN_LigneVisite>>(context, false) {
            @Override
            public void onSuccess(List<DN_LigneVisite> response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    App.database.dnLigneVisiteDAO().deleteAll();
                    App.database.dnLigneVisiteDAO().insertAll(response);


                }
                lastLoaded = "getAllLignevisitebyUser";
                getallDNFamille();

            }

            @Override
            public void onUnauthorized() {
                showRetryButton();
            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();
            }
        });
    }


    /**
     * get distribution numérique  DNfamille  data from server
     */
    private void getallDNFamille() {
        if (progress != null) progress.setProgress(73);
        if (loading != null) loading.setText("Chargement des données distribution numérique 6 ...");
        DistributionNumeriqueDataManager.getInstance().getalldnFamille(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<DNFamille>>(context, false) {
            @Override
            public void onSuccess(List<DNFamille> response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    App.database.dnFamilleVisite().insertAll(response);
                }
                lastLoaded = "getallDNFamille";
                getReclamations();

                // App.prefUtils.setIsInitiated(false);
                //  prefUtils.setLoadData("full");
                //  App.database.appPropertiesDAO().insert(new AppProperties(new Date().getTime(), 0L));

                //   startActivity(MainActivity.class);
            }

            @Override
            public void onUnauthorized() {
                showRetryButton();
            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();
            }
        });
    }


    /**
     * get distribution numérique  DNVIsiteS  data from server
     */
    private void getVisitebyCode() {
        if (progress != null) progress.setProgress(100);
        if (loading != null) loading.setText("Chargement des données distribution numérique 4 ...");
        DistributionNumeriqueDataManager.getInstance().getVisitebyCode(new GenericObject(prefUtils.getBaseConfig(), new DNVIsite("VS_2211000001", App.prefUtils.getExercice())), new RemoteCallback<DNVIsite>(context, false) {
            @Override
            public void onSuccess(DNVIsite response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    App.database.dnVisitesDAO().insert(response);
                }

            }

            @Override
            public void onUnauthorized() {

            }

            @Override
            public void onFailed(Throwable throwable) {
                Toasty.info(context, throwable.getMessage()).show();
            }
        });
    }


    /**
     * get devises data from server
     */
    void getDevises() {
        if (progress != null) progress.setProgress(23);
        if (loading != null) loading.setText("Chargement des données devises ...");
        DeviseDataManager.getInstance().getDevises(new GenericObject(App.prefUtils.getBaseConfig()), new RemoteCallback<List<Devise>>(context, false) {
            @Override
            public void onSuccess(List<Devise> response) {
                App.database.deviseDAO().insertAll(response);
                Devise devise = App.database.deviseDAO().getActiveOne();
                if (devise != null) {
                    App.prefUtils.setCurrency(devise.getSymbole());
                    App.prefUtils.setDecimalCount(devise.getNbreChiffreVirgule());
                }
                lastLoaded = "getDevises";
                if (prefUtils.getDepenseAuthorization())      getExpenses();
              else getStatistics();
            }

            @Override
            public void onUnauthorized() {
                showRetryButton();

            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();

            }
        });
    }

    /**
     * get depenses data from server
     */
    void getExpenses() {
        progress.setProgress(24);
        if (loading!=null) loading.setText("Chargement des données depenses ...");

        DepenceDataManager.getInstance().getDepence(new GenericObject(App.prefUtils.getBaseConfig()), new RemoteCallback<List<DepenceType>>(context, false) {
            @Override
            public void onSuccess(List<DepenceType> response) {
                if (response != null) {
                    lastLoaded = "getExpenses";

                    if (!response.isEmpty()) {
                        App.database.depenceTypeDAO().insertAll(response);

                    }
                    getDepenceCaisseByCaisseId();
                }





            }

            @Override
            public void onUnauthorized() {
                showRetryButton();
            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();
            }
        });

    }




    void getDepenceCaisseByCaisseId(){
        progress.setProgress(24);
        if (loading!=null) loading.setText("Chargement des données depenses ...");
        String ddm = "";
        ddm = App.database.depenceCaisseDAO().getDDM();
        DepenceCaisseDataManager.getInstance().getDepenceCaisseByCaisseId(new GenericObject(prefUtils.getBaseConfig(), new DepenceCaisse(App.prefUtils.getCaisseCode(), ddm)),
                new RemoteCallback<List<DepenceCaisse>>(context, false) {
                    @Override
                    public void onSuccess(List<DepenceCaisse> response) {
                        if (!response.isEmpty()) {
                            lastLoaded = "getDepenceCaisseByCaisseId";
                            App.database.depenceCaisseDAO().deleteAll();

                            for (DepenceCaisse depenceCaisse : response) {

                                if(depenceCaisse.getDepCodeM()==null)
                                    depenceCaisse.setDepCodeM(depenceCaisse.getDepCode());
                                App.database.depenceCaisseDAO().insert(depenceCaisse);
                            }
                            //App.database.depenceCaisseDAO().insertAll(response);


                        }
                        getStatistics();
                    }

                    @Override
                    public void onUnauthorized() {
                        showRetryButton();
                    }

                    @Override
                    public void onFailed(Throwable throwable) {
                        showRetryButton();
                    }
                });
    }
    /**
     * get depenses data from server
     */
  /*  void getExpensesCaisse() {
        progress.setProgress(23);
        if (loading!=null)

            loading.setText("Chargement des données depenses caisse ...");

        DepenceCaisseDataManager.getInstance().getDepenceCaisse(new GenericObject(App.prefUtils.getBaseConfig()), new RemoteCallback<>(context, false) {
            @Override
            public void onSuccess(List<DepenceCaisse> response) {

                App.database.depenceCaisseDAO().insertAll(response);

                getStatistics();
            }

            @Override
            public void onUnauthorized() {

            }

            @Override
            public void onFailed(Throwable throwable) {

            }
        });

    }
*/
    /**
     * get num ticket from server
     */
    void getMaxTikNum() {
        if (progress != null) progress.setProgress(7);
        if (prefUtils.getBlAuthorization()) {

            if (loading != null) loading.setText("Chargement des données numéro ticket ...");
            TicketDataManager.getInstance().getMaxTikNum(new GenericObject(App.prefUtils.getBaseConfig()), new RemoteCallback<Integer>(context, false) {
                @Override
                public void onSuccess(Integer response) {

                    if (response != null) {
                        prefUtils.setMaxNumTicket(response);
                    }
                    lastLoaded = "getMaxTikNum";
                    getSessionCaisses();
                }

                @Override
                public void onUnauthorized() {
                    showRetryButton();

                }

                @Override
                public void onFailed(Throwable throwable) {
                    showRetryButton();

                }
            });
        } else {
            getSessionCaisses();
        }
    }

    /**
     * get prefixes data from server
     */
    void getPrefixes() {
        if (progress != null) progress.setProgress(5);
        if (loading != null) loading.setText("Chargement des données prefixes ...");
        PrefixeDataManager.getInstance().getPrefixes(new GenericObject(App.prefUtils.getBaseConfig()), new RemoteCallback<List<Prefixe>>(context, false) {
            @Override
            public void onSuccess(List<Prefixe> response) {
                App.database.prefixeDAO().insertAll(response);

                lastLoaded = "getPrefixes";
                getMaxTikNum();
            }

            @Override
            public void onUnauthorized() {
                showRetryButton();
            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();
            }
        });
    }

    /**
     * get prefixes data from server
     */
    void getPrices() {
        if (progress != null) progress.setProgress(13);
        if (loading != null) loading.setText("Chargement des données prix ...");
        PricePerStationDataManager.getInstance().getPricesByStation(new GenericObject(App.prefUtils.getBaseConfig()),
                new RemoteCallback<List<PricePerStation>>(context, false) {
                    @Override
                    public void onSuccess(List<PricePerStation> response) {
                        App.database.pricePerStationDAO().insertAll(response);

                        lastLoaded = "getPrices";
                        getStations();
                    }

                    @Override
                    public void onUnauthorized() {
                        showRetryButton();

                    }

                    @Override
                    public void onFailed(Throwable throwable) {
                        showRetryButton();

                    }
                });
    }

    /**
     * get stations data from server
     */
    void getStations() {
        if (progress != null) progress.setProgress(15);
        if (loading != null) loading.setText("Chargement des données stations ...");

        StationDataManager.getInstance().getAllStations(new GenericObject(App.prefUtils.getBaseConfig()), new RemoteCallback<List<Station>>(context, false) {
            @Override
            public void onSuccess(List<Station> response) {
                App.database.stationDAO().insertAll(response);
                Station userStat = App.database.stationDAO().getOneByCode(prefUtils.getUserStationId());
                if (userStat != null)
                    prefUtils.setGPSParams(userStat.getStatMetrageM(), userStat.getStatSecondeM());

                lastLoaded = "getStations";
                getFornisseurs();
            }

            @Override
            public void onUnauthorized() {
                showRetryButton();

            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();
            }
        });
    }

    void getFornisseurs() {
        if (progress != null) progress.setProgress(18);
        if (loading != null) loading.setText("Chargement des données fornisseurs ...");
        FournisseurDataManager.getInstance().getFournisseurs(new GenericObject(App.prefUtils.getBaseConfig()), new RemoteCallback<List<Fournisseur>>(context, false) {
            @Override
            public void onSuccess(List<Fournisseur> response) {
                App.database.fournisseurDAO().insertAll(response);

                lastLoaded = "getFornisseurs";

                getEtablisements();
            }

            @Override
            public void onUnauthorized() {
                showRetryButton();

            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();

            }
        });
    }

    /**
     * get etablisments data from server
     */
    void getEtablisements() {
        if (progress != null) progress.setProgress(20);
        if (loading != null) loading.setText("Chargement des données Etablisement ...");
        EtablisementDataManager.getInstance().getEtablisements(new GenericObject(App.prefUtils.getBaseConfig()), new RemoteCallback<Etablisement>(context, false) {
            @Override
            public void onSuccess(Etablisement response) {
                App.database.etablisementDAO().insertAll(response);
                lastLoaded = "getEtablisements";
                getDevises();
            }

            @Override
            public void onUnauthorized() {
                showRetryButton();

            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();

            }
        });
    }

    /**
     * get statistics data from server
     */
    void getStatistics() {
        if (loading != null) loading.setText("Chargement des données statistiques ...");
        if (progress != null) progress.setProgress(25);
        MiscDataManager.getInstance().getStatistics(new GenericObject(App.prefUtils.getBaseConfig(), App.prefUtils.getSessionCaisseId()), new RemoteCallback<Statistics>(context, false) {
            @Override
            public void onSuccess(Statistics response) {
                response.setUpdated_at(new Date().getTime());
                Paper.book().write(STATISTICS_DB_KEY, response);

                lastLoaded = "getStatistics";
                getCartesResto();
            }

            @Override
            public void onUnauthorized() {
                showRetryButton();
            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();
            }
        });
    }

    /**
     * get restaurant carte data from server
     */
    void getCartesResto() {

        if (progress != null) progress.setProgress(30);
        if (loading != null) loading.setText("Chargement des données carte resto ...");
        CarteRestoDataManager.getInstance().getCartesResto(new GenericObject(App.prefUtils.getBaseConfig()), new RemoteCallback<List<CarteResto>>(context, false) {
            @Override
            public void onSuccess(List<CarteResto> response) {
                App.database.carteRestoDAO().insertAll(response);

                lastLoaded = "getCartesResto";
                getBanques();
            }

            @Override
            public void onUnauthorized() {
                showRetryButton();
            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();
            }
        });
    }

    void getClientsArticlePrix() {
        if (progress != null) progress.setProgress(95);
        if (loading != null) loading.setText("Chargement des données clients articles prix ...");
        ClientArticlePrixDataManager.getInstance().getClientArticlePrix(
                new GenericObject(App.prefUtils.getBaseConfig()),
                new RemoteCallback<List<ClientArticlePrix>>(context, false) {
                    @Override
                    public void onSuccess(List<ClientArticlePrix> response) {
                        App.database.clientArticlePrixDAO().insertAll(response);
                        lastLoaded = "getClientsArticlePrix";
                        getVilles();
                    }

                    @Override
                    public void onUnauthorized() {
                        showRetryButton();
                    }

                    @Override
                    public void onFailed(Throwable throwable) {
                        showRetryButton();
                    }
                });
    }

    private void getVCNewProducts() {
        if (progress != null) progress.setProgress(48);
        if (loading != null) loading.setText("Chargement des données veuille concurrentielle ...");
        VeuilleConcurrentielleDataManager.getInstance().getVCNewProducts(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<VCNewProduct>>(context, false) {
            @Override
            public void onSuccess(List<VCNewProduct> response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    App.database.vcNewProductDAO().deleteAll();
                    App.database.vcNewProductDAO().insertAll(response);
                }
                lastLoaded = "getVCNewProducts";
                getVCPromos();
            }

            @Override
            public void onUnauthorized() {
                showRetryButton();
            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();
                Toasty.info(context, throwable.getMessage()).show();
            }
        });
    }

    private void getVCPromos() {
        if (progress != null) progress.setProgress(50);
        if (loading != null) loading.setText("Chargement des données veuille concurrentielle ...");
        VeuilleConcurrentielleDataManager.getInstance().getVCPromos(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<VCPromo>>(context, false) {
            @Override
            public void onSuccess(List<VCPromo> response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    App.database.vcPromosDAO().deleteAll();
                    App.database.vcPromosDAO().insertAll(response);
                }
                lastLoaded = "getVCPromos";
                getVCPrix();
            }

            @Override
            public void onUnauthorized() {
                showRetryButton();
            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();
                Toasty.info(context, throwable.getMessage()).show();
            }
        });
    }

    private void getVCPrix() {
        if (progress != null) progress.setProgress(56);
        if (loading != null) loading.setText("Chargement des données veuille concurrentielle ...");
        VeuilleConcurrentielleDataManager.getInstance().getVCPrices(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<VCPrix>>(context, false) {
            @Override
            public void onSuccess(List<VCPrix> response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    App.database.vcPricesDAO().deleteAll();
                    App.database.vcPricesDAO().insertAll(response);
                }
                lastLoaded = "getVCPrix";
                getVCAutre();
            }

            @Override
            public void onUnauthorized() {
                showRetryButton();
            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();
                Toasty.info(context, throwable.getMessage()).show();
            }
        });
    }

    private void getVCAutre() {
        if (progress != null) progress.setProgress(60);
        if (loading != null) loading.setText("Chargement des données veuille concurrentielle ...");
        VeuilleConcurrentielleDataManager.getInstance().getVCautres(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<VCAutre>>(context, false) {
            @Override
            public void onSuccess(List<VCAutre> response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    App.database.vcAutreDAO().deleteAll();
                    App.database.vcAutreDAO().insertAll(response);
                }
                lastLoaded = "getVCAutre";
                getVCTypeCommunication();
            }

            @Override
            public void onUnauthorized() {
                showRetryButton();
            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();
                Toasty.info(context, throwable.getMessage()).show();
            }
        });
    }

    private void getVCTypeCommunication() {
        if (progress != null) progress.setProgress(63);
        if (loading != null) loading.setText("Chargement des données veuille concurrentielle ...");
        VeuilleConcurrentielleDataManager.getInstance().getVCTypeCommunications(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<VCTypeCommunication>>(context, false) {
            @Override
            public void onSuccess(List<VCTypeCommunication> response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    App.database.vcTypeCommunicationDAO().insertAll(response);
                }
                lastLoaded = "getVCTypeCommunication";
                getVCListConcurrent();
            }

            @Override
            public void onUnauthorized() {
                showRetryButton();
            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();
                Toasty.info(context, throwable.getMessage()).show();
            }
        });
    }

    private void getVCListConcurrent() {
        if (progress != null) progress.setProgress(66);
        if (loading != null) loading.setText("Chargement des données veuille concurrentielle ...");
        VeuilleConcurrentielleDataManager.getInstance().getVCListeConcurrents(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<VCListeConcurrent>>(context, false) {
            @Override
            public void onSuccess(List<VCListeConcurrent> response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    Log.d("getVCListConcurrent", String.valueOf(response));
                    App.database.vcListeConcurrentDAO().insertAll(response);
                }
                lastLoaded = "getVCListConcurrent";
                getVCImages();
            }

            @Override
            public void onUnauthorized() {
                showRetryButton();
            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();
                Toasty.info(context, throwable.getMessage()).show();
            }
        });
    }

    private void getVCImages() {
        if (progress != null) progress.setProgress(70);
        if (loading != null) loading.setText("Chargement des données veuille concurrentielle ...");
        VeuilleConcurrentielleDataManager.getInstance().getVCImages(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<VCImage>>(context, false) {
            @Override
            public void onSuccess(List<VCImage> response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    for (VCImage vcImage : response) {
                        vcImage.isSync = true;
                        vcImage.status = Globals.ITEM_STATUS.SELECTED.getStatus();
                    }
                    App.database.vcImageDAO().deleteAll();
                    App.database.vcImageDAO().insertAll(response);
                }
                lastLoaded = "getVCImages";
                getAllTypeServices();

            }

            @Override
            public void onUnauthorized() {

            }

            @Override
            public void onFailed(Throwable throwable) {
                showRetryButton();
                Toasty.info(context, throwable.getMessage()).show();
            }
        });
    }

    void getData() {
        AsyncJob.doOnMainThread(() -> {
            if (loading != null) loading.setText(getString(R.string.loading_content));
            if (progress != null) progress.setProgress(1);


            LoadBackUpLicensesUrl();
            //      getPrefixes();

        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        unbinder.unbind();
        unbindDrawables(findViewById(R.id.progressLayout));
        System.gc();
    }

    private void unbindDrawables(View view) {
        if (view.getBackground() != null) {
            view.getBackground().setCallback(null);
        }
        if (view instanceof ViewGroup && !(view instanceof AdapterView)) {
            for (int i = 0; i < ((ViewGroup) view).getChildCount(); i++) {
                unbindDrawables(((ViewGroup) view).getChildAt(i));
            }
            ((ViewGroup) view).removeAllViews();
        }
    }

    void getBackUpUrlLicenceFromLocalDB() {
        List<LicenseResponseItem> licenseResp = App.database.backUpLicenseUrlDAO().getAll();

        Log.e("eedsqs",licenseResp.size()+"");
        if (!licenseResp.isEmpty()) {
            for (LicenseResponseItem backUp : licenseResp) {
              /*  URL url = null;
                try {
                    url = new URL(backUp.getUrl());
                } catch (MalformedURLException e) {
                    e.printStackTrace();
                }
                if(url!= null){
                    String path = url.getFile().substring(0, url.getFile().lastIndexOf('/')+1);

                    if(backUp.getDesignation_url().equals("check_license")){
                        CHECK_LICENCE_URL = url.getFile()+"";
                        BACKUP_BASEURL = url.getProtocol() + "://" + url.getHost() + path;


                    }
                    if(backUp.getDesignation_url().equals("selection_base_config ")){
                             GET_BASE_CONFIG_URL = url.getFile()+"";
                    }
                }*/

                if (backUp.getDesignation_url().equals("check_license")) {
                    CHECK_LICENCE_URL = backUp.getUrl().substring(backUp.getUrl().lastIndexOf("/") + 1);
                    CHECK_LICENCE_BASEURL = backUp.getUrl().substring(0, backUp.getUrl().lastIndexOf("/") + 1);

                }
                if (backUp.getDesignation_url().replaceAll("\\s+$", "").equals("selection_base_config")) {
                    GET_BASE_CONFIG_URL = backUp.getUrl().substring(backUp.getUrl().lastIndexOf("/") + 1);
                    GET_BASE_CONFIG_BASEURL = backUp.getUrl().substring(0, backUp.getUrl().lastIndexOf("/") + 1);
                }
            }
        }
    }

    /**
     * delete all the data from local database
     */
    void purgeData() {
        App.database.trakingDAO().deleteAll();
        App.database.clientArticlePrixDAO().deleteAll();
        App.database.etablisementDAO().deleteAll();
        App.database.deviseDAO().deleteAll();
        App.database.caisseDAO().deleteAll();
        App.database.carteRestoDAO().deleteAll();
        App.database.bonRetourDAO().deleteAll();
        App.database.ligneBonRetourDAO().deleteAll();
        App.database.appPropertiesDAO().deleteAll();
        App.database.chequeCaisseDAO().deleteAll();
        App.database.articleCodeBarDAO().deleteAll();
        App.database.bonCommandeDAO().deleteAll();
        App.database.deviseDAO().deleteAll();
        App.database.etablisementDAO().deleteAll();
        App.database.etatOrdreMissionDAO().deleteAll();
        App.database.reglementCaisseDAO().deleteAll();
        App.database.familleDAO().deleteAll();
        App.database.fournisseurDAO().deleteAll();
        App.database.ligneBonCommandeDAO().deleteAll();
        App.database.ordreMissionDAO().deleteAll();
        App.database.pricePerStationDAO().deleteAll();
        App.database.sessionCaisseDAO().deleteAll();
        App.database.stationDAO().deleteAll();
        App.database.stationStockDAO().deleteAll();
        App.database.timbreDAO().deleteAll();
        App.database.trakingAlarmDAO().deleteAll();
        App.database.villeDAO().deleteAll();
        App.database.ligneTicketDAO().deleteAll();
        App.database.ticketDAO().deleteAll();
        App.database.articleDAO().deleteAll();
        App.database.clientDAO().deleteAll();
        App.database.banqueDAO().deleteAll();
        App.database.carteRestoDAO().deleteAll();
        App.database.prefixeDAO().deleteAll();
        App.database.authorizationDAO().deleteAll();
        App.database.dnFamilleVisite().deleteAll();
        App.database.dnLigneVisiteDAO().deleteAll();
        App.database.dnVisitesDAO().deleteAll();
        App.database.dnSuperficieDAO().deleteAll();
        App.database.dnTypePVenteSDAO().deleteAll();
        App.database.dnTypeServicesDAO().deleteAll();

        App.database.vcPromosDAO().deleteAll();
        App.database.vcNewProductDAO().deleteAll();
        App.database.vcPricesDAO().deleteAll();
        App.database.vcAutreDAO().deleteAll();
        App.database.vcImageDAO().deleteAll();
        App.database.vcListeConcurrentDAO().deleteAll();
        App.database.vcTypeCommunicationDAO().deleteAll();

    }




}
