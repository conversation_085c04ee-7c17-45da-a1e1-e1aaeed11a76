package com.asmtunis.procaissemobility.data.network.services;

import com.asmtunis.procaissemobility.data.models.DepenceCaisse;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Ticket;

import java.util.List;

import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * Created by WAEL on 8/30/22.
 */
public interface DepenceCaisseService {
    @POST("getDepenseCaisse")
    Call<List<DepenceCaisse>> getDepencesCaisse(@Body GenericObject connexion);

    //  @POST("getDepenseByIdCaiise")
    //   Call<ResponseBody> getDepencesCaisseByIdCaisse(@Body GenericObject connexion);

    @POST("getDepenseByIdCaisse")
    Call<List<DepenceCaisse>> getDepenceCaisseByCaisseId(@Body GenericObject genericObject);

    @POST("addBatchDepense")
    Call<ResponseBody> addBatchDepences(@Body GenericObject connexion);

    @POST("deleteDepense")
    Call<ResponseBody> deleteDepenseCaisse(@Body GenericObject connexion);

}
