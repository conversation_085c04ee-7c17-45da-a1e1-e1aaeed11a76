package com.asmtunis.procaissemobility.comparators;

/**
 * Created by me on 10/8/2017.
 */


public final class CheckLinesComparators {

    private CheckLinesComparators() {
        //no instance
    }
/*

    public static Comparator<RestoTicket> getRestoTicketCodeComparator() {
        return new RestoTicketCodeComparator();
    }

    public static Comparator<RestoTicket> getRestoTicketDiscountComparator() {
        return new RestoTicketDiscountComparator();
    }

    public static Comparator<RestoTicket> getRestoTicketCompanyComparator() {
        return new RestoTicketCompanyComparator();
    }

    public static Comparator<RestoTicket> getRestoTicketUnitAmountComparator() {
        return new RestoTicketUnitAmountComparator();
    }

    public static Comparator<RestoTicket> getRestoTicketAmountComparator() {
        return new RestoTicketAmountComparator();
    }


    private static class RestoTicketCompanyComparator implements Comparator<RestoTicket> {

        @Override
        public int compare(final RestoTicket ligneTicket1, final RestoTicket ligneTicket2) {
            return ligneTicket1.getArticle().getaRTDesignation().toLowerCase().compareTo(ligneTicket2.getArticle().getaRTDesignation().toLowerCase());
        }
    }


    private static class RestoTicketCodeComparator implements Comparator<RestoTicket> {

        @Override
        public int compare(final RestoTicket ligneTicket1, final RestoTicket ligneTicket2) {
            if (ligneTicket1.getlTQte() < ligneTicket2.getlTQte()) return -1;
            if (ligneTicket1.getlTQte() > ligneTicket2.getlTQte()) return 1;
            return 0;
        }
    }

    private static class RestoTicketAmountComparator implements Comparator<RestoTicket> {

        @Override
        public int compare(final RestoTicket ligneTicket1, final RestoTicket ligneTicket2) {
            if (ligneTicket1.getlTMtTTC() < ligneTicket2.getlTMtTTC()) return -1;
            if (ligneTicket1.getlTMtTTC() > ligneTicket2.getlTMtTTC()) return 1;
            return 0;
        }
    }

 */
}