package com.asmtunis.procaissemobility.data.models;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 14/09/2017.
 */

public class Unite extends BaseModel{

    private String UNI_Code;
    private String UNI_Designation;
    private String UNI_User;
    private String UNI_Station;

    public Unite() {
    }



    public String getUNI_Code() {
        return UNI_Code;
    }

    public void setUNI_Code(String UNI_Code) {
        this.UNI_Code = UNI_Code;
    }

    public String getUNI_Designation() {
        return UNI_Designation;
    }

    public void setUNI_Designation(String UNI_Designation) {
        this.UNI_Designation = UNI_Designation;
    }

    public String getUNI_User() {
        return UNI_User;
    }

    public void setUNI_User(String UNI_User) {
        this.UNI_User = UNI_User;
    }

    public String getUNI_Station() {
        return UNI_Station;
    }

    public void setUNI_Station(String UNI_Station) {
        this.UNI_Station = UNI_Station;
    }

    @Override
    public String toString() {
        return "unite{" +
                "UNI_Code=" + UNI_Code +
                ", UNI_Designation='" + UNI_Designation + '\'' +
                ", UNI_User='" + UNI_User + '\'' +
                ", UNI_Station='" + UNI_Station + '\'' +

                '}';
    }

}
