package com.asmtunis.procaissemobility.data.models;


import com.google.gson.annotations.SerializedName;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 14/09/2017.
 */

public class Couleur extends  BaseModel{

    @SerializedName("COU_Code")
    private String COU_Code;
    @SerializedName("COU_Designation")
    private String COU_Designation;
    @SerializedName("COU_User")
    private String COU_User;
    @SerializedName("COU_Station")
    private String COU_Station;

    public Couleur() {
    }



    public String getCOU_Code() {
        return COU_Code;
    }

    public void setCOU_Code(String COU_Code) {
        this.COU_Code = COU_Code;
    }

    public String getCOU_Designation() {
        return COU_Designation;
    }

    public void setCOU_Designation(String COU_Designation) {
        this.COU_Designation = COU_Designation;
    }

    public String getCOU_User() {
        return COU_User;
    }

    public void setCOU_User(String COU_User) {
        this.COU_User = COU_User;
    }

    public String getCOU_Station() {
        return COU_Station;
    }

    public void setCOU_Station(String COU_Station) {
        this.COU_Station = COU_Station;
    }

    @Override
    public String toString() {
        return "couleur{" +
                "COU_Code=" + COU_Code +
                ", COU_Designation='" + COU_Designation + '\'' +
                ", COU_User='" + COU_User + '\'' +
                ", COU_b_station='" + COU_Station + '\'' +

                '}';
    }


}
