package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.DepenceType;

import java.util.List;

/**
 * Created by WAEL on 29/08/2022.
 */
@Dao
public interface DepenceTypeDAO {

  //  @Query("SELECT * FROM DEPENCE WHERE  isSync=1 and Status='SELECTED'")
    @Query("SELECT * FROM DepenceType")
    LiveData<List<DepenceType>> getAllMutable();

    //@Query("SELECT * FROM DEPENCE WHERE  isSync=1 and Status='SELECTED'")
    @Query("SELECT * FROM DepenceType")
    List<DepenceType> getAll();
/*
    @Query("SELECT * FROM DEPENCE WHERE DEPENCE = :devise ")
    Devise getOneByDevise(String devise);

    @Query("SELECT * FROM Devise WHERE Activite = '1' ")
    Devise getActiveOne();

    @Query("SELECT * FROM Devise LIMIT 1")
    Devise getOne();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(Devise item);
*/
    @Query("DELETE FROM DepenceType")
    void deleteAll();

    @Query("SELECT count(*) FROM DepenceType where isSync=0 and (Status='INSERTED'  or Status='UPDATED')")
    int getNoSyncCount();

    @Query("SELECT count(*) FROM DepenceType where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    LiveData<Integer> getNoSyncCountMutable();

    @Query("UPDATE DepenceType SET Status=:status where DEP_Code =:depCode")
    void updateDepenseStatus(String depCode, String status);

    @Query("SELECT * FROM DepenceType WHERE  isSync=0 and Status='INSERTED' order by strftime('%Y-%m-%d %H-%M',DEP_DDm) desc")
    List<DepenceType> getNonSync();


    @Query("SELECT * FROM DepenceType WHERE DEP_Code_M = :code ")
    DepenceType getOneByCodeM(String code);


    @Query("UPDATE DepenceType SET IsSync=1, Status='SELECTED' where DEP_Code_M =:depCodeM")
    void updateDepenseStatus(String depCodeM);

    @Query("UPDATE DepenceType SET IsSync=1, Status='SELECTED', DEP_Code =:depCode WHERE DEP_Code_M =:depCodeM")
    void updateDepenseStatusByCodeM(String depCode, String depCodeM);

    @Query("UPDATE DepenceType SET Status='SELECTED' WHERE status='UPDATED'")
    void refreshEntity();

    @Query("SELECT * FROM DepenceType where DEP_Code=:depcode")
    DepenceType getDepenseDetails(String depcode);

    @Query("SELECT count(*) FROM DepenceType where Status='UPDATED' and DEP_Code=:depCode")
    int isSelectedDepence(String depCode);

    @Query("SELECT count(*) FROM DepenceType where Status='UPDATED'")
    int isRowUptaded();

    @Query("SELECT count(*) FROM DepenceType where DEP_Lib like:lib")
    int isDepenseTypeExist(String lib);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<DepenceType> items);

}