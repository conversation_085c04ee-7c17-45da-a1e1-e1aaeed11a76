package com.asmtunis.procaissemobility.ui.activities;

import static com.asmtunis.procaissemobility.R.id.EntitledInputField;
import static com.asmtunis.procaissemobility.R.id.TaxRegistrationNumberInputField;
import static com.asmtunis.procaissemobility.helper.Globals.CLIENT_INTENT_ID_KEY;
import static com.basgeekball.awesomevalidation.ValidationStyle.COLORATION;

import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Intent;
import android.location.Address;
import android.location.Geocoder;
import android.os.Bundle;
import android.os.Handler;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.CheckBox;
import android.widget.Spinner;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.Toolbar;
import androidx.core.content.res.ResourcesCompat;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.AppProperties;
import com.asmtunis.procaissemobility.data.models.BonCommande;
import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.VCPrix;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.datamanager.ClientDataManager;
import com.asmtunis.procaissemobility.enums.DataSyncStatus;
import com.asmtunis.procaissemobility.helper.Globals;
import com.asmtunis.procaissemobility.helper.utils.DateUtils;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.helper.utils.Utils;
import com.asmtunis.procaissemobility.listener.IDataSyncListener;
import com.asmtunis.procaissemobility.ui.components.ExtendedMaterialEditText;
import com.asmtunis.procaissemobility.ui.components.MapBoxView;
import com.basgeekball.awesomevalidation.AwesomeValidation;
import com.basgeekball.awesomevalidation.utility.RegexTemplate;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ObjectUtils;
import com.esotericsoftware.minlog.Log;
import com.google.android.gms.location.places.Place;
import com.google.android.gms.location.places.ui.PlacePicker;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.snackbar.Snackbar;
import com.mapbox.mapboxsdk.geometry.LatLng;
import com.mapbox.mapboxsdk.maps.MapView;
import com.mikepenz.google_material_typeface_library.GoogleMaterial;
import com.mikepenz.iconics.IconicsDrawable;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.regex.Pattern;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnItemClick;
import butterknife.Unbinder;
import es.dmoral.toasty.Toasty;
import searchablespinner.interfaces.OnItemSelectedListener;

public class AddClientActivity extends BaseActivity implements AdapterView.OnItemSelectedListener {

    double latitude, longitude = 0;
    public static final int PLACE_PICKER_REQUEST = 5251;

    String clientType;
    AwesomeValidation mAwesomeValidation = new AwesomeValidation(COLORATION);

    @BindView(R.id.toolbar)
    Toolbar toolbar;

    @BindView(TaxRegistrationNumberInputField)
    ExtendedMaterialEditText taxRegistrationNumberInputField;

    @BindView(EntitledInputField)
    ExtendedMaterialEditText entitledInputField;

    @BindView(R.id.AddressInputField)
    ExtendedMaterialEditText addressInputField;

    @BindView(R.id.EmailInputField)
    ExtendedMaterialEditText emailInputField;

    @BindView(R.id.ProfessionInputField)
    ExtendedMaterialEditText professionInputField;

    @BindView(R.id.CompanyInputField)
    ExtendedMaterialEditText companyInputField;

    /* @BindView(R.id.NumeroInputField)
     ExtendedMaterialEditText numeroInputField;

     @BindView(R.id.DateInputField)
     ExtendedMaterialEditText dateInputField;
 */
    @BindView(R.id.stampCheckField)
    CheckBox stampCheckField;

    @BindView(R.id.creditCheckField)
    CheckBox creditCheckField;
    /*
        @BindView(R.id.exemptingCheckField)
        CheckBox exemptingCheckField;
    */
    @BindView(R.id.Phone1InputField)
    ExtendedMaterialEditText phone1InputField;


    @BindView(R.id.longitude)
    ExtendedMaterialEditText longitudeInput;


    @BindView(R.id.type_client_spinner)
    Spinner typeClientSpinner;


    @BindView(R.id.latitude)
    ExtendedMaterialEditText latitudeInput;

    @BindView(R.id.Phone2InputField)
    ExtendedMaterialEditText phone2InputField;

    @BindView(R.id.gouvernoratInputField)
    ExtendedMaterialEditText gouvernoratInputField;
    @BindView(R.id.delegationInputField)
    ExtendedMaterialEditText delegationInputField;


    MapView mapView;
    MapBoxView mapBoxView;

    Client client;

    private final Pattern telRegex = Pattern.compile(RegexTemplate.TELEPHONE);
    private final Pattern emailRegex = Pattern.compile("^([a-zA-Z0-9_\\-\\.]+)@([a-zA-Z0-9_\\-\\.]+)\\.([a-zA-Z]{2,5})$");
    Date date = new Date();
    Activity context = this;

    Unbinder unbinder;

    String from ="";

    private Boolean isUpdate = false;
    private Client CltToUpdate ;
    @Override
    protected void onCreate(Bundle savedInstanceState) {

        super.onCreate(savedInstanceState);
        unbinder = ButterKnife.bind(this);

        setSupportActionBar(toolbar);
        //set the back arrow in the toolbar
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);


        Bundle extras = getIntent().getExtras();

        isUpdate = getIntent().getBooleanExtra(Globals.UPDATE_CLIENT, false);
        CltToUpdate = (Client) getIntent().getSerializableExtra(Globals.CLIENT_TO_UPDATE);


        if(isUpdate){
            getSupportActionBar().setTitle(R.string.modify_client);

            setfillViewWithCltInfoToUpdate(CltToUpdate);

            if(CltToUpdate.isSync)
            taxRegistrationNumberInputField.setEnabled(false);
            else{
                if(CltToUpdate.getcLICodeM().equals(CltToUpdate.getcLICode())) taxRegistrationNumberInputField.setEnabled(true);
                else taxRegistrationNumberInputField.setEnabled(false);
            }
        }
        else {
            taxRegistrationNumberInputField.setEnabled(true);
            if (extras != null) {
                from = extras.getString("from");
                //The key argument here must match that used in the other activity

                if(from.equals("prospect")) getSupportActionBar().setTitle(R.string.add_Prospect);
                else  getSupportActionBar().setTitle(R.string.add_client);
            }
            else  getSupportActionBar().setTitle(R.string.add_client);

        }


// Create an ArrayAdapter using the string array and a default spinner layout.
        ArrayAdapter<CharSequence> adapter = ArrayAdapter.createFromResource(
                this,
                R.array.clients_type_list,
                android.R.layout.simple_spinner_item
        );
// Specify the layout to use when the list of choices appears.
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
// Apply the adapter to the spinner.
        typeClientSpinner.setAdapter(adapter);

        typeClientSpinner.setOnItemSelectedListener(this);
        //    typeClientSpinner.setOnItemSelectedListener();

       clientType = "Permanant";



        client = new Client();



        mAwesomeValidation.addValidation(this, R.id.EntitledInputField, RegexTemplate.NOT_EMPTY, R.string.empty_field);
        mAwesomeValidation.addValidation(this, R.id.AddressInputField, RegexTemplate.NOT_EMPTY, R.string.empty_field);

        if(!from.equals("prospect"))
            mAwesomeValidation.addValidation(this, R.id.TaxRegistrationNumberInputField, s -> {
            return !s.isEmpty();
        }, R.string.field_error);

        mAwesomeValidation.addValidation(this, R.id.phone1InputField, s -> {
            if (!s.isEmpty()) {
                return telRegex.matcher(s).find();
            }
            return true;
        }, R.string.field_error);

        mAwesomeValidation.addValidation(this, R.id.phone2InputField, s -> {
            if (!s.isEmpty()) {
                return telRegex.matcher(s).find();
            }
            return true;
        }, R.string.field_error);

        mAwesomeValidation.addValidation(this, R.id.EmailInputField, s -> {
            if (!s.isEmpty()) {
                return emailRegex.matcher(s).find();
            }
            return true;
        }, R.string.field_error);





        mapView = findViewById(R.id.clientmapView);

        mapBoxView = new MapBoxView(context, context, mapView, this::setLatLng);

        mapBoxView.setupMap(client);
        mapView.setEnabled(false);

        MaterialButton updBtn = findViewById(R.id.updButton);

         updBtn.setOnClickListener(view -> mapBoxView.requestLocation(client));


        if(from.equals("prospect")){
            creditCheckField.setVisibility(View.GONE);
            stampCheckField.setVisibility(View.GONE);
        }
    }

    String checkIfNull(String string){
        String str="";
          if(string== null) str="";
        else if(string.isEmpty()) str ="";  else str=string;
        return str;
    }
    private void setLatLng(LatLng point) {
        client.setLongitude(point.getLongitude());
        client.setLatitude(point.getLatitude());
        longitudeInput.setText(String.valueOf(client.getLongitude()));
        latitudeInput.setText(String.valueOf(client.getLatitude()));


        setAdressFroGps(point.getLatitude(), point.getLongitude());
    }


    void  setfillViewWithCltInfoToUpdate(Client client){
        taxRegistrationNumberInputField.setText(client.CLI_MatFisc);
        entitledInputField.setText(client.getcLINomPren());

        addressInputField.setText(client.getcLIAdresse());
        emailInputField.setText(client.getcLIMail());
        professionInputField.setText(client.getcLtProfession());
        companyInputField.setText(client.getcLISociete());
        stampCheckField.setChecked(client.getcLITimbre() == 1);
        creditCheckField.setChecked(client.getCliisCredit() == 1);
        phone1InputField.setText(client.getcLITel1());
        phone2InputField.setText(client.getcLITel2());
        longitudeInput.setText(String.format("%s", client.getLongitude()));
        latitudeInput.setText(String.format("%s", client.getLatitude()));

        gouvernoratInputField.setText(client.getCltGouvernorat());
        delegationInputField.setText(client.getCltVille());

    }

    void setAdressFroGps(double latitude, double longitude){

        Geocoder geocoder;
        List<Address> addresses;
        geocoder = new Geocoder(this, Locale.getDefault());

        try {
            addresses = geocoder.getFromLocation(latitude, longitude, 1); // Here 1 represent max location result to returned, by documents it recommended 1 to 5

            String address = checkIfNull(addresses.get(0).getAddressLine(0)); // If any additional address line present than only, check with max available address lines by getMaxAddressLineIndex()
            String city = checkIfNull(addresses.get(0).getLocality());
            String state = checkIfNull(addresses.get(0).getAdminArea());
            String country = checkIfNull(addresses.get(0).getCountryName());
            String postalCode = checkIfNull(addresses.get(0).getPostalCode());
            String knownName = checkIfNull(addresses.get(0).getFeatureName()); // Only if available else return NULL



            addressInputField.setText(knownName+", " + city +" "+state+", " + country);
            delegationInputField.setText(city);
            gouvernoratInputField.setText(state);
        } catch (IOException e) {
            Toasty.info(context, "Il faut étre connecté pour avoir l'adresse").show();
            addressInputField.setText("");
            e.printStackTrace();
        }

    }

    /**
     * detect activity resume after stop
     */
    @Override
    protected void onResume() {
        super.onResume();

    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == PLACE_PICKER_REQUEST) {
            if (resultCode == RESULT_OK) {
                Place place = PlacePicker.getPlace(data, this);
                longitude = place.getLatLng().longitude;
                latitude = place.getLatLng().latitude;
                longitudeInput.setText(String.format("%s", longitude));
                latitudeInput.setText(String.format("%s", latitude));

                setAdressFroGps(latitude, longitude);
              /*  if (latitude != 0 && longitude != 0) {
                    String imageURL = "https://maps.googleapis.com/maps/api/staticmap?center=" + latitude + "," + longitude + "&zoom=16&size=865x512&maptype=mapnik&markers=color:red%7C%7C" + latitude + "," + longitude;
                }*/
            }
        }
    }

    /**
     * link the activity with its xml file
     */
    @Override
    protected int setContentView() {
        return R.layout.activity_add_client;
    }

    /**
     * controle the toolbar menu buttons
     */
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.add_client_menu, menu);
        MenuItem save = menu.findItem(R.id.save);
        save.setIcon(new IconicsDrawable(context).icon(GoogleMaterial.Icon.gmd_save)
                .color(ResourcesCompat.getColor(getResources(), R.color.material_white, null)).sizeDp(24));
        //  save.setEnabled(false);
        save.setOnMenuItemClickListener(item -> {
            Toasty.info(context, client.cLICode).show();
            if(clientType== null) {
                Toasty.error(context, "Select Client Type").show();


            }



            save.setEnabled(false);
            if (mAwesomeValidation.validate()) {
                onValidationSucceeded();
            }

            try {
                Thread.sleep(300);
                save.setEnabled(true);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            return false;
        });
        return super.onCreateOptionsMenu(menu);
    }


    @Override
    public void onBackPressed() {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setTitle(getString(R.string.message));
        builder.setMessage(getString(R.string.did_you_like_quit));
        builder.setPositiveButton(getString(R.string.yes), (dialog, which) -> finish());
        builder.setNegativeButton(getString(R.string.no), (dialog, which) -> {
            dialog.dismiss();
        });

        AlertDialog alert = builder.create();
        alert.show();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem menuItem) {
        switch (menuItem.getItemId()) {
            case android.R.id.home:
                onBackPressed();
                break;
        }

        return super.onOptionsItemSelected(menuItem);
    }

    /**
     * get in inserted client object
     */
    void getClient() {
        if (App.database.prefixeDAO().getOneById("client") == null) {
            Toasty.warning(context, getString(R.string.there_is_no_client_prefix_try_update_data_base)).show();
            return;
        }
     /*   client.setcLICode(StringUtils.PrefixFormatter(6,
                App.database.prefixeDAO().getOneById("client").getpREPrefixe() + App.prefUtils.getUserId(),
                App.database.clientDAO().getNewCode(App.database.prefixeDAO().getOneById("client").getpREPrefixe() + App.prefUtils.getUserId()).toString()));
*/
        String cltCodeM ="";
        String cltCode ="";
        if(!isUpdate){
            cltCodeM = cltCode =   Utils.generateClientCode(App.prefUtils.getUserStationId(), client.cLICode);
        }
        else {
            cltCodeM = CltToUpdate.getcLICodeM();
            cltCode = CltToUpdate.getcLICode();
        }

       client.setcLICode(cltCode);
        if (ObjectUtils.isEmpty(client.cLICodeM)) {
            client.cLICodeM =cltCodeM;
        }
        client.cLIAdresse = (addressInputField.getText().toString());
        client.cltGouvernorat = (gouvernoratInputField.getText().toString());
        client.cltVille = (delegationInputField.getText().toString());

        client.setcLITel1(phone1InputField.getText().toString());
        client.setcLITel2(phone2InputField.getText().toString());
        //client.setLongitude(longitude);
        //client.setLatitude(latitude);
        client.setcLIMail(emailInputField.getText().toString());
        client.setcLITimbre(stampCheckField.isChecked() ? Globals.Boolean.TRUE.getValue() : Globals.Boolean.FALSE.getValue());


        client.setCliisCredit(creditCheckField.isChecked() ? Globals.Boolean.TRUE.getValue() : Globals.Boolean.FALSE.getValue());

        client.cLINomPren = (entitledInputField.getText().toString());
        client.setCLI_MatFisc(taxRegistrationNumberInputField.getText().toString());
        client.cLIDateCre = (DateUtils.dateToStr(date, "yyyy-MM-dd HH:mm:ss"));
      // client.setcLISociete(companyInputField.getText().toString());
        client.setcLtNomMagasin(companyInputField.getText().toString());
        client.cLIFodec = ("0");
        if(from.equals("prospect")) client.setcLIType("Prospect");
       // else client.setcLIType(/*"Permanant"*/App.prefUtils.getUserType());
        else client.setcLIType(clientType);
        client.setCliExeno("False");
        client.setCliDDm(DateUtils.dateToStr(date, "MM/dd/yyyy HH:mm:ss"));
       // client.setCltEpoux(professionInputField.getText().toString());
        client.setcLtProfession(professionInputField.getText().toString());

        if(from.equals("prospect")) client.setCltInfo1("Prospect");
            else client.setCltInfo1(App.prefUtils.getUserType());
        client.setCliUser(App.prefUtils.getUserId());

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        unbinder.unbind();
    }


    public void onValidationSucceeded() {
        getClient();
        if (client == null) {
            return;
        }
        saveData(client);
    }

    void saveData(Client client) {
        client.setSync(false);
        client.setCliEtat("Active");
        client.setStatus(Globals.ITEM_STATUS.INSERTED.getStatus());
        client.setCLIStation(App.prefUtils.getUserStationId());
        client.setExercice(App.prefUtils.getExercice());
       // client.setcLIExoValable(App.prefUtils.getUserId());
        client.setcLIExoValable(DateUtils.dateToStr(date, "MM/dd/yyyy HH:mm:ss"));



        App.database.clientDAO().insert(client);
        finish();
        /*if (PrefUtils.isAutoSync() && NetworkUtils.isConnected()) {
            List<Client> clients = new ArrayList<>();
            clients.add(client);
            ProgressDialog mprogress = new ProgressDialog(this);
            sychronize(clients, new IDataSyncListener() {
                @Override
                public void onLoading(String msg) {
                    runOnUiThread(() -> {
                        mprogress.show();
                        mprogress.setMessage(msg);
                        mprogress.setCancelable(false);
                    });
                }

                @Override
                public void onFailure(String msg, DataSyncStatus syncStatus) {
                    runOnUiThread(() -> {
                        mprogress.dismiss();
                        mprogress.setCancelable(false);
                    });
                }

                @Override
                public void onComplete() {
                    runOnUiThread(() -> {
                        if (mprogress.isShowing()) {
                            mprogress.dismiss();
                            mprogress.setCancelable(false);
                        }
                    });
                }

                @Override
                public void onComplete(Object object) {
                    try {
                        ((List<Client>) object).get(0);
                        Snackbar.make(getCurrentFocus(), R.string.added_succesfully, Snackbar.LENGTH_LONG)
                                .show();
                        new Handler().postDelayed(() -> {
                            setResult(RESULT_OK, getIntent().putExtra(CLIENT_INTENT_ID_KEY, ((List<Client>) object).get(0)));
                            finish();
                        }, 1000);
                    } catch (Exception e) {
                        Toasty.error(context, e.getMessage());
                    }

                }
            });
        } else {
            Snackbar.make(getCurrentFocus(), R.string.added_succesfully, Snackbar.LENGTH_LONG)
                    .show();
            new Handler().postDelayed(() -> {
                Toasty.info(context, client.cLICode).show();
                setResult(RESULT_OK, getIntent().putExtra(CLIENT_INTENT_ID_KEY, client));
                finish();
            }, 1000);
        }

         */
    }

    void sychronize(List<Client> oldClients, IDataSyncListener listener) {
        ClientDataManager.getInstance().addBatchClient(new GenericObject(new PrefUtils(context).getBaseConfig(), App.database.clientDAO().getByQuery()), new RemoteCallback<List<Client>>
                (context, false) {
            @Override
            public void onSuccess(List<Client> response) {
                if (response != null) {
                    for (Client client : response) {
                        client.setSync(true);
                        client.setCliEtat("Active");
                        client.setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());
                        App.database.clientDAO().insert(client);
                    }
                    for (int i = 0; i < response.size(); i++) {
                        if (i < oldClients.size())
                            App.database.clientDAO().deleteById(oldClients.get(i).cLICode);
                    }

                    if (App.database.appPropertiesDAO().getOne() != null)
                        App.database.appPropertiesDAO().insert(new AppProperties(App.database.appPropertiesDAO().getOne().getUpdated_at(), new Date().getTime()));
                    if (listener != null) {
                        listener.onComplete(response);
                    }
                }
            }

            @Override
            public void onUnauthorized() {
                if (listener != null) {
                    listener.onFailure("onUnauthorized syncClients...", DataSyncStatus.Failure);
                }


            }

            @Override
            public void onFailed(Throwable throwable) {
                listener.onFailure(throwable.getMessage(), DataSyncStatus.Failure);

            }
        });


    }





    @Override
    public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {

        final String[] tempArray = getResources().getStringArray(R.array.clients_type_list);
        final List<String> stringList = Arrays.asList(tempArray);

        clientType = stringList.get(position);



    }

    @Override
    public void onNothingSelected(AdapterView<?> parent) {
        final String[] tempArray = getResources().getStringArray(R.array.clients_type_list);
        final List<String> stringList = Arrays.asList(tempArray);
        clientType = stringList.get(0);
    }


}
