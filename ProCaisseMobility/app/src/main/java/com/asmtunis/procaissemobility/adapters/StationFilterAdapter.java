package com.asmtunis.procaissemobility.adapters;

import android.content.Context;

import androidx.core.content.ContextCompat;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.data.models.Station;
import com.yalantis.filter.adapter.FilterAdapter;
import com.yalantis.filter.widget.FilterItem;

import org.jetbrains.annotations.NotNull;

import java.util.List;

public class StationFilterAdapter extends FilterAdapter<Station> {
    Context context;
    public int[] mColors;
    public List<Station> stations;

    public StationFilterAdapter(Context context, List<Station> list) {
        super(list);
        this.context = context;
        mColors = context.getResources().getIntArray(R.array.colors);
        this.stations = list;
    }

    @NotNull
    @Override
    public FilterItem createView(int i, Station station) {
        FilterItem filterItem = new FilterItem(context);
        filterItem.setStrokeColor(mColors[0]);
        filterItem.setTextColor(mColors[0]);
        filterItem.setCornerRadius(14);
        filterItem.setCheckedTextColor(ContextCompat.getColor(context, android.R.color.white));
        filterItem.setColor(ContextCompat.getColor(context, android.R.color.white));
        filterItem.setCheckedColor(mColors[i < mColors.length ? i : mColors.length - 1]);
        filterItem.setText(station.getText());
        filterItem.deselect();
        station.setColorCode(mColors[i < mColors.length ? i : mColors.length - 1]);
        stations.get(i).setColorCode(mColors[i < mColors.length ? i : mColors.length - 1]);
        return filterItem;
    }

    public int getColor(int i) {
        return mColors[i < mColors.length ? i : mColors.length - 1];
    }


    public Station getStationByCode(String code) {
        int i = 0;
        if (stations == null || stations.isEmpty())
            return null;
        for (Station station : stations) {
            if (station.getSTATCode().equals(code)) {
                station.setColorCode(mColors[i < mColors.length ? i : mColors.length - 1]);
                return station;
            }
            i++;
        }
        return null;
    }

    public int getColorByCode(String code) {
        int i = 0;
        if (stations == null || stations.isEmpty())
            return 0;
        for (Station station : stations) {
            if (station.getSTATCode().equals(code)) {
                station.setColorCode(mColors[i < mColors.length ? i : mColors.length - 1]);
                return mColors[i < mColors.length ? i : mColors.length - 1];
            }
            i++;
        }
        return 0;
    }
}
