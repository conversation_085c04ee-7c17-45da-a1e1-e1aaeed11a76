package com.asmtunis.procaissemobility.data;

import android.content.Context;

import androidx.room.Room;
import androidx.room.RoomDatabase;

import com.asmtunis.procaissemobility.data.dao.AppPropertiesDAO;
import com.asmtunis.procaissemobility.data.dao.ArticleCodeBarDAO;
import com.asmtunis.procaissemobility.data.dao.ArticleDAO;
import com.asmtunis.procaissemobility.data.dao.AuthorizationDAO;
import com.asmtunis.procaissemobility.data.dao.BackUpLicenseUrlDAO;
import com.asmtunis.procaissemobility.data.dao.BanqueDAO;
import com.asmtunis.procaissemobility.data.dao.BaseConfigDAO;
import com.asmtunis.procaissemobility.data.dao.BonCommandeDAO;
import com.asmtunis.procaissemobility.data.dao.BonRetourDAO;
import com.asmtunis.procaissemobility.data.dao.CaisseDAO;
import com.asmtunis.procaissemobility.data.dao.CarteRestoDAO;
import com.asmtunis.procaissemobility.data.dao.ChequeCaisseDAO;
import com.asmtunis.procaissemobility.data.dao.ClientArticlePrixDAO;
import com.asmtunis.procaissemobility.data.dao.ClientDAO;
import com.asmtunis.procaissemobility.data.dao.ClientImmoDAO;
import com.asmtunis.procaissemobility.data.dao.DNFamilleVisiteDAO;
import com.asmtunis.procaissemobility.data.dao.DNLigneVisiteDAO;
import com.asmtunis.procaissemobility.data.dao.DNSuperficieDAO;
import com.asmtunis.procaissemobility.data.dao.DNTypePVenteDAO;
import com.asmtunis.procaissemobility.data.dao.DNTypeServicesDAO;
import com.asmtunis.procaissemobility.data.dao.DNVisitesDAO;
import com.asmtunis.procaissemobility.data.dao.DepenceTypeDAO;
import com.asmtunis.procaissemobility.data.dao.DepenceCaisseDAO;
import com.asmtunis.procaissemobility.data.dao.DeviseDAO;
import com.asmtunis.procaissemobility.data.dao.EtablisementDAO;
import com.asmtunis.procaissemobility.data.dao.EtatOrdreMissionDAO;
import com.asmtunis.procaissemobility.data.dao.FactureDAO;
import com.asmtunis.procaissemobility.data.dao.FamilleDAO;
import com.asmtunis.procaissemobility.data.dao.FournisseurDAO;
import com.asmtunis.procaissemobility.data.dao.LigneBonCommandeDAO;
import com.asmtunis.procaissemobility.data.dao.LigneBonRetourDAO;
import com.asmtunis.procaissemobility.data.dao.LigneOrdreMissionDAO;
import com.asmtunis.procaissemobility.data.dao.LigneTicketDAO;
import com.asmtunis.procaissemobility.data.dao.MarqueDAO;
import com.asmtunis.procaissemobility.data.dao.OrdreMissionDAO;
import com.asmtunis.procaissemobility.data.dao.OrdreWithLinesDAO;
import com.asmtunis.procaissemobility.data.dao.PrefixeDAO;
import com.asmtunis.procaissemobility.data.dao.PricePerStationDAO;
import com.asmtunis.procaissemobility.data.dao.ReclamationDAO;
import com.asmtunis.procaissemobility.data.dao.ReglementCaisseDAO;
import com.asmtunis.procaissemobility.data.dao.SessionCaisseDAO;
import com.asmtunis.procaissemobility.data.dao.StationDAO;
import com.asmtunis.procaissemobility.data.dao.StationStockDAO;
import com.asmtunis.procaissemobility.data.dao.StatisticsDAO;
import com.asmtunis.procaissemobility.data.dao.TicketDAO;
import com.asmtunis.procaissemobility.data.dao.TimbreDAO;
import com.asmtunis.procaissemobility.data.dao.TraiteCaisseDAO;
import com.asmtunis.procaissemobility.data.dao.TrakingAlarmDAO;
import com.asmtunis.procaissemobility.data.dao.TrakingDAO;
import com.asmtunis.procaissemobility.data.dao.VCAutreDAO;
import com.asmtunis.procaissemobility.data.dao.VCImageDAO;
import com.asmtunis.procaissemobility.data.dao.VCListeConcurrentDAO;
import com.asmtunis.procaissemobility.data.dao.VCNewProductDAO;
import com.asmtunis.procaissemobility.data.dao.VCPricesDAO;
import com.asmtunis.procaissemobility.data.dao.VCPromosDAO;
import com.asmtunis.procaissemobility.data.dao.VCTypeCommunicationDAO;
import com.asmtunis.procaissemobility.data.dao.VilleDAO;
import com.asmtunis.procaissemobility.data.models.AppProperties;
import com.asmtunis.procaissemobility.data.models.Article;
import com.asmtunis.procaissemobility.data.models.ArticleCodeBar;
import com.asmtunis.procaissemobility.data.models.Authorization;
import com.asmtunis.procaissemobility.data.models.Banque;
import com.asmtunis.procaissemobility.data.models.BonCommande;
import com.asmtunis.procaissemobility.data.models.BonRetour;
import com.asmtunis.procaissemobility.data.models.Caisse;
import com.asmtunis.procaissemobility.data.models.CarteResto;
import com.asmtunis.procaissemobility.data.models.ChequeCaisse;
import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.data.models.ClientArticlePrix;
import com.asmtunis.procaissemobility.data.models.Facture;
import com.asmtunis.procaissemobility.data.models.Immobilisation;
import com.asmtunis.procaissemobility.data.models.Connexion;
import com.asmtunis.procaissemobility.data.models.DNFamille;
import com.asmtunis.procaissemobility.data.models.DNSuperficie;
import com.asmtunis.procaissemobility.data.models.DNTypePVente;
import com.asmtunis.procaissemobility.data.models.DNTypeServices;
import com.asmtunis.procaissemobility.data.models.DNVIsite;
import com.asmtunis.procaissemobility.data.models.DN_LigneVisite;
import com.asmtunis.procaissemobility.data.models.DepenceType;
import com.asmtunis.procaissemobility.data.models.DepenceCaisse;
import com.asmtunis.procaissemobility.data.models.Devise;
import com.asmtunis.procaissemobility.data.models.Etablisement;
import com.asmtunis.procaissemobility.data.models.EtatOrdreMission;
import com.asmtunis.procaissemobility.data.models.Famille;
import com.asmtunis.procaissemobility.data.models.Fournisseur;
import com.asmtunis.procaissemobility.data.models.LicenseResponseItem;
import com.asmtunis.procaissemobility.data.models.LigneBonCommande;
import com.asmtunis.procaissemobility.data.models.LigneBonRetour;
import com.asmtunis.procaissemobility.data.models.LigneOrdreMission;
import com.asmtunis.procaissemobility.data.models.LigneTicket;
import com.asmtunis.procaissemobility.data.models.Marque;
import com.asmtunis.procaissemobility.data.models.OrdreMission;
import com.asmtunis.procaissemobility.data.models.Prefixe;
import com.asmtunis.procaissemobility.data.models.PricePerStation;
import com.asmtunis.procaissemobility.data.models.Reclamation;
import com.asmtunis.procaissemobility.data.models.ReglementCaisse;
import com.asmtunis.procaissemobility.data.models.SessionCaisse;
import com.asmtunis.procaissemobility.data.models.Station;
import com.asmtunis.procaissemobility.data.models.StationStock;
import com.asmtunis.procaissemobility.data.models.Ticket;
import com.asmtunis.procaissemobility.data.models.Timbre;
import com.asmtunis.procaissemobility.data.models.TraiteCaisse;
import com.asmtunis.procaissemobility.data.models.Traking;
import com.asmtunis.procaissemobility.data.models.TrakingAlarm;
import com.asmtunis.procaissemobility.data.models.VCAutre;
import com.asmtunis.procaissemobility.data.models.VCImage;
import com.asmtunis.procaissemobility.data.models.VCListeConcurrent;
import com.asmtunis.procaissemobility.data.models.VCNewProduct;
import com.asmtunis.procaissemobility.data.models.VCPrix;
import com.asmtunis.procaissemobility.data.models.VCPromo;
import com.asmtunis.procaissemobility.data.models.VCTypeCommunication;
import com.asmtunis.procaissemobility.data.models.Ville;

/**
 * Created by PC on 11/18/2017.
 */

@androidx.room.Database(entities =
        {AppProperties.class, PricePerStation.class, Devise.class, Prefixe.class, Article.class, Banque.class, Caisse.class, CarteResto.class,
                ChequeCaisse.class, Client.class, LigneTicket.class, ReglementCaisse.class, SessionCaisse.class, Ticket.class, TraiteCaisse.class,
                Reclamation.class, StationStock.class, Station.class, Fournisseur.class, Etablisement.class, BonRetour.class, BonCommande.class,
                LigneBonCommande.class, LigneBonRetour.class, Traking.class, OrdreMission.class, EtatOrdreMission.class, LigneOrdreMission.class,
                TrakingAlarm.class, Timbre.class, ClientArticlePrix.class, ArticleCodeBar.class, Ville.class, Famille.class, Authorization.class,
                VCNewProduct.class, VCPromo.class, VCPrix.class, VCAutre.class, VCListeConcurrent.class, VCTypeCommunication.class, VCImage.class,
                DNTypeServices.class, DNSuperficie.class, DNTypePVente.class, DNVIsite.class, DN_LigneVisite.class, DNFamille.class, Connexion.class,
                LicenseResponseItem.class, DepenceType.class, DepenceCaisse.class, Marque.class, Immobilisation.class, Facture.class
        },

        version = 234, exportSchema = false)
public abstract class Database extends RoomDatabase {
    public abstract FactureDAO factureDAO();
    public abstract ArticleDAO articleDAO();
    public abstract MarqueDAO marqueDAO();
    public abstract BanqueDAO banqueDAO();

    public abstract FournisseurDAO fournisseurDAO();

    public abstract CaisseDAO caisseDAO();

    public abstract CarteRestoDAO carteRestoDAO();

    public abstract ChequeCaisseDAO chequeCaisseDAO();

    public abstract ClientDAO clientDAO();

    public abstract ClientImmoDAO clientImooDAO();

    public abstract LigneTicketDAO ligneTicketDAO();

    public abstract ReglementCaisseDAO reglementCaisseDAO();

    public abstract TicketDAO ticketDAO();

    public abstract TraiteCaisseDAO traiteCaisseDAO();
    public abstract PrefixeDAO prefixeDAO();

    public abstract DeviseDAO deviseDAO();

    public abstract AppPropertiesDAO appPropertiesDAO();

    public abstract StatisticsDAO statisticsDAO();

    public abstract ReclamationDAO reclamationDAO();

    public abstract StationStockDAO stationStockDAO();

    public abstract StationDAO stationDAO();

    public abstract EtablisementDAO etablisementDAO();

    public abstract BonRetourDAO bonRetourDAO();

    public abstract BaseConfigDAO baseConfigDAO();

    public abstract BackUpLicenseUrlDAO backUpLicenseUrlDAO();


    public abstract BonCommandeDAO bonCommandeDAO();

    public abstract LigneBonCommandeDAO ligneBonCommandeDAO();

    public abstract LigneBonRetourDAO ligneBonRetourDAO();

    public abstract PricePerStationDAO pricePerStationDAO();

    public abstract TrakingDAO trakingDAO();

    public abstract OrdreMissionDAO ordreMissionDAO();

    public abstract LigneOrdreMissionDAO ligneOrdreMissionDAO();

    public abstract OrdreWithLinesDAO ordreWithLinesDAO();

    public abstract EtatOrdreMissionDAO etatOrdreMissionDAO();

    public abstract TrakingAlarmDAO trakingAlarmDAO();

    public abstract TimbreDAO timbreDAO();

    public abstract ClientArticlePrixDAO clientArticlePrixDAO();

    public abstract ArticleCodeBarDAO articleCodeBarDAO();

    public abstract VilleDAO villeDAO();

    public abstract SessionCaisseDAO sessionCaisseDAO();

    public abstract FamilleDAO familleDAO();

    public abstract AuthorizationDAO authorizationDAO();

    public abstract VCNewProductDAO vcNewProductDAO();

    public abstract VCPromosDAO vcPromosDAO();

    public abstract VCPricesDAO vcPricesDAO();

    public abstract VCAutreDAO vcAutreDAO();

    public abstract DNTypeServicesDAO dnTypeServicesDAO();
    public abstract DNSuperficieDAO dnSuperficieDAO();
    public abstract DNTypePVenteDAO dnTypePVenteSDAO();
    public abstract DNVisitesDAO dnVisitesDAO();
    public abstract DNLigneVisiteDAO dnLigneVisiteDAO();

    public  abstract DNFamilleVisiteDAO dnFamilleVisite();

    public abstract VCTypeCommunicationDAO vcTypeCommunicationDAO();

    public abstract VCListeConcurrentDAO vcListeConcurrentDAO();

    public abstract VCImageDAO vcImageDAO();

    public abstract DepenceTypeDAO depenceTypeDAO();

    public abstract DepenceCaisseDAO depenceCaisseDAO();

    private static Database INSTANCE;

    public static Database getInstance(Context context) {
        if (INSTANCE == null) {
            INSTANCE = Room.databaseBuilder(context.getApplicationContext(), Database.class, "ProCaisseMobility")
                    .allowMainThreadQueries().fallbackToDestructiveMigration()
                    .build();

        }
        return INSTANCE;
    }

    public static void destroyInstance() {
        INSTANCE = null;
    }

}
