package com.asmtunis.procaissemobility.data.network.services;


import license.model.Licence;
import retrofit2.Call;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;
import retrofit2.http.Path;

/**
 * Created by Achraf on 28/09/2017.
 */

public interface ActivationService {

    @POST("checkLicenseV2")
    @FormUrlEncoded
    Call<Licence> CheckActivation (@Field("iddevice") String deviceId, @Field("produit") String product);


    @POST("{id}")
    @FormUrlEncoded
    Call<Licence> CheckActivationBackUp (@Path("id") String postId, @Field("iddevice") String deviceId, @Field("produit") String product);

}
