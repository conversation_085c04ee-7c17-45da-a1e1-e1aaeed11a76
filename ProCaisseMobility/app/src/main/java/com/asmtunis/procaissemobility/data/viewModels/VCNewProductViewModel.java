package com.asmtunis.procaissemobility.data.viewModels;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelProviders;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.dao.VCNewProductDAO;
import com.asmtunis.procaissemobility.data.models.VCNewProduct;

import java.util.List;

public class VCNewProductViewModel extends ViewModel {
    public VCNewProductDAO vcNewProductDAO;
    private static VCNewProductViewModel instance;

    public static VCNewProductViewModel getInstance(Fragment fragment){
        if(instance==null){
            instance = new ViewModelProvider(fragment).get(VCNewProductViewModel.class);
            instance.vcNewProductDAO = App.database.vcNewProductDAO();
        }
        return instance;
    }


    /*  public static DNVisiteViewModel getInstance(Fragment activity) {
        if (instance == null)
            instance = ViewModelProviders.of(activity).get(DNVisiteViewModel.class);
        instance.dnVisiteDAO = App.database.dnVisitesDAO();
        return instance;
    }*/

    public static VCNewProductViewModel getInstance(FragmentActivity activity) {
        if (instance == null)
            instance = new ViewModelProvider(activity).get(VCNewProductViewModel.class);
        instance.vcNewProductDAO = App.database.vcNewProductDAO();
        return instance;
    }
    public LiveData<List<VCNewProduct>> getAllVCNewProducts(){
        return vcNewProductDAO.getAll();
    }

    public LiveData<Integer> getNoSyncCountMubtale(){
        return vcNewProductDAO.getNoSyncCountMubtale();
    }


    public LiveData<Integer> getNoSyncCountMubtaleToDelete(){
        return vcNewProductDAO.getCountNoSyncedToDeleteMubtale();
    }


}
