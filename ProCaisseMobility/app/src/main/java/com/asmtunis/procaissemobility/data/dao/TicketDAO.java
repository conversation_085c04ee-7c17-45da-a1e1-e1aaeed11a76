package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.Ticket;

import java.util.List;

/**
 * Created by PC on 11/18/2017.
 */
@Dao
public interface TicketDAO {

    @Query("SELECT * FROM Ticket order by strftime('%Y-%m-%d %H-%M',TIK_DateHeureTicket) desc")
    List<Ticket> getAll();

    @Query("SELECT * FROM Ticket where TIK_is_Contrat = 0 and TIK_Num_Contrat IS NULL and TIK_IdSCaisse= :session and TIK_station= :station order by strftime('%Y-%m-%d %H-%M-%S',TIK_DateHeureTicket) desc")
    LiveData<List<Ticket>> getAllTicketBySessionMutable(String session, String station);

    @Query("SELECT count(*) FROM Ticket where TIK_is_Contrat = 0 and TIK_Num_Contrat IS NULL and TIK_IdSCaisse= :session and TIK_station= :station")
    Integer getAllCountBySession(String session, String station);

    @Query("SELECT count(*) FROM Ticket where TIK_is_Contrat = 0 and TIK_Num_Contrat IS NULL and TIK_IdSCaisse= :session and TIK_station= :station")
    LiveData<Integer> getAllCountBySessionMutable(String session, String station);

    @Query("SELECT * FROM Ticket where TIK_IdSCaisse=:session and (TIK_is_Contrat = 1 and TIK_Num_Contrat IS NOT NULL)  order by strftime('%Y-%m-%d %H-%M-%S',TIK_DateHeureTicket) desc")
    List<Ticket> getAllOrderBySession(String session);

    @Query("SELECT * FROM Ticket WHERE TIK_NumTicket = :num ")
    Ticket getOneByCode(int num);
    @Query("SELECT TIK_NumTicket FROM Ticket WHERE TIK_NumTicket_M = :tik_num_ticket_m ")
    int getOneByMCode(String tik_num_ticket_m);

    @Query("SELECT * FROM Ticket WHERE TIK_NumTicket = :num and TIK_Exerc=:exercie")
    Ticket getOneByCode(int num,String exercie);

    @Query("SELECT * FROM Ticket WHERE TIK_NumTicket_M = :num and TIK_Exerc=:exercie")
    Ticket getOneByCodeM(String num,String exercie);

    @Query("SELECT * FROM Ticket WHERE TIK_NumTicket = :num ")
    LiveData<Ticket> getOneByCodeMutable(String num);

    //@Query("SELECT * FROM Ticket WHERE  isSync=0 and (Status='INSERTED'  or Status='UPDATED') order by strftime('%Y-%m-%d %H-%M',TIK_DateHeureTicket) desc")

    @Query("SELECT * FROM Ticket WHERE  isSync=0 and (Status='INSERTED'  or Status='UPDATED') order by TIK_NumTicket asc")
    List<Ticket> getNonSync();

    @Query("SELECT * FROM Ticket WHERE  TIK_CodClt=:codeClient and TIK_Etat='Credit' and TIK_Annuler=0 and TIK_Exerc=:exercice order by strftime('%Y-%m-%d %H-%M',TIK_DateHeureTicket) desc")
    List<Ticket> getClientCredit(String codeClient,String exercice);

    @Query("SELECT MAX(TIK_NumTicket) + 1 FROM Ticket where TIK_IdCarnet= :carnet")
    int getNewNumTicket(String carnet);

    @Query("SELECT count(*) FROM Ticket where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    int getNoSyncCount();

    @Query("SELECT count(*) FROM Ticket where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    LiveData<Integer> getNoSyncCountMutable();

    @Query("SELECT count(*) FROM Ticket where isSync=0 and  (Status='INSERTED' or Status='UPDATED')")
    Integer getNoSyncCountNonMutable();

    @Query("SELECT count(*) FROM Ticket where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    LiveData<Integer> getCountMutable();


    @Query("SELECT * FROM Ticket LIMIT 1")
    Ticket getOne();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(Ticket item);


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<Ticket> items);

    @Query("UPDATE Ticket SET TIK_CodClt = :code_client where TIK_CodClt = :oldCodeClient")
    void updateCodeClient(String code_client, String oldCodeClient);



    @Query("UPDATE Ticket SET TIK_NumTicket=:tik_num_ticket where TIK_NumTicket_M = :tik_num_ticket_M")
    void updateBLNumber( int tik_num_ticket,String tik_num_ticket_M);

    @Query("UPDATE Ticket SET TIK_NumeroBL = :tikNumBl,TIK_NumTicket=:tikNumTicket where TIK_NumTicket_M = :tikNumTicketM")
    void updateTicketNumber(String tikNumBl, int tikNumTicket,String tikNumTicketM);

   // @Query("DELETE FROM Ticket where Status='SELECTED'")
  //  void deleteAll();

    @Query("DELETE FROM Ticket")
    void deleteAll();

    @Query("DELETE FROM Ticket where TIK_NumTicket=:code and TIK_Exerc=:exercice")
    void deleteByCode(int code,String exercice);

    @Query("DELETE FROM Ticket where TIK_NumTicket_M=:code and TIK_Exerc=:exercice and isSync=0")
    void deleteByCodeM(String code,String exercice);

    @Delete
    void delete(Ticket ticket);

    @Query("SELECT ifnull(MAX(cast(substr(TIK_Num_Contrat,length(:prefix) + 1 ,length('TIK_Num_Contrat'))as integer)),0)+1 FROM   Ticket WHERE substr(TIK_Num_Contrat, 0 ,length(:prefix)+1) = :prefix")
    String getNewCode(String prefix);

    @Query("SELECT strftime('%Y-%m-%d',TIK_DDm) FROM Ticket order by strftime('%Y-%m-%d %H-%M',TIK_DDm) desc limit 1")
    String getDDM();
}
