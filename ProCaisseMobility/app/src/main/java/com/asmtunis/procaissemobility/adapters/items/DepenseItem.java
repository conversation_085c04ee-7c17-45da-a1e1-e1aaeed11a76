package com.asmtunis.procaissemobility.adapters.items;

import android.content.Context;
import android.os.Build;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.recyclerview.widget.RecyclerView;

import com.afollestad.materialdialogs.DialogAction;
import com.afollestad.materialdialogs.MaterialDialog;
import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Article;
import com.asmtunis.procaissemobility.data.models.DepenceType;
import com.asmtunis.procaissemobility.data.models.DepenceCaisse;
import com.asmtunis.procaissemobility.helper.Globals;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.ui.components.ExtendedMaterialEditText;
import com.asmtunis.procaissemobility.ui.dialogs.UnitPriceDialog;
import com.mikepenz.fastadapter.IAdapter;
import com.mikepenz.fastadapter.items.AbstractItem;
import com.mikepenz.fastadapter.listeners.OnClickListener;
import com.mobsandgeeks.saripaar.Validator;
import com.mobsandgeeks.saripaar.annotation.NotEmpty;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import cn.nekocode.badge.BadgeDrawable;

/**
 * Created by WAEL on 9/1/22.
 */
public class DepenseItem extends AbstractItem<DepenseItem, DepenseItem.ViewHolder> implements OnClickListener<DepenseItem> {

    public DepenceType depenceType;
    public List<DepenceCaisse> depenceCaisseList;
    PrefUtils prefUtils;
    boolean dialogShown = false;
    Validator validator;
    @NotEmpty
    ExtendedMaterialEditText priceDepenceInputField;
    @NotEmpty
    ExtendedMaterialEditText descriptionDepenseInputField;
    @NotEmpty
    ExtendedMaterialEditText priceInputField;
    LinearLayout discountView;
    int count = 0;
    Context context;

    public DepenseItem(Context context, DepenceType depenceType, List<DepenceCaisse> depenceCaisseList) {
        this.depenceType = depenceType;
        this.context = context;
        this.depenceCaisseList = depenceCaisseList;
    }

    public DepenceType getDepence() {
        return depenceType;
    }

    public void setDepence(DepenceType depenceType) {
        this.depenceType = depenceType;
    }

    @Override
    public int getType() {
        return R.id.fastadapter_depence_item_id;
    }

    @Override
    public int getLayoutRes() {
        return R.layout.grid_item_depense;
    }

    @Override
    public DepenseItem withOnItemClickListener(OnClickListener<DepenseItem> onItemClickListener) {
        return super.withOnItemClickListener(onItemClickListener);
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    @Override
    public void bindView(final ViewHolder viewHolder, List<Object> payloads) {
        super.bindView(viewHolder, payloads);
        prefUtils = new PrefUtils(context);
        if (depenceType != null) {
            viewHolder.title.setText(depenceType.getDepLib());

            int itemStatus = App.database.depenceTypeDAO().isSelectedDepence(depenceType.getDepCode());

            String price = "";

            for (DepenceCaisse depenceCaisse : depenceCaisseList) {
                if (depenceType.getDepCode().equals(depenceCaisse.getDepCode()))
                    price = depenceCaisse.getDepMontant();
            }

            viewHolder.thumbnail.setImageResource(R.drawable.ic_box);

            viewHolder.selectecItemCount.setVisibility(itemStatus == 0 ? View.GONE : View.VISIBLE);

            if (!price.equals("")) {
                final BadgeDrawable badgeDrawable =
                        new BadgeDrawable.Builder()
                                .type(BadgeDrawable.TYPE_WITH_TWO_TEXT_COMPLEMENTARY)
                                .cornerRadius(5)
                                .badgeColor(0xff009999)
                                .text1(String.valueOf(StringUtils.parseDouble(price, 0)))
                                .text2(new PrefUtils(context).getCurrency())
                                .build();

                // viewHolder.selectecItemCount.setSecondaryText("10"/*String.format("%s %s", StringUtils.priceFormat(Double.parseDouble("10"priceDepenceInputField.getText().toString())))*/);

                viewHolder.count.setImageDrawable(badgeDrawable);
            }
        }



    }

    @Override
    public void unbindView(ViewHolder holder) {
        super.unbindView(holder);
        //  holder.thumbnail.setImage(null);
        holder.title.setText(null);
        //  holder.subtitle.setText(null);
    }

    //Init the viewHolder for this Item
    @Override
    public ViewHolder getViewHolder(View v) {
        return new ViewHolder(v);
    }

    @Override
    public boolean onClick(View v, IAdapter<DepenseItem> adapter, DepenseItem item, int position) {
        boolean dialogShown = false;

        if (!dialogShown) {
            boolean wrapInScrollView = true;
            MaterialDialog dialog = new MaterialDialog.Builder(context)
                    .title(item.depenceType.getDepLib())
                    .customView(R.layout.add_depence_view, wrapInScrollView).autoDismiss(false).cancelable(false)
                    .positiveText("R.string.positive")
                    .onPositive(new MaterialDialog.SingleButtonCallback() {
                        @Override
                        public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {

                            validator.validate();

                            App.database.depenceTypeDAO().updateDepenseStatus(
                                    item.depenceType.getDepCode(),
                                    Globals.ITEM_STATUS.UPDATED.getStatus());
                          /*  if (validator.isValidating()) {
                                count = Integer.parseInt(quantityInputField.getText().toString());

                            }*/
                            dialog.dismiss();
                        }
                    })
                    .onNegative((dialog1, which) -> {
                        App.database.depenceTypeDAO().updateDepenseStatus(item.depenceType.getDepCode(),
                                Globals.ITEM_STATUS.SELECTED.getStatus());
                    }).build();

            View view = dialog.getCustomView();
            priceDepenceInputField = view.findViewById(R.id.etPriceExpense);
            descriptionDepenseInputField = view.findViewById(R.id.etDescriptionExpense);

            dialog.show();
        }


        return false;
    }

    private void checkCurrenPrice(UnitPriceDialog unitPriceDialog, Article article) {

        if (Double.parseDouble(article.getArtPrixPublique()) == article.getPvttc()) {
            unitPriceDialog.check(R.id.publicPrice);
            return;
        }
        if (Double.parseDouble(article.getPrixGros1()) == article.getPvttc()) {
            unitPriceDialog.check(R.id.priceGros1);
            return;

        }
        if (Double.parseDouble(article.getPrixGros2()) == article.getPvttc()) {
            unitPriceDialog.check(R.id.priceGros2);
            return;

        }
        if (Double.parseDouble(article.getPrixGros3()) == article.getPvttc()) {
            unitPriceDialog.check(R.id.priceGros3);
            return;

        }
    }

    //The viewHolder used for this item. This viewHolder is always reused by the RecyclerView so scrolling is blazing fast
    protected static class ViewHolder extends RecyclerView.ViewHolder {
        protected View view;
        @BindView(R.id.title)
        TextView title;


        @BindView(R.id.count)
        ImageView count;
        @BindView(R.id.thumbnail)
        ImageView thumbnail;
        @BindView(R.id.countLabel)
        jp.shts.android.library.TriangleLabelView selectecItemCount;


        public ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
            this.view = view;
        }
    }
}

