package com.asmtunis.procaissemobility.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.DN_LigneVisite;
import com.asmtunis.procaissemobility.listener.DistributionNumRVClickInterface;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;


public class DistributionNumRVAdapter extends RecyclerView.Adapter<DistributionNumRVAdapter.MyViewHolder> {
    //public class RecyclerViewAdapter extends PagingDataAdapter<RecyclerData, RecyclerViewAdapter.MyViewHolder> {
    // Define Loading ViewType PAGINGVERSION
    //  public static final int LOADING_ITEM = 0;
    // Define Recyc ViewType PAGINGVERSION
    //  public static final int IMAGE_ITEM = 1;
    private List<DN_LigneVisite> listItems;
    private String from;

    public void setListItems(List<DN_LigneVisite> listItems , String from){
        this.listItems = listItems;
        this.from = from;
    }

    static DistributionNumRVClickInterface recyclerViewClickInterface;
    /*
    //PAGING VERSION
    public static DiffUtil.ItemCallback<RecyclerData> DIFF_CALLBACK = new DiffUtil.ItemCallback<RecyclerData>() {
          @Override
          public boolean areItemsTheSame(@NonNull RecyclerData oldItem, @NonNull RecyclerData newItem) {
              return oldItem.id == newItem.id;
          }

          @Override
          public boolean areContentsTheSame(@NonNull RecyclerData oldItem, @NonNull RecyclerData newItem) {
              return oldItem.equals(newItem);
          }
      };



    public RecyclerViewAdapter(RecyclerViewClickInterface itemClickListener) {
          super(DIFF_CALLBACK); WITHPAGING
          this.recyclerViewClickInterface = itemClickListener;
      }*/
    public DistributionNumRVAdapter(DistributionNumRVClickInterface recyclerViewClickInterface) {
        DistributionNumRVAdapter.recyclerViewClickInterface = recyclerViewClickInterface;
    }

    @NonNull
    @Override
    public MyViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {

        // CREATE VIEW HOLDER AND INFLATING ITS XML LAYOUT
        Context context = parent.getContext();
        LayoutInflater inflater = LayoutInflater.from(context);
        View view = inflater.inflate(R.layout.distribution_num_item, parent, false);

        return new MyViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull MyViewHolder holder, int position) {
        holder.updateWithGithubUser(listItems.get(position), from);
    }

    @Override
    public int getItemCount() {
        if (listItems == null)
            return 0;
        else
            return listItems.size();
    }




    public  static class MyViewHolder extends RecyclerView.ViewHolder {

        @BindView(R.id.name_productTxvw)
        TextView nameproductTxvw;

        @BindView(R.id.fournissseurTxvw)
        TextView fournissseurTxvw;

        @BindView(R.id.remarqueTxvw)
        TextView remarqueTxvw;

        @BindView(R.id.rem)
        TextView rem;

        @BindView(R.id.deleteDistibution_num)
        ImageView deleteDistibutionnum;
        public MyViewHolder(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }

        public void updateWithGithubUser(DN_LigneVisite listItems, String from){
            // this.nameproductTxvw.setText(listItems.getLgVISFamille());

            String nameprod ;
            if(App.database.dnFamilleVisite().getByCode(listItems.getLgVISFamille())!=null)
                nameprod= App.database.dnFamilleVisite().getByCode(listItems.getLgVISFamille()).getDesgFamille();
            else nameprod =listItems.getLgVISFamille();
            this.nameproductTxvw.setText(nameprod );
           // this.fournissseurTxvw.setText(listItems.getLgVISTier());

            String concurent ;
            if(App.database.vcListeConcurrentDAO().getConcurrent(listItems.getLgVISTier())!=null)
                concurent= App.database.vcListeConcurrentDAO().getConcurrent(listItems.getLgVISTier());
            else concurent =listItems.getLgVISTier();
            this.fournissseurTxvw.setText(concurent);

            if(listItems.getLgVISInfo1()!=null){
                if(listItems.getLgVISInfo1().equals("")){
                    this.remarqueTxvw.setVisibility(View.GONE);
                    this.rem.setVisibility(View.GONE);
                }
                else{
                    this.remarqueTxvw.setVisibility(View.VISIBLE);
                    this.rem.setVisibility(View.VISIBLE);
                    this.remarqueTxvw.setText(listItems.getLgVISInfo1());
                }
            }
            else{
                this.remarqueTxvw.setVisibility(View.GONE);
                this.rem.setVisibility(View.GONE);
            }


            if(from.equals("dnVisfrag")) this.deleteDistibutionnum.setVisibility(View.GONE);
            this.deleteDistibutionnum.setOnClickListener(view -> recyclerViewClickInterface.onItemDeleteClick(listItems));

        }
    }
}

