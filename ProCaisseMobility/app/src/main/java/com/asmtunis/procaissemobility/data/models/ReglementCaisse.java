package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.Nullable;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.annotation.NonNull;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * Created by PC on 10/25/2017.
 */
@Entity(primaryKeys =
        {"REGC_Code", "REGC_Exercice", "REGC_IdSCaisse", "REGC_Code_M"})
public class ReglementCaisse extends BaseModel implements Serializable {
    @NonNull
    @ColumnInfo(name = "REGC_Code")
    @SerializedName("REGC_Code")
    @Expose
    public String rEGCCode;

    public String getrEGCCode_M() {
        return rEGCCode_M;
    }

    public void setrEGCCode_M(String rEGCCode_M) {
        this.rEGCCode_M = rEGCCode_M;
    }
    @NonNull
    @ColumnInfo(name = "REGC_Code_M")
    @SerializedName("REGC_Code_M")
    @Expose
    public String rEGCCode_M;
    @NonNull
    @ColumnInfo(name = "REGC_Exercice")
    @SerializedName("REGC_Exercice")
    @Expose
    public String rEGCExercice;
    @ColumnInfo(name = "REGC_IdCarnet")
    @SerializedName("REGC_IdCarnet")
    @Expose
    public String rEGCIdCarnet;
    @ColumnInfo(name = "REGC_NumTicket")
    @SerializedName("REGC_NumTicket")
    @Expose
    public Long rEGCNumTicket;
    @NonNull
    @ColumnInfo(name = "REGC_IdSCaisse")
    @SerializedName("REGC_IdSCaisse")
    @Expose
    public String rEGCIdSCaisse;


    public Long getrEGNumTicketPart() {
        return rEGNumTicketPart;
    }

    public void setrEGNumTicketPart(Long rEGNumTicketPart) {
        this.rEGNumTicketPart = rEGNumTicketPart;
    }

    @ColumnInfo(name = "REGC_NumTicketPart")
    @SerializedName("REGC_NumTicketPart")
    @Expose
    public Long rEGNumTicketPart;



    @ColumnInfo(name = "REGC_IdCaisse")
    @SerializedName("REGC_IdCaisse")
    @Expose
    public String rEGCIdCaisse;
    @ColumnInfo(name = "REGC_IdStation")
    @SerializedName("REGC_IdStation")
    @Expose
    public String rEGCIdStation;
    @ColumnInfo(name = "REGC_CodeClient")
    @SerializedName("REGC_CodeClient")
    @Expose
    public String rEGCCodeClient;
    @ColumnInfo(name = "REGC_NomPrenom")
    @SerializedName("REGC_NomPrenom")
    @Expose
    public String rEGCNomPrenom;
    @SerializedName("REGC_ModeReg")
    @ColumnInfo(name = "REGC_ModeReg")
    @Expose
    public String rEGCModeReg;
    @SerializedName("REGC_DateReg")
    @ColumnInfo(name = "REGC_DateReg")
    @Expose
    public String rEGCDateReg;
    @SerializedName("REGC_MntEspece")
    @ColumnInfo(name = "REGC_MntEspece")
    @Expose
    public Double rEGCMntEspece;
    @SerializedName("REGC_MntCarteBancaire")
    @ColumnInfo(name = "REGC_MntCarteBancaire")
    @Expose
    public Double rEGCMntCarteBancaire;
    @SerializedName("REGC_MntCh\u00e9que")
    @Expose(serialize = false, deserialize = true)
    @ColumnInfo(name = "REGC_MntCheque")
    public Double REGC_MntCheque;
    @SerializedName("REGC_MntTraite")
    @ColumnInfo(name = "REGC_MntTraite")
    @Expose
    public Double rEGCMntTraite;
    @SerializedName("REGC_Remarque")
    @ColumnInfo(name = "REGC_Remarque")
    @Expose
    public String rEGCRemarque;
    @SerializedName("REGC_Montant")

    @ColumnInfo(name = "REGC_Montant")
    @Expose
    public Double rEGCMontant;
    @SerializedName("REGC_Station")
    @ColumnInfo(name = "REGC_Station")
    @Expose
    public String rEGCStation;
    @SerializedName("REGC_User")
    @ColumnInfo(name = "REGC_User")
    @Expose
    public String rEGCUser;
    @SerializedName("REGC_MntTotalRecue")
    @ColumnInfo(name = "REGC_MntTotalRecue")
    @Expose
    public Double rEGCMntTotalRecue;
    @SerializedName("REGC_MntEspeceRecue")
    @ColumnInfo(name = "REGC_MntEspeceRecue")
    @Expose
    public Double rEGCMntEspeceRecue;
    @SerializedName("rest")
    @ColumnInfo(name = "rest")
    @Expose
    public Double rest;

  @SerializedName("made")
  @ColumnInfo(name = "made")
  @Expose
    public Double made ;

    public ReglementCaisse() {
    }


    public ReglementCaisse(String rEGCCode,
                           String rEGCCode_M,
                           String rEGCExercice,
                           String rEGCIdCarnet,
                           Long rEGCNumTicket,
                           Long rEGNumTicketPart,

                           String rEGCIdSCaisse,
                           String rEGCIdCaisse,
                           String rEGCIdStation,
                           String rEGCCodeClient,
                           String rEGCNomPrenom,
                           String rEGCModeReg,
                           String rEGCDateReg,
                           double rEGCMntEspece,
                           double rEGCMntCarteBancaire,
                           double rEGCMntChQue,
                           double rEGCMntTraite,
                           String rEGCRemarque,
                           double rEGCMontant,
                           String rEGCStation,
                           String rEGCUser,
                           double rEGCMntTotalRecue,
                           double rEGCMntEspeceRecue,
                           double rest,
                           double made) {
        this.rEGCCode = rEGCCode;
        this.rEGCCode_M = rEGCCode_M;
        this.rEGCExercice = rEGCExercice;
        this.rEGCIdCarnet = rEGCIdCarnet;
        this.rEGCNumTicket = rEGCNumTicket;
        this.rEGNumTicketPart = rEGNumTicketPart;
        this.rEGCIdSCaisse = rEGCIdSCaisse;
        this.rEGCIdCaisse = rEGCIdCaisse;
        this.rEGCIdStation = rEGCIdStation;
        this.rEGCCodeClient = rEGCCodeClient;
        this.rEGCNomPrenom = rEGCNomPrenom;
        this.rEGCModeReg = rEGCModeReg;
        this.rEGCDateReg = rEGCDateReg;
        this.rEGCMntEspece = rEGCMntEspece;
        this.rEGCMntCarteBancaire = rEGCMntCarteBancaire;
        this.REGC_MntCheque = rEGCMntChQue;
        this.rEGCMntTraite = rEGCMntTraite;
        this.rEGCRemarque = rEGCRemarque;
        this.rEGCMontant = rEGCMontant;
        this.rEGCStation = rEGCStation;
        this.rEGCUser = rEGCUser;
        this.rEGCMntTotalRecue = rEGCMntTotalRecue;
        this.rEGCMntEspeceRecue = rEGCMntEspeceRecue;
        this.rest = rest;
        this.made = made;
    }

    public double getMade() {
        return made;
    }

    public void setMade(double made) {
        this.made = made;
    }

    public double getRest() {
        return rest;
    }

    public void setRest(double rest) {
        this.rest = rest;
    }

    public String getrEGCCode() {
        return rEGCCode;
    }

    public void setrEGCCode(String rEGCCode) {
        this.rEGCCode = rEGCCode;
    }

    public String getrEGCExercice() {
        return rEGCExercice;
    }

    public void setrEGCExercice(String rEGCExercice) {
        this.rEGCExercice = rEGCExercice;
    }

    public String getrEGCIdCarnet() {
        return rEGCIdCarnet;
    }

    public void setrEGCIdCarnet(String rEGCIdCarnet) {
        this.rEGCIdCarnet = rEGCIdCarnet;
    }

    public long getrEGCNumTicket() {
        return rEGCNumTicket;
    }

    public void setrEGCNumTicket(long rEGCNumTicket) {
        this.rEGCNumTicket = rEGCNumTicket;
    }

    public String getrEGCIdSCaisse() {
        return rEGCIdSCaisse;
    }

    public void setrEGCIdSCaisse(String rEGCIdSCaisse) {
        this.rEGCIdSCaisse = rEGCIdSCaisse;
    }

    public String getrEGCIdCaisse() {
        return rEGCIdCaisse;
    }

    public void setrEGCIdCaisse(String rEGCIdCaisse) {
        this.rEGCIdCaisse = rEGCIdCaisse;
    }

    public String getrEGCIdStation() {
        return rEGCIdStation;
    }

    public void setrEGCIdStation(String rEGCIdStation) {
        this.rEGCIdStation = rEGCIdStation;
    }

    public String getrEGCCodeClient() {
        return rEGCCodeClient;
    }

    public void setrEGCCodeClient(String rEGCCodeClient) {
        this.rEGCCodeClient = rEGCCodeClient;
    }

    public String getrEGCNomPrenom() {
        return rEGCNomPrenom;
    }

    public void setrEGCNomPrenom(String rEGCNomPrenom) {
        this.rEGCNomPrenom = rEGCNomPrenom;
    }

    public String getrEGCModeReg() {
        return rEGCModeReg;
    }

    public void setrEGCModeReg(String rEGCModeReg) {
        this.rEGCModeReg = rEGCModeReg;
    }

    public String getrEGCDateReg() {
        return rEGCDateReg;
    }

    public void setrEGCDateReg(String rEGCDateReg) {
        this.rEGCDateReg = rEGCDateReg;
    }

    public double getrEGCMntEspece() {
        return rEGCMntEspece;
    }

    public void setrEGCMntEspece(double rEGCMntEspece) {
        this.rEGCMntEspece = rEGCMntEspece;
    }

    public double getrEGCMntCarteBancaire() {
        return rEGCMntCarteBancaire;
    }

    public void setrEGCMntCarteBancaire(double rEGCMntCarteBancaire) {
        this.rEGCMntCarteBancaire = rEGCMntCarteBancaire;
    }

    public double getrEGCMntChQue() {
        return REGC_MntCheque;
    }

    public void setrEGCMntChQue(double rEGCMntChQue) {
        this.REGC_MntCheque = rEGCMntChQue;
    }

    public double getrEGCMntTraite() {
        return rEGCMntTraite;
    }

    public void setrEGCMntTraite(double rEGCMntTraite) {
        this.rEGCMntTraite = rEGCMntTraite;
    }

    public String getrEGCRemarque() {
        return rEGCRemarque;
    }

    public void setrEGCRemarque(String rEGCRemarque) {
        this.rEGCRemarque = rEGCRemarque;
    }

    public double getrEGCMontant() {
        return rEGCMontant;
    }

    public void setrEGCMontant(double rEGCMontant) {
        this.rEGCMontant = rEGCMontant;
    }

    public String getrEGCStation() {
        return rEGCStation;
    }

    public void setrEGCStation(String rEGCStation) {
        this.rEGCStation = rEGCStation;
    }

    public String getrEGCUser() {
        return rEGCUser;
    }

    public void setrEGCUser(String rEGCUser) {
        this.rEGCUser = rEGCUser;
    }

    public double getrEGCMntTotalRecue() {
        return rEGCMntTotalRecue;
    }

    public void setrEGCMntTotalRecue(double rEGCMntTotalRecue) {
        this.rEGCMntTotalRecue = rEGCMntTotalRecue;
    }

    public double getrEGCMntEspeceRecue() {
        return rEGCMntEspeceRecue;
    }

    public void setrEGCMntEspeceRecue(double rEGCMntEspeceRecue) {
        this.rEGCMntEspeceRecue = rEGCMntEspeceRecue;
    }

    @Override
    public String toString() {
        return "ReglementCaisse{" +
                "rEGCCode='" + rEGCCode + '\'' +
                ", rEGCExercice='" + rEGCExercice + '\'' +
                ", rEGCIdCarnet='" + rEGCIdCarnet + '\'' +
                ", rEGCNumTicket=" + rEGCNumTicket +
                ", rEGCIdSCaisse='" + rEGCIdSCaisse + '\'' +
                ", rEGCIdCaisse='" + rEGCIdCaisse + '\'' +
                ", rEGCIdStation='" + rEGCIdStation + '\'' +
                ", rEGCCodeClient='" + rEGCCodeClient + '\'' +
                ", rEGCNomPrenom='" + rEGCNomPrenom + '\'' +
                ", rEGCModeReg='" + rEGCModeReg + '\'' +
                ", rEGCDateReg='" + rEGCDateReg + '\'' +
                ", rEGCMntEspece=" + rEGCMntEspece +
                ", rEGCMntCarteBancaire=" + rEGCMntCarteBancaire +
                ", REGC_MntCheque=" + REGC_MntCheque +
                ", rEGCMntTraite=" + rEGCMntTraite +
                ", rEGCRemarque='" + rEGCRemarque + '\'' +
                ", rEGCMontant=" + rEGCMontant +
                ", rEGCStation='" + rEGCStation + '\'' +
                ", rEGCUser='" + rEGCUser + '\'' +
                ", rEGCMntTotalRecue=" + rEGCMntTotalRecue +
                ", rEGCMntEspeceRecue=" + rEGCMntEspeceRecue +
                ", rest=" + rest +
                ", status=" + status +
                ", isSync=" + isSync +
                '}';
    }
}

