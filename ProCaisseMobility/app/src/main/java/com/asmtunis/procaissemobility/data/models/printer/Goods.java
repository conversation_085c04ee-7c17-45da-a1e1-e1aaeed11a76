package com.asmtunis.procaissemobility.data.models.printer;


public class Goods {

    private String name;

    private int format;
    // half-width character width 58mm per line 32 80mm each line 48
    private int width;
    // placeholder format $ {time}
    private String variable;



    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getFormat() {
        return format;
    }

    public void setFormat(int format) {
        this.format = format;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public String getVariable() {
        return variable;
    }

    public void setVariable(String variable) {
        this.variable = variable;
    }
}
