package com.asmtunis.procaissemobility.data.network.services;


import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Statistics;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Headers;
import retrofit2.http.POST;

/**
 * Created by Achraf on 28/09/2017.
 */

public interface StatisticService {

    @Headers("User-Agent: android-api-client")
    @POST("getReglementStatistics")
    Call<Statistics> getReglementStatistics(@Body GenericObject genericObject);


    @Headers("User-Agent: android-api-client")
    @POST("getVenteStatistics")
    Call<Statistics> getVenteStatistics(@Body GenericObject genericObject);

    @Headers("User-Agent: android-api-client")
    @POST("getTopNClients")
    Call<List<Client>> getTopNClients(@Body GenericObject genericObject);

}
