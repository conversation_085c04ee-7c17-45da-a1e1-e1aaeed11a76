package com.asmtunis.procaissemobility.adapters.items;

import android.content.Context;
import android.graphics.Paint;
import android.os.Build;
import android.util.Log;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.RequiresApi;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.RecyclerView;
import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.BonCommande;
import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.data.models.LigneBonCommande;
import com.asmtunis.procaissemobility.helper.utils.DateUtils;
import com.asmtunis.procaissemobility.listener.ItemCallback;
import com.asmtunis.procaissemobility.listener.MenuItemsAction;
import com.mikepenz.fastadapter.items.AbstractItem;
import com.mikepenz.fontawesome_typeface_library.FontAwesome;
import com.mikepenz.iconics.IconicsDrawable;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.TemporalAccessor;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.TimeZone;

/**
 * Created by Oussama AZIZI on 5/26/22.
 */

public class InvPatItem extends AbstractItem<InvPatItem, InvPatItem.ViewHolder> {

    private  final int UNSELECTED = -1;
    private Context context;
    public BonCommande ticket;
    private int selectedItem = UNSELECTED;
    MenuItemsAction menuItemsAction;
    protected ItemCallback itemCallback;
    boolean isOrder;
    int menuId;

    public InvPatItem(Context context, boolean isOrder, BonCommande ticket, int menuId, MenuItemsAction
            menuItemsAction, ItemCallback itemCallback){
        this.ticket = ticket;
        this.context = context;
        this.menuItemsAction = menuItemsAction;
        this.menuId = menuId;
        this.itemCallback=itemCallback;
        this.isOrder = isOrder;
    }

    public BonCommande getTicket() {
        return ticket;
    }

    public void setTicket(BonCommande ticket) {
        this.ticket = ticket;
    }

    @Override
    public int getType() {
        return R.id.fastadapter_ticket_item_id;
    }

    @Override
    public int getLayoutRes() {
        return R.layout.ticket_item;
    }


    @RequiresApi(api = Build.VERSION_CODES.O)
    @Override
    public void bindView(final InvPatItem.ViewHolder viewHolder, List<Object> payloads) {
        super.bindView(viewHolder, payloads);
        Client client = App.database.clientDAO().getOneByCode(ticket.getDEVCodeClient());
        viewHolder.ticketNumber.setText(String.format(context.getString( R.string.invpat_number_field ), ticket.getDEVNum()));
        viewHolder.ticketUser.setText((client!=null)?client.cLINomPren:ticket.getBONLIVNum());
        viewHolder.setIsRecyclable(false);





       // viewHolder.dateCreation.setText(ticket.getDEVDDm().replace("Z","").replace("T"," "));
        viewHolder.dateCreation.setText(ticket.getDEVDDm().replace(".000",""));




        viewHolder.toolbar.setTag("toolbar_"+ticket.getBONLIVNum());
        viewHolder.itemStatusLabel.setTag("itemStatusLabel_ "+ticket.getBONLIVNum());


        List<LigneBonCommande> ligneBonCommandes;
        if(ticket.isSync) ligneBonCommandes = App.database.ligneBonCommandeDAO().getByBCCode(ticket.getDEVNum());
        else ligneBonCommandes = App.database.ligneBonCommandeDAO().getByBCCode(ticket.getDevCodeM());
        viewHolder.price.setText("Nbr Art Pat : "+String.valueOf(ligneBonCommandes.size()));


        if(!ticket.isSync) {
            //viewHolder.price.setPaintFlags(viewHolder.price.getPaintFlags() & (~Paint.STRIKE_THRU_TEXT_FLAG));
            viewHolder.dateCreation.setTextColor(context.getResources().getColor(R.color.warningColor));

            setTriangleView(viewHolder.itemStatusLabel, 0);
        }
       else{
            setTriangleView(viewHolder.itemStatusLabel, 2);
            viewHolder.dateCreation.setTextColor(context.getResources().getColor(R.color.successColor));
        }

        if(ticket.getCode()!=null) {
         //   App.database.ligneBonCommandeDAO().getByBCCode(ticket.getDEVNum())
            if(!ticket.getCode().equals("")) {
                viewHolder.state.setVisibility(View.VISIBLE);
                viewHolder.state.setText("!");
            //    viewHolder.price.setPaintFlags(viewHolder.price.getPaintFlags() & (~Paint.STRIKE_THRU_TEXT_FLAG));
                viewHolder.state.setBackground(context.getResources().getDrawable(R.drawable.round_corner_a));
             //   viewHolder.price.setTextColor(context.getResources().getColor(R.color.errorColor));
            }
          /*  else{
                viewHolder.dateCreation.setTextColor(context.getResources().getColor(R.color.successColor));
            }*/
        }




        if (itemCallback!=null)
        {
            viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onViewClick(viewHolder);
                }
            });
            viewHolder.toolbar.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onViewClick(viewHolder);
                }
            });
        }
    }

    void onViewClick(InvPatItem.ViewHolder viewHolder)
    {
        if (ticket != null) {
            if (itemCallback == null) {
                return;
            } else {
                itemCallback.onItemClicked(viewHolder,ticket);
            }
        }
    }

    //reset the view here (this is an optional method, but recommended)
    @Override
    public void unbindView(final InvPatItem.ViewHolder holder) {
        super.unbindView(holder);
    }

    //Init the viewHolder for this Item
    @Override
    public InvPatItem.ViewHolder getViewHolder(View v) {
        return new InvPatItem.ViewHolder(v,menuId,menuItemsAction,ticket);
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        com.asmtunis.procaissemobility.ui.components.TicketView  ticketView;
        LinearLayout footerLayout;
        public Toolbar toolbar;
        TextView dateCreation;
        TextView price;
        TextView ticketNumber;
        TextView ticketUser;
        FrameLayout content;

        TextView state;


        jp.shts.android.library.TriangleLabelView itemStatusLabel;
        BonCommande ticket;

        public ViewHolder(View view,int menuId,MenuItemsAction menuItemsAction, BonCommande ticket) {
            super(view);
            this.ticket=ticket;
            ticketView = view.findViewById(R.id.layout_ticket);
            footerLayout = view.findViewById(R.id.footer_layout);
            toolbar = view.findViewById(R.id.toolbar);
            price = view.findViewById(R.id.price);
            ticketNumber = view.findViewById(R.id.ticketNumber);
            ticketUser = view.findViewById(R.id.ticketUser);
            dateCreation = view.findViewById(R.id.dateCreation);
            content = view.findViewById(R.id.content_layout);
            itemStatusLabel = view.findViewById(R.id.item_status_label);
            state = view.findViewById(R.id.state);

        }
    }


    void setTriangleView(jp.shts.android.library.TriangleLabelView labelView, int status) {
        labelView.setVisibility(View.VISIBLE);
        switch (status) {
            case 0:
                labelView.setTriangleBackgroundColorResource(R.color.warningColor);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.notSync);
                labelView.setPrimaryTextColorResource(R.color.md_red_100);

                break;

            case 1:
                labelView.setTriangleBackgroundColorResource(R.color.md_green_800);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.new_label);
                labelView.setPrimaryTextColorResource(R.color.md_green_100);
                break;

            case  2 :
                labelView.setTriangleBackgroundColorResource(R.color.md_green_800);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.Sync);
                labelView.setPrimaryTextColorResource(R.color.md_green_100);
                break;
            default:
                labelView.setVisibility(View.GONE);

                break;
        }

    }

}


