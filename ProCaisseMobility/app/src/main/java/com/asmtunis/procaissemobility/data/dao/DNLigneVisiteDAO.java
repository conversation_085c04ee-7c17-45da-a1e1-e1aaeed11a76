package com.asmtunis.procaissemobility.data.dao;


import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.DN_LigneVisite;

import java.util.List;

@Dao
public interface DNLigneVisiteDAO {
    @Query("SELECT * FROM DN_LigneVisite")
    LiveData<List<DN_LigneVisite>> getAll();

    @Query("SELECT * FROM DN_LigneVisite")
    List<DN_LigneVisite> getAllList();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<DN_LigneVisite> dnLigneVisite);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(DN_LigneVisite DnSuperficie);

    @Query("SELECT * FROM DN_LigneVisite where isSync=0 and  (Status='INSERTED'  or Status='UPDATED') ")
    List<DN_LigneVisite> getNoSynced();

    @Query("SELECT count(*) FROM DN_LigneVisite where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    LiveData<Integer> getNoSyncCountMubtale();


    @Query("SELECT * FROM DN_LigneVisite WHERE LG_VISNum = :code")
    DN_LigneVisite getByCode(String code);



    @Query("SELECT * FROM DN_LigneVisite WHERE LG_VISNum = :code")
    List<DN_LigneVisite> getListByCode(String code);

    @Query("delete from DN_LigneVisite")
    void deleteAll();

    @Query("DELETE FROM DN_LigneVisite where LG_VISNum=:lgVISNum")
    void deleteById(String lgVISNum);



    @Delete
    void delete(DN_LigneVisite dnLigneVisite);

  @Query("UPDATE DN_LigneVisite SET LG_VISNum = :code and  Status = :status and isSync= :sync where LG_VISNum = :CodeMobile")
 void updateCloud(String code, String status,Boolean sync, String CodeMobile);


    @Query("UPDATE DN_LigneVisite SET LG_VISNum = :code  where LG_VISNum = :CodeMobile")
    void updateCloud(String code,  String CodeMobile);


}
