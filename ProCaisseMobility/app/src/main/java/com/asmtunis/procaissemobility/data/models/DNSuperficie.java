package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

@Entity
public class DNSuperficie extends BaseModel implements Serializable {
    public DNSuperficie() {
    }


    @NonNull
    public String getCodeSuperf() {
        return codeSuperf;
    }

    public void setCodeSuperf(@NonNull String codeSuperf) {
        this.codeSuperf = codeSuperf;
    }

    public String getTypeSuperf() {
        return typeSuperf;
    }

    public void setTypeSuperf(String typeSuperf) {
        this.typeSuperf = typeSuperf;
    }

    public String getNoteSuperf() {
        return noteSuperf;
    }

    public void setNoteSuperf(String noteSuperf) {
        this.noteSuperf = noteSuperf;
    }

    public int getEtatSuperf() {
        return etatSuperf;
    }

    public void setEtatSuperf(int etatSuperf) {
        this.etatSuperf = etatSuperf;
    }

    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "CodeSuperf")//
    @SerializedName("CodeSuperf")
    @Expose
    private String codeSuperf;

    @ColumnInfo(name = "TypeSuperf")//
    @SerializedName("TypeSuperf")
    @Expose
    private String typeSuperf;


    @ColumnInfo(name = "NoteSuperf")//
    @SerializedName("NoteSuperf")
    @Expose
    private String noteSuperf;

    @ColumnInfo(name = "EtatSuperf")////
    @SerializedName("EtatSuperf")
    @Expose
    private int etatSuperf;



}
