package com.asmtunis.procaissemobility.data.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.EtatOrdreMission;

import java.util.List;

@Dao
public interface EtatOrdreMissionDAO {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<EtatOrdreMission> etatOrdreMissionList);

    @Query("select * from EtatOrdreMission")
    List<EtatOrdreMission> getAll();

    @Query("select * from EtatOrdreMission where CodeEtatOrd = :code")
    EtatOrdreMission getOne(String code);

    @Query("DELETE FROM EtatOrdreMission")
    void deleteAll();
}
