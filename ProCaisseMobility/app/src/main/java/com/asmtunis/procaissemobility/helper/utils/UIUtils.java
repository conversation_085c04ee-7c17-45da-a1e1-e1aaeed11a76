package com.asmtunis.procaissemobility.helper.utils;

import static com.asmtunis.procaissemobility.App.prefUtils;
import static com.asmtunis.procaissemobility.helper.utils.StringUtils.isEmptyString;

import android.app.Activity;
import android.content.Context;
import android.content.res.XmlResourceParser;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.WindowManager;

import androidx.appcompat.widget.Toolbar;
import androidx.core.content.ContextCompat;

import com.afollestad.materialdialogs.MaterialDialog;
import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.blankj.utilcode.util.ObjectUtils;
import com.mikepenz.google_material_typeface_library.GoogleMaterial;
import com.mikepenz.iconics.IconicsDrawable;
import com.mikepenz.iconics.view.IconicsButton;

import org.xmlpull.v1.XmlPullParserException;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import co.chiragm.sbn.StatusBarNotifier;

/**
 * Created by pc on 10/3/17.
 */

public class UIUtils {


    public static void showDialog(Context context, int title, int content) {
        new MaterialDialog.Builder(context)
                .title(title)
                .content(content)
                .negativeText(R.string.valid)
                .show();
    }

    public static MaterialDialog showDialog(Context context, String title, String content, MaterialDialog.SingleButtonCallback singleButtonCallback) {
        return new MaterialDialog.Builder(context)
                .title(title)
                .content(content)
                .positiveText(R.string.yes)
                .negativeText(R.string.no).onPositive(singleButtonCallback)
                .show();
    }

    public static MaterialDialog showDialogWithoutChoice(Context context, String title, String content, MaterialDialog.SingleButtonCallback singleButtonCallback) {
        return new MaterialDialog.Builder(context)
                .title(title)
                .content(content)
                .positiveText(R.string.dialog_ok)
                .onPositive(singleButtonCallback)
                .cancelable(false)
                .show();
    }

    public static MaterialDialog showDialog(Context context, int title, int content, MaterialDialog.SingleButtonCallback singleButtonCallback) {
        return new MaterialDialog.Builder(context)
                .title(title)
                .content(content)
                .positiveText("ok").onPositive(singleButtonCallback)
                .show();
    }

    public static MaterialDialog showDialog2(Context context, int title, int content,
                                             MaterialDialog.SingleButtonCallback singleButtonCallback,
                                             MaterialDialog.SingleButtonCallback cancelButtonCallback) {
        return new MaterialDialog.Builder(context)
                .title(title)
                .content(content)
                .positiveText(R.string.yes)
                .negativeText(R.string.cancel)
                .onNegative(cancelButtonCallback)
                .onPositive(singleButtonCallback)
                .show();
    }


    public static void showDialog(Context context, int title) {
        new MaterialDialog.Builder(context)
                .title(title)
                .negativeText(R.string.valid)
                .show();
    }


    public static MaterialDialog showDialog(Context context, int title, String content) {
        MaterialDialog dialog = null;
        try {
            return new MaterialDialog.Builder(context)
                    .title(title)
                    .content(content)
                    .negativeText(R.string.valid)
                    .show();
        } catch (Exception e) {

        }
        return dialog;
    }


    public static int dp2px(Context context, float spValue) {

        final float fontScale = context.getResources().getDisplayMetrics().scaledDensity;
        return (int) (spValue * fontScale + 0.5f);

    }


    public static int setDiscountVisibility() {
        return hasDiscountAuthority() ? View.VISIBLE : View.GONE;
    }

    public static int getThemeAccentColor(final Context context) {
        final TypedValue value = new TypedValue();
        context.getTheme().resolveAttribute(R.attr.colorAccent, value, true);
        return value.data;
    }


    public static boolean hasDiscountAuthority() {
        return !isEmptyString(prefUtils.getDiscountAuthorization()) && (!prefUtils.getDiscountAuthorization().equalsIgnoreCase("-"));
    }


    public static List<Integer> getAllMaterialColors(Context context) throws IOException, XmlPullParserException {
        XmlResourceParser xrp = context.getResources().getXml(R.xml.materialcolor);
        List<Integer> allColors = new ArrayList<>();
        int nextEvent;
        while ((nextEvent = xrp.next()) != XmlResourceParser.END_DOCUMENT) {
            String s = xrp.getName();
            if ("color".equals(s)) {
                String color = xrp.nextText();
                allColors.add(Color.parseColor(color));
            }
        }
        return allColors;
    }

    public static void showStatusBar(Activity context) {
        if (DateUtils.getRemainingTime(App.prefUtils.getExpirationDate()) <= 30 &&
                DateUtils.getRemainingTime(App.prefUtils.getExpirationDate()) > 0) {
            context.getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                    WindowManager.LayoutParams.FLAG_FULLSCREEN);
            Toolbar Toolbar = context.findViewById(R.id.toolbar);

            final StatusBarNotifier statusBarNotifier = new StatusBarNotifier
                    .Builder(context)
                    .setAutoHide(false)
                    .build();
            statusBarNotifier.setText(String.format("%s: %d %s", context.getResources().getString(R.string.remaining_days),
                    DateUtils.getRemainingTime(App.prefUtils.getExpirationDate()),
                    context.getResources().getString(R.string.days)));
            statusBarNotifier.getTextView().setTextColor(Color.WHITE);
            statusBarNotifier.setBackgroundColor(ContextCompat.getColor(context, R.color.md_red800));
            statusBarNotifier.show();
            if (ObjectUtils.isNotEmpty(Toolbar)) {
                ViewTreeObserver vto = statusBarNotifier.getTextView().getViewTreeObserver();
                vto.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                    @Override
                    public void onGlobalLayout() {
                        statusBarNotifier.getTextView().getViewTreeObserver().removeOnGlobalLayoutListener(this);
                        if (Toolbar.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
                            ViewGroup.MarginLayoutParams p = (ViewGroup.MarginLayoutParams) Toolbar.getLayoutParams();
                            p.setMargins(p.leftMargin, statusBarNotifier.getTextView().getMeasuredHeight(), p.rightMargin, p.bottomMargin);
                            Toolbar.requestLayout();
                        }
                    }
                });

            }
        }
    }

    public static void setDefaultAutoScanColor(IconicsButton icon, Context context) {
        if(!prefUtils.getIsAutoScan()) {
            icon.getIconicsDrawableBottom().setColorFilter(context.getResources().getColor(R.color.material_drawer_accent), PorterDuff.Mode.SRC_ATOP);
        }
        else {
            icon.getIconicsDrawableBottom().setColorFilter(context.getResources().getColor(R.color.material_amber500), PorterDuff.Mode.SRC_ATOP);
        }
    }

    public static IconicsDrawable getIconicsDrawable(GoogleMaterial.Icon icon, int color, Context context) {
        IconicsDrawable scanIcon = new IconicsDrawable(context);
        scanIcon.icon(icon);
        scanIcon.sizeDp(30);
        scanIcon.setColorFilter(context.getResources().getColor(color), PorterDuff.Mode.SRC_ATOP);
        return scanIcon;
    }

    public static void switchAutoScanColor(IconicsButton icon, Context context) {
        if(!prefUtils.getIsAutoScan()) {
            icon.getIconicsDrawableBottom().setColorFilter(context.getResources().getColor(R.color.material_amber500), PorterDuff.Mode.SRC_ATOP);
            prefUtils.setIsAutoScan(true);
        }
        else {
            icon.getIconicsDrawableBottom().setColorFilter(context.getResources().getColor(R.color.material_drawer_accent), PorterDuff.Mode.SRC_ATOP);
            prefUtils.setIsAutoScan(false);

        }
    }
}

