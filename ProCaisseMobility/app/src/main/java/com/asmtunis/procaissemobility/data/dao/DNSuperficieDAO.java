package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.DNSuperficie;
import com.asmtunis.procaissemobility.data.models.DNTypeServices;
import com.asmtunis.procaissemobility.data.models.VCPrix;

import java.util.List;

@Dao
public interface  DNSuperficieDAO {
        @Query("SELECT * FROM DNSuperficie")
       // LiveData<List<DNSuperficie>> getAll();
         List<DNSuperficie> getAll();

        @Insert(onConflict = OnConflictStrategy.REPLACE)
        void insertAll(List<DNSuperficie> DnSuperficies);

        @Insert(onConflict = OnConflictStrategy.REPLACE)
        void insert(DNSuperficie DnSuperficie);

        @Query("SELECT * FROM DNSuperficie where isSync=0 and  (Status='INSERTED'  or Status='UPDATED') ")
        List<DNSuperficie> getNoSynced();


        @Query("SELECT * FROM DNSuperficie WHERE CodeSuperf=:codeSuperf")
        DNSuperficie getbyCode(String codeSuperf);

        @Query("SELECT count(*) FROM DNSuperficie where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
        LiveData<Integer> getNoSyncCountMubtale();


        @Query("delete from DNSuperficie")
        void deleteAll();

        @Query("DELETE FROM DNSuperficie where CodeSuperf=:codeSuperf")
        void deleteById(String codeSuperf);


//@Query("UPDATE DNTypeServices SET CodeVCPrix = :code_procaiss where CodeVCPrixM = :CodeMobile")
// void updateCloudCode(String code_procaiss, String CodeMobile);

}