package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

@Entity
public class DNFamille extends BaseModel implements Serializable {


    public DNFamille() {
    }


    @NonNull
    public String getCodeFamille() {
        return codeFamille;
    }

    public void setCodeFamille(@NonNull String codeFamille) {
        this.codeFamille = codeFamille;
    }

    public String getDesgFamille() {
        return desgFamille;
    }

    public void setDesgFamille(String desgFamille) {
        this.desgFamille = desgFamille;
    }

    public String getNoteFamille() {
        return noteFamille;
    }

    public void setNoteFamille(String noteFamille) {
        this.noteFamille = noteFamille;
    }

    public Integer getEtatFamille() {
        return etatFamille;
    }

    public void setEtatFamille(Integer etatFamille) {
        this.etatFamille = etatFamille;
    }

    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "CodeFamille")
    @SerializedName("CodeFamille")
    @Expose
    private String codeFamille;


    @ColumnInfo(name = "DesgFamille")
    @SerializedName("DesgFamille")
    @Expose
    private String desgFamille;



    @ColumnInfo(name = "NoteFM")
    @SerializedName("NoteFM")
    @Expose
    private String noteFamille;



    @ColumnInfo(name = "EtatFM")
    @SerializedName("EtatFM")
    @Expose
    private Integer etatFamille;

}
