package com.asmtunis.procaissemobility.adapters.tables;

import android.content.Context;
import android.graphics.Typeface;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.LigneBonCommande;
import com.asmtunis.procaissemobility.data.models.LigneTicket;
import com.asmtunis.procaissemobility.helper.utils.Calculator;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.ui.components.SortableLigneCommandeTableView;
import com.asmtunis.procaissemobility.ui.components.SortableLigneInvTableView;

import java.text.NumberFormat;
import java.util.List;

import de.codecrafters.tableview.toolkit.LongPressAwareTableDataAdapter;

/**
 * Created by Oussama AZIZI on 5/26/22.
 */

public class LigneInventaireTableDataAdapter extends LongPressAwareTableDataAdapter<LigneBonCommande> {

    private static final int TEXT_SIZE = 16;
    private static final NumberFormat PRICE_FORMATTER = NumberFormat.getNumberInstance();

    public LigneInventaireTableDataAdapter(final Context context, final List<LigneBonCommande> data, final SortableLigneInvTableView tableView) {
        super(context, data, tableView);
    }

    @Override
    public View getDefaultCellView(int rowIndex, int columnIndex, ViewGroup parentView) {
        final LigneBonCommande ligneTicket = getRowData(rowIndex);
        View renderedView = null;

        switch (columnIndex) {
            case 0:
           //     renderedView = renderQuantity(ligneTicket);
                renderedView =   renderNumSerie(ligneTicket);
                break;

            case 1:
              //  renderedView =   renderNumSerie(ligneTicket);
                renderedView = renderLigneTicketName(ligneTicket);
                break;


          /*  case 2:
                renderedView = renderLigneTicketName(ligneTicket);
                break;*/



        }

        return renderedView;
    }

    @Override
    public View getLongPressCellView(int rowIndex, int columnIndex, ViewGroup parentView) {

        return getDefaultCellView(rowIndex, columnIndex, parentView);
    }


    private View renderLigneTicketName(final LigneBonCommande ligneTicket) {
        final TextView textView = new TextView(getContext());
         String ligneTicketNameString="";
        if(ligneTicket.getArticle()!=null){
            ligneTicketNameString = StringUtils.isEmptyString(ligneTicket.getArticle().getaRTDesignation()) ? ligneTicket.getArticle().getmARDesignation() : ligneTicket.getArticle().getaRTDesignation();
        }
        else {
            ligneTicketNameString = ligneTicket.getLGDEVCodeArt();
        }

        // ligneTicket.getArticle() always null
        //   final String ligneTicketNameString = StringUtils.isEmptyString(ligneTicket.getArticle().getaRTDesignation()) ? ligneTicket.getArticle().getmARDesignation() : ligneTicket.getArticle().getaRTDesignation();

      //  final String ligneTicketNameString = App.database.articleDAO().getOneByCode(ligneTicket.getLGDEVCodeArt()).aRTDesignation;

        textView.setText(ligneTicketNameString);
        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        textView.setHeight(140);
        textView.setGravity(Gravity.CENTER_VERTICAL);
        return textView;
    }

    private View renderQuantity(final LigneBonCommande ligneTicket) {
        final TextView textView = new TextView(getContext());

        final String quantityString = String.valueOf(StringUtils.decimalFormat(Double.parseDouble(ligneTicket.getLGDEVQte())));

        textView.setText(quantityString);
        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        textView.setHeight(140);
        textView.setGravity(Gravity.CENTER_VERTICAL);
        return textView;
    }

    private View renderNumSerie(final LigneBonCommande ligneTicket) {
        final TextView textView = new TextView(getContext());
        final String ligneTicketNameString = StringUtils.isEmptyString(ligneTicket.getArticle().getaRTDesignation()) ? ligneTicket.getArticle().getmARDesignation() : ligneTicket.getArticle().getaRTDesignation();

        String str= ligneTicket.lGDevNumSerie;

        if(ligneTicket.getMsgLigne()!=null)
        if (!ligneTicket.getMsgLigne().equals("")) str= str+" ("+ ligneTicket.getMsgLigne() +")";


        textView.setText(str);
        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        textView.setHeight(160);
        textView.setGravity(Gravity.CENTER_VERTICAL);
        return textView;
    }

}

