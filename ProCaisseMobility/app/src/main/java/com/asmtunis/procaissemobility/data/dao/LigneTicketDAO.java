package com.asmtunis.procaissemobility.data.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.asmtunis.procaissemobility.data.models.LigneTicket;

import java.util.List;

/**
 * Created by PC on 11/18/2017.
 */
@Dao
public interface LigneTicketDAO {

    @Query("SELECT * FROM LigneTicket")
    List<LigneTicket> getAll();

    @Query("SELECT * FROM LigneTicket WHERE LT_NumTicket = :lt_NumTicket and LT_IdCarnet= :LT_IdCarnet and LT_Exerc = :LT_Exerc")
    List<LigneTicket> getByTicket(int lt_NumTicket,String LT_IdCarnet,String LT_Exerc);


   // @Query("SELECT * FROM LigneTicket WHERE LT_NumTicket_M = :lt_NumTicket and LT_IdCarnet= :LT_IdCarnet and LT_Exerc = :LT_Exerc")
   // List<LigneTicket> getByTicketM(String lt_NumTicket,String LT_IdCarnet,String LT_Exerc);

      @Query("SELECT * FROM LigneTicket WHERE LT_NumTicket_M = :lt_NumTicket ")
      List<LigneTicket> getByTicketM(String lt_NumTicket);


    @Query("SELECT sum(LT_MtTTC) FROM LigneTicket WHERE LT_NumTicket = :lt_NumTicket and LT_Exerc=:exercice")
    double getSumPriceByNumTicket(int lt_NumTicket, String exercice);

    @Query("SELECT * FROM LigneTicket LIMIT 1")
    LigneTicket getOne();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(LigneTicket item);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<LigneTicket> items);

    @Query("Update  LigneTicket set  LT_NumTicket=:newCode where LT_NumTicket=:oldCode and LT_Exerc=:exercice and LT_IdCarnet=:carnet")
    void updateNumTicket(int oldCode, int newCode,String exercice,String carnet);

   // @Query("DELETE FROM LigneTicket where Status='SELECTED'")
   // void deleteAll();

    @Query("DELETE FROM LigneTicket")
    void deleteAll();

    @Query("DELETE FROM LigneTicket where LT_NumTicket=:numTicket and LT_IdCarnet= :LT_IdCarnet and LT_Exerc = :LT_Exerc")
    void deleteByTicket(int numTicket, String LT_IdCarnet, String LT_Exerc);

    @Query("DELETE FROM LigneTicket where LT_NumTicket_M=:numTicket and LT_Exerc = :LT_Exerc  and isSync=0")
    void deleteByTicketM(String numTicket,  String LT_Exerc);



}
