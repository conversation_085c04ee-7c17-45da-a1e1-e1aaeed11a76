package com.asmtunis.procaissemobility.adapters.items;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;
import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.data.models.custom.SliderItem;
import com.makeramen.roundedimageview.RoundedImageView;

import java.util.List;

public class SliderAdapter extends RecyclerView.Adapter<SliderAdapter.SliderViewHolder> {
    private List<SliderItem> sliderItems;
    private ViewPager2 viewPager2;
    @NonNull
    @Override
    public SliderViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new SliderViewHolder(LayoutInflater.from(parent.getContext()).inflate(
                R.layout.slide_item_container,
                parent,
                false)
        );
    }

    public  SliderAdapter( ViewPager2 viewPager2) {
        this.viewPager2 = viewPager2;

    }

    public void setSliderItems(List<SliderItem> sliderItems) {
        this.sliderItems = sliderItems;
    }

    @Override
    public void onBindViewHolder(@NonNull SliderViewHolder holder, int position) {
       holder.setImageView(sliderItems.get(position));
       if(position==sliderItems.size()-2){
           viewPager2.post(runnable);
       }
    }

    @Override
    public int getItemCount() {
        return sliderItems.size();
    }

    class SliderViewHolder extends RecyclerView.ViewHolder{

        private RoundedImageView imageView;

        public SliderViewHolder(@NonNull View itemView) {
            super(itemView);
            this.imageView = itemView.findViewById(R.id.imageSlide);
        }
        void setImageView(SliderItem sliderItem ){
            imageView.setImageBitmap(sliderItem.getImage());
        }
    }
    private Runnable runnable = new Runnable() {
        @Override
        public void run() {
          sliderItems.addAll(sliderItems);
          notifyDataSetChanged();
        }
    };
}
