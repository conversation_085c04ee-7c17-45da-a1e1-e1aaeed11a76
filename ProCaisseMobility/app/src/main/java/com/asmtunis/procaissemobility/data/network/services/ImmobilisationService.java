package com.asmtunis.procaissemobility.data.network.services;

import com.asmtunis.procaissemobility.data.models.Immobilisation;
import com.asmtunis.procaissemobility.data.models.GenericObject;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Headers;
import retrofit2.http.POST;


public interface ImmobilisationService {
        @Headers("User-Agent: android-api-client")
        @POST("getImmobilisation")
        Call<List<Immobilisation>> getImmobilisation(@Body GenericObject genericObject);

    }
