package com.asmtunis.procaissemobility.data.network.services;

import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.OrdreWithLines;
import java.util.List;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

public interface OrdreMissionService {
    @POST("displayordre")
    Call<List<OrdreWithLines>> getOrdreMissionWithLines(@Body GenericObject genericObject);

    @POST("updateetatordremission")
    Call<Boolean> updateLigneOrdreMission(@Body GenericObject genericObject);

    @POST("AddOrdreMission")
    Call<Boolean> addOrdreMission(@Body GenericObject genericObject);

    @POST("BatchUpdateOrdreMission")
    Call<Boolean> batchUpdateLigneOrdreMission(@Body GenericObject genericObject);

}
