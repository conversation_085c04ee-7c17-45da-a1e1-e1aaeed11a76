package com.asmtunis.procaissemobility.data.models;

import com.asmtunis.procaissemobility.helper.utils.ChequeCaisseList;
import com.asmtunis.procaissemobility.helper.utils.TraiteCaisseList;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

/**
 * Created by PC on 10/12/2017.
 */

public class TicketWithLinesAndPayments extends BaseModel implements Serializable {
    @SerializedName("ticket")
    @Expose
    Ticket ticket;
    @SerializedName("lignesTicket")
    @Expose
    List<LigneTicket> ligneTicket;

    @SerializedName("cheques")
    @Expose
    List<ChequeCaisse> cheques;

    @SerializedName("traites")
    @Expose
    List<TraiteCaisse> traites;

    @SerializedName("reglement")
    @Expose
    ReglementCaisse reglement;





    public TicketWithLinesAndPayments() {
    }



    public TicketWithLinesAndPayments(TicketWithLines ticketWithLines, ReglementCaisse reglement, List<ChequeCaisse> cheques, List<TraiteCaisse> traites) {
        if (ticketWithLines.getTicket()!=null)
        {this.ticket = ticketWithLines.getTicket();}
        if (ticketWithLines.getLigneTicket()!=null) {
            this.ligneTicket = ticketWithLines.getLigneTicket();
        }
        this.cheques = cheques;
        this.traites = traites;
        this.reglement = reglement;
    }

    public TicketWithLinesAndPayments(ReglementCaisse reglement, List<ChequeCaisse> cheques, List<TraiteCaisse> traites) {
        this.cheques = cheques;
        this.traites = traites;
        this.reglement = reglement;
    }



    public Ticket getTicket() {
        return ticket;
    }

    public void setTicket(Ticket ticket) {
        this.ticket = ticket;
    }

    public List<LigneTicket> getLigneTicket() {
        return ligneTicket;
    }

    public void setLigneTicket(List<LigneTicket> ligneTicket) {
        this.ligneTicket = ligneTicket;
    }

    public List<ChequeCaisse> getCheques() {
        return cheques;
    }

    public void setCheques(List<ChequeCaisse> cheques) {
        this.cheques = cheques;
    }

    public List<TraiteCaisse> getTraites() {
        return traites;
    }

    public void setTraites(List<TraiteCaisse> traites) {
        this.traites = traites;
    }

    public ReglementCaisse getReglement() {
        return reglement;
    }

    public void setReglement(ReglementCaisse reglement) {
        this.reglement = reglement;
    }


    @Override
    public String toString() {
        return "TicketWithLinesAndPayments{" +
                "ticket=" + ticket +
                ", ligneTicket=" + ligneTicket +
                ", cheques=" + cheques +
                ", traites=" + traites +
                ", reglement=" + reglement +
                '}';
    }
}
