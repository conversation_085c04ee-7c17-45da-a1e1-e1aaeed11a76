package com.asmtunis.procaissemobility.data.models;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.annotation.NonNull;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * Created by PC on 31/01/2018.
 */

@Entity
public class Devise {
    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "Devise")
    @SerializedName("Devise")
    @Expose
    private String devise;
    @ColumnInfo(name = "Unite")
    @SerializedName("Unite")
    @Expose
    private String unite;
    @ColumnInfo(name = "Symbole")
    @SerializedName("Symbole")
    @Expose
    private String symbole;
    @ColumnInfo(name = "Principale")
    @SerializedName("Principale")
    @Expose
    private String principale;
    @ColumnInfo(name = "Cours")
    @SerializedName("Cours")
    @Expose
    private String cours;
    @ColumnInfo(name = "Activite")
    @SerializedName("Activite")
    @Expose
    private String activite;
    @ColumnInfo(name = "Nbre_Chiffre_virgule")
    @SerializedName("Nbre_Chiffre_virgule")
    @Expose
    private int nbreChiffreVirgule;

    public Devise() {
    }

    public String getDevise() {
        return devise;
    }

    public void setDevise(String devise) {
        this.devise = devise;
    }

    public String getUnite() {
        return unite;
    }

    public void setUnite(String unite) {
        this.unite = unite;
    }

    public String getSymbole() {
        return symbole;
    }

    public void setSymbole(String symbole) {
        this.symbole = symbole;
    }

    public String getPrincipale() {
        return principale;
    }

    public void setPrincipale(String principale) {
        this.principale = principale;
    }

    public String getCours() {
        return cours;
    }

    public void setCours(String cours) {
        this.cours = cours;
    }

    public String getActivite() {
        return activite;
    }

    public void setActivite(String activite) {
        this.activite = activite;
    }

    public int getNbreChiffreVirgule() {
        return nbreChiffreVirgule;
    }

    public void setNbreChiffreVirgule(int nbreChiffreVirgule) {
        this.nbreChiffreVirgule = nbreChiffreVirgule;
    }

    @Override
    public String toString() {
        return "Devise{" +
                "devise='" + devise + '\'' +
                ", unite='" + unite + '\'' +
                ", symbole='" + symbole + '\'' +
                ", principale='" + principale + '\'' +
                ", cours='" + cours + '\'' +
                ", activite='" + activite + '\'' +
                ", nbreChiffreVirgule='" + nbreChiffreVirgule + '\'' +
                '}';
    }
}

