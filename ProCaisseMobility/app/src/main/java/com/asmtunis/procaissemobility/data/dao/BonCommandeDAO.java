package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.airbnb.lottie.L;
import com.asmtunis.procaissemobility.data.models.BonCommande;
import java.util.List;

@Dao
public interface BonCommandeDAO {
    @Query("SELECT * FROM BonCommande order by strftime('%Y-%m-%d %H-%M',DEV_Date) desc")
    List<BonCommande> getAll();
    @Query("SELECT * FROM BonCommande order by strftime('%Y-%m-%d %H-%M',DEV_Date) desc")
    LiveData<List<BonCommande>> getAllMutable();
    @Query("SELECT * FROM BonCommande where DEV_Station =:station and DEV_Etat LIKE :BCType order by strftime('%Y-%m-%d %H-%M',DEV_Date) desc")
    LiveData<List<BonCommande>> getByStationMutable(String  station, String BCType);


    @Query("SELECT * FROM BonCommande where DEV_Station =:station and (DEV_Etat =:BCType or DEV_Etat =:info) and DEV_info3 =:DEVinfo3 order by strftime('%Y-%m-%d %H-%M',DEV_Date) desc")
    LiveData<List<BonCommande>> getPatByStationAndTypeMutable(String  station, String BCType, String info, String DEVinfo3);


    @Query("SELECT * FROM BonCommande where DEV_Num= :codeCommande order by strftime('%Y-%m-%d %H-%M',DEV_Date) desc")
    List<BonCommande>  getByNumOrderedBydate(String codeCommande);


    @Query("SELECT * FROM BonCommande where isSync=0 and  (Status='INSERTED'  or Status='UPDATED') order by strftime('%Y-%m-%d %H-%M',DEV_Date)")
    List<BonCommande> getNoSynced();
    @Query("UPDATE BonCommande SET DEV_CodeClient = :code_client where DEV_CodeClient = :oldCodeClient")
    void updateCodeClient(String code_client, String oldCodeClient);



    @Query("SELECT ifnull(MAX(cast(substr(DEV_Num,length(:prefix) + 1 ,length('DEV_Num'))as integer)),0)+1 FROM   BonCommande WHERE substr(DEV_Num, 0 ,length(:prefix)+1) = :prefix")
    String getNewCode(String prefix);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(BonCommande item);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<BonCommande> items);

    @Query("SELECT count(*) FROM BonCommande where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    int getCountNonSync();

    @Query("SELECT count(*) FROM BonCommande")
    int getCount();

    @Query("SELECT count(*) FROM BonCommande where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    LiveData<Integer> getNoSyncCountMubtale();

    @Query("DELETE FROM BonCommande")
    void deleteAll();

    @Query("DELETE FROM BonCommande where DEV_Code_M= :codeCommande/* and BON_LIV_Exerc=:exercie*/")
    void deleteByIdM(String codeCommande/*,String exercie*/);

    @Query("SELECT *  FROM BonCommande where DEV_Code_M= :codeCommande and BON_LIV_Exerc=:exercie")
    BonCommande getListByCodeM(String codeCommande, String exercie);


    @Query("SELECT *  FROM BonCommande where DEV_Num= :codeCommande and BON_LIV_Exerc=:exercie")
    BonCommande getByCode(String codeCommande, String exercie);




    @Query("SELECT *  FROM BonCommande where DEV_CodeClient= :codeCommande and DEV_info3=:devinf3")
   List<BonCommande>  getByCodeClientandPatEtat(String codeCommande, String devinf3);



    @Query("SELECT *  FROM BonCommande where DEV_CodeClient= :codeCommande and DEV_info3=:devinf3")
    LiveData<List<BonCommande>>  getByCodeClientandPatEtatLiveData(String codeCommande, String devinf3);


    @Query("SELECT count(*) FROM BonCommande where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    Integer getNoSyncCountNonMubtale();

    @Query("SELECT count(*) FROM BonCommande where DEV_Station =:station AND DEV_Etat like 'BCC_client'")
    Integer getAllCountBySession(String station);

    @Query("SELECT count(*) FROM BonCommande where DEV_Station =:station")
    LiveData<Integer> getAllCountBySessionMutable(String station);


    @Query("UPDATE BonCommande SET DEV_Observation = :DEVObservation where DEV_Num = :DEV_Num  and BON_LIV_Exerc=:exercie")
    void updateObservation(String DEVObservation, String DEV_Num, String exercie);

}
