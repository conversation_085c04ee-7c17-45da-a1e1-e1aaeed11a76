package com.asmtunis.procaissemobility.data.viewModels;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelProviders;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.dao.StationStockDAO;
import com.asmtunis.procaissemobility.data.models.StationStock;

import java.util.List;

public class StationStockViewModel extends ViewModel {

    public StationStockDAO dao;
    private static StationStockViewModel instance;


    public static StationStockViewModel getInstance(Fragment activity) {
        if (instance == null)
            instance = new ViewModelProvider(activity).get(StationStockViewModel.class);
        instance.dao = App.database.stationStockDAO();
        return instance;
    }
    public static StationStockViewModel getInstance(FragmentActivity activity) {
        if (instance == null)
            instance = new ViewModelProvider(activity).get(StationStockViewModel.class);
        instance.dao = App.database.stationStockDAO();
        return instance;
    }

    public  LiveData<List<StationStock>> getByCodeStation(String station){
        return  dao.getByCodeStationMutable(station);
    }
    public  LiveData<List<StationStock>> getByCodeStationNonNullQuantity(String station){
        return  dao.getByCodeStationNonNullQuantityMutable(station);
    }

}
