package com.asmtunis.procaissemobility.data.network.datamanager;


import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Ticket;
import com.asmtunis.procaissemobility.data.models.TicketUpdate;
import com.asmtunis.procaissemobility.data.models.TicketWithLines;
import com.asmtunis.procaissemobility.data.models.TicketWithLinesAndPayments;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.TicketService;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;

import java.util.List;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

/**
 * Created by PC on 10/4/2017.
 */

public class TicketDataManager {
    private static TicketDataManager sInstance;

    private final TicketService mTicketService;

    public TicketDataManager() {
        mTicketService = new ServiceFactory<>(TicketService.class, String.format(BASE_URL, PrefUtils.getServerIPAddress(), PrefUtils.getServerPort(),"Ticket")).makeService();
    }

    public static TicketDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new TicketDataManager();
        }
        return sInstance;
    }

    public void getMaxTikNum(GenericObject genericObject,
                                     RemoteCallback<Integer> listener) {
        mTicketService.getMaxNumTicket(genericObject, App.prefUtils.getExercice(), App.prefUtils.getCarnetId())
                .enqueue(listener);
    }

    public void getTicketsByCaisseId(GenericObject genericObject, RemoteCallback<List<Ticket>> listener, Boolean archive, boolean zone) {
        String ddm = App.database.ticketDAO().getDDM();
        mTicketService.getTicketsByCaisseId(genericObject, ddm, App.prefUtils.getExercice(), archive, zone)
                .enqueue(listener);
    }

    public void addBatchTicketWithLignesTicketAndPayment(GenericObject ticketWithLinesAndPayments,
                                                          RemoteCallback<List<TicketUpdate>> listener) {

        mTicketService.addBatchTicketWithLignesTicketAndPayment(ticketWithLinesAndPayments, App.prefUtils.getAutoFactureEnabled())
                .enqueue(listener);
    }
}

