package com.asmtunis.procaissemobility.data.network.services;

import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.LigneBonRetour;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

public interface LigneBonRetourService {
    @POST("getLigneRetour")
    Call<List<LigneBonRetour>> getLigneBonRetours(@Body GenericObject genericObject);

    @POST("addBatchLigneRetour")
    Call<List<LigneBonRetour>> addBatchLigneBonRetours(@Body GenericObject genericObject);
}
