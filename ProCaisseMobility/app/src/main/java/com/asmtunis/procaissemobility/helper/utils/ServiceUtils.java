package com.asmtunis.procaissemobility.helper.utils;

import android.content.ComponentName;
import android.content.ServiceConnection;
import android.os.IBinder;

import com.asmtunis.procaissemobility.listener.MapObserverListener;
import com.asmtunis.procaissemobility.services.LocationUpdatesService;

public class ServiceUtils {
    // A reference to the service used to get location updates.
    public static LocationUpdatesService mService = null;
    public static MapObserverListener mapObserverListener;
    // Tracks the bound state of the service.
    private static boolean mBound = false;
    // Monitors the state of the connection to the service.
    public static boolean destroyed = false;
    public static final ServiceConnection mServiceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            LocationUpdatesService.LocalBinder binder = (LocationUpdatesService.LocalBinder) service;
            mService = binder.getService();
            mService.requestLocationUpdates(mapObserverListener);
            mBound = true;
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            mService = null;
            mBound = false;
        }
    };
}
