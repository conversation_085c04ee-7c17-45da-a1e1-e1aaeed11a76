package com.asmtunis.procaissemobility;

import static com.asmtunis.procaissemobility.helper.Globals.DEFAULT_VALUE;

import android.app.Application;
import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.multidex.MultiDex;

import com.asmtunis.procaissemobility.data.Database;
import com.asmtunis.procaissemobility.helper.DataSyncHelper;
import com.asmtunis.procaissemobility.helper.DeviceNameGetter;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ObjectUtils;
import com.blankj.utilcode.util.Utils;

import org.polaric.colorful.Colorful;

import io.paperdb.Paper;

/**
 * Created by PC on 9/29/2017.
 */

public class App extends Application {

    private static App instance;
    public static Database database;
    public static PrefUtils prefUtils;
    public double latitude = -33.85416325;
    public double longitude = 151.20916;

    @NonNull
    public static synchronized App getInstance() {
        return instance;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        instance = this;
        Context context = this;
        init();
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        MultiDex.install(this);
    }

    @Override
    public void onTerminate() {
        super.onTerminate();
        database.close();
        Database.destroyInstance();
    }

    private void init() {
        Paper.init(this);
        Utils.init(this);
        initDatabase();
        setupTheme();
        prefUtils = PrefUtils.getInstance(getApplicationContext());
        DeviceNameGetter.getInstance().loadDevice();
      //  observeForNewObject();
    }



    void initDatabase() {
        database = Database.getInstance(getApplicationContext());
    }

    public void setupTheme() {
        Colorful.defaults()
                .primaryColor(Colorful.ThemeColor.TEAL)
                .accentColor(Colorful.ThemeColor.TEAL)
                .translucent(true)
                .dark(false);
        Colorful.init(this);
    }

}