package com.asmtunis.procaissemobility.adapters.tables;

import android.content.Context;
import android.graphics.Typeface;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.data.models.LigneBonRetour;
import com.asmtunis.procaissemobility.data.models.LigneTicket;
import com.asmtunis.procaissemobility.helper.utils.Calculator;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.ui.components.SortableLigneRetourTableView;

import java.text.NumberFormat;
import java.util.List;

import de.codecrafters.tableview.toolkit.LongPressAwareTableDataAdapter;

/**
 * ²
 */

public class LigneBonRetourTableDataAdapter extends LongPressAwareTableDataAdapter<LigneBonRetour> {

    private static final int TEXT_SIZE = 16;
    private static final NumberFormat PRICE_FORMATTER = NumberFormat.getNumberInstance();

    public LigneBonRetourTableDataAdapter(final Context context, final List<LigneBonRetour> data , final SortableLigneRetourTableView tableView) {
        super(context, data, tableView);
    }

    @Override
    public View getDefaultCellView(int rowIndex, int columnIndex, ViewGroup parentView) {
        final LigneBonRetour ligneTicket = getRowData(rowIndex);
        View renderedView = null;

        switch (columnIndex) {
            case 0:
                renderedView = renderQuantity(ligneTicket);
                break;

            case 1:
               renderedView = renderLigneTicketName(ligneTicket);
                break;

            case 2:
                renderedView = renderUnitPrice(ligneTicket);
                break;

            case 3:

                renderedView =  renderPriceWithDiscount(ligneTicket);
                break;

        }

        return renderedView;
    }

    @Override
    public View getLongPressCellView(int rowIndex, int columnIndex, ViewGroup parentView) {

        return getDefaultCellView(rowIndex, columnIndex, parentView);
    }


    private View renderLigneTicketName(final LigneBonRetour ligneTicket) {
        final TextView textView = new TextView(getContext());
        final String ligneTicketNameString = StringUtils.isEmptyString(ligneTicket.getArticle().getaRTDesignation()) ?ligneTicket.getArticle().getmARDesignation():ligneTicket.getArticle().getaRTDesignation();
        textView.setText(ligneTicketNameString);
        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        textView.setHeight(140);
        textView.setGravity(Gravity.CENTER_VERTICAL);
        return textView;
    }

    private View renderQuantity(final LigneBonRetour ligneTicket) {
        final TextView textView = new TextView(getContext());

        final String quantityString = String.valueOf(StringUtils.decimalFormat(Double.parseDouble(ligneTicket.getLIGBonEntreeQte())));

        textView.setText(quantityString);
        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        textView.setHeight(140);
        textView.setGravity(Gravity.CENTER_VERTICAL);
        return textView;
    }


    private View renderUnitPrice(final LigneBonRetour ligneTicket) {
        final TextView textView = new TextView(getContext());

        final String priceString = StringUtils.priceFormat(ligneTicket.getArticle().getPvttc());

        textView.setText(priceString);
        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        textView.setHeight(140);
        textView.setGravity(Gravity.CENTER_VERTICAL);
        double amountTTC  = Double.parseDouble(ligneTicket.getLIGBonEntreeMntTTC());
        if (amountTTC > 50000) {
            textView.setTextColor(ContextCompat.getColor(getContext(), R.color.material_green700));
        } else if (amountTTC > 100000) {
            textView.setTextColor(ContextCompat.getColor(getContext(), R.color.material_red600));
        }

        return textView;
    }


    private View renderPriceWithDiscount(final LigneBonRetour ligneTicket) {
        final TextView textView = new TextView(getContext());
        ligneTicket.setLIGBonEntreeMntTTC(( Double.parseDouble(ligneTicket.getLIGBonEntreeQte()) * Calculator.calculateAmountTTCNet(ligneTicket.getArticle().getPvttc(),
                Double.parseDouble(ligneTicket.getLIGBonEntreeRemise())))+"");
        final String priceString =  StringUtils.priceFormat(Double.parseDouble(ligneTicket.getLIGBonEntreeMntTTC()));
        textView.setText(priceString);
        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        textView.setTypeface(textView.getTypeface(), Typeface.BOLD);
        textView.setHeight(140);
        textView.setGravity(Gravity.CENTER_VERTICAL);
        if (Double.parseDouble(ligneTicket.getLIGBonEntreeRemise()) > 0) {
            textView.setTextColor(ContextCompat.getColor(getContext(), R.color.md_blue_grey_800));

        }

        return textView;
    }


    private View renderDiscount(final LigneBonRetour ligneTicket) {
        final TextView textView = new TextView(getContext());

        final String priceString = PRICE_FORMATTER.format(ligneTicket.getLIGBonEntreeRemise()) + " %";

        textView.setText(priceString);
        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        textView.setHeight(140);
        textView.setGravity(Gravity.CENTER_VERTICAL);
        if (Double.parseDouble(ligneTicket.getLIGBonEntreeMntTTC()) > 0) {
            textView.setTextColor(ContextCompat.getColor(getContext(), R.color.material_green600));
        }

        return textView;
    }


    private View renderString(final String value) {
        final TextView textView = new TextView(getContext());
        textView.setText(value);
        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        textView.setHeight(140);
        textView.setGravity(Gravity.CENTER_VERTICAL);
        return textView;
    }

    private static class LigneTicketNameUpdater implements TextWatcher {

        private LigneTicket ligneTicketToUpdate;

        public LigneTicketNameUpdater(LigneTicket ligneTicketToUpdate) {
            this.ligneTicketToUpdate = ligneTicketToUpdate;
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            // no used
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            // not used
        }

        @Override
        public void afterTextChanged(Editable s) {
            //    ligneTicketToUpdate.setName(s.toString());
        }
    }



}
