package com.asmtunis.procaissemobility.comparators;

import com.asmtunis.procaissemobility.data.models.LigneBonCommande;

import java.util.Comparator;

/**
 * Created by me on 10/8/2017.
 */


public final class LigneBonCommandeComparators {

    private LigneBonCommandeComparators() {
    }
    public static LigneBonCommandeComparators.QuantityComparator getLigneTicketQuantityComparator() {
        return new LigneBonCommandeComparators.QuantityComparator();
    }

    public static DiscountComparator getLigneTicketDiscountComparator() {
        return new DiscountComparator();
    }

    public static NameComparator getLigneTicketNameComparator() {
        return new NameComparator();
    }

    public static LigneBonCommandeComparators.LigneTicketUnitPriceComparator getLigneTicketUnitPriceComparator() {
        return new LigneBonCommandeComparators.LigneTicketUnitPriceComparator();
    }

    public static PriceComparator getLigneTicketPriceComparator() {
        return new PriceComparator();
    }

    private static class NameComparator implements Comparator<LigneBonCommande> {

        @Override
        public int compare(final LigneBonCommande ligneTicket1, final LigneBonCommande ligneTicket2) {
            return ligneTicket1.getArticle().getaRTDesignation().toLowerCase().compareTo(ligneTicket2.getArticle().getaRTDesignation().toLowerCase());
        }
    }

    private static class QuantityComparator implements Comparator<LigneBonCommande> {

        @Override
        public int compare(final LigneBonCommande ligneTicket1, final LigneBonCommande ligneTicket2) {
            return (Double.parseDouble(ligneTicket1.getLGDEVQte()) < Double.parseDouble(
                    ligneTicket2.getLGDEVQte()))?-1:1;
        }
    }

    private static class PriceComparator implements Comparator<LigneBonCommande> {

        @Override
        public int compare(final LigneBonCommande ligneTicket1, final LigneBonCommande ligneTicket2) {
            return(Double.parseDouble(ligneTicket1.getLGDEVMntTTC()) < Double.parseDouble(ligneTicket2.getLGDEVMntTTC()))? -1 : 1;
        }
    }

    private static class DiscountComparator implements Comparator<LigneBonCommande> {

        @Override
        public int compare(final LigneBonCommande ligneTicket1, final LigneBonCommande ligneTicket2) {
            return Double.parseDouble(ligneTicket1.getLGDEVRemise()) <Double.parseDouble( ligneTicket2.getLGDEVRemise()) ?-1:1;

        }
    }

    private static class LigneTicketUnitPriceComparator implements Comparator<LigneBonCommande> {

        @Override
        public int compare(final LigneBonCommande ligneTicket1, final LigneBonCommande ligneTicket2) {
            if (ligneTicket1.getArticle().getaRTPrixUnitaireHT() < ligneTicket2.getArticle().getaRTPrixUnitaireHT())
                return -1;
            if (ligneTicket1.getArticle().getaRTPrixUnitaireHT() > ligneTicket2.getArticle().getaRTPrixUnitaireHT())
                return 1;
            return 0;
        }
    }

}