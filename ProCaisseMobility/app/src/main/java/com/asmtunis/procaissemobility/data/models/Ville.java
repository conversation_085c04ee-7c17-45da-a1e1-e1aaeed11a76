package com.asmtunis.procaissemobility.data.models;

/**
 * Created by Oussama AZIZI on 3/22/22.
 */

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

@Entity
public class Ville {

    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "Carte_Fidelite")
    @SerializedName("Carte_Fidelite")
    @Expose
    private String carteFidelite;
    @SerializedName("Is_Tacktil")
    @Expose
    private String isTacktil;
    @SerializedName("D_Code_Article")
    @Expose
    private String dCodeArticle;
    @SerializedName("F_Code_Article")
    @Expose
    private String fCodeArticle;
    @SerializedName("D_Poid_Article")
    @Expose
    private String dPoidArticle;
    @SerializedName("F_Poid_Article")
    @Expose
    private String fPoidArticle;
    @SerializedName("D_Prix_Article")
    @Expose
    private String dPrixArticle;
    @SerializedName("F_Prix_Article")
    @Expose
    private String fPrixArticle;
    @SerializedName("MP_Modification")
    @Expose
    private String mPModification;
    @SerializedName("Deux_Ecran")
    @Expose
    private String deuxEcran;
    @SerializedName("is_Commande")
    @Expose
    private String isCommande;
    @SerializedName("is_Direct")
    @Expose
    private String isDirect;
    @SerializedName("is_Article_Session")
    @Expose
    private String isArticleSession;
    @SerializedName("is_MP_Caisse")
    @Expose
    private String isMPCaisse;
    @SerializedName("is_calc_monnaie")
    @Expose
    private String isCalcMonnaie;
    @SerializedName("is_2vente")
    @Expose
    private String is2vente;
    @SerializedName("is_promotion_nb_art")
    @Expose
    private String isPromotionNbArt;
    @SerializedName("is_prix_gros")
    @Expose
    private String isPrixGros;
    @SerializedName("Mode_Ticket")
    @Expose
    private String modeTicket;
    @SerializedName("Mode_Ticket_Solde")
    @Expose
    private String modeTicketSolde;
    @SerializedName("balance")
    @Expose
    private String balance;
    @SerializedName("Transfert")
    @Expose
    private String transfert;
    @SerializedName("Caisse_sans_Ticket")
    @Expose
    private String caisseSansTicket;
    @SerializedName("Mp_Supprimer_Article")
    @Expose
    private String mpSupprimerArticle;
    @SerializedName("MP_Liste_Ticket")
    @Expose
    private String mPListeTicket;
    @SerializedName("MP_En_Instance")
    @Expose
    private String mPEnInstance;
    @SerializedName("Stock_Article_Caisse")
    @Expose
    private String stockArticleCaisse;
    @SerializedName("Article_Inactif")
    @Expose
    private String articleInactif;
    @SerializedName("Facturation")
    @Expose
    private String facturation;
    @SerializedName("MP_Modif_Qte")
    @Expose
    private String mPModifQte;
    @SerializedName("Gestion_Inventaire")
    @Expose
    private String gestionInventaire;
    @SerializedName("Bon_retour")
    @Expose
    private String bonRetour;
    @SerializedName("Carte_Fidelite_Point")
    @Expose
    private String carteFidelitePoint;
    @SerializedName("Aff_RFid_Ticket")
    @Expose
    private String affRFidTicket;
    @SerializedName("BotArticleTaktil")
    @Expose
    private String botArticleTaktil;
    @SerializedName("BotfamilleTaktil")
    @Expose
    private String botfamilleTaktil;
    @SerializedName("Librerie")
    @Expose
    private String librerie;
    @SerializedName("GridStock")
    @Expose
    private String gridStock;
    @SerializedName("GridRupture")
    @Expose
    private String gridRupture;
    @SerializedName("GridCA_Aricle")
    @Expose
    private String gridCAAricle;
    @SerializedName("GridCA_Client")
    @Expose
    private String gridCAClient;
    @SerializedName("GridCA_MargeFamille")
    @Expose
    private String gridCAMargeFamille;
    @SerializedName("GridCA_MargeFournisseur")
    @Expose
    private String gridCAMargeFournisseur;
    @SerializedName("GridCA_MargeFamille2")
    @Expose
    private String gridCAMargeFamille2;
    @SerializedName("GridCA_MargeMarque")
    @Expose
    private String gridCAMargeMarque;
    @SerializedName("Grid_AvoirArticle")
    @Expose
    private String gridAvoirArticle;
    @SerializedName("Grid_BonComArticle")
    @Expose
    private String gridBonComArticle;
    @SerializedName("Ordre_Fam_Alphabetique")
    @Expose
    private String ordreFamAlphabetique;
    @SerializedName("Version_Light")
    @Expose
    private String versionLight;
    @SerializedName("MP_Acompte_Annuler")
    @Expose
    private String mPAcompteAnnuler;
    @SerializedName("MP_Acompte_Depasse_Ticket_Annuler")
    @Expose
    private String mPAcompteDepasseTicketAnnuler;
    @SerializedName("Bot_Taktil_Fam_Frs")
    @Expose
    private String botTaktilFamFrs;
    @SerializedName("Vente_Credit")
    @Expose
    private String venteCredit;
    @SerializedName("StockDormant")
    @Expose
    private String stockDormant;
    @SerializedName("GridStockDormant")
    @Expose
    private String gridStockDormant;
    @SerializedName("MP_ClientCaisse")
    @Expose
    private String mPClientCaisse;
    @SerializedName("Import_Export")
    @Expose
    private String importExport;
    @SerializedName("Famille_Mere")
    @Expose
    private String familleMere;
    @SerializedName("Aff_PEnc_Caisse")
    @Expose
    private String affPEncCaisse;
    @SerializedName("Reg_Frs")
    @Expose
    private String regFrs;
    @SerializedName("Calcul_Inverse_Qte_EEnc")
    @Expose
    private String calculInverseQteEEnc;
    @SerializedName("Peremption")
    @Expose
    private String peremption;
    @SerializedName("Etat_Reg_Fact_Nadhir")
    @Expose
    private String etatRegFactNadhir;
    @SerializedName("Carte_prepayee")
    @Expose
    private String cartePrepayee;
    @SerializedName("Reg_PointMerci")
    @Expose
    private String regPointMerci;
    @SerializedName("Taux_Reduction_Carte_Resto")
    @Expose
    private String tauxReductionCarteResto;
    @SerializedName("Tiroir_RJ11")
    @Expose
    private String tiroirRJ11;
    @SerializedName("Reg_BonAchat")
    @Expose
    private String regBonAchat;
    @SerializedName("Garantie_Ticket")
    @Expose
    private String garantieTicket;
    @SerializedName("Transfert_DUX")
    @Expose
    private String transfertDUX;
    @SerializedName("Facturation_BL")
    @Expose
    private String facturationBL;
    @SerializedName("Facturation_BE")
    @Expose
    private String facturationBE;
    @SerializedName("MP_Remise_Ticket")
    @Expose
    private String mPRemiseTicket;
    @SerializedName("MP_Duplicata_Ticket")
    @Expose
    private String mPDuplicataTicket;
    @SerializedName("Impression_ModeReg_Ticket")
    @Expose
    private String impressionModeRegTicket;
    @SerializedName("Mp_Supprimer_Depence")
    @Expose
    private String mpSupprimerDepence;
    @SerializedName("Mp_Ouvrir_Tiroire")
    @Expose
    private String mpOuvrirTiroire;
    @SerializedName("MP_TRemise_LigArticle")
    @Expose
    private String mPTRemiseLigArticle;
    @SerializedName("Splash")
    @Expose
    private String splash;
    @SerializedName("Alert_Stock")
    @Expose
    private String alertStock;
    @SerializedName("FactureA5_Senegal")
    @Expose
    private String factureA5Senegal;
    @SerializedName("L_Reg_Frs")
    @Expose
    private String lRegFrs;
    @SerializedName("L_Vente_Credit")
    @Expose
    private String lVenteCredit;
    @SerializedName("L_balance")
    @Expose
    private String lBalance;
    @SerializedName("L_Facturation")
    @Expose
    private String lFacturation;
    @SerializedName("L_Carte_prepayee")
    @Expose
    private String lCartePrepayee;
    @SerializedName("RG_Ticket_TTC")
    @Expose
    private String rGTicketTTC;
    @SerializedName("Gestion_dechets")
    @Expose
    private String gestionDechets;
    @SerializedName("Mp_Gestion_dechets")
    @Expose
    private String mpGestionDechets;
    @SerializedName("Ticket_Cadeau")
    @Expose
    private String ticketCadeau;
    @SerializedName("MP_Ticket_Cadeau")
    @Expose
    private String mPTicketCadeau;
    @SerializedName("poste_preparation")
    @Expose
    private String postePreparation;
    @SerializedName("MP_Liste_Commande")
    @Expose
    private String mPListeCommande;
    @SerializedName("Max_TauxRemise")
    @Expose
    private String maxTauxRemise;
    @SerializedName("MP_Clot_Caisse")
    @Expose
    private String mPClotCaisse;
    @SerializedName("Commentaire_Ticket")
    @Expose
    private String commentaireTicket;
    @SerializedName("Vente_WebService")
    @Expose
    private String venteWebService;
    @SerializedName("Point_Fidelite")
    @Expose
    private String pointFidelite;
    @SerializedName("Numero_Tallon")
    @Expose
    private String numeroTallon;
    @SerializedName("SousFamille")
    @Expose
    private String sousFamille;
    @SerializedName("WS_commande")
    @Expose
    private String wSCommande;
    @SerializedName("WS_commande_service")
    @Expose
    private String wSCommandeService;
    @SerializedName("CommandeA4")
    @Expose
    private String commandeA4;
    @SerializedName("NbrImpCom")
    @Expose
    private String nbrImpCom;
    @SerializedName("RegEsp_DemImp")
    @Expose
    private String regEspDemImp;
    @SerializedName("Fusionligneticket")
    @Expose
    private String fusionligneticket;
    @SerializedName("Numero_serie")
    @Expose
    private String numeroSerie;
    @SerializedName("Supplement")
    @Expose
    private String supplement;
    @SerializedName("Gestion_Tier")
    @Expose
    private String gestionTier;
    @SerializedName("Gestion_bancaire")
    @Expose
    private String gestionBancaire;
    @SerializedName("TB_Article")
    @Expose
    private String tBArticle;
    @SerializedName("TB_Frs")
    @Expose
    private String tBFrs;
    @SerializedName("TB_Client")
    @Expose
    private String tBClient;
    @SerializedName("Mailing")
    @Expose
    private String mailing;
    @SerializedName("Carnet_Auto")
    @Expose
    private String carnetAuto;
    @SerializedName("Distributeur")
    @Expose
    private String distributeur;
    @SerializedName("Esthetique")
    @Expose
    private String esthetique;
    @SerializedName("Wait_Esthetique")
    @Expose
    private String waitEsthetique;
    @SerializedName("BalanceCom")
    @Expose
    private String balanceCom;
    @SerializedName("EmploiPrestataire")
    @Expose
    private String emploiPrestataire;
    @SerializedName("CalandAuto")
    @Expose
    private String calandAuto;
    @SerializedName("Merchandiser")
    @Expose
    private String merchandiser;
    @SerializedName("SMS")
    @Expose
    private String sms;
    @SerializedName("RegEsp_AvecImp")
    @Expose
    private String regEspAvecImp;
    @SerializedName("RegEsp_SansImp")
    @Expose
    private String regEspSansImp;
    @SerializedName("Forcement_MAJ")
    @Expose
    private String forcementMAJ;
    @SerializedName("Clot_CaisseAuto")
    @Expose
    private String clotCaisseAuto;
    @SerializedName("PPrepParProduit")
    @Expose
    private String pPrepParProduit;
    @SerializedName("MP_Clotu_Caisse")
    @Expose
    private String mPClotuCaisse;
    @SerializedName("CBParTalon")
    @Expose
    private String cBParTalon;
    @SerializedName("Commercial_Oblig")
    @Expose
    private String commercialOblig;
    @SerializedName("Commercial_ObligMP")
    @Expose
    private String commercialObligMP;
    @SerializedName("Detruite_Serv_Pr")
    @Expose
    private String detruiteServPr;
    @SerializedName("Article_Poid_Qte")
    @Expose
    private String articlePoidQte;
    @SerializedName("ValiditeBAE")
    @Expose
    private String validiteBAE;
    @SerializedName("BARecu_TchC")
    @Expose
    private String bARecuTchC;
    @SerializedName("Talon_ServiceArticle")
    @Expose
    private String talonServiceArticle;
    @SerializedName("Arrond_MntTick")
    @Expose
    private String arrondMntTick;
    @SerializedName("Remise_DetaTick")
    @Expose
    private String remiseDetaTick;
    @SerializedName("Blocage_OvrSession")
    @Expose
    private String blocageOvrSession;
    @SerializedName("Ingredients")
    @Expose
    private String ingredients;
    @SerializedName("Tarif_Client")
    @Expose
    private String tarifClient;
    @SerializedName("NbrImpTicket")
    @Expose
    private String nbrImpTicket;
    @SerializedName("NoteTicket")
    @Expose
    private String noteTicket;
    @SerializedName("FreeShop")
    @Expose
    private String freeShop;
    @SerializedName("Notif_AnnivClt")
    @Expose
    private String notifAnnivClt;
    @SerializedName("Affichage_SF_Article")
    @Expose
    private String affichageSFArticle;
    @SerializedName("CodeArt_MSFF")
    @Expose
    private String codeArtMSFF;
    @SerializedName("Ticket_Obligatoire")
    @Expose
    private String ticketObligatoire;
    @SerializedName("Supp_Bt_DevalINV")
    @Expose
    private String suppBtDevalINV;
    @SerializedName("MNT_MinCheque")
    @Expose
    private String mNTMinCheque;
    @SerializedName("MNT_MinCB")
    @Expose
    private String mNTMinCB;
    @SerializedName("Article_Site")
    @Expose
    private String articleSite;
    @SerializedName("BlocStkNeg")
    @Expose
    private String blocStkNeg;
    @SerializedName("PrixG_Taux")
    @Expose
    private String prixGTaux;
    @SerializedName("Sans_PUTicket")
    @Expose
    private String sansPUTicket;
    @SerializedName("Crd_ET_Cmd")
    @Expose
    private String crdETCmd;
    @SerializedName("PieceEgPrix_PEM")
    @Expose
    private String pieceEgPrixPEM;
    @SerializedName("Armurerie")
    @Expose
    private String armurerie;
    @SerializedName("Supp_Bt_DechC")
    @Expose
    private String suppBtDechC;
    @SerializedName("Supp_Bt_DepC")
    @Expose
    private String suppBtDepC;
    @SerializedName("MP_Rec_Caisse")
    @Expose
    private String mPRecCaisse;
    @SerializedName("Prest_Imprimante")
    @Expose
    private String prestImprimante;
    @SerializedName("Dressing")
    @Expose
    private String dressing;
    @SerializedName("Boucherie")
    @Expose
    private String boucherie;
    @SerializedName("MP_Ret_Caisse")
    @Expose
    private String mPRetCaisse;
    @SerializedName("BCI_Nonvalor")
    @Expose
    private String bCINonvalor;
    @SerializedName("MP_Clt_Caisse")
    @Expose
    private String mPCltCaisse;
    @SerializedName("WS_Produit")
    @Expose
    private String wSProduit;
    @SerializedName("Devis")
    @Expose
    private String devis;
    @SerializedName("Facture_Frs")
    @Expose
    private String factureFrs;
    @SerializedName("Article_Ecommerce")
    @Expose
    private String articleEcommerce;
    @SerializedName("BSI")
    @Expose
    private String bsi;
    @SerializedName("MAJ_PVGrid")
    @Expose
    private String mAJPVGrid;
    @SerializedName("Porcelaine")
    @Expose
    private String porcelaine;
    @SerializedName("Tracabilite")
    @Expose
    private String tracabilite;
    @SerializedName("Libra_Poidauto")
    @Expose
    private String libraPoidauto;
    @SerializedName("Chemin_demande")
    @Expose
    private String cheminDemande;
    @SerializedName("Chemin_reponse")
    @Expose
    private String cheminReponse;
    @SerializedName("Chemin_ecoute")
    @Expose
    private String cheminEcoute;
    @SerializedName("FArticle")
    @Expose
    private String fArticle;
    @SerializedName("PromPerdProd")
    @Expose
    private String promPerdProd;
    @SerializedName("Libra_Type")
    @Expose
    private String libraType;
    @SerializedName("DF_DLC_Art")
    @Expose
    private String dFDLCArt;
    @SerializedName("Compta")
    @Expose
    private String compta;
    @SerializedName("BCWeb")
    @Expose
    private String bCWeb;
    @SerializedName("Chargement_BCW")
    @Expose
    private String chargementBCW;
    @SerializedName("PFact_Stat")
    @Expose
    private String pFactStat;
    @SerializedName("PBL_SUser")
    @Expose
    private String pBLSUser;
    @SerializedName("Edition")
    @Expose
    private String edition;
    @SerializedName("Sans_LogTicket")
    @Expose
    private String sansLogTicket;
    @SerializedName("Sans_VendTicket")
    @Expose
    private String sansVendTicket;
    @SerializedName("Imp_TVATicket")
    @Expose
    private String impTVATicket;
    @SerializedName("Drive")
    @Expose
    private String drive;
    @SerializedName("Dibal_Ticketauto")
    @Expose
    private String dibalTicketauto;
    @SerializedName("DibalChemin_rep")
    @Expose
    private String dibalCheminRep;
    @SerializedName("DibalChemin_rgi")
    @Expose
    private String dibalCheminRgi;
    @SerializedName("MP_Credit")
    @Expose
    private String mPCredit;
    @SerializedName("Commercial_sans")
    @Expose
    private String commercialSans;
    @SerializedName("isPiece_unite")
    @Expose
    private String isPieceUnite;
    @SerializedName("CreationAutoArt")
    @Expose
    private String creationAutoArt;
    @SerializedName("OuvertureAutoArt")
    @Expose
    private String ouvertureAutoArt;
    @SerializedName("WS_AutoArt")
    @Expose
    private String wSAutoArt;
    @SerializedName("MP_Supp_BL")
    @Expose
    private String mPSuppBL;
    @SerializedName("BL_Lot_PG")
    @Expose
    private String bLLotPG;
    @SerializedName("Flouci")
    @Expose
    private String flouci;
    @SerializedName("Mbre_Famille")
    @Expose
    private String mbreFamille;
    @SerializedName("Pointg_Tick")
    @Expose
    private String pointgTick;
    @SerializedName("MNT_MinRC")
    @Expose
    private String mNTMinRC;
    @SerializedName("Aff_CAPrest")
    @Expose
    private String affCAPrest;
    @SerializedName("Aff_Cred_FC")
    @Expose
    private String affCredFC;
    @SerializedName("MP_DateLiv")
    @Expose
    private String mPDateLiv;
    @SerializedName("Imp_EtatOrdre")
    @Expose
    private String impEtatOrdre;
    @SerializedName("PgClt_Auto")
    @Expose
    private String pgCltAuto;
    @SerializedName("RsClt_Auto")
    @Expose
    private String rsCltAuto;
    @SerializedName("MPC_PGRS")
    @Expose
    private String mpcPgrs;
    @SerializedName("BlocSkNg_BL")
    @Expose
    private String blocSkNgBL;
    @SerializedName("BlocSkNg_BT")
    @Expose
    private String blocSkNgBT;
    @SerializedName("MGUpdateBL")
    @Expose
    private String mGUpdateBL;
    @SerializedName("MGUpdateBT")
    @Expose
    private String mGUpdateBT;
    @SerializedName("NbrImpTick")
    @Expose
    private String nbrImpTick;
    @SerializedName("ProMode")
    @Expose
    private String proMode;
    @SerializedName("FactDirect")
    @Expose
    private String factDirect;
    @SerializedName("PackArticle")
    @Expose
    private String packArticle;
    @SerializedName("ComposArt")
    @Expose
    private String composArt;
    @SerializedName("CreatRapArt")
    @Expose
    private String creatRapArt;
    @SerializedName("D17")
    @Expose
    private String d17;
    @SerializedName("DibalSup200")
    @Expose
    private String dibalSup200;
    @SerializedName("ChDibalSup200")
    @Expose
    private String chDibalSup200;
    @SerializedName("Bijouterie")
    @Expose
    private String bijouterie;
    @SerializedName("SyncVPN")
    @Expose
    private String syncVPN;
    @SerializedName("FideVPN")
    @Expose
    private String fideVPN;
    @SerializedName("EcomApiBC")
    @Expose
    private String ecomApiBC;
    @SerializedName("FactBaseTVA")
    @Expose
    private String factBaseTVA;
    @SerializedName("ImpTalentCom")
    @Expose
    private String impTalentCom;
    @SerializedName("MPUpdateBE")
    @Expose
    private String mPUpdateBE;
    @SerializedName("MPUpdateBR")
    @Expose
    private String mPUpdateBR;

    @ColumnInfo(name = "MaxEspPassagM")
    @SerializedName("MaxEspPassagM")
    @Expose
    private double maxEspPassagM;

    public String getCarteFidelite() {
        return carteFidelite;
    }

    public void setCarteFidelite(String carteFidelite) {
        this.carteFidelite = carteFidelite;
    }

    public String getIsTacktil() {
        return isTacktil;
    }

    public void setIsTacktil(String isTacktil) {
        this.isTacktil = isTacktil;
    }

    public String getDCodeArticle() {
        return dCodeArticle;
    }

    public void setDCodeArticle(String dCodeArticle) {
        this.dCodeArticle = dCodeArticle;
    }

    public String getFCodeArticle() {
        return fCodeArticle;
    }

    public void setFCodeArticle(String fCodeArticle) {
        this.fCodeArticle = fCodeArticle;
    }

    public String getDPoidArticle() {
        return dPoidArticle;
    }

    public void setDPoidArticle(String dPoidArticle) {
        this.dPoidArticle = dPoidArticle;
    }

    public String getFPoidArticle() {
        return fPoidArticle;
    }

    public void setFPoidArticle(String fPoidArticle) {
        this.fPoidArticle = fPoidArticle;
    }

    public String getDPrixArticle() {
        return dPrixArticle;
    }

    public void setDPrixArticle(String dPrixArticle) {
        this.dPrixArticle = dPrixArticle;
    }

    public String getFPrixArticle() {
        return fPrixArticle;
    }

    public void setFPrixArticle(String fPrixArticle) {
        this.fPrixArticle = fPrixArticle;
    }

    public String getMPModification() {
        return mPModification;
    }

    public void setMPModification(String mPModification) {
        this.mPModification = mPModification;
    }

    public String getDeuxEcran() {
        return deuxEcran;
    }

    public void setDeuxEcran(String deuxEcran) {
        this.deuxEcran = deuxEcran;
    }

    public String getIsCommande() {
        return isCommande;
    }

    public void setIsCommande(String isCommande) {
        this.isCommande = isCommande;
    }

    public String getIsDirect() {
        return isDirect;
    }

    public void setIsDirect(String isDirect) {
        this.isDirect = isDirect;
    }

    public String getIsArticleSession() {
        return isArticleSession;
    }

    public void setIsArticleSession(String isArticleSession) {
        this.isArticleSession = isArticleSession;
    }

    public String getIsMPCaisse() {
        return isMPCaisse;
    }

    public void setIsMPCaisse(String isMPCaisse) {
        this.isMPCaisse = isMPCaisse;
    }

    public String getIsCalcMonnaie() {
        return isCalcMonnaie;
    }

    public void setIsCalcMonnaie(String isCalcMonnaie) {
        this.isCalcMonnaie = isCalcMonnaie;
    }

    public String getIs2vente() {
        return is2vente;
    }

    public void setIs2vente(String is2vente) {
        this.is2vente = is2vente;
    }

    public String getIsPromotionNbArt() {
        return isPromotionNbArt;
    }

    public void setIsPromotionNbArt(String isPromotionNbArt) {
        this.isPromotionNbArt = isPromotionNbArt;
    }

    public String getIsPrixGros() {
        return isPrixGros;
    }

    public void setIsPrixGros(String isPrixGros) {
        this.isPrixGros = isPrixGros;
    }

    public String getModeTicket() {
        return modeTicket;
    }

    public void setModeTicket(String modeTicket) {
        this.modeTicket = modeTicket;
    }

    public String getModeTicketSolde() {
        return modeTicketSolde;
    }

    public void setModeTicketSolde(String modeTicketSolde) {
        this.modeTicketSolde = modeTicketSolde;
    }

    public String getBalance() {
        return balance;
    }

    public void setBalance(String balance) {
        this.balance = balance;
    }

    public String getTransfert() {
        return transfert;
    }

    public void setTransfert(String transfert) {
        this.transfert = transfert;
    }

    public String getCaisseSansTicket() {
        return caisseSansTicket;
    }

    public void setCaisseSansTicket(String caisseSansTicket) {
        this.caisseSansTicket = caisseSansTicket;
    }

    public String getMpSupprimerArticle() {
        return mpSupprimerArticle;
    }

    public void setMpSupprimerArticle(String mpSupprimerArticle) {
        this.mpSupprimerArticle = mpSupprimerArticle;
    }

    public String getMPListeTicket() {
        return mPListeTicket;
    }

    public void setMPListeTicket(String mPListeTicket) {
        this.mPListeTicket = mPListeTicket;
    }

    public String getMPEnInstance() {
        return mPEnInstance;
    }

    public void setMPEnInstance(String mPEnInstance) {
        this.mPEnInstance = mPEnInstance;
    }

    public String getStockArticleCaisse() {
        return stockArticleCaisse;
    }

    public void setStockArticleCaisse(String stockArticleCaisse) {
        this.stockArticleCaisse = stockArticleCaisse;
    }

    public String getArticleInactif() {
        return articleInactif;
    }

    public void setArticleInactif(String articleInactif) {
        this.articleInactif = articleInactif;
    }

    public String getFacturation() {
        return facturation;
    }

    public void setFacturation(String facturation) {
        this.facturation = facturation;
    }

    public String getMPModifQte() {
        return mPModifQte;
    }

    public void setMPModifQte(String mPModifQte) {
        this.mPModifQte = mPModifQte;
    }

    public String getGestionInventaire() {
        return gestionInventaire;
    }

    public void setGestionInventaire(String gestionInventaire) {
        this.gestionInventaire = gestionInventaire;
    }

    public String getBonRetour() {
        return bonRetour;
    }

    public void setBonRetour(String bonRetour) {
        this.bonRetour = bonRetour;
    }

    public String getCarteFidelitePoint() {
        return carteFidelitePoint;
    }

    public void setCarteFidelitePoint(String carteFidelitePoint) {
        this.carteFidelitePoint = carteFidelitePoint;
    }

    public String getAffRFidTicket() {
        return affRFidTicket;
    }

    public void setAffRFidTicket(String affRFidTicket) {
        this.affRFidTicket = affRFidTicket;
    }

    public String getBotArticleTaktil() {
        return botArticleTaktil;
    }

    public void setBotArticleTaktil(String botArticleTaktil) {
        this.botArticleTaktil = botArticleTaktil;
    }

    public String getBotfamilleTaktil() {
        return botfamilleTaktil;
    }

    public void setBotfamilleTaktil(String botfamilleTaktil) {
        this.botfamilleTaktil = botfamilleTaktil;
    }

    public String getLibrerie() {
        return librerie;
    }

    public void setLibrerie(String librerie) {
        this.librerie = librerie;
    }

    public String getGridStock() {
        return gridStock;
    }

    public void setGridStock(String gridStock) {
        this.gridStock = gridStock;
    }

    public String getGridRupture() {
        return gridRupture;
    }

    public void setGridRupture(String gridRupture) {
        this.gridRupture = gridRupture;
    }

    public String getGridCAAricle() {
        return gridCAAricle;
    }

    public void setGridCAAricle(String gridCAAricle) {
        this.gridCAAricle = gridCAAricle;
    }

    public String getGridCAClient() {
        return gridCAClient;
    }

    public void setGridCAClient(String gridCAClient) {
        this.gridCAClient = gridCAClient;
    }

    public String getGridCAMargeFamille() {
        return gridCAMargeFamille;
    }

    public void setGridCAMargeFamille(String gridCAMargeFamille) {
        this.gridCAMargeFamille = gridCAMargeFamille;
    }

    public String getGridCAMargeFournisseur() {
        return gridCAMargeFournisseur;
    }

    public void setGridCAMargeFournisseur(String gridCAMargeFournisseur) {
        this.gridCAMargeFournisseur = gridCAMargeFournisseur;
    }

    public String getGridCAMargeFamille2() {
        return gridCAMargeFamille2;
    }

    public void setGridCAMargeFamille2(String gridCAMargeFamille2) {
        this.gridCAMargeFamille2 = gridCAMargeFamille2;
    }

    public String getGridCAMargeMarque() {
        return gridCAMargeMarque;
    }

    public void setGridCAMargeMarque(String gridCAMargeMarque) {
        this.gridCAMargeMarque = gridCAMargeMarque;
    }

    public String getGridAvoirArticle() {
        return gridAvoirArticle;
    }

    public void setGridAvoirArticle(String gridAvoirArticle) {
        this.gridAvoirArticle = gridAvoirArticle;
    }

    public String getGridBonComArticle() {
        return gridBonComArticle;
    }

    public void setGridBonComArticle(String gridBonComArticle) {
        this.gridBonComArticle = gridBonComArticle;
    }

    public String getOrdreFamAlphabetique() {
        return ordreFamAlphabetique;
    }

    public void setOrdreFamAlphabetique(String ordreFamAlphabetique) {
        this.ordreFamAlphabetique = ordreFamAlphabetique;
    }

    public String getVersionLight() {
        return versionLight;
    }

    public void setVersionLight(String versionLight) {
        this.versionLight = versionLight;
    }

    public String getMPAcompteAnnuler() {
        return mPAcompteAnnuler;
    }

    public void setMPAcompteAnnuler(String mPAcompteAnnuler) {
        this.mPAcompteAnnuler = mPAcompteAnnuler;
    }

    public String getMPAcompteDepasseTicketAnnuler() {
        return mPAcompteDepasseTicketAnnuler;
    }

    public void setMPAcompteDepasseTicketAnnuler(String mPAcompteDepasseTicketAnnuler) {
        this.mPAcompteDepasseTicketAnnuler = mPAcompteDepasseTicketAnnuler;
    }

    public String getBotTaktilFamFrs() {
        return botTaktilFamFrs;
    }

    public void setBotTaktilFamFrs(String botTaktilFamFrs) {
        this.botTaktilFamFrs = botTaktilFamFrs;
    }

    public String getVenteCredit() {
        return venteCredit;
    }

    public void setVenteCredit(String venteCredit) {
        this.venteCredit = venteCredit;
    }

    public String getStockDormant() {
        return stockDormant;
    }

    public void setStockDormant(String stockDormant) {
        this.stockDormant = stockDormant;
    }

    public String getGridStockDormant() {
        return gridStockDormant;
    }

    public void setGridStockDormant(String gridStockDormant) {
        this.gridStockDormant = gridStockDormant;
    }

    public String getMPClientCaisse() {
        return mPClientCaisse;
    }

    public void setMPClientCaisse(String mPClientCaisse) {
        this.mPClientCaisse = mPClientCaisse;
    }

    public String getImportExport() {
        return importExport;
    }

    public void setImportExport(String importExport) {
        this.importExport = importExport;
    }

    public String getFamilleMere() {
        return familleMere;
    }

    public void setFamilleMere(String familleMere) {
        this.familleMere = familleMere;
    }

    public String getAffPEncCaisse() {
        return affPEncCaisse;
    }

    public void setAffPEncCaisse(String affPEncCaisse) {
        this.affPEncCaisse = affPEncCaisse;
    }

    public String getRegFrs() {
        return regFrs;
    }

    public void setRegFrs(String regFrs) {
        this.regFrs = regFrs;
    }

    public String getCalculInverseQteEEnc() {
        return calculInverseQteEEnc;
    }

    public void setCalculInverseQteEEnc(String calculInverseQteEEnc) {
        this.calculInverseQteEEnc = calculInverseQteEEnc;
    }

    public String getPeremption() {
        return peremption;
    }

    public void setPeremption(String peremption) {
        this.peremption = peremption;
    }

    public String getEtatRegFactNadhir() {
        return etatRegFactNadhir;
    }

    public void setEtatRegFactNadhir(String etatRegFactNadhir) {
        this.etatRegFactNadhir = etatRegFactNadhir;
    }

    public String getCartePrepayee() {
        return cartePrepayee;
    }

    public void setCartePrepayee(String cartePrepayee) {
        this.cartePrepayee = cartePrepayee;
    }

    public String getRegPointMerci() {
        return regPointMerci;
    }

    public void setRegPointMerci(String regPointMerci) {
        this.regPointMerci = regPointMerci;
    }

    public String getTauxReductionCarteResto() {
        return tauxReductionCarteResto;
    }

    public void setTauxReductionCarteResto(String tauxReductionCarteResto) {
        this.tauxReductionCarteResto = tauxReductionCarteResto;
    }

    public String getTiroirRJ11() {
        return tiroirRJ11;
    }

    public void setTiroirRJ11(String tiroirRJ11) {
        this.tiroirRJ11 = tiroirRJ11;
    }

    public String getRegBonAchat() {
        return regBonAchat;
    }

    public void setRegBonAchat(String regBonAchat) {
        this.regBonAchat = regBonAchat;
    }

    public String getGarantieTicket() {
        return garantieTicket;
    }

    public void setGarantieTicket(String garantieTicket) {
        this.garantieTicket = garantieTicket;
    }

    public String getTransfertDUX() {
        return transfertDUX;
    }

    public void setTransfertDUX(String transfertDUX) {
        this.transfertDUX = transfertDUX;
    }

    public String getFacturationBL() {
        return facturationBL;
    }

    public void setFacturationBL(String facturationBL) {
        this.facturationBL = facturationBL;
    }

    public String getFacturationBE() {
        return facturationBE;
    }

    public void setFacturationBE(String facturationBE) {
        this.facturationBE = facturationBE;
    }

    public String getMPRemiseTicket() {
        return mPRemiseTicket;
    }

    public void setMPRemiseTicket(String mPRemiseTicket) {
        this.mPRemiseTicket = mPRemiseTicket;
    }

    public String getMPDuplicataTicket() {
        return mPDuplicataTicket;
    }

    public void setMPDuplicataTicket(String mPDuplicataTicket) {
        this.mPDuplicataTicket = mPDuplicataTicket;
    }

    public String getImpressionModeRegTicket() {
        return impressionModeRegTicket;
    }

    public void setImpressionModeRegTicket(String impressionModeRegTicket) {
        this.impressionModeRegTicket = impressionModeRegTicket;
    }

    public String getMpSupprimerDepence() {
        return mpSupprimerDepence;
    }

    public void setMpSupprimerDepence(String mpSupprimerDepence) {
        this.mpSupprimerDepence = mpSupprimerDepence;
    }

    public String getMpOuvrirTiroire() {
        return mpOuvrirTiroire;
    }

    public void setMpOuvrirTiroire(String mpOuvrirTiroire) {
        this.mpOuvrirTiroire = mpOuvrirTiroire;
    }

    public String getMPTRemiseLigArticle() {
        return mPTRemiseLigArticle;
    }

    public void setMPTRemiseLigArticle(String mPTRemiseLigArticle) {
        this.mPTRemiseLigArticle = mPTRemiseLigArticle;
    }

    public String getSplash() {
        return splash;
    }

    public void setSplash(String splash) {
        this.splash = splash;
    }

    public String getAlertStock() {
        return alertStock;
    }

    public void setAlertStock(String alertStock) {
        this.alertStock = alertStock;
    }

    public String getFactureA5Senegal() {
        return factureA5Senegal;
    }

    public void setFactureA5Senegal(String factureA5Senegal) {
        this.factureA5Senegal = factureA5Senegal;
    }

    public String getLRegFrs() {
        return lRegFrs;
    }

    public void setLRegFrs(String lRegFrs) {
        this.lRegFrs = lRegFrs;
    }

    public String getLVenteCredit() {
        return lVenteCredit;
    }

    public void setLVenteCredit(String lVenteCredit) {
        this.lVenteCredit = lVenteCredit;
    }

    public String getLBalance() {
        return lBalance;
    }

    public void setLBalance(String lBalance) {
        this.lBalance = lBalance;
    }

    public String getLFacturation() {
        return lFacturation;
    }

    public void setLFacturation(String lFacturation) {
        this.lFacturation = lFacturation;
    }

    public String getLCartePrepayee() {
        return lCartePrepayee;
    }

    public void setLCartePrepayee(String lCartePrepayee) {
        this.lCartePrepayee = lCartePrepayee;
    }

    public String getRGTicketTTC() {
        return rGTicketTTC;
    }

    public void setRGTicketTTC(String rGTicketTTC) {
        this.rGTicketTTC = rGTicketTTC;
    }

    public String getGestionDechets() {
        return gestionDechets;
    }

    public void setGestionDechets(String gestionDechets) {
        this.gestionDechets = gestionDechets;
    }

    public String getMpGestionDechets() {
        return mpGestionDechets;
    }

    public void setMpGestionDechets(String mpGestionDechets) {
        this.mpGestionDechets = mpGestionDechets;
    }

    public String getTicketCadeau() {
        return ticketCadeau;
    }

    public void setTicketCadeau(String ticketCadeau) {
        this.ticketCadeau = ticketCadeau;
    }

    public String getMPTicketCadeau() {
        return mPTicketCadeau;
    }

    public void setMPTicketCadeau(String mPTicketCadeau) {
        this.mPTicketCadeau = mPTicketCadeau;
    }

    public String getPostePreparation() {
        return postePreparation;
    }

    public void setPostePreparation(String postePreparation) {
        this.postePreparation = postePreparation;
    }

    public String getMPListeCommande() {
        return mPListeCommande;
    }

    public void setMPListeCommande(String mPListeCommande) {
        this.mPListeCommande = mPListeCommande;
    }

    public String getMaxTauxRemise() {
        return maxTauxRemise;
    }

    public void setMaxTauxRemise(String maxTauxRemise) {
        this.maxTauxRemise = maxTauxRemise;
    }

    public String getMPClotCaisse() {
        return mPClotCaisse;
    }

    public void setMPClotCaisse(String mPClotCaisse) {
        this.mPClotCaisse = mPClotCaisse;
    }

    public String getCommentaireTicket() {
        return commentaireTicket;
    }

    public void setCommentaireTicket(String commentaireTicket) {
        this.commentaireTicket = commentaireTicket;
    }

    public String getVenteWebService() {
        return venteWebService;
    }

    public void setVenteWebService(String venteWebService) {
        this.venteWebService = venteWebService;
    }

    public String getPointFidelite() {
        return pointFidelite;
    }

    public void setPointFidelite(String pointFidelite) {
        this.pointFidelite = pointFidelite;
    }

    public String getNumeroTallon() {
        return numeroTallon;
    }

    public void setNumeroTallon(String numeroTallon) {
        this.numeroTallon = numeroTallon;
    }

    public String getSousFamille() {
        return sousFamille;
    }

    public void setSousFamille(String sousFamille) {
        this.sousFamille = sousFamille;
    }

    public String getWSCommande() {
        return wSCommande;
    }

    public void setWSCommande(String wSCommande) {
        this.wSCommande = wSCommande;
    }

    public String getWSCommandeService() {
        return wSCommandeService;
    }

    public void setWSCommandeService(String wSCommandeService) {
        this.wSCommandeService = wSCommandeService;
    }

    public String getCommandeA4() {
        return commandeA4;
    }

    public void setCommandeA4(String commandeA4) {
        this.commandeA4 = commandeA4;
    }

    public String getNbrImpCom() {
        return nbrImpCom;
    }

    public void setNbrImpCom(String nbrImpCom) {
        this.nbrImpCom = nbrImpCom;
    }

    public String getRegEspDemImp() {
        return regEspDemImp;
    }

    public void setRegEspDemImp(String regEspDemImp) {
        this.regEspDemImp = regEspDemImp;
    }

    public String getFusionligneticket() {
        return fusionligneticket;
    }

    public void setFusionligneticket(String fusionligneticket) {
        this.fusionligneticket = fusionligneticket;
    }

    public String getNumeroSerie() {
        return numeroSerie;
    }

    public void setNumeroSerie(String numeroSerie) {
        this.numeroSerie = numeroSerie;
    }

    public String getSupplement() {
        return supplement;
    }

    public void setSupplement(String supplement) {
        this.supplement = supplement;
    }

    public String getGestionTier() {
        return gestionTier;
    }

    public void setGestionTier(String gestionTier) {
        this.gestionTier = gestionTier;
    }

    public String getGestionBancaire() {
        return gestionBancaire;
    }

    public void setGestionBancaire(String gestionBancaire) {
        this.gestionBancaire = gestionBancaire;
    }

    public String getTBArticle() {
        return tBArticle;
    }

    public void setTBArticle(String tBArticle) {
        this.tBArticle = tBArticle;
    }

    public String getTBFrs() {
        return tBFrs;
    }

    public void setTBFrs(String tBFrs) {
        this.tBFrs = tBFrs;
    }

    public String getTBClient() {
        return tBClient;
    }

    public void setTBClient(String tBClient) {
        this.tBClient = tBClient;
    }

    public String getMailing() {
        return mailing;
    }

    public void setMailing(String mailing) {
        this.mailing = mailing;
    }

    public String getCarnetAuto() {
        return carnetAuto;
    }

    public void setCarnetAuto(String carnetAuto) {
        this.carnetAuto = carnetAuto;
    }

    public String getDistributeur() {
        return distributeur;
    }

    public void setDistributeur(String distributeur) {
        this.distributeur = distributeur;
    }

    public String getEsthetique() {
        return esthetique;
    }

    public void setEsthetique(String esthetique) {
        this.esthetique = esthetique;
    }

    public String getWaitEsthetique() {
        return waitEsthetique;
    }

    public void setWaitEsthetique(String waitEsthetique) {
        this.waitEsthetique = waitEsthetique;
    }

    public String getBalanceCom() {
        return balanceCom;
    }

    public void setBalanceCom(String balanceCom) {
        this.balanceCom = balanceCom;
    }

    public String getEmploiPrestataire() {
        return emploiPrestataire;
    }

    public void setEmploiPrestataire(String emploiPrestataire) {
        this.emploiPrestataire = emploiPrestataire;
    }

    public String getCalandAuto() {
        return calandAuto;
    }

    public void setCalandAuto(String calandAuto) {
        this.calandAuto = calandAuto;
    }

    public String getMerchandiser() {
        return merchandiser;
    }

    public void setMerchandiser(String merchandiser) {
        this.merchandiser = merchandiser;
    }

    public String getSms() {
        return sms;
    }

    public void setSms(String sms) {
        this.sms = sms;
    }

    public String getRegEspAvecImp() {
        return regEspAvecImp;
    }

    public void setRegEspAvecImp(String regEspAvecImp) {
        this.regEspAvecImp = regEspAvecImp;
    }

    public String getRegEspSansImp() {
        return regEspSansImp;
    }

    public void setRegEspSansImp(String regEspSansImp) {
        this.regEspSansImp = regEspSansImp;
    }

    public String getForcementMAJ() {
        return forcementMAJ;
    }

    public void setForcementMAJ(String forcementMAJ) {
        this.forcementMAJ = forcementMAJ;
    }

    public String getClotCaisseAuto() {
        return clotCaisseAuto;
    }

    public void setClotCaisseAuto(String clotCaisseAuto) {
        this.clotCaisseAuto = clotCaisseAuto;
    }

    public String getPPrepParProduit() {
        return pPrepParProduit;
    }

    public void setPPrepParProduit(String pPrepParProduit) {
        this.pPrepParProduit = pPrepParProduit;
    }

    public String getMPClotuCaisse() {
        return mPClotuCaisse;
    }

    public void setMPClotuCaisse(String mPClotuCaisse) {
        this.mPClotuCaisse = mPClotuCaisse;
    }

    public String getCBParTalon() {
        return cBParTalon;
    }

    public void setCBParTalon(String cBParTalon) {
        this.cBParTalon = cBParTalon;
    }

    public String getCommercialOblig() {
        return commercialOblig;
    }

    public void setCommercialOblig(String commercialOblig) {
        this.commercialOblig = commercialOblig;
    }

    public String getCommercialObligMP() {
        return commercialObligMP;
    }

    public void setCommercialObligMP(String commercialObligMP) {
        this.commercialObligMP = commercialObligMP;
    }

    public String getDetruiteServPr() {
        return detruiteServPr;
    }

    public void setDetruiteServPr(String detruiteServPr) {
        this.detruiteServPr = detruiteServPr;
    }

    public String getArticlePoidQte() {
        return articlePoidQte;
    }

    public void setArticlePoidQte(String articlePoidQte) {
        this.articlePoidQte = articlePoidQte;
    }

    public String getValiditeBAE() {
        return validiteBAE;
    }

    public void setValiditeBAE(String validiteBAE) {
        this.validiteBAE = validiteBAE;
    }

    public String getBARecuTchC() {
        return bARecuTchC;
    }

    public void setBARecuTchC(String bARecuTchC) {
        this.bARecuTchC = bARecuTchC;
    }

    public String getTalonServiceArticle() {
        return talonServiceArticle;
    }

    public void setTalonServiceArticle(String talonServiceArticle) {
        this.talonServiceArticle = talonServiceArticle;
    }

    public String getArrondMntTick() {
        return arrondMntTick;
    }

    public void setArrondMntTick(String arrondMntTick) {
        this.arrondMntTick = arrondMntTick;
    }

    public String getRemiseDetaTick() {
        return remiseDetaTick;
    }

    public void setRemiseDetaTick(String remiseDetaTick) {
        this.remiseDetaTick = remiseDetaTick;
    }

    public String getBlocageOvrSession() {
        return blocageOvrSession;
    }

    public void setBlocageOvrSession(String blocageOvrSession) {
        this.blocageOvrSession = blocageOvrSession;
    }

    public String getIngredients() {
        return ingredients;
    }

    public void setIngredients(String ingredients) {
        this.ingredients = ingredients;
    }

    public String getTarifClient() {
        return tarifClient;
    }

    public void setTarifClient(String tarifClient) {
        this.tarifClient = tarifClient;
    }

    public String getNbrImpTicket() {
        return nbrImpTicket;
    }

    public void setNbrImpTicket(String nbrImpTicket) {
        this.nbrImpTicket = nbrImpTicket;
    }

    public String getNoteTicket() {
        return noteTicket;
    }

    public void setNoteTicket(String noteTicket) {
        this.noteTicket = noteTicket;
    }

    public String getFreeShop() {
        return freeShop;
    }

    public void setFreeShop(String freeShop) {
        this.freeShop = freeShop;
    }

    public String getNotifAnnivClt() {
        return notifAnnivClt;
    }

    public void setNotifAnnivClt(String notifAnnivClt) {
        this.notifAnnivClt = notifAnnivClt;
    }

    public String getAffichageSFArticle() {
        return affichageSFArticle;
    }

    public void setAffichageSFArticle(String affichageSFArticle) {
        this.affichageSFArticle = affichageSFArticle;
    }

    public String getCodeArtMSFF() {
        return codeArtMSFF;
    }

    public void setCodeArtMSFF(String codeArtMSFF) {
        this.codeArtMSFF = codeArtMSFF;
    }

    public String getTicketObligatoire() {
        return ticketObligatoire;
    }

    public void setTicketObligatoire(String ticketObligatoire) {
        this.ticketObligatoire = ticketObligatoire;
    }

    public String getSuppBtDevalINV() {
        return suppBtDevalINV;
    }

    public void setSuppBtDevalINV(String suppBtDevalINV) {
        this.suppBtDevalINV = suppBtDevalINV;
    }

    public String getMNTMinCheque() {
        return mNTMinCheque;
    }

    public void setMNTMinCheque(String mNTMinCheque) {
        this.mNTMinCheque = mNTMinCheque;
    }

    public String getMNTMinCB() {
        return mNTMinCB;
    }

    public void setMNTMinCB(String mNTMinCB) {
        this.mNTMinCB = mNTMinCB;
    }

    public String getArticleSite() {
        return articleSite;
    }

    public void setArticleSite(String articleSite) {
        this.articleSite = articleSite;
    }

    public String getBlocStkNeg() {
        return blocStkNeg;
    }

    public void setBlocStkNeg(String blocStkNeg) {
        this.blocStkNeg = blocStkNeg;
    }

    public String getPrixGTaux() {
        return prixGTaux;
    }

    public void setPrixGTaux(String prixGTaux) {
        this.prixGTaux = prixGTaux;
    }

    public String getSansPUTicket() {
        return sansPUTicket;
    }

    public void setSansPUTicket(String sansPUTicket) {
        this.sansPUTicket = sansPUTicket;
    }

    public String getCrdETCmd() {
        return crdETCmd;
    }

    public void setCrdETCmd(String crdETCmd) {
        this.crdETCmd = crdETCmd;
    }

    public String getPieceEgPrixPEM() {
        return pieceEgPrixPEM;
    }

    public void setPieceEgPrixPEM(String pieceEgPrixPEM) {
        this.pieceEgPrixPEM = pieceEgPrixPEM;
    }

    public String getArmurerie() {
        return armurerie;
    }

    public void setArmurerie(String armurerie) {
        this.armurerie = armurerie;
    }

    public String getSuppBtDechC() {
        return suppBtDechC;
    }

    public void setSuppBtDechC(String suppBtDechC) {
        this.suppBtDechC = suppBtDechC;
    }

    public String getSuppBtDepC() {
        return suppBtDepC;
    }

    public void setSuppBtDepC(String suppBtDepC) {
        this.suppBtDepC = suppBtDepC;
    }

    public String getMPRecCaisse() {
        return mPRecCaisse;
    }

    public void setMPRecCaisse(String mPRecCaisse) {
        this.mPRecCaisse = mPRecCaisse;
    }

    public String getPrestImprimante() {
        return prestImprimante;
    }

    public void setPrestImprimante(String prestImprimante) {
        this.prestImprimante = prestImprimante;
    }

    public String getDressing() {
        return dressing;
    }

    public void setDressing(String dressing) {
        this.dressing = dressing;
    }

    public String getBoucherie() {
        return boucherie;
    }

    public void setBoucherie(String boucherie) {
        this.boucherie = boucherie;
    }

    public String getMPRetCaisse() {
        return mPRetCaisse;
    }

    public void setMPRetCaisse(String mPRetCaisse) {
        this.mPRetCaisse = mPRetCaisse;
    }

    public String getBCINonvalor() {
        return bCINonvalor;
    }

    public void setBCINonvalor(String bCINonvalor) {
        this.bCINonvalor = bCINonvalor;
    }

    public String getMPCltCaisse() {
        return mPCltCaisse;
    }

    public void setMPCltCaisse(String mPCltCaisse) {
        this.mPCltCaisse = mPCltCaisse;
    }

    public String getWSProduit() {
        return wSProduit;
    }

    public void setWSProduit(String wSProduit) {
        this.wSProduit = wSProduit;
    }

    public String getDevis() {
        return devis;
    }

    public void setDevis(String devis) {
        this.devis = devis;
    }

    public String getFactureFrs() {
        return factureFrs;
    }

    public void setFactureFrs(String factureFrs) {
        this.factureFrs = factureFrs;
    }

    public String getArticleEcommerce() {
        return articleEcommerce;
    }

    public void setArticleEcommerce(String articleEcommerce) {
        this.articleEcommerce = articleEcommerce;
    }

    public String getBsi() {
        return bsi;
    }

    public void setBsi(String bsi) {
        this.bsi = bsi;
    }

    public String getMAJPVGrid() {
        return mAJPVGrid;
    }

    public void setMAJPVGrid(String mAJPVGrid) {
        this.mAJPVGrid = mAJPVGrid;
    }

    public String getPorcelaine() {
        return porcelaine;
    }

    public void setPorcelaine(String porcelaine) {
        this.porcelaine = porcelaine;
    }

    public String getTracabilite() {
        return tracabilite;
    }

    public void setTracabilite(String tracabilite) {
        this.tracabilite = tracabilite;
    }

    public String getLibraPoidauto() {
        return libraPoidauto;
    }

    public void setLibraPoidauto(String libraPoidauto) {
        this.libraPoidauto = libraPoidauto;
    }

    public String getCheminDemande() {
        return cheminDemande;
    }

    public void setCheminDemande(String cheminDemande) {
        this.cheminDemande = cheminDemande;
    }

    public String getCheminReponse() {
        return cheminReponse;
    }

    public void setCheminReponse(String cheminReponse) {
        this.cheminReponse = cheminReponse;
    }

    public String getCheminEcoute() {
        return cheminEcoute;
    }

    public void setCheminEcoute(String cheminEcoute) {
        this.cheminEcoute = cheminEcoute;
    }

    public String getFArticle() {
        return fArticle;
    }

    public void setFArticle(String fArticle) {
        this.fArticle = fArticle;
    }

    public String getPromPerdProd() {
        return promPerdProd;
    }

    public void setPromPerdProd(String promPerdProd) {
        this.promPerdProd = promPerdProd;
    }

    public String getLibraType() {
        return libraType;
    }

    public void setLibraType(String libraType) {
        this.libraType = libraType;
    }

    public String getDFDLCArt() {
        return dFDLCArt;
    }

    public void setDFDLCArt(String dFDLCArt) {
        this.dFDLCArt = dFDLCArt;
    }

    public String getCompta() {
        return compta;
    }

    public void setCompta(String compta) {
        this.compta = compta;
    }

    public String getBCWeb() {
        return bCWeb;
    }

    public void setBCWeb(String bCWeb) {
        this.bCWeb = bCWeb;
    }

    public String getChargementBCW() {
        return chargementBCW;
    }

    public void setChargementBCW(String chargementBCW) {
        this.chargementBCW = chargementBCW;
    }

    public String getPFactStat() {
        return pFactStat;
    }

    public void setPFactStat(String pFactStat) {
        this.pFactStat = pFactStat;
    }

    public String getPBLSUser() {
        return pBLSUser;
    }

    public void setPBLSUser(String pBLSUser) {
        this.pBLSUser = pBLSUser;
    }

    public String getEdition() {
        return edition;
    }

    public void setEdition(String edition) {
        this.edition = edition;
    }

    public String getSansLogTicket() {
        return sansLogTicket;
    }

    public void setSansLogTicket(String sansLogTicket) {
        this.sansLogTicket = sansLogTicket;
    }

    public String getSansVendTicket() {
        return sansVendTicket;
    }

    public void setSansVendTicket(String sansVendTicket) {
        this.sansVendTicket = sansVendTicket;
    }

    public String getImpTVATicket() {
        return impTVATicket;
    }

    public void setImpTVATicket(String impTVATicket) {
        this.impTVATicket = impTVATicket;
    }

    public String getDrive() {
        return drive;
    }

    public void setDrive(String drive) {
        this.drive = drive;
    }

    public String getDibalTicketauto() {
        return dibalTicketauto;
    }

    public void setDibalTicketauto(String dibalTicketauto) {
        this.dibalTicketauto = dibalTicketauto;
    }

    public String getDibalCheminRep() {
        return dibalCheminRep;
    }

    public void setDibalCheminRep(String dibalCheminRep) {
        this.dibalCheminRep = dibalCheminRep;
    }

    public String getDibalCheminRgi() {
        return dibalCheminRgi;
    }

    public void setDibalCheminRgi(String dibalCheminRgi) {
        this.dibalCheminRgi = dibalCheminRgi;
    }

    public String getMPCredit() {
        return mPCredit;
    }

    public void setMPCredit(String mPCredit) {
        this.mPCredit = mPCredit;
    }

    public String getCommercialSans() {
        return commercialSans;
    }

    public void setCommercialSans(String commercialSans) {
        this.commercialSans = commercialSans;
    }

    public String getIsPieceUnite() {
        return isPieceUnite;
    }

    public void setIsPieceUnite(String isPieceUnite) {
        this.isPieceUnite = isPieceUnite;
    }

    public String getCreationAutoArt() {
        return creationAutoArt;
    }

    public void setCreationAutoArt(String creationAutoArt) {
        this.creationAutoArt = creationAutoArt;
    }

    public String getOuvertureAutoArt() {
        return ouvertureAutoArt;
    }

    public void setOuvertureAutoArt(String ouvertureAutoArt) {
        this.ouvertureAutoArt = ouvertureAutoArt;
    }

    public String getWSAutoArt() {
        return wSAutoArt;
    }

    public void setWSAutoArt(String wSAutoArt) {
        this.wSAutoArt = wSAutoArt;
    }

    public String getMPSuppBL() {
        return mPSuppBL;
    }

    public void setMPSuppBL(String mPSuppBL) {
        this.mPSuppBL = mPSuppBL;
    }

    public String getBLLotPG() {
        return bLLotPG;
    }

    public void setBLLotPG(String bLLotPG) {
        this.bLLotPG = bLLotPG;
    }

    public String getFlouci() {
        return flouci;
    }

    public void setFlouci(String flouci) {
        this.flouci = flouci;
    }

    public String getMbreFamille() {
        return mbreFamille;
    }

    public void setMbreFamille(String mbreFamille) {
        this.mbreFamille = mbreFamille;
    }

    public String getPointgTick() {
        return pointgTick;
    }

    public void setPointgTick(String pointgTick) {
        this.pointgTick = pointgTick;
    }

    public String getMNTMinRC() {
        return mNTMinRC;
    }

    public void setMNTMinRC(String mNTMinRC) {
        this.mNTMinRC = mNTMinRC;
    }

    public String getAffCAPrest() {
        return affCAPrest;
    }

    public void setAffCAPrest(String affCAPrest) {
        this.affCAPrest = affCAPrest;
    }

    public String getAffCredFC() {
        return affCredFC;
    }

    public void setAffCredFC(String affCredFC) {
        this.affCredFC = affCredFC;
    }

    public String getMPDateLiv() {
        return mPDateLiv;
    }

    public void setMPDateLiv(String mPDateLiv) {
        this.mPDateLiv = mPDateLiv;
    }

    public String getImpEtatOrdre() {
        return impEtatOrdre;
    }

    public void setImpEtatOrdre(String impEtatOrdre) {
        this.impEtatOrdre = impEtatOrdre;
    }

    public String getPgCltAuto() {
        return pgCltAuto;
    }

    public void setPgCltAuto(String pgCltAuto) {
        this.pgCltAuto = pgCltAuto;
    }

    public String getRsCltAuto() {
        return rsCltAuto;
    }

    public void setRsCltAuto(String rsCltAuto) {
        this.rsCltAuto = rsCltAuto;
    }

    public String getMpcPgrs() {
        return mpcPgrs;
    }

    public void setMpcPgrs(String mpcPgrs) {
        this.mpcPgrs = mpcPgrs;
    }

    public String getBlocSkNgBL() {
        return blocSkNgBL;
    }

    public void setBlocSkNgBL(String blocSkNgBL) {
        this.blocSkNgBL = blocSkNgBL;
    }

    public String getBlocSkNgBT() {
        return blocSkNgBT;
    }

    public void setBlocSkNgBT(String blocSkNgBT) {
        this.blocSkNgBT = blocSkNgBT;
    }

    public String getMGUpdateBL() {
        return mGUpdateBL;
    }

    public void setMGUpdateBL(String mGUpdateBL) {
        this.mGUpdateBL = mGUpdateBL;
    }

    public String getMGUpdateBT() {
        return mGUpdateBT;
    }

    public void setMGUpdateBT(String mGUpdateBT) {
        this.mGUpdateBT = mGUpdateBT;
    }

    public String getNbrImpTick() {
        return nbrImpTick;
    }

    public void setNbrImpTick(String nbrImpTick) {
        this.nbrImpTick = nbrImpTick;
    }

    public String getProMode() {
        return proMode;
    }

    public void setProMode(String proMode) {
        this.proMode = proMode;
    }

    public String getFactDirect() {
        return factDirect;
    }

    public void setFactDirect(String factDirect) {
        this.factDirect = factDirect;
    }

    public String getPackArticle() {
        return packArticle;
    }

    public void setPackArticle(String packArticle) {
        this.packArticle = packArticle;
    }

    public String getComposArt() {
        return composArt;
    }

    public void setComposArt(String composArt) {
        this.composArt = composArt;
    }

    public String getCreatRapArt() {
        return creatRapArt;
    }

    public void setCreatRapArt(String creatRapArt) {
        this.creatRapArt = creatRapArt;
    }

    public String getD17() {
        return d17;
    }

    public void setD17(String d17) {
        this.d17 = d17;
    }

    public String getDibalSup200() {
        return dibalSup200;
    }

    public void setDibalSup200(String dibalSup200) {
        this.dibalSup200 = dibalSup200;
    }

    public String getChDibalSup200() {
        return chDibalSup200;
    }

    public void setChDibalSup200(String chDibalSup200) {
        this.chDibalSup200 = chDibalSup200;
    }

    public String getBijouterie() {
        return bijouterie;
    }

    public void setBijouterie(String bijouterie) {
        this.bijouterie = bijouterie;
    }

    public String getSyncVPN() {
        return syncVPN;
    }

    public void setSyncVPN(String syncVPN) {
        this.syncVPN = syncVPN;
    }

    public String getFideVPN() {
        return fideVPN;
    }

    public void setFideVPN(String fideVPN) {
        this.fideVPN = fideVPN;
    }

    public String getEcomApiBC() {
        return ecomApiBC;
    }

    public void setEcomApiBC(String ecomApiBC) {
        this.ecomApiBC = ecomApiBC;
    }

    public String getFactBaseTVA() {
        return factBaseTVA;
    }

    public void setFactBaseTVA(String factBaseTVA) {
        this.factBaseTVA = factBaseTVA;
    }

    public String getImpTalentCom() {
        return impTalentCom;
    }

    public void setImpTalentCom(String impTalentCom) {
        this.impTalentCom = impTalentCom;
    }

    public String getMPUpdateBE() {
        return mPUpdateBE;
    }

    public void setMPUpdateBE(String mPUpdateBE) {
        this.mPUpdateBE = mPUpdateBE;
    }

    public String getMPUpdateBR() {
        return mPUpdateBR;
    }

    public void setMPUpdateBR(String mPUpdateBR) {
        this.mPUpdateBR = mPUpdateBR;
    }

    public double getMaxEspPassagM() {
        return maxEspPassagM;
    }

    public void setMaxEspPassagM(double maxEspPassagM) {
        this.maxEspPassagM = maxEspPassagM;
    }

}
