package com.asmtunis.procaissemobility.data.models;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * Created by Achraf on 28/09/2017.
 */

public class Utilisateur extends BaseModel{

    @SerializedName("Code_Ut")
    @Expose
    private String codeUt;
    @SerializedName("Nom")
    @Expose
    private String nom;
    @SerializedName("Prenom")
    @Expose
    private String prenom;
    @SerializedName("Tel")
    @Expose
    private Object tel;
    @SerializedName("Mail")
    @Expose
    private String mail;
    @SerializedName("Login")
    @Expose
    private String login;
    @SerializedName("Passe")
    @Expose
    private String passe;
    @SerializedName("Etat")
    @Expose
    private String etat;
    @SerializedName("Groupe")
    @Expose
    private Object groupe;
    @SerializedName("Station")
    @Expose
    private String station;
    @SerializedName("Type_user")
    @Expose
    private String typeUser;
    @SerializedName("export")
    @Expose
    private String export;
    @SerializedName("DDm")
    @Expose
    private String dDm;
    @SerializedName("CodeCle")
    @Expose
    private String codeCle;
    @SerializedName("Autorise_R")
    @Expose
    private String autoriseR;

    @SerializedName("Autorise_Rt")
    @Expose
    private Integer autoriseRt;

    @SerializedName("Autorise_Bc")
    @Expose
    private Integer autoriseBc;

    @SerializedName("AutoriseCrCt")
    @Expose
    private Integer autoriseCrCt;

    @SerializedName("Autorise_Clt")
    @Expose
    private int autoriseClt;

    @SerializedName("AutoriseTourne")
    @Expose
    private Integer autorizeTour;

    @SerializedName("CodeCle1")
    @Expose
    private Object codeCle1;
    @SerializedName("Code_Caisse")
    @Expose
    private String codeCaisse;
    @SerializedName("Code_Carnet")
    @Expose
    private String codeCarnet;

    @SerializedName("FiltreClt")
    @Expose
    private Integer filtreClt;

    @SerializedName("ClotSessAuto")
    @Expose
    private Boolean ClotSessAuto;

    @SerializedName("CrtourneAuto")
    @Expose
    private Boolean CrtourneAuto;

    @SerializedName("CltEquivalent")
    @Expose
    private String CltEquivalent;

    @SerializedName("autorisationUser")
    @Expose
    private List<Authorization> autorisationsUser;

    @SerializedName("zones")
    @Expose
    private List<String> zones;

    public Utilisateur(String login, String passe) {
        this.login = login;
        this.passe = passe;
    }

    public List<Authorization> getAutorisationUser() {
        return autorisationsUser;
    }

    public List<String> getZone() {
        return zones;
    }

    public void setZone(List<String> zone) {
        this.zones = zone;
    }

    public void setAutorisationUser(List<Authorization> autorisationUser) {
        this.autorisationsUser = autorisationUser;
    }

    public String getCltEquivalent() {
        return CltEquivalent;
    }

    public void setCltEquivalent(String cltEquivalent) {
        CltEquivalent = cltEquivalent;
    }

    public Boolean getCrtourneAuto() {
        return CrtourneAuto;
    }

    public void setCrtourneAuto(Boolean crtourneAuto) {
        CrtourneAuto = crtourneAuto;
    }

    public void setFiltreClt(Integer filtreClt) {
        this.filtreClt = filtreClt;
    }

    public Integer getFiltreClt() {
        return filtreClt;
    }

    public Integer getAutoriseClt() {
        return autoriseClt;
    }

    public Integer getAutorizeTour() {
        return autorizeTour;
    }

    public void setAutoriseBc(int autoriseBc) {
        this.autoriseBc = autoriseBc;
    }

    public Integer getAutoriseBc() {
        return autoriseBc;
    }

    public void setAutoriseRt(Integer autoriseRt) {
        this.autoriseRt = autoriseRt;
    }

    public Integer getAutoriseRt() {
        return autoriseRt;
    }

    public void setAutoriseCrCt(Integer autoriseCrCt) {
        this.autoriseCrCt = autoriseCrCt;
    }

    public Integer getAutoriseCrCt() {
        return autoriseCrCt;
    }

    public String getCodeUt() {
        return codeUt;
    }

    public void setCodeUt(String codeUt) {
        this.codeUt = codeUt;
    }

    public String getNom() {
        return nom;
    }

    public void setNom(String nom) {
        this.nom = nom;
    }

    public String getPrenom() {
        return prenom;
    }

    public void setPrenom(String prenom) {
        this.prenom = prenom;
    }

    public Object getTel() {
        return tel;
    }

    public void setTel(Object tel) {
        this.tel = tel;
    }

    public String getMail() {
        return mail;
    }

    public void setMail(String mail) {
        this.mail = mail;
    }

    public String getLogin() {
        return login;
    }

    public void setLogin(String login) {
        this.login = login;
    }

    public String getPasse() {
        return passe;
    }

    public void setPasse(String passe) {
        this.passe = passe;
    }

    public String getEtat() {
        return etat;
    }

    public void setEtat(String etat) {
        this.etat = etat;
    }

    public Object getGroupe() {
        return groupe;
    }

    public void setGroupe(Object groupe) {
        this.groupe = groupe;
    }

    public String getStation() {
        return station;
    }

    public void setStation(String station) {
        this.station = station;
    }

    public String getTypeUser() {
        return typeUser;
    }

    public void setTypeUser(String typeUser) {
        this.typeUser = typeUser;
    }

    public String getExport() {
        return export;
    }

    public void setExport(String export) {
        this.export = export;
    }

    public String getDDm() {
        return dDm;
    }

    public void setDDm(String dDm) {
        this.dDm = dDm;
    }

    public String getCodeCle() {
        return codeCle;
    }

    public void setCodeCle(String codeCle) {
        this.codeCle = codeCle;
    }

    public String getAutoriseR() {
        return autoriseR;
    }

    public void setAutoriseR(String autoriseR) {
        this.autoriseR = autoriseR;
    }

    public Object getCodeCle1() {
        return codeCle1;
    }

    public void setCodeCle1(Object codeCle1) {
        this.codeCle1 = codeCle1;
    }

    public String getCodeCaisse() {
        return codeCaisse;
    }

    public void setCodeCaisse(String codeCaisse) {
        this.codeCaisse = codeCaisse;
    }

    public String getCodeCarnet() {
        return codeCarnet;
    }

    public void setCodeCarnet(String codeCarnet) {
        this.codeCarnet = codeCarnet;
    }

    public void setClotSessAuto(Boolean clotSessAuto) {
        ClotSessAuto = clotSessAuto;
    }

    public Boolean getClotSessAuto() {
        return ClotSessAuto;
    }

}
