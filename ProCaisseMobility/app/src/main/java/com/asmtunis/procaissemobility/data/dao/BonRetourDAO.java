package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.BonRetour;

import java.util.List;
@Dao
public interface BonRetourDAO {
    @Query("SELECT * FROM BonRetour order by CAST( BOR_date AS DATE ) asc,BOR_Numero desc ")
    List<BonRetour> getAll();

    @Query("SELECT * FROM BonRetour order by CAST( BOR_date AS DATE ) asc,BOR_Numero desc  ")
    LiveData<List<BonRetour>> getAllMutable();

    @Query("SELECT * FROM BonRetour where isSync=0 and  (Status='INSERTED'  or Status='UPDATED') order by BOR_Numero desc")
    List<BonRetour> getNonSynced();
    @Query("SELECT * FROM BonRetour WHERE  BOR_codefrs=:codeClient and BOR_Exercice=:exercice order by BOR_date desc")
    List<BonRetour> getClientSolde(String codeClient, String exercice);
    @Query("UPDATE BonRetour SET BOR_codefrs = :code_client where BOR_codefrs = :oldCodeClient")
    void updateCodeClient(String code_client, String oldCodeClient);


    @Query("SELECT ifnull(MAX(cast(substr(BOR_Numero,length(:prefix) + 1 ,length('BOR_Numero'))as integer)),0)+1 FROM   BonRetour WHERE substr(BOR_Numero, 0 ,length(:prefix)+1) = :prefix")
    String getNewCode(String prefix);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(BonRetour item);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<BonRetour> items);

    @Query("SELECT count(*) FROM BonRetour where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    int getCountNonSync();

    @Query("SELECT count(*) FROM BonRetour")
    int getCount();

    @Query("SELECT count(*) FROM BonRetour where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    LiveData<Integer> getNoSyncCountMutable();

    @Query("DELETE FROM BonRetour")
    void deleteAll();

    @Query("DELETE FROM BonRetour where BOR_Numero= :codeRetour and BOR_Exercice=:exercice")
    void deleteById(String codeRetour,String exercice);

    @Query("SELECT * FROM BonRetour where BOR_Station=:station order by strftime('%Y-%m-%d %H-%M', BOR_date) DESC")
    LiveData<List<BonRetour>> getByStationMutable(String station);

    @Query("SELECT count(*) FROM BonRetour where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    Integer getNoSyncCountNonMutable();

    @Query("SELECT count(*) FROM BonRetour where BOR_Station=:station")
    Integer getAllCountBySession(String station);

    @Query("SELECT count(*) FROM BonRetour where BOR_Station=:station")
    LiveData<Integer> getAllCountBySessionMutable(String station);
}
