package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

@Entity(primaryKeys =
        {"LG_DEV_NumBon", "LG_DEV_Exerc", "LG_DEV_CodeArt", "LG_DEV_NumOrdre"})
public class LigneBonCommande extends BaseModel {

    @NonNull
    @ColumnInfo(name = "LG_DEV_NumBon")
    @SerializedName("LG_DEV_NumBon")
    @Expose
    private String lGDEVNumBon;

    public String getlGDEVCodeM() {
        return lGDEVCodeM;
    }

    public void setlGDEVCodeM(String lGDEVCodeM) {
        this.lGDEVCodeM = lGDEVCodeM;
    }

    @ColumnInfo(name = "LG_DEV_Code_M")
    @SerializedName("LG_DEV_Code_M")
    @Expose
    public String lGDEVCodeM;
    @NonNull
    @SerializedName("LG_DEV_Exerc")
    @ColumnInfo(name = "LG_DEV_Exerc")
    @Expose
    private String lGDEVExerc;
    @NonNull
    @ColumnInfo(name = "LG_DEV_CodeArt")
    @SerializedName("LG_DEV_CodeArt")
    @Expose
    private String lGDEVCodeArt;
    @SerializedName("LG_DEV_Qte")
    @Expose
    private String lGDEVQte;
    @SerializedName("LG_DEV_Unite")
    @Expose
    private String lGDEVUnite;
    @SerializedName("LG_DEV_PUHT")
    @Expose
    private String lGDEVPUHT;
    @SerializedName("LG_DEV_Tva")
    @Expose
    private String lGDEVTva;
    @SerializedName("LG_DEV_Netht")
    @Expose
    private String lGDEVNetht;
    @SerializedName("LG_DEV_Remise")
    @Expose
    private String lGDEVRemise;
    @SerializedName("LG_DEV_station")
    @Expose
    private String lGDEVStation;
    @SerializedName("LG_DEV_User")
    @Expose
    private String lGDEVUser;
    @NonNull
    @ColumnInfo(name = "LG_DEV_NumOrdre")
    @SerializedName("LG_DEV_NumOrdre")
    @Expose
    public int lGDEVNumOrdre;
    @SerializedName("LG_DEV_Tarif")
    @Expose
    private String lGDEVTarif;
    @SerializedName("LG_DEV_QtePiece")
    @Expose
    private String lGDEVQtePiece;
    @SerializedName("LG_DEV_export")
    @Expose
    private String lGDEVExport;
    @SerializedName("LG_DEV_DDm")
    @Expose
    private String lGDEVDDm;
    @SerializedName("LG_DEV_MntTTC")
    @Expose
    private String lGDEVMntTTC;
    @SerializedName("LG_DEV_MntHT")
    @Expose
    private String lGDEVMntHT;
    @SerializedName("LG_DEV_PUTTC")
    @Expose
    private String lGDEVPUTTC;
    @SerializedName("LG_DEV_TauxFodec")
    @Expose
    private String lGDEVTauxFodec;
    @SerializedName("LG_DEV_TauxDc")
    @Expose
    private String lGDEVTauxDc;
    @SerializedName("LG_DEV_MntBrutHT")
    @Expose
    private String lGDEVMntBrutHT;




    @SerializedName("LG_DEV_CMarq")
    @Expose
    private String lGDEVCMarq;

    @SerializedName("LG_DEV_MntFodec")
    @Expose
    private String lGDEVMntFodec;
    @SerializedName("LG_DEV_MntDc")
    @Expose
    private String lGDEVMntDc;
    @SerializedName("LG_DEV_MntTva")
    @Expose
    private String lGDEVMntTva;
    @SerializedName("LG_DEV_QteGratuite")
    @Expose
    private String lGDEVQteGratuite;
    @Ignore
    private Article article;

    @SerializedName("LG_DEV_NumSerie")
    @ColumnInfo(name = "LG_DEV_NumSerie")
    public String lGDevNumSerie;

    public String getCodeLigne() {
        return codeLigne;
    }

    public void setCodeLigne(String codeLigne) {
        this.codeLigne = codeLigne;
    }

    public String getMsgLigne() {
        return msgLigne;
    }

    public void setMsgLigne(String msgLigne) {
        this.msgLigne = msgLigne;
    }

    @SerializedName("codeLigne")
    @ColumnInfo(name = "codeLigne")
    private transient String codeLigne;


    @SerializedName("msgLigne")
    @ColumnInfo(name = "msgLigne")
    private transient String msgLigne;

    public Article getArticle() {
        return article;
    }

    public void setArticle(Article article) {
        this.article = article;
    }

    public String getLGDEVNumBon() {
        return lGDEVNumBon;
    }

    public void setLGDEVNumBon(String lGDEVNumBon) {
        this.lGDEVNumBon = lGDEVNumBon;
    }

    public String getLGDEVExerc() {
        return lGDEVExerc;
    }

    public void setLGDEVExerc(String lGDEVExerc) {
        this.lGDEVExerc = lGDEVExerc;
    }

    public String getLGDEVCodeArt() {
        return lGDEVCodeArt;
    }

    public void setLGDEVCodeArt(String lGDEVCodeArt) {
        this.lGDEVCodeArt = lGDEVCodeArt;
    }

    public String getLGDEVQte() {
        return lGDEVQte;
    }

    public void setLGDEVQte(String lGDEVQte) {
        this.lGDEVQte = lGDEVQte;
    }

    public String getLGDEVUnite() {
        return lGDEVUnite;
    }

    public void setLGDEVUnite(String lGDEVUnite) {
        this.lGDEVUnite = lGDEVUnite;
    }

    public String getLGDEVPUHT() {
        return lGDEVPUHT;
    }

    public void setLGDEVPUHT(String lGDEVPUHT) {
        this.lGDEVPUHT = lGDEVPUHT;
    }

    public String getLGDEVTva() {
        return lGDEVTva;
    }

    public void setLGDEVTva(String lGDEVTva) {
        this.lGDEVTva = lGDEVTva;
    }

    public String getLGDEVNetht() {
        return lGDEVNetht;
    }

    public void setLGDEVNetht(String lGDEVNetht) {
        this.lGDEVNetht = lGDEVNetht;
    }

    public String getLGDEVRemise() {
        return lGDEVRemise;
    }

    public void setLGDEVRemise(String lGDEVRemise) {
        this.lGDEVRemise = lGDEVRemise;
    }

    public String getLGDEVStation() {
        return lGDEVStation;
    }

    public void setLGDEVStation(String lGDEVStation) {
        this.lGDEVStation = lGDEVStation;
    }

    public String getLGDEVUser() {
        return lGDEVUser;
    }

    public void setLGDEVUser(String lGDEVUser) {
        this.lGDEVUser = lGDEVUser;
    }

    public String getLGDEVTarif() {
        return lGDEVTarif;
    }

    public void setLGDEVTarif(String lGDEVTarif) {
        this.lGDEVTarif = lGDEVTarif;
    }

    public String getLGDEVQtePiece() {
        return lGDEVQtePiece;
    }

    public void setLGDEVQtePiece(String lGDEVQtePiece) {
        this.lGDEVQtePiece = lGDEVQtePiece;
    }

    public String getLGDEVExport() {
        return lGDEVExport;
    }

    public void setLGDEVExport(String lGDEVExport) {
        this.lGDEVExport = lGDEVExport;
    }

    public String getLGDEVDDm() {
        return lGDEVDDm;
    }

    public void setLGDEVDDm(String lGDEVDDm) {
        this.lGDEVDDm = lGDEVDDm;
    }

    public String getLGDEVMntTTC() {
        return lGDEVMntTTC;
    }

    public void setLGDEVMntTTC(String lGDEVMntTTC) {
        this.lGDEVMntTTC = lGDEVMntTTC;
    }

    public String getLGDEVMntHT() {
        return lGDEVMntHT;
    }

    public void setLGDEVMntHT(String lGDEVMntHT) {
        this.lGDEVMntHT = lGDEVMntHT;
    }

    public String getLGDEVPUTTC() {
        return lGDEVPUTTC;
    }

    public void setLGDEVPUTTC(String lGDEVPUTTC) {
        this.lGDEVPUTTC = lGDEVPUTTC;
    }

    public String getLGDEVTauxFodec() {
        return lGDEVTauxFodec;
    }

    public void setLGDEVTauxFodec(String lGDEVTauxFodec) {
        this.lGDEVTauxFodec = lGDEVTauxFodec;
    }

    public String getLGDEVTauxDc() {
        return lGDEVTauxDc;
    }

    public void setLGDEVTauxDc(String lGDEVTauxDc) {
        this.lGDEVTauxDc = lGDEVTauxDc;
    }

    public String getLGDEVMntBrutHT() {
        return lGDEVMntBrutHT;
    }

    public void setLGDEVMntBrutHT(String lGDEVMntBrutHT) {
        this.lGDEVMntBrutHT = lGDEVMntBrutHT;
    }


    public String getLGDEVMntFodec() {
        return lGDEVMntFodec;
    }

    public void setLGDEVMntFodec(String lGDEVMntFodec) {
        this.lGDEVMntFodec = lGDEVMntFodec;
    }

    public String getLGDEVMntDc() {
        return lGDEVMntDc;
    }

    public void setLGDEVMntDc(String lGDEVMntDc) {
        this.lGDEVMntDc = lGDEVMntDc;
    }

    public String getLGDEVMntTva() {
        return lGDEVMntTva;
    }

    public void setLGDEVMntTva(String lGDEVMntTva) {
        this.lGDEVMntTva = lGDEVMntTva;
    }

    public String getLGDEVQteGratuite() {
        return lGDEVQteGratuite;
    }

    public void setLGDEVQteGratuite(String lGDEVQteGratuite) {
        this.lGDEVQteGratuite = lGDEVQteGratuite;
    }

    public String getLGDEVCMarq() {
        return lGDEVCMarq;
    }

    public void setLGDEVCMarq(String lGDEVCMarq) {
        this.lGDEVCMarq = lGDEVCMarq;
    }


}
