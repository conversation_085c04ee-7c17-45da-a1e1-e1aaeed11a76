package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

@Entity
public class DNTypePVente extends BaseModel implements Serializable {

    public DNTypePVente() {
    }


    @NonNull
    public String getCodeTypePV() {
        return codeTypePV;
    }

    public void setCodeTypePV(@NonNull String codeTypePV) {
        this.codeTypePV = codeTypePV;
    }

    public String getTypePV() {
        return typePV;
    }

    public void setTypePV(String typePV) {
        this.typePV = typePV;
    }

    public String getNotePV() {
        return notePV;
    }

    public void setNotePV(String notePV) {
        this.notePV = notePV;
    }

    public int getEtatPv() {
        return etatPv;
    }

    public void setEtatPv(int etatPv) {
        this.etatPv = etatPv;
    }

    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "CodeTypePV")
    @SerializedName("CodeTypePV")
    @Expose
    private String codeTypePV;

    @ColumnInfo(name = "TypePV")
    @SerializedName("TypePV")
    @Expose
    private String typePV;


    @ColumnInfo(name = "NotePV")
    @SerializedName("NotePV")
    @Expose
    private String notePV;


    @ColumnInfo(name = "EtatPv")
    @SerializedName("EtatPv")
    @Expose
    private int etatPv;
}




