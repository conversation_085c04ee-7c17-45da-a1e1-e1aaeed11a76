package com.asmtunis.procaissemobility.data.network.services;

import com.asmtunis.procaissemobility.data.models.Facture;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.TicketUpdate;
import com.asmtunis.procaissemobility.data.models.TicketWithLinesAndPayments;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Headers;
import retrofit2.http.POST;

/**
 * Created by Oussama AZIZI on 3/11/22.
 */

public interface FactureService {
    @Headers("User-Agent: android-api-client")
    @POST("addBatchFactureWithLines")
    Call<List<TicketUpdate>> addBatchFactureWithLines(@Body GenericObject ticketWithLinesAndPayments);


    @Headers("User-Agent: android-api-client")
    @POST("getFacture")
    Call<List<Facture>> getFacture(@Body GenericObject genericObject);

}
