package com.asmtunis.procaissemobility.data.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.Reclamation;

import java.util.List;
@Dao
public interface ReclamationDAO {

    @Query("SELECT * FROM Reclamation")
    List<Reclamation> getAll();


    @Query("SELECT * FROM Reclamation WHERE Rec_Code = :code ")
    Reclamation getOneByCode(String code);

    @Query("SELECT COUNT(*) FROM Reclamation")
    int count();

    @Query("SELECT count(*) FROM Reclamation where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    int getCount();

    @Query("SELECT * FROM Reclamation LIMIT 1")
    Reclamation getOne();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(Reclamation item);


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<Reclamation> items);

    @Query("DELETE FROM Reclamation")
    void deleteAll();

    @Query("SELECT * FROM Reclamation WHERE isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    List<Reclamation> getByQuery();


    @Query("SELECT * FROM Reclamation WHERE isSync=0 and Rec_Image IS NOT NULL and  (Status='INSERTED'  or Status='UPDATED')")
    List<Reclamation> getByQueryWithImage();


    @Query("SELECT ifnull(MAX(cast(substr(Rec_Code,length(:prefix) + 1 ,length('Rec_Code'))as integer)),0)+1 FROM   Reclamation WHERE substr(Rec_Code, 0 ,length(:prefix)+1) = :prefix")
    String getNewCode(String prefix);}

