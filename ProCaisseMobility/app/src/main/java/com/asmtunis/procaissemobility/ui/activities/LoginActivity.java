package com.asmtunis.procaissemobility.ui.activities;

import static com.asmtunis.procaissemobility.App.prefUtils;
import static com.asmtunis.procaissemobility.helper.Globals.getApplicationVerion;

import android.app.Activity;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.text.Html;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Utilisateur;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.datamanager.UtilisateurDataManager;
import com.asmtunis.procaissemobility.helper.AppHelper;
import com.asmtunis.procaissemobility.helper.utils.UIUtils;
import com.blankj.utilcode.util.ObjectUtils;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.mobsandgeeks.saripaar.ValidationError;
import com.mobsandgeeks.saripaar.Validator;
import com.mobsandgeeks.saripaar.annotation.NotEmpty;
import com.mobsandgeeks.saripaar.annotation.Password;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;
import es.dmoral.toasty.Toasty;
import io.paperdb.Paper;

public class LoginActivity extends BaseActivity implements Validator.ValidationListener {

    @BindView(R.id.userLoginInputField)
    @NotEmpty
    EditText userLoginInputField;
    @BindView(R.id.passwordLoginInputField)
    @Password(min = 1, scheme = Password.Scheme.ANY)
    EditText passwordLoginInputField;
    @BindView(R.id.loginButton)
    Button loginButton;
    TextView baseConfigName;

    @BindView(R.id.linkChangeKey)
    TextView linkChangeKey;

    @BindView(R.id.applicationVesion)
    TextView applicationVesion;
    Validator validator;
    Activity context = this;
    Unbinder unbinder;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        prefUtils.setIsConnected(false);
        unbinder = ButterKnife.bind(this);
        applicationVesion.setText(getApplicationVerion());
        // linkChangeKey.setPaintFlags(linkChangeKey.getPaintFlags() | Paint.UNDERLINE_TEXT_FLAG);
        validator = new Validator(this);
        validator.setValidationListener(this);

        //purgeData();
    }

    @Override
    protected int setContentView() {
        return R.layout.activity_authentification;
    }

    @OnClick(R.id.linkChangeKey)
    public void changeKey() {
        new MaterialAlertDialogBuilder(context)
                .setTitle(getString(R.string.confirm))
                .setMessage(getString(R.string.confirm_remove_data))
                //.setPositiveButton("Conservé la base de données" , (dialogInterface, i) -> {
                /*   .setPositiveButton("Changé seulement la base Config" , (dialogInterface, i) -> {
                       prefUtils.clearSharedPreferences();
                      Paper.book().destroy();
                     App.database.appPropertiesDAO().deleteAll();
                    purgeData();
                     AppHelper.doRestart(this);

                    // Intent myIntent = new Intent(LoginActivity.this, SplashscreenActivity.class);
                   //  myIntent.putExtra("actionfromLoginActivity", "load_local_base_config"); //Optional parameters
                   //  LoginActivity.this.startActivity(myIntent);
                 })*/
               .setNeutralButton("Supprimeé la base de données", (dialogInterface, i) -> {
                   prefUtils.setIsConnected(false);
                     prefUtils.clearSharedPreferences();
                     Paper.book().destroy();
                     App.database.appPropertiesDAO().deleteAll();
                     purgeData();
                     AppHelper.doRestart(this);
                 })
                .setNegativeButton("Annuler", (dialogInterface, i) -> dialogInterface.dismiss())
                .show();
    }

    /**
     * detect on login click listener
     */
    @OnClick(R.id.loginButton)
    public void login() {
        // TODO submit data to server...
        validator.validate();
    }

    /**
     * detect when the activity has been destroyed
     */
    @Override
    protected void onDestroy() {
        super.onDestroy();
        unbinder.unbind();
    }


    @Override
    protected void onResume() {
        baseConfigName = findViewById(R.id.baseConfigName);
        baseConfigName.setText(Build.VERSION.SDK_INT >= Build.VERSION_CODES.N ?
                Html.fromHtml(App.prefUtils.getBaseConfigName(), Html.FROM_HTML_MODE_COMPACT) : Html.fromHtml(App.prefUtils.getBaseConfigName()));

        super.onResume();
    }


    /**
     * delete all the data from local database
     */
    void purgeData() {
        App.database.trakingDAO().deleteAll();
        App.database.clientArticlePrixDAO().deleteAll();
        App.database.etablisementDAO().deleteAll();
        App.database.deviseDAO().deleteAll();
        App.database.caisseDAO().deleteAll();
        App.database.carteRestoDAO().deleteAll();
        App.database.bonRetourDAO().deleteAll();
        App.database.ligneBonRetourDAO().deleteAll();
        App.database.appPropertiesDAO().deleteAll();
        App.database.chequeCaisseDAO().deleteAll();
        App.database.articleCodeBarDAO().deleteAll();
        App.database.bonCommandeDAO().deleteAll();
        App.database.deviseDAO().deleteAll();
        App.database.etablisementDAO().deleteAll();
        App.database.etatOrdreMissionDAO().deleteAll();
        App.database.reglementCaisseDAO().deleteAll();
        App.database.familleDAO().deleteAll();
        App.database.fournisseurDAO().deleteAll();
        App.database.ligneBonCommandeDAO().deleteAll();
        App.database.ordreMissionDAO().deleteAll();
        App.database.pricePerStationDAO().deleteAll();
        App.database.sessionCaisseDAO().deleteAll();
        App.database.stationDAO().deleteAll();
        App.database.stationStockDAO().deleteAll();
        App.database.timbreDAO().deleteAll();
        App.database.trakingAlarmDAO().deleteAll();
        App.database.villeDAO().deleteAll();
        App.database.ligneTicketDAO().deleteAll();
        App.database.ticketDAO().deleteAll();
        App.database.articleDAO().deleteAll();
        App.database.clientDAO().deleteAll();
        App.database.clientImooDAO().deleteAll();
        App.database.banqueDAO().deleteAll();
        App.database.carteRestoDAO().deleteAll();
        App.database.prefixeDAO().deleteAll();
        App.database.authorizationDAO().deleteAll();
    }

    @Override
    public void onValidationSucceeded() {

        String userLogin = userLoginInputField.getText().toString();
        String password = passwordLoginInputField.getText().toString();
        loginButton.setEnabled(false);
        UtilisateurDataManager.getInstance().authentification(new GenericObject(prefUtils.getBaseConfig(), new Utilisateur(userLogin, password)), new RemoteCallback<Utilisateur>(context, true) {
            @Override
            public void onSuccess(Utilisateur response) {
            //    Log.d("dgfdgdgd","vvvvv "+ response.getAutorisationUser().toString());
                if (ObjectUtils.isNotEmpty(response) && ObjectUtils.isNotEmpty(response.getCodeUt())) {
                    if (!prefUtils.getUserId().equals(response.getCodeUt())) {
                        prefUtils.setLoadData("");
                        purgeData();
                    }

                     // rEpeated in splash screen :  void getUser()
                     try {
                     prefUtils.setUserAccount(response);
                     } catch (Exception e) {
                     e.printStackTrace();
                     UIUtils.showDialogWithoutChoice(context, context.getString(R.string.error), context.getString(R.string.aut_error), (dialog, which) -> {
                     dialog.dismiss();
                     startActivity(LoginActivity.class);
                     });


                     //if error then only add auth to db
                     if(ObjectUtils.isNotEmpty(response.getAutorisationUser())) {

                         Log.d("dgfdgdgd","ccc "+ response.getAutorisationUser().toString());
                     App.database.authorizationDAO().insertAll(response.getAutorisationUser());
                     }
                     }

                prefUtils.setIsConnected(true);
                 startActivity(new Intent(context, SplashscreenActivity.class));
                 finish();
                } else {
                    loginButton.setEnabled(true);
                    Toasty.error(context, R.string.error_invalid_credentials).show();
                }
            }

            @Override
            public void onUnauthorized() {
                loginButton.setEnabled(true);
            }

            @Override
            public void onFailed(Throwable throwable) {
                loginButton.setEnabled(true);
            }
        });
    }

    /**
     * open a new activity
     */
    void startActivity(Class aClass) {
        startActivity(new Intent(this, aClass));
        finish();
    }

    @Override
    public void onValidationFailed(List<ValidationError> errors) {
        for (ValidationError error : errors) {
            View view = error.getView();
            String message = error.getCollatedErrorMessage(this);

            // Display error messages ;)
            if (view instanceof EditText) {
                ((EditText) view).setError(message);
            } else {
                Toast.makeText(this, message, Toast.LENGTH_LONG).show();
            }
        }
    }
}
