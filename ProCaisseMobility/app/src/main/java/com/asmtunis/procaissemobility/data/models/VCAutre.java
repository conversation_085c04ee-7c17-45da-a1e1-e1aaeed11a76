package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * Created by Oussama AZIZI on 6/24/22.
 */

@Entity
public class VCAutre extends BaseModel {

    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "CodeAutre")
    @SerializedName("CodeAutre")
    @Expose
    private String codeAutre;

    @ColumnInfo(name = "Autre")
    @SerializedName("Autre")
    @Expose
    private String autre;

    @ColumnInfo(name = "AutreNote")
    @SerializedName("AutreNote")
    @Expose
    private String autreNote;

    @ColumnInfo(name = "DateOp")
    @SerializedName("DateOp")
    @Expose
    private String dateOp;

    @ColumnInfo(name = "CodeConcur")
    @SerializedName("CodeConcur")
    @Expose
    private String codeConcur;

    @ColumnInfo(name = "NoteOp")
    @SerializedName("NoteOp")
    @Expose
    private String noteOp;

    @ColumnInfo(name = "CodeUser")
    @SerializedName("CodeUser")
    @Expose
    private Integer codeUser;

    @ColumnInfo(name = "InfoOp1")
    @SerializedName("InfoOp1")
    @Expose
    private String infoOp1;

    @ColumnInfo(name = "CodeTypeCom")
    @SerializedName("CodeTypeCom")
    @Expose
    private String codeTypeCom;

    @ColumnInfo(name = "Code_Mob")
    @SerializedName("Code_Mob")
    @Expose
    private String codeMob;

    public String getCodeMob() {
        return codeMob;
    }

    public void setCodeMob(String codeMob) {
        this.codeMob = codeMob;
    }

    public String getCodeAutre() {
        return codeAutre;
    }

    public void setCodeAutre(String codeAutre) {
        this.codeAutre = codeAutre;
    }

    public String getAutre() {
        return autre;
    }

    public void setAutre(String autre) {
        this.autre = autre;
    }

    public String getAutreNote() {
        return autreNote;
    }

    public void setAutreNote(String autreNote) {
        this.autreNote = autreNote;
    }

    public String getDateOp() {
        return dateOp;
    }

    public void setDateOp(String dateOp) {
        this.dateOp = dateOp;
    }

    public String getCodeConcur() {
        return codeConcur;
    }

    public void setCodeConcur(String codeConcur) {
        this.codeConcur = codeConcur;
    }

    public String getNoteOp() {
        return noteOp;
    }

    public void setNoteOp(String noteOp) {
        this.noteOp = noteOp;
    }

    public int getCodeUser() {
        return codeUser;
    }

    public void setCodeUser(int codeUser) {
        this.codeUser = codeUser;
    }

    public String getInfoOp1() {
        return infoOp1;
    }

    public void setInfoOp1(String infoOp1) {
        this.infoOp1 = infoOp1;
    }

    public String getCodeTypeCom() {
        return codeTypeCom;
    }

    public void setCodeTypeCom(String codeTypeCom) {
        this.codeTypeCom = codeTypeCom;
    }



    public VCAutre(String codeAutre) {
        this.codeAutre = codeAutre;


    }




    public VCAutre() {
    }
}
