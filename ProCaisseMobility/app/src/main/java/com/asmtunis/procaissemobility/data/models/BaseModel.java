package com.asmtunis.procaissemobility.data.models;

import  androidx.room.ColumnInfo;

import com.asmtunis.procaissemobility.data.converters.Exclude;
import com.asmtunis.procaissemobility.helper.Globals;

import java.io.Serializable;

/**
 * Created by PC on 11/8/2017.
 */

public class BaseModel implements Serializable{
    @ColumnInfo(name = "Status")
     @Exclude
    public String status = Globals.ITEM_STATUS.SELECTED.getStatus();

    @ColumnInfo(name = "IsSync")
    @Exclude
    public  boolean isSync=true;

    public BaseModel(String status, boolean isSync) {
        this.status = status;
        this.isSync = isSync;
    }

    public BaseModel() {
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public boolean isSync() {
        return isSync;
    }

    public void setSync(boolean sync) {
        isSync = sync;
    }

    @Override
    public String toString() {
        return "BaseModel{" +
                '}';
    }
}
