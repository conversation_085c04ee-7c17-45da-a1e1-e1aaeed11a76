package com.asmtunis.procaissemobility.data.network.services;

import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.LigneBonCommande;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

public interface LigneBonCommandeService {
    @POST("getLigneCommande")
    Call<List<LigneBonCommande>> getLigneBonCommande(@Body GenericObject genericObject);

    @POST("addBatchLigneCommande")
    Call<List<LigneBonCommande>> addBatchLigneCommande(@Body GenericObject genericObject);
}
