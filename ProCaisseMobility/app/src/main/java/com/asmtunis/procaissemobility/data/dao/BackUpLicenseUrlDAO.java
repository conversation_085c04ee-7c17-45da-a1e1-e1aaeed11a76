package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.BonRetour;
import com.asmtunis.procaissemobility.data.models.LicenseResponse;
import com.asmtunis.procaissemobility.data.models.LicenseResponseItem;

import java.util.List;


@Dao
public interface BackUpLicenseUrlDAO {
    @Query("SELECT * FROM LicenseResponseItem")
    List<LicenseResponseItem> getAll();



    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(List<LicenseResponseItem> item);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<LicenseResponseItem> items);


    @Query("SELECT count(*) FROM BonRetour")
    int getCount();



    @Query("DELETE FROM LicenseResponseItem")
    void deleteAll();



}
