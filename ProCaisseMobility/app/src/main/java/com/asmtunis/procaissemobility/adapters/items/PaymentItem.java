package com.asmtunis.procaissemobility.adapters.items;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Paint;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.RecyclerView;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.ReglementCaisse;
import com.asmtunis.procaissemobility.data.models.Ticket;
import com.asmtunis.procaissemobility.helper.utils.DateUtils;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.listener.ItemCallback;
import com.asmtunis.procaissemobility.listener.MenuItemClickListener;
import com.asmtunis.procaissemobility.listener.MenuItemsAction;
import com.mikepenz.fastadapter.items.AbstractItem;
import com.mikepenz.fontawesome_typeface_library.FontAwesome;
import com.mikepenz.iconics.IconicsDrawable;
import com.rengwuxian.materialedittext.MaterialEditText;

import net.cachapa.expandablelayout.ExpandableLayout;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

import static android.content.Context.LAYOUT_INFLATER_SERVICE;

/**
 * Created by PC on 10/11/2017.
 */

public class PaymentItem extends AbstractItem<PaymentItem, PaymentItem.ViewHolder> {
    protected ItemCallback itemCallback;

    private Context context;
    public ReglementCaisse reglementCaisse;
    int menuId;
    MenuItemsAction menuItemsAction;

    public PaymentItem(Context context, ReglementCaisse reglementCaisse, int menuId, MenuItemsAction
            menuItemsAction, ItemCallback itemCallback) {
        this.reglementCaisse = reglementCaisse;
        this.context = context;
        this.itemCallback = itemCallback;
        this.menuItemsAction = menuItemsAction;
        this.menuId = menuId;
    }

    //The unique ID for this type of item
    @Override
    public int getType() {
        return R.id.fastadapter_caisse_item_id;
    }

    //The unit_price_dialog to be used for this type of item
    @Override
    public int getLayoutRes() {
        return R.layout.list_item;
    }

    //The logic to bind your data to the view
    @Override
    public void bindView(final ViewHolder viewHolder, List<Object> payloads) {
        //call super so the selection is already handled for you
        super.bindView(viewHolder, payloads);

        viewHolder.toolbar.setTitle(reglementCaisse.getrEGCNomPrenom());

        Ticket ticket = App.database.ticketDAO().getOneByCode((int) reglementCaisse.getrEGCNumTicket(), App.prefUtils.getExercice());
        if (ticket != null) {
            //  viewHolder.toolbar.setSubtitle(reglementCaisse.getrEGCNumTicket() > 0 ? String.format(context.getString(R.string.ticket_number_field), App.database.ticketDAO().getOneByCode((int) reglementCaisse.getrEGCNumTicket(), App.prefUtils.getExercice()).tikNumTicketM + "") : context.getString(R.string.credit_label));
            // viewHolder.toolbar.setSubtitle(reglementCaisse.getrEGCNumTicket() > 0 ? App.database.ticketDAO().getOneByCode((int) reglementCaisse.getrEGCNumTicket(), App.prefUtils.getExercice()).tikNumTicketM : context.getString(R.string.credit_label));


            String tikNum = "";
            if (String.valueOf(ticket.gettIK_NumeroBL()).equals("null")) {

                tikNum = ticket.tikNumTicketM;
                if (tikNum.contains("_")) {
                    String[] numTicket = tikNum.split("_");
                    tikNum = context.getString(R.string.ticket_number_field, numTicket[2] + "_" + numTicket[3] + "_" + numTicket[4]);
                }

            } else
                tikNum = context.getString(R.string.fact, String.valueOf(ticket.gettIK_NumeroBL()));

            if (ticket.tIKAnnuler == 1) {
                viewHolder.toolbar.setSubtitleTextColor(context.getResources().getColor(R.color.errorColor));
                viewHolder.toolbar.setTitleTextColor(context.getResources().getColor(R.color.errorColor));
                tikNum = tikNum + " (Annuler)";
            } else {
                viewHolder.toolbar.setSubtitleTextColor(context.getResources().getColor(R.color.gray));
                viewHolder.toolbar.setTitleTextColor(context.getResources().getColor(R.color.black));
            }


            if (reglementCaisse.getrEGNumTicketPart() != null) {
                if (reglementCaisse.getrEGNumTicketPart() > 0)
                    viewHolder.toolbar.setSubtitle(context.getString(R.string.paiement_Partiel));
                else  viewHolder.toolbar.setSubtitle(reglementCaisse.getrEGCNumTicket() > 0 ? tikNum : context.getString(R.string.credit_label));

            } else{
                if(reglementCaisse.getrEGCRemarque().equals("Regler Acpt"))
                    viewHolder.toolbar.setSubtitle("Reglement Libre");
              else  viewHolder.toolbar.setSubtitle(reglementCaisse.getrEGCNumTicket() > 0 ? tikNum : context.getString(R.string.credit_label));
            }


        }
        else {
            if (reglementCaisse.getrEGNumTicketPart() != null) {
                if (reglementCaisse.getrEGNumTicketPart() > 0)
                    viewHolder.toolbar.setSubtitle(context.getString(R.string.paiement_Partiel));
                else{
                    if(reglementCaisse.getrEGCRemarque().equals("Regler Acpt"))
                        viewHolder.toolbar.setSubtitle("Reglement Libre");
                    else viewHolder.toolbar.setSubtitle(reglementCaisse.getrEGCNumTicket() > 0 ? String.valueOf(reglementCaisse.rEGCNumTicket) : context.getString(R.string.credit_label));
                }

            } else{
                if(reglementCaisse.getrEGCRemarque().equals("Regler Acpt"))
                    viewHolder.toolbar.setSubtitle("Reglement Libre");
                else viewHolder.toolbar.setSubtitle(reglementCaisse.getrEGCNumTicket() > 0 ? String.valueOf(reglementCaisse.rEGCNumTicket) : context.getString(R.string.credit_label));
            }


        }


        viewHolder.price.setText(StringUtils.priceFormat(reglementCaisse.getrEGCMntEspece() + reglementCaisse.getrEGCMntChQue() + reglementCaisse.getrEGCMntTraite()));
        //  reglementCaisse.getrEGCMntChQue() NULL
        /*viewHolder.dateCreation.setText(DateUtils.dateToStr(
                DateUtils.strToDate(reglementCaisse.getrEGCDateReg().replaceAll("/", "-"),
                        "yyyy-MM-dd HH:mm:ss"), "yyyy-MM-dd HH:mm:ss"));*/

        viewHolder.dateCreation.setText(reglementCaisse.getrEGCDateReg());
        //      viewHolder.dateCreation.setText(reglementCaisse.getrEGCDateReg().replaceAll("/", "-"));
        if (itemCallback != null) {
            viewHolder.itemView.setOnClickListener(v -> onViewClick(viewHolder));
            viewHolder.toolbar.setOnClickListener(v -> onViewClick(viewHolder));
        }
        setRegType(viewHolder, reglementCaisse);


        viewHolder.toolbar.inflateMenu(menuId);

        if (menuId != 0) {
            viewHolder.toolbar.getMenu().findItem(R.id.edit_item).setVisible(false);
            viewHolder.toolbar.getMenu().findItem(R.id.invoice_item).setVisible(false);
            viewHolder.toolbar.getMenu().findItem(R.id.print_item).setVisible(true);

            viewHolder.toolbar.setOnMenuItemClickListener(new MenuItemClickListener<ReglementCaisse>(reglementCaisse, menuItemsAction));
            viewHolder.toolbar.getMenu().findItem(R.id.print_item).setIcon(new IconicsDrawable(context)
                    .icon(FontAwesome.Icon.faw_print)
                    .color(context.getResources().getColor(R.color.material_teal700))
                    .sizeDp(20));
            //if(ticket.tIK_Source == null) viewHolder.toolbar.getMenu().findItem(R.id.edit_item).setVisible(false);


        }
    }


    void onViewClick(ViewHolder viewHolder) {
        if (reglementCaisse != null) {
            if (itemCallback == null) {
                return;
            } else {
                itemCallback.onItemClicked(viewHolder, reglementCaisse);
            }
        }
    }

    private void setRegType(ViewHolder viewHolder, ReglementCaisse reglementCaisse) {
        if (Math.abs(reglementCaisse.getrEGCMntEspece()) > 0)
            viewHolder.type_espece.setVisibility(View.VISIBLE);
        else viewHolder.type_espece.setVisibility(View.GONE);

        if (Math.abs(reglementCaisse.getrEGCMntChQue()) > 0)
            viewHolder.type_cheque.setVisibility(View.VISIBLE);
        else viewHolder.type_cheque.setVisibility(View.GONE);

        if (Math.abs(reglementCaisse.getrEGCMntTraite()) > 0)
            viewHolder.type_traite.setVisibility(View.VISIBLE);
        else viewHolder.type_traite.setVisibility(View.GONE);
    }

    //reset the view here (this is an optional method, but recommended)
    @Override
    public void unbindView(final ViewHolder holder) {
        super.unbindView(holder);
        holder.toolbar.setTitle(null);
        holder.toolbar.setSubtitle(null);
    }

    //Init the viewHolder for this Item
    @Override
    public ViewHolder getViewHolder(View v) {
        return new ViewHolder(v);
    }

    void setText(MaterialEditText editText, String s) {
        try {
            if (s.length() <= 1) {
                editText.setVisibility(View.GONE);
            } else {
                editText.setText(s);
            }
        } catch (NullPointerException e) {
        }
    }

    //The viewHolder used for this item. This viewHolder is always reused by the RecyclerView so scrolling is blazing fast
    public class ViewHolder extends RecyclerView.ViewHolder {

        @BindView(R.id.expandable_layout)
        public ExpandableLayout expandableLayout;
        @BindView(R.id.toolbar)
        public
        Toolbar toolbar;
        @BindView(R.id.price)
        public TextView price;
        @BindView(R.id.type_espece)
        public TextView type_espece;
        @BindView(R.id.type_cheque)
        public TextView type_cheque;
        @BindView(R.id.type_traite)
        public TextView type_traite;
        @BindView(R.id.dateCreation)
        public TextView dateCreation;

        @BindView(R.id.ll)
        public LinearLayout ll;


        public ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);

            LayoutInflater inflater = (LayoutInflater) context.getSystemService(LAYOUT_INFLATER_SERVICE);
            expandableLayout.addView(inflater.inflate(R.layout.ticket_list_item_details_view, null));
        }
    }

}
