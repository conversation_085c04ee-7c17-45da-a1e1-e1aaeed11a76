package com.asmtunis.procaissemobility.data.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.Authorization;

import java.util.List;

/**
 * Created by Oussama AZIZI on 5/27/22.
 */

@Dao
public interface AuthorizationDAO {
        @Insert(onConflict = OnConflictStrategy.REPLACE)
        void insertAll(List<Authorization> authorizationList);

        @Query("delete from Authorization")
        void deleteAll();

        @Query("select * from Authorization")
        List<Authorization> getAll();

        @Query("select * from Authorization WHERE AutoCodeAu = :idAuth")
        Authorization getAuthState(int idAuth);

        @Query("SELECT EXISTS(select * from Authorization WHERE AutoCodeAu = :idAuth and AutEtat = 1)")
        boolean hasAuth(int idAuth);
}
