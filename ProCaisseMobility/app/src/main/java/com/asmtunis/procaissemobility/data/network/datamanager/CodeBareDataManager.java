package com.asmtunis.procaissemobility.data.network.datamanager;


import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.ArticleCodeBar;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.CodeBareService;

import java.util.List;

public class CodeBareDataManager {
    private static CodeBareDataManager sInstance;
    private final CodeBareService mCodeBareService;

    public CodeBareDataManager() {
        mCodeBareService = new ServiceFactory<>(CodeBareService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(), App.prefUtils.getServerPort(), "Article")).makeService();
    }

    public static CodeBareDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new CodeBareDataManager();
        }
        return sInstance;
    }

    public void getCodeBares(GenericObject genericObject,
                             RemoteCallback<List<ArticleCodeBar>> listener) {
        mCodeBareService.getArticleCodeBare(genericObject)
                .enqueue(listener);
    }

    public void addArticleCodeBarMobile(GenericObject genericObject,
                                        RemoteCallback<List<ArticleCodeBar>> listener) {
        mCodeBareService.addArticleCodeBarMobile(genericObject)
                .enqueue(listener);
    }


}


