package com.asmtunis.procaissemobility.data.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.ChequeCaisse;

import java.util.List;

/**
 * Created by PC on 11/18/2017.
 */
@Dao
public interface ChequeCaisseDAO {

    @Query("SELECT * FROM ChequeCaisse")
    List<ChequeCaisse> getAll();

    @Query("SELECT * FROM ChequeCaisse WHERE reglement_m = :code")
    List<ChequeCaisse> getByReglementM(String code);


    @Query("SELECT * FROM ChequeCaisse WHERE NumCheque = :num ")
    ChequeCaisse getOneByCode(String num);

    @Query("SELECT * FROM ChequeCaisse LIMIT 1")
    ChequeCaisse getOne();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(ChequeCaisse item);


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<ChequeCaisse> items);

    @Query("DELETE FROM ChequeCaisse where Status='SELECTED'")
    void deleteAll();


}
