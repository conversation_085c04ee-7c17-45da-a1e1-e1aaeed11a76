package com.asmtunis.procaissemobility.ui.fragments;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.SearchManager;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.appcompat.widget.SearchView;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.LinearLayoutManager;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewPropertyAnimator;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.Toast;

import com.afollestad.materialdialogs.MaterialDialog;
import com.arasthel.asyncjob.AsyncJob;
import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.adapters.items.RetourItem;
import com.asmtunis.procaissemobility.adapters.tables.LigneBonRetourTableDataAdapter;
import com.asmtunis.procaissemobility.adapters.tables.LigneTicketRestoTableDataAdapter;
import com.asmtunis.procaissemobility.data.models.TraiteCaisse;
import com.asmtunis.procaissemobility.data.viewModels.BonRetourViewModel;
import com.asmtunis.procaissemobility.data.models.Article;
import com.asmtunis.procaissemobility.data.models.BonRetour;
import com.asmtunis.procaissemobility.data.models.ChequeCaisse;
import com.asmtunis.procaissemobility.data.models.LigneBonRetour;
import com.asmtunis.procaissemobility.data.models.TicketWithLinesAndPayments;
import com.asmtunis.procaissemobility.helper.BluetoothService;
import com.asmtunis.procaissemobility.helper.Globals;
import com.asmtunis.procaissemobility.helper.PrinterHelper;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.helper.utils.Utils;
import com.asmtunis.procaissemobility.helper.wifi_print.Comman;
import com.asmtunis.procaissemobility.helper.wifi_print.WifiPrint;
import com.asmtunis.procaissemobility.ui.activities.BonRetourActivity;
import com.asmtunis.procaissemobility.ui.activities.DeviceListActivity;
import com.asmtunis.procaissemobility.ui.activities.MainActivity;
import com.asmtunis.procaissemobility.ui.components.SortableLigneCommandeTableView;
import com.asmtunis.procaissemobility.ui.components.SortableLigneTicketTableView;
import com.asmtunis.procaissemobility.ui.fragments.base.BaseListFragment;
import com.asmtunis.procaissemobility.listener.ItemCallback;
import com.asmtunis.procaissemobility.ui.components.LockableBottomSheetBehavior;
import com.asmtunis.procaissemobility.listener.MenuItemsAction;
import com.asmtunis.procaissemobility.ui.components.SortableBankCheckLineTableView;
import com.asmtunis.procaissemobility.ui.components.SortableLigneRetourTableView;
import com.asmtunis.procaissemobility.ui.components.SortableRestoTicketTableView;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.snackbar.Snackbar;
import com.mikepenz.google_material_typeface_library.GoogleMaterial;
import com.mikepenz.iconics.IconicsDrawable;
import com.mikepenz.iconics.view.IconicsButton;

import net.cachapa.expandablelayout.ExpandableLayout;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

import butterknife.BindView;
import es.dmoral.toasty.Toasty;
import id.ionbit.ionalert.IonAlert;

import static android.app.Activity.RESULT_OK;
import static android.content.Context.LAYOUT_INFLATER_SERVICE;
import static com.asmtunis.procaissemobility.helper.Globals.DEFAULT_ENCODING;
import static com.asmtunis.procaissemobility.helper.Globals.DEFAULT_VALUE;
import static net.cachapa.expandablelayout.ExpandableLayout.State.EXPANDED;
import static net.cachapa.expandablelayout.ExpandableLayout.State.EXPANDING;


public class RetourFragment extends BaseListFragment<RetourItem> {
    public static final int REQUEST_CONNECT_DEVICE = 845;
    public static final int REQUEST_ENABLE_BT = 875;
    public static BluetoothDevice con_dev = null;
    public BluetoothService mService = null;
    int ticketNumber = 0;
    public final static int REQUEST_TICKET_CODE = 15645;
    MenuItem add;
    SearchView searchView;
    @BindView(R.id.layout_bottom_sheet)
    RelativeLayout mLayoutBottomSheet;
    private BottomSheetBehavior mBottomSheetBehavior;
    @BindView(R.id.bottom_sheet_content)
    FrameLayout bottomSheetContent;
    ViewPropertyAnimator mAppBarLayoutAnimation;
    List<ChequeCaisse> chequeCaisseList = new ArrayList<>();
    List<TraiteCaisse> traiteCaisseList = new ArrayList<>();
    private static final int UNSELECTED = -1;
    private int selectedItem = UNSELECTED;
    TicketWithLinesAndPayments ticketWithLinesAndPayments = null;
    private int count = 0;

    @Override
    protected <T> Comparator getComparator() {
        return null;
    }

    public RetourFragment() {
    }

    public static RetourFragment newInstance() {
        RetourFragment fragment = new RetourFragment();
        Bundle args = new Bundle();
        args.putString("Title", KEY);
        fragment.setArguments(args);
        return (fragment);
    }

    @Override
    protected int getFragmentLayout() {
        return R.layout.list_recycler_view;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {

        return super.onCreateView(inflater, container, savedInstanceState);

    }

    @Override
    public void onViewCreated(@NonNull View view, final Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mBottomSheetBehavior = LockableBottomSheetBehavior.from(mLayoutBottomSheet);

        AppBarLayout appBarLayout = ((AppBarLayout) ((MainActivity) getActivity()).findViewById(R.id.app_bar_layout));

        mBottomSheetBehavior.setBottomSheetCallback(new LockableBottomSheetBehavior.BottomSheetCallback() {
            @SuppressLint("WrongConstant")
            @Override
            public void onStateChanged(@NonNull View bottomSheet, int newState) {
                switch (newState) {
                    case LockableBottomSheetBehavior.STATE_COLLAPSED:
                        bottomSheetContent.removeAllViews();
                        break;
                    case LockableBottomSheetBehavior.STATE_EXPANDED:
                        if (mBottomSheetBehavior instanceof LockableBottomSheetBehavior) {
                            ((LockableBottomSheetBehavior) mBottomSheetBehavior).setLocked(true);
                        }
                        break;
                    case BottomSheetBehavior.STATE_SETTLING:
                        break;
                    case BottomSheetBehavior.STATE_DRAGGING:
                        mBottomSheetBehavior.setState(EXPANDED);
                        break;
                }
            }

            @Override
            public void onSlide(@NonNull View bottomSheet, float slideOffset) {
            }
        });
    }

    @Override
    protected void getData(boolean getFromService) {
        BonRetourViewModel.getInstance(this).getBystation(App.prefUtils.getUserStationId(),App.prefUtils.getCaisseId()).observe(this, bonRetours -> {
            final LinearLayoutManager layoutManager = new LinearLayoutManager(context);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            recyclerView.setLayoutManager(layoutManager);
            if (!bonRetours.isEmpty()) {
                showList(bonRetours);
            } else {
                emptyView.setVisibility(View.VISIBLE);
                swipeRefreshLayout.setVisibility(View.GONE);
            }
        });
    }

    private void showList(List<BonRetour> bonRetours) {
        items.clear();
        for (BonRetour item : bonRetours) {
            List<LigneBonRetour> ligneBonRetours = App.database.ligneBonRetourDAO().getByBRNum(item.getBORNumero());
            putArticleInLignes(ligneBonRetours);
            calcuteTotalAndRemisePrices(item, ligneBonRetours);
            if (item != null)
                try {
                    items.add(new RetourItem(context, false, item, R.menu.ticket_item_menu, new MenuItemsAction<BonRetour>() {

                        @Override
                        public boolean itemsAction(MenuItem menuItem, int position) {
                            return false;
                        }

                        @RequiresApi(api = Build.VERSION_CODES.N)
                        @Override
                        public boolean itemsAction(MenuItem menuItem, BonRetour item) {
                            if(menuItem.getItemId() == R.id.print_item) {
                                if (ligneBonRetours != null && ligneBonRetours.size() > 0) {
                                    if(prefUtils.getPrintA4Enabled()){
                                        new File(Objects.requireNonNull(Comman.Companion.getAppPath(context))).mkdirs();
                                        WifiPrint.Companion.createBonRetourPDFFile(context, item, ligneBonRetours,() -> {

                                        });

                                    }
                                    else {
                                        printTicket(item, ligneBonRetours);
                                    }

                                } else {
                                    Toasty.info(context, "Aucune ligne à été trouvée pour cette B.R!!").show();
                                }
                            }
                            else if(menuItem.getItemId() == R.id.edit_item) {
                                intent = new Intent(MainActivity.instance, BonRetourActivity.class);
                                intent.putExtra(Globals.TICKET_TO_UPDATE, item);
                                intent.putExtra(Globals.UPDATE_TICKET, true);
                                MainActivity.instance.startActivityForResult(intent, REQUEST_TICKET_CODE);
                            }
                            return false;
                        }
                    }, new ItemCallback<BonRetour, RetourItem.ViewHolder>() {
                        @Override
                        public void onItemClicked(BonRetour itemIndex) {
                        }

                        @Override
                        public void onItemClicked(RetourItem.ViewHolder viewHolder, BonRetour itemIndex) {
                            if (mBottomSheetBehavior.getState() != BottomSheetBehavior.STATE_EXPANDED) {
                                mBottomSheetBehavior.setState(BottomSheetBehavior.STATE_EXPANDED);
                                initUI(itemIndex);
                            }
                        }


                    }));
                } catch (Exception e) {
                    Log.d("ddd", e.getMessage());
                }

        }
        // Send the result to the UI thread and show it on a Toast
        AsyncJob.doOnMainThread(() -> {
                    fastItemAdapter.add(items);
                    fastItemAdapter.withSelectable(false);
                    fastItemAdapter.notifyAdapterDataSetChanged();
                    if (fastItemAdapter != null) {
                        recyclerView.setAdapter(fastItemAdapter);
                        if (emptyView != null && swipeRefreshLayout != null) {
                            emptyView.setVisibility(View.GONE);
                            swipeRefreshLayout.setVisibility(View.VISIBLE);
                        }

                    } else {
                        emptyView.setVisibility(View.VISIBLE);
                        swipeRefreshLayout.setVisibility(View.GONE);
                    }

                }
        );
    }

    private void calcuteTotalAndRemisePrices(BonRetour item, List<LigneBonRetour> ligneBonRetours) {

        double mntRemise = 0.0;
        double total = 0.0;
        for (LigneBonRetour ligneTicket : ligneBonRetours
        ) {
            try {
                mntRemise += Double.parseDouble(ligneTicket.getLIGBonEntreeQte()) *
                        ligneTicket.getArticle().pvttc * Double.parseDouble(ligneTicket.getLIGBonEntreeRemise()) / 100;
                total += Double.parseDouble(ligneTicket.getLIGBonEntreeQte()) * ligneTicket.getArticle().pvttc;
            } catch (Exception e) {
                Log.d("errcoammabdefragment", e.getMessage());
            }
        }
        item.setBORMntRemise(mntRemise + "");
        item.setBORMntTTC(total + "");
    }

    private void putArticleInLignes(List<LigneBonRetour> ligneTickets) {
        for (int i = 0; i < ligneTickets.size(); i++) {
            ligneTickets.get(i).setArticle(App.database.articleDAO().getOneByCodeAndStation(
                    ligneTickets.get(i).getLIGBonEntreeCodeArt(), ligneTickets.get(i).getLIGBonEntreeStation()));
        }
    }


    private void printTicket(BonRetour item, List<LigneBonRetour> ligneBonRetours) {
        mService = new BluetoothService(context, new Handler() {
            @Override
            public void handleMessage(Message msg2) {
                switch (msg2.what) {
                    case BluetoothService.MESSAGE_STATE_CHANGE:
                        switch (msg2.arg1) {
                            case BluetoothService.STATE_CONNECTED:
                                Toast.makeText(context, "Connect successful",
                                        Toast.LENGTH_SHORT).show();

                                try {
                                    new PrinterHelper(mService, DEFAULT_ENCODING).printBonRetour(context, item, ligneBonRetours, false);
                                } catch (IOException e) {
                                    e.printStackTrace();
                                }
                                break;
                            case BluetoothService.STATE_CONNECTING:
                                //   Log.d(TAG, "Bluetooth is connecting");
                                break;
                            case BluetoothService.STATE_LISTEN:
                            case BluetoothService.STATE_NONE:
                                // Log.d(TAG, "Bluetooth state listen or none");
                                break;
                        }
                        break;
                    case BluetoothService.MESSAGE_CONNECTION_LOST:
                        Toast.makeText(context, "Device connection was lost",
                                Toast.LENGTH_SHORT).show();

                        break;
                    case BluetoothService.MESSAGE_UNABLE_CONNECT:
                        Toast.makeText(context, "Unable to connect device",
                                Toast.LENGTH_SHORT).show();
                        break;
                }
            }

        });
        if (!mService.isBTopen()) {
            Intent enableIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
            startActivityForResult(enableIntent, REQUEST_ENABLE_BT);
        }
        if (!mService.isAvailable()) {
            Toast.makeText(context, "Bluetooth is not available", Toast.LENGTH_LONG).show();
        } else {
            Intent serverIntent = new Intent(context, DeviceListActivity.class);
            startActivityForResult(serverIntent, REQUEST_CONNECT_DEVICE);
        }
    }

    @Override
    protected boolean getFilter(RetourItem item, CharSequence constraint) {
        return false;
    }

    @RequiresApi(api = Build.VERSION_CODES.O)
    @Override
    public void onCreateOptionsMenu(Menu menu, MenuInflater inflater) {
        inflater.inflate(R.menu.ticket_menu, menu);
        SearchManager searchManager =
                (SearchManager) context.getSystemService(Context.SEARCH_SERVICE);
        searchView =
                (SearchView) menu.findItem(R.id.search).getActionView();
        searchView.setSearchableInfo(
                searchManager.getSearchableInfo(context.getComponentName()));

        if (android.os.Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB) {
            searchView.setOnQueryTextListener(new SearchView.OnQueryTextListener() {
                @Override
                public boolean onQueryTextSubmit(String s) {
                    return true;

                }


                @Override
                public boolean onQueryTextChange(String s) {
                    fastItemAdapter.filter(s);
                    return true;
                }
            });
        } else {
            searchView.setVisibility(View.GONE);
        }

        add = menu.findItem(R.id.add);
        add.setIcon(new IconicsDrawable(getContext()).icon(GoogleMaterial.Icon.gmd_add_shopping_cart)
                .color(Color.WHITE).sizeDp(24));
        add.setOnMenuItemClickListener(item -> {
            add.setEnabled(false);
            //enable button after 1000 millisecond
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                add.setEnabled(true);
            }, 1000);
          //  if(prefUtils.getClotSess() && !Utils.isNotSessionCaisseExpired(prefUtils.getSessionDateOuv())/* == -1*/) {
            if ((prefUtils.getClotSess() && Utils.isSessionCaisseExpired(prefUtils.getSessionDateOuv()) == -1)||(prefUtils.getSessionDateOuv().equals("") && App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.br)))) {
                    new MaterialDialog.Builder(context)
                        .title(R.string.confirmation)
                        .content(R.string.session_caisse_expired)
                        .positiveText(R.string.yes)
                        .negativeText(R.string.no)
                        .onPositive((dialog, which) -> {
                            MainActivity.instance.closeSession(true);
                        }).onNegative((dialog, which) -> {
                            dialog.dismiss();
                            })
                        .show();
            }
            else {
                    if(App.prefUtils.getSessionCaisseId().equals(DEFAULT_VALUE) && prefUtils.getBlAuthorization()){
                        new MaterialDialog.Builder(context)
                                //  .title(R.string.confirmation)
                                .content("ID Session Invalid")
                                .positiveText(R.string.quitter)
                                // .negativeText(R.string.no)
                                .onPositive((dialog, which) -> {
                                    dialog.dismiss();
                                })

                                //.onNegative((dialog, which) -> dialog.dismiss())
                                .show();

                    }else {
                        if(App.database.stationStockDAO().getByCodeStation(App.prefUtils.getUserStationId()).isEmpty()){
                            new IonAlert(getActivity(), IonAlert.WARNING_TYPE)
                                    .setTitleText(getString(R.string.no_article_found))
                                    .setContentText(getString(R.string.no_article_found_in_this_station))
                                    .setConfirmText("OK")
                                    .show();
                            return false;
                        }
                        intent = new Intent(context, BonRetourActivity.class);
                        intent.putExtra(Globals.TICKET_NUMBER_VALUE, ticketNumber);
                        getActivity().startActivityForResult(intent, REQUEST_TICKET_CODE);
                    }

            }


            return false;
        });

    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mService != null)
            mService.stop();
        mService = null;
    }

    @Override
    public void onResume() {
        super.onResume();
        if (add != null) {
            add.setEnabled(true);
        }
    }

    @Override
    public void itemsFiltered(@Nullable CharSequence constraint, @Nullable List results) {

    }


    @Override
    public void onReset() {

    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case REQUEST_ENABLE_BT:
                if (resultCode == RESULT_OK) {
                    Toast.makeText(context, "Bluetooth open successful", Toast.LENGTH_LONG).show();
                } else {
                    Toast.makeText(context, "Bluetooth failed to connect", Toast.LENGTH_LONG).show();
                }
                break;
            case REQUEST_CONNECT_DEVICE:
                if (resultCode == RESULT_OK) {
                    String address = data.getExtras()
                            .getString(DeviceListActivity.EXTRA_DEVICE_ADDRESS);
                    con_dev = mService.getDevByMac(address);

                    mService.connect(con_dev);
                }
                break;
            case REQUEST_TICKET_CODE:
                if (resultCode == RESULT_OK) {
                    getData(false);
                    if (context.getCurrentFocus() != null)
                        Snackbar.make(context.getCurrentFocus(), R.string.added_succesfully, Snackbar.LENGTH_LONG)
                                .show();
                }
                break;


        }
    }


    private void initUI(BonRetour ticket) {
        LayoutInflater inflater = (LayoutInflater) context.getSystemService(LAYOUT_INFLATER_SERVICE);
        bottomSheetContent.addView(inflater.inflate(R.layout.ticket_subitem, null));
        SortableLigneTicketTableView tableViewLigneTicket = context.findViewById(R.id.tableViewLigneTicket);
        SortableLigneRetourTableView tableViewLigneRetour = context.findViewById(R.id.tableViewLigneRetour);
        SortableLigneCommandeTableView tableViewLigneCommande = context.findViewById(R.id.tableViewLigneCommande);



        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            tableViewLigneRetour.setNestedScrollingEnabled(true);
        }
        SortableBankCheckLineTableView ligneCheckTableView = context.findViewById(R.id.bankDataTableView);
        SortableRestoTicketTableView ligneTraiteTableView = context.findViewById(R.id.restoTicketDataTableView);
        List<LigneBonRetour> lignesTicket = App.database.ligneBonRetourDAO().getByBRNum(ticket.getBORNumero());

        if (!lignesTicket.isEmpty()) {
            for (LigneBonRetour ligneTicket :
                    lignesTicket) {
                Article article = App.database.articleDAO().getOneByCodeAndStation(ligneTicket.getLIGBonEntreeCodeArt(), App.prefUtils.getUserStationId());
                ligneTicket.article = article;
            }
        }
        AppCompatTextView tableTitle = context.findViewById(R.id.produitTxtVw);

        Toolbar toolbar = context.findViewById(R.id.expanded_toolbar);
        ExpandableLayout productExpandableLayout = context.findViewById(R.id.product_expandable_layout);
        ExpandableLayout paymentExpandableLayout = context.findViewById(R.id.payment_expandable_layout);
        LinearLayout productDrawer = context.findViewById(R.id.product_drawer);
        CardView cashLayout = context.findViewById(R.id.cash_view);
        CardView checkLayout = context.findViewById(R.id.check_view);
        CardView ticketRestoLayout = context.findViewById(R.id.ticket_resto_view);
        LinearLayout paymentDrawer = context.findViewById(R.id.payment_drawer);
        IconicsButton productDrawerButton = context.findViewById(R.id.product_drawer_button);
        IconicsButton paymentDrawerButton = context.findViewById(R.id.payment_drawer_button);
        EditText amountInputField = context.findViewById(R.id.AmountInputField);
//        EditText discountInputField = context.findViewById(R.id.DiscountInputField);
//        RelativeLayout discountInputLayout = context.findViewById(R.id.discountLayout);
//        discountInputLayout.setVisibility(View.GONE);
        EditText totalPaymentValue = (EditText) context.findViewById(R.id.total_payment_value);
        toolbar.inflateMenu(R.menu.ticket_subitem_menu);
        MenuItem closeButton = toolbar.getMenu().findItem(R.id.close);
        closeButton.setIcon(new IconicsDrawable(context)
                .icon(GoogleMaterial.Icon.gmd_close)
                .color(context.getResources().getColor(R.color.white))
                .sizeDp(16));
        closeButton.setOnMenuItemClickListener(item -> {
            mBottomSheetBehavior.setState(BottomSheetBehavior.STATE_COLLAPSED);

            return false;
        });
        toolbar.setTitle(String.format(getString(R.string.br_number_field), ticket.getBORNumero() + ""));
        toolbar.setSubtitle(ticket.getBORCodefrs());
        cashLayout.setVisibility(View.GONE);
        checkLayout.setVisibility(View.GONE);
        ligneTraiteTableView.setDataAdapter(new LigneTicketRestoTableDataAdapter(context, traiteCaisseList, ligneTraiteTableView));
        ticketRestoLayout.setVisibility(View.GONE);
        if (!lignesTicket.isEmpty()) {
            tableViewLigneRetour.setVisibility(View.VISIBLE);
            tableViewLigneCommande.setVisibility(View.GONE);
            tableViewLigneTicket.setVisibility(View.GONE);


            tableViewLigneRetour.setSwipeToRefreshEnabled(false);
            tableViewLigneRetour.setHeaderBackground(R.color.material_drawer_primary);
            tableViewLigneRetour.setVerticalScrollBarEnabled(true);
            tableViewLigneRetour.setScrollbarFadingEnabled(true);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                tableViewLigneRetour.setNestedScrollingEnabled(true);
                tableViewLigneRetour.setElevation(10);
            }
            tableViewLigneRetour.setWeightSum(1);
            tableViewLigneRetour.setDataAdapter(new LigneBonRetourTableDataAdapter(context, lignesTicket, tableViewLigneRetour));
            productExpandableLayout.setExpanded(true);
        } else {
            productExpandableLayout.setExpanded(false);
            tableViewLigneCommande.setVisibility(View.GONE);
            tableViewLigneTicket.setVisibility(View.GONE);
            tableViewLigneRetour.setVisibility(View.GONE);
        }
        amountInputField.setText(StringUtils.priceFormat(Double.parseDouble(ticket.getBORMntTTC())));
//        discountInputField.setText(ticket.getBORMntRemise() + "");
//        discountInputField.setEnabled(false);
        tableTitle.setText(context.getString(R.string.product_title ,String.valueOf(lignesTicket.size())));


        onExpansionUpdate(productExpandableLayout, productDrawerButton);
        onExpansionUpdate(paymentExpandableLayout, paymentDrawerButton);
        productDrawer.setOnClickListener(v -> {
            if (!productExpandableLayout.isExpanded()) {
                productExpandableLayout.expand(true);
                paymentExpandableLayout.collapse(true);
            } else {
                productExpandableLayout.collapse(true);

            }
        });
        paymentDrawer.setOnClickListener(v -> {

            if (!paymentExpandableLayout.isExpanded()) {
                paymentExpandableLayout.expand(true);
                productExpandableLayout.collapse(true);
            } else {
                paymentExpandableLayout.collapse(true);

            }
        });


    }

    void onExpansionUpdate(ExpandableLayout expandableLayout, com.mikepenz.iconics.view.IconicsButton button) {
        expandableLayout.setOnExpansionUpdateListener((expansionFraction, state) -> {
            button.setText((state == EXPANDING) ? "{faw-chevron-up}" : "{faw-chevron-down}");
        });
    }
}