package com.asmtunis.procaissemobility.comparators;

import com.asmtunis.procaissemobility.adapters.items.ArticleItem;
import com.asmtunis.procaissemobility.adapters.items.ClientItem;
import com.asmtunis.procaissemobility.adapters.items.DepenseCaisseItem;
import com.asmtunis.procaissemobility.adapters.items.DepenseItem;
import com.asmtunis.procaissemobility.adapters.items.InvPatItem;
import com.asmtunis.procaissemobility.adapters.items.PaymentItem;
import com.asmtunis.procaissemobility.adapters.items.TicketItem;
import com.asmtunis.procaissemobility.helper.utils.DateUtils;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;

import java.util.Comparator;

/**
 * Created by PC on 10/31/2017.
 */

public class AdapterComparators {

    public AdapterComparators() {
    }


    public static Comparator<ArticleItem> getArticleItemCountComparator() {
        return new ArticleItemCountComparator();
    }


    public static Comparator<DepenseItem> getExpenseItemCountComparator() {
        return new ExpenseItemCountComparator();
    }

    public static Comparator<ArticleItem> getArticleItemComparator() {
        return new ArticleItemComparator();
    }


    public static Comparator<TicketItem> getTicketItemComparator() {
        return new TicketItemComparator();
    }


    public static Comparator<DepenseCaisseItem> getExpenseItemComparator() {
        return new ExpenseItemComparator();
    }


    public static Comparator<InvPatItem> getInvPatrimoineItemComparator() {
        return new InvPatrimoineItemComparator();
    }


    public static Comparator<ClientItem> getClientItemComparator() {
        return new ClientItemComparator();
    }


    public static Comparator<PaymentItem> getPaymentItemComparator() {
        return new PaymentItemComparator();
    }

    private static class ArticleItemCountComparator implements Comparator<ArticleItem> {

        @Override
        public int compare(final ArticleItem lhs, final ArticleItem rhs) {
            return (StringUtils.decimalFormat(lhs.getArticle().getCount()) > StringUtils.decimalFormat(rhs.getArticle().getCount())) ? -1 : (StringUtils.decimalFormat(lhs.getArticle().getCount()) < StringUtils.decimalFormat(rhs.getArticle().getCount()) ? 1 : 0);

        }


    }

    private static class ExpenseItemCountComparator implements Comparator<DepenseItem> {

        @Override
        public int compare(final DepenseItem lhs, final DepenseItem rhs) {
            return 0;
                    //(StringUtils.decimalFormat(lhs.getDepence().getCount()) > StringUtils.decimalFormat(rhs.getArticle().getCount())) ? -1 : (StringUtils.decimalFormat(lhs.getArticle().getCount()) < StringUtils.decimalFormat(rhs.getArticle().getCount()) ? 1 : 0);

        }


    }


    private static class ArticleItemComparator implements Comparator<ArticleItem> {

        @Override
        public int compare(final ArticleItem lhs, final ArticleItem rhs) {

            //  return (StringUtils.parseDouble(lhs.getArticle().getaRTQteStock(),0) >  StringUtils.parseDouble(rhs.getArticle().getaRTQteStock(),0) ) ? -1  :(StringUtils.parseDouble(lhs.getArticle().getaRTQteStock(),0) <  StringUtils.parseDouble(rhs.getArticle().getaRTQteStock(),0) ? 1 : 0);
            if (rhs != null)
       /*return rhs.getArticle().getaRTDesignation()
                    .compareTo(lhs.getArticle().getaRTDesignation());*/
                return 1;
            else return 0;
        }


    }

    private static class TicketItemComparator implements Comparator<TicketItem> {

        @Override
        public int compare(final TicketItem lhs, final TicketItem rhs) {


            if (lhs == null && rhs == null) {
                return 0;
            }
            if (lhs == null && rhs != null) {
                return -1;
            }
            if (lhs != null && rhs == null) {
                return 1;
            }
            int result = 0;
/*
            if (rhs.getTicket().gettIKDateHeureTicket() == null && lhs.getTicket().gettIKDateHeureTicket() == null) {
                result = 0;
            } else if (rhs.getTicket().gettIKDateHeureTicket() == null && lhs.getTicket().gettIKDateHeureTicket() != null) {
                result = -1;
            } else if (rhs.getTicket().gettIKDateHeureTicket() != null && lhs.getTicket().gettIKDateHeureTicket() == null) {
                result = 1;
            }
            else {
                result = DateUtils.strToDate(rhs.getTicket().gettIKDateHeureTicket(), "yyyy-MM-dd HH:mm")
                        .compareTo(DateUtils.strToDate(lhs.getTicket().gettIKDateHeureTicket(), "yyyy-MM-dd HH:mm"));
            }*/

            final int numTicket1 = lhs.getTicket().gettIKNumTicket();
            final int numTicket2 = rhs.getTicket().gettIKNumTicket();

            if (result == 0) {
                if (numTicket1 < numTicket2) {
                    result = 1;
                } else if (numTicket1 > numTicket2) {
                    result = -1;
                } else {
                    result = 0;
                }
            }
            return result;
        }
    }

    private static class ExpenseItemComparator implements Comparator<DepenseCaisseItem> {

        @Override
        public int compare(final DepenseCaisseItem lhs, final DepenseCaisseItem rhs) {


            if (lhs == null && rhs == null) {
                return 0;
            }
            if (lhs == null && rhs != null) {
                return -1;
            }
            if (lhs != null && rhs == null) {
                return 1;
            }
            int result = 0;
/*
            if (rhs.getTicket().gettIKDateHeureTicket() == null && lhs.getTicket().gettIKDateHeureTicket() == null) {
                result = 0;
            } else if (rhs.getTicket().gettIKDateHeureTicket() == null && lhs.getTicket().gettIKDateHeureTicket() != null) {
                result = -1;
            } else if (rhs.getTicket().gettIKDateHeureTicket() != null && lhs.getTicket().gettIKDateHeureTicket() == null) {
                result = 1;
            }
            else {
                result = DateUtils.strToDate(rhs.getTicket().gettIKDateHeureTicket(), "yyyy-MM-dd HH:mm")
                        .compareTo(DateUtils.strToDate(lhs.getTicket().gettIKDateHeureTicket(), "yyyy-MM-dd HH:mm"));
            }*/

           // final int numTicket1 = lhs.getDepenceCaisse().getDepCode();
          //  final int numTicket2 = rhs.getTicket().gettIKNumTicket();

          /*  if (result == 0) {
                if (numTicket1 < numTicket2) {
                    result = 1;
                } else if (numTicket1 > numTicket2) {
                    result = -1;
                } else {
                    result = 0;
                }
            }*/
            return result;
        }
    }

    private static class InvPatrimoineItemComparator implements Comparator<InvPatItem> {

        @Override
        public int compare(final InvPatItem lhs, final InvPatItem rhs) {


            if (lhs == null && rhs == null) {
                return 0;
            }
            if (lhs == null && rhs != null) {
                return -1;
            }
            if (lhs != null && rhs == null) {
                return 1;
            }
            int result = 0;
/*
            if (rhs.getTicket().gettIKDateHeureTicket() == null && lhs.getTicket().gettIKDateHeureTicket() == null) {
                result = 0;
            } else if (rhs.getTicket().gettIKDateHeureTicket() == null && lhs.getTicket().gettIKDateHeureTicket() != null) {
                result = -1;
            } else if (rhs.getTicket().gettIKDateHeureTicket() != null && lhs.getTicket().gettIKDateHeureTicket() == null) {
                result = 1;
            }
            else {
                result = DateUtils.strToDate(rhs.getTicket().gettIKDateHeureTicket(), "yyyy-MM-dd HH:mm")
                        .compareTo(DateUtils.strToDate(lhs.getTicket().gettIKDateHeureTicket(), "yyyy-MM-dd HH:mm"));
            }*/

            return result;
        }
    }


    private static class ClientItemComparator implements Comparator<ClientItem> {

        @Override
        public int compare(final ClientItem lhs, final ClientItem rhs) {

            return DateUtils.strToDate((rhs.getClient().getcLIDateCre() != null ? rhs.getClient().getcLIDateCre() :
                            "2020-30-11 00:00"), "yyyy-MM-dd HH:mm")
                    .compareTo(DateUtils.strToDate((rhs.getClient().getcLIDateCre() != null ? rhs.getClient().getcLIDateCre() :
                            "2020-30-11 00:00"), "yyyy-MM-dd HH:mm"));

        }


    }


    private static class PaymentItemComparator implements Comparator<PaymentItem> {

        @Override
        public int compare(final PaymentItem lhs, final PaymentItem rhs) {

            if (lhs == null && rhs == null) {
                return 0;
            }
            if (lhs == null && rhs != null) {
                return -1;
            }
            if (lhs != null && rhs == null) {
                return 1;
            }

            return rhs.reglementCaisse.getrEGCCode().compareTo(lhs.reglementCaisse.getrEGCCode());

         /*   return DateUtils.strToDate(rhs.reglementCaisse.getrEGCDateReg(),"yyyy-MM-dd HH:mm")
                    .compareTo(DateUtils.strToDate(rhs.reglementCaisse.getrEGCDateReg(),"yyyy-MM-dd HH:mm"));*/


        }


    }


}
