package com.asmtunis.procaissemobility.data.viewModels;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelProviders;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.dao.DNVisitesDAO;
import com.asmtunis.procaissemobility.data.models.DNVIsite;
import com.asmtunis.procaissemobility.data.models.VCPrix;

import java.util.List;


public class DNVisiteViewModel extends ViewModel {
    public DNVisitesDAO dnVisiteDAO;
    private static DNVisiteViewModel instance;

    public static DNVisiteViewModel getInstance(Fragment fragment){
        if (instance==null  ){
            instance = new ViewModelProvider(fragment).get(DNVisiteViewModel.class);
            instance.dnVisiteDAO= App.database.dnVisitesDAO();

        }
        return instance;
    }



  /*  public static DNVisiteViewModel getInstance(Fragment activity) {
        if (instance == null)
            instance = ViewModelProviders.of(activity).get(DNVisiteViewModel.class);
        instance.dnVisiteDAO = App.database.dnVisitesDAO();
        return instance;
    }*/

    public static DNVisiteViewModel getInstance(FragmentActivity activity) {
        if (instance == null)
            instance = new ViewModelProvider(activity).get(DNVisiteViewModel.class);
        instance.dnVisiteDAO = App.database.dnVisitesDAO();
        return instance;
    }

    public LiveData<Integer> getNoSyncCount() {
        return dnVisiteDAO.getNoSyncCountMubtale();
    }
    public LiveData<List<DNVIsite>> getAll(){
        return dnVisiteDAO.getAll();
    }
}
