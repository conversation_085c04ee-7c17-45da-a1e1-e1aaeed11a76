package com.asmtunis.procaissemobility.ui.activities;

import static android.provider.Settings.ACTION_REQUEST_SCHEDULE_EXACT_ALARM;
import static android.text.InputType.TYPE_CLASS_NUMBER;
import static android.text.InputType.TYPE_NUMBER_FLAG_DECIMAL;
import static com.asmtunis.procaissemobility.App.prefUtils;
import static com.asmtunis.procaissemobility.helper.Globals.DEFAULT_VALUE;
import static com.asmtunis.procaissemobility.helper.Globals.STATISTICS_DB_KEY;
import static com.asmtunis.procaissemobility.helper.Globals.nbCharAllowed;
import static com.asmtunis.procaissemobility.helper.utils.DateUtils.getCurrentDate;
import static com.asmtunis.procaissemobility.helper.utils.StringUtils.decimalFormat;
import static com.google.android.material.internal.ContextUtils.getActivity;
import static com.google.android.play.core.install.model.AppUpdateType.IMMEDIATE;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.AlarmManager;
import android.app.AlertDialog;
import android.app.ProgressDialog;
import android.app.SearchManager;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.IntentSender;
import android.content.ServiceConnection;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.PowerManager;
import android.provider.Settings;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.RelativeLayout;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.text.HtmlCompat;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Observer;
import androidx.preference.PreferenceManager;

import com.afollestad.materialdialogs.MaterialDialog;
import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.AppProperties;
import com.asmtunis.procaissemobility.data.models.Article;
import com.asmtunis.procaissemobility.data.models.ArticleCodeBar;
import com.asmtunis.procaissemobility.data.models.Banque;
import com.asmtunis.procaissemobility.data.models.BonCommande;
import com.asmtunis.procaissemobility.data.models.BonRetour;
import com.asmtunis.procaissemobility.data.models.CarteResto;
import com.asmtunis.procaissemobility.data.models.ChequeCaisse;
import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.data.models.ClientArticlePrix;
import com.asmtunis.procaissemobility.data.models.Facture;
import com.asmtunis.procaissemobility.data.models.Immobilisation;
import com.asmtunis.procaissemobility.data.models.DepenceType;
import com.asmtunis.procaissemobility.data.models.DNFamille;
import com.asmtunis.procaissemobility.data.models.DNSuperficie;
import com.asmtunis.procaissemobility.data.models.DNTypePVente;
import com.asmtunis.procaissemobility.data.models.DNTypeServices;
import com.asmtunis.procaissemobility.data.models.DNVIsite;
import com.asmtunis.procaissemobility.data.models.DN_LigneVisite;
import com.asmtunis.procaissemobility.data.models.DepenceCaisse;
import com.asmtunis.procaissemobility.data.models.Devise;
import com.asmtunis.procaissemobility.data.models.Etablisement;
import com.asmtunis.procaissemobility.data.models.EtatOrdreMission;
import com.asmtunis.procaissemobility.data.models.Exercice;
import com.asmtunis.procaissemobility.data.models.Famille;
import com.asmtunis.procaissemobility.data.models.Fournisseur;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.LicenseResponse;
import com.asmtunis.procaissemobility.data.models.LigneBonCommande;
import com.asmtunis.procaissemobility.data.models.LigneBonRetour;
import com.asmtunis.procaissemobility.data.models.LigneTicket;
import com.asmtunis.procaissemobility.data.models.Marque;
import com.asmtunis.procaissemobility.data.models.OrdreMission;
import com.asmtunis.procaissemobility.data.models.OrdreWithLines;
import com.asmtunis.procaissemobility.data.models.Parametrages;
import com.asmtunis.procaissemobility.data.models.Prefixe;
import com.asmtunis.procaissemobility.data.models.PricePerStation;
import com.asmtunis.procaissemobility.data.models.Reclamation;
import com.asmtunis.procaissemobility.data.models.ReglementCaisse;
import com.asmtunis.procaissemobility.data.models.SessionCaisse;
import com.asmtunis.procaissemobility.data.models.Station;
import com.asmtunis.procaissemobility.data.models.StationStock;
import com.asmtunis.procaissemobility.data.models.Statistics;
import com.asmtunis.procaissemobility.data.models.Ticket;
import com.asmtunis.procaissemobility.data.models.TicketWithLinesAndPayments;
import com.asmtunis.procaissemobility.data.models.Timbre;
import com.asmtunis.procaissemobility.data.models.TraiteCaisse;
import com.asmtunis.procaissemobility.data.models.TrakingAlarm;
import com.asmtunis.procaissemobility.data.models.Utilisateur;
import com.asmtunis.procaissemobility.data.models.VCAutre;
import com.asmtunis.procaissemobility.data.models.VCImage;
import com.asmtunis.procaissemobility.data.models.VCListeConcurrent;
import com.asmtunis.procaissemobility.data.models.VCNewProduct;
import com.asmtunis.procaissemobility.data.models.VCPrix;
import com.asmtunis.procaissemobility.data.models.VCPromo;
import com.asmtunis.procaissemobility.data.models.VCTypeCommunication;
import com.asmtunis.procaissemobility.data.models.Vendeur;
import com.asmtunis.procaissemobility.data.models.Ville;
import com.asmtunis.procaissemobility.data.models.custom.SessionActivation;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.datamanager.ArticleDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.BanqueDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.BonCommandeDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.BonRetourDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.CarteRestoDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.ChequeCaisseDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.ClientArticlePrixDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.ClientDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.CodeBareDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.DepenceCaisseDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.DepenceDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.DeviseDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.DistributionNumeriqueDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.EtablisementDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.EtatOrdreMissionDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.ExerciceDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.FactureDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.FamilleDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.FournisseurDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.ImmobilisationDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.LicenseDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.LigneBonCommandeDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.LigneBonRetourDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.LigneTicketDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.MarqueDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.MiscDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.OrdreMissionDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.ParametragesDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.PrefixeDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.PricePerStationDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.ReclamationDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.ReglementCaisseDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.SessionCaisseDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.StationDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.StationStockDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.TicketDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.TimbreDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.TraiteCaisseDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.UtilisateurDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.VeuilleConcurrentielleDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.VilleDataManager;
import com.asmtunis.procaissemobility.data.viewModels.BonComandeViewModel;
import com.asmtunis.procaissemobility.data.viewModels.BonRetourViewModel;
import com.asmtunis.procaissemobility.data.viewModels.ClientViewModel;
import com.asmtunis.procaissemobility.data.viewModels.DNVisiteViewModel;
import com.asmtunis.procaissemobility.data.viewModels.ExpensesViewModel;
import com.asmtunis.procaissemobility.data.viewModels.ReglementCaisseViewModel;
import com.asmtunis.procaissemobility.data.viewModels.SessionCaisseViewModel;
import com.asmtunis.procaissemobility.data.viewModels.TicketViewModel;
import com.asmtunis.procaissemobility.data.viewModels.VCAutreViewModel;
import com.asmtunis.procaissemobility.data.viewModels.VCNewProductViewModel;
import com.asmtunis.procaissemobility.data.viewModels.VCPrixViewModel;
import com.asmtunis.procaissemobility.data.viewModels.VCPromoViewModel;
import com.asmtunis.procaissemobility.enums.DataSyncStatus;
import com.asmtunis.procaissemobility.helper.ActivityResultBus;
import com.asmtunis.procaissemobility.helper.ActivityResultEvent;
import com.asmtunis.procaissemobility.helper.AppHelper;
import com.asmtunis.procaissemobility.helper.DataSyncHelper;
import com.asmtunis.procaissemobility.helper.Globals;
import com.asmtunis.procaissemobility.helper.utils.DateUtils;
import com.asmtunis.procaissemobility.helper.utils.LocationUtils;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;
import com.asmtunis.procaissemobility.helper.utils.ServiceUtils;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.helper.utils.UIUtils;
import com.asmtunis.procaissemobility.listener.IDataSyncListener;
import com.asmtunis.procaissemobility.recivers.ConnectivityReceiver;
import com.asmtunis.procaissemobility.services.LocationUpdatesService;
import com.asmtunis.procaissemobility.ui.fragments.SettingsFragment;
import com.asmtunis.procaissemobility.ui.fragments.TicketsFragment;
import com.avatarfirst.avatargenlib.AvatarGenerator;
import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.DeviceUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ObjectUtils;
import com.github.thunder413.datetimeutils.DateTimeUtils;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.snackbar.Snackbar;
import com.google.android.play.core.appupdate.AppUpdateInfo;
import com.google.android.play.core.appupdate.AppUpdateManager;
import com.google.android.play.core.appupdate.AppUpdateManagerFactory;
import com.google.android.play.core.appupdate.AppUpdateOptions;
import com.google.android.play.core.install.model.UpdateAvailability;
import com.google.android.gms.tasks.Task;
import com.mikepenz.fontawesome_typeface_library.FontAwesome;
import com.mikepenz.google_material_typeface_library.GoogleMaterial;
import com.mikepenz.materialdrawer.AccountHeader;
import com.mikepenz.materialdrawer.AccountHeaderBuilder;
import com.mikepenz.materialdrawer.Drawer;
import com.mikepenz.materialdrawer.DrawerBuilder;
import com.mikepenz.materialdrawer.holder.BadgeStyle;
import com.mikepenz.materialdrawer.holder.StringHolder;
import com.mikepenz.materialdrawer.model.DividerDrawerItem;
import com.mikepenz.materialdrawer.model.ExpandableDrawerItem;
import com.mikepenz.materialdrawer.model.PrimaryDrawerItem;
import com.mikepenz.materialdrawer.model.ProfileDrawerItem;
import com.mikepenz.materialdrawer.model.SecondaryDrawerItem;
import com.mikepenz.materialdrawer.model.interfaces.IDrawerItem;
import com.mikepenz.materialdrawer.model.interfaces.IProfile;
import com.mikepenz.materialdrawer.model.interfaces.Nameable;

import net.cachapa.expandablelayout.ExpandableLayout;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import butterknife.BindView;
import butterknife.ButterKnife;
import es.dmoral.toasty.Toasty;
import io.paperdb.Paper;
import static com.asmtunis.procaissemobility.helper.Globals.LEFT_TEXT_MAX_LENGTH;
import static com.asmtunis.procaissemobility.helper.Globals.RIGHT_LENGTH;
import static com.asmtunis.procaissemobility.helper.Globals.LEFT_LENGTH;
public class MainActivity extends BaseActivity implements Drawer.OnDrawerItemClickListener, SharedPreferences.OnSharedPreferenceChangeListener {
    private static final int REQUEST_CODE_UPDATE = 999;
    private static final int REQUEST_SCHEDULE_EXACT_ALARM_PERMISSION = 9998;
    AppUpdateManager appUpdateManager;
    // Used in checking for runtime permissions.
    private static final int REQUEST_PERMISSIONS_REQUEST_CODE = 34;

    // The BroadcastReceiver used to listen from broadcasts from the service.
    private BroadcastReceiver myReceiver;

    // A reference to the service used to get location updates.
    private LocationUpdatesService mService = null;

    // Boolean newSession = false;

    // Tracks the bound state of the service.
    private boolean mBound = false;
    private boolean wasDisconected = true;
    private long lastConnectedTimestamp = 0;
    private static final String TAG = "main activity";
    private static final int REQUEST_APP_UPDATE = 600;
    @BindView(R.id.toolbar)
    Toolbar toolbar;
    public static MainActivity instance;
    boolean doubleBackToExitPressedOnce = false;
    RelativeLayout mLayoutBottomSheet;
    PrimaryDrawerItem syncDrawerItem;
    PrimaryDrawerItem ticketDrawerItem;
    PrimaryDrawerItem expenseDrawerItem;
    PrimaryDrawerItem DashboardDrawerItem;
    PrimaryDrawerItem commandDrawerItem;
    PrimaryDrawerItem retourDrawerItem;
    PrimaryDrawerItem recapTicketsDrawerItem;
    PrimaryDrawerItem settingDrawerItem;
    PrimaryDrawerItem closeSessionDrawerItem;

    PrimaryDrawerItem clientsDrawerItem;
    ExpandableDrawerItem veilleDrawerItem;


    ExpandableDrawerItem invPatrDrawerItem;
    SecondaryDrawerItem invPatAffectationDrawerItem;
    SecondaryDrawerItem invPatInventaireDrawerItem;
    SecondaryDrawerItem invPatDeplacementOutDrawerItem;
    SecondaryDrawerItem invPatDeplacementInDrawerItem;

    ExpandableDrawerItem distributionNumeriqueDrawerItem;
    SecondaryDrawerItem dnclientsDrawerItem;
    SecondaryDrawerItem dnProspectDrawerItem;
    SecondaryDrawerItem dnvisiteDrawerItem;
    private BottomSheetBehavior mBottomSheetBehavior;
    ProgressDialog mprogress;
    Activity context = this;
    Bundle extras;
    Drawer result;
    /*
        AppUpdateManager appUpdateManager;
    */
    DrawerBuilder drawerBuilder;
    List<TicketWithLinesAndPayments> ticketWithLinesAndPayments = new ArrayList<>();
    int count = 0;
    int BLcount = 0;
    int Expensescount = 0;
    int BCcount = 0;
    int BRcount = 0;
    int CLCount = 0;
    int DNCLCount = 0;
    int DNVisitCount = 0;
    int DNprospectCount = 0;
    String SessionCaiiseID = "";

    ConnectivityReceiver connectivityReceiver;

    // Monitors the state of the connection to the service.
    private final ServiceConnection mServiceConnection = new ServiceConnection() {

        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            LocationUpdatesService.LocalBinder binder = (LocationUpdatesService.LocalBinder) service;
            mService = binder.getService();
            mService.requestLocationUpdates(null);
            mBound = true;
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            mService = null;
            mBound = false;
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
      //  Log.i("DEBUG ACTIVITY", "" + (true ? null : 0));

        ButterKnife.bind(this);
        extras = getIntent().getExtras();

        if(prefUtils.getCheckUpdateEnabled()) {
            //  Log.d("ttgfggdsxdsdx", "check update");
            checkForUpdate();
        } //else Log.d("ttgfggdsxdsdx", "dont check update");
        nbCharAllowed =  prefUtils.getPrinterWidth();
         LEFT_TEXT_MAX_LENGTH = nbCharAllowed/3;

         LEFT_LENGTH = (int) (nbCharAllowed/2);

         RIGHT_LENGTH = (int) (nbCharAllowed/2);
        instance = this;
        if (!PrefUtils.getUserLogin().equals(DEFAULT_VALUE)) {
            listenToCountNoSyncDataUpdate();
            observeForNewObject();
            updateNoSyncCount();
            updateBLCount();
            updateExpensesCount();
            updateBCCount();
            updateBRCount();
            updatePDCount();
            updatednClientCount();
            updatednProspectCount();

            updateSessionCaisseId();

            mprogress = new ProgressDialog(this);
            setSupportActionBar(toolbar);
            Objects.requireNonNull(getSupportActionBar()).setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle(R.string.dashboard_title);
            handleIntent(getIntent());
            createDrawerBuilder(savedInstanceState);
            createDrawerItems();
            setDrawerHeader(savedInstanceState);
            buildDrawer();
            //   AppUpdateManager();

/**
 *  to open dashboard fragment
 */

            //    changeContainer(Globals.DRAWER_ITEMS.DASHBOARD.getFragment());

        } else {
            startActivity(new Intent(this, LoginActivity.class));
            finish();
        }
        // Check that the user hasn't revoked permissions by going to Settings.
        if (LocationUtils.requestingLocationUpdates(this)) {
            if (!checkPermissions()) {
                requestPermissions();
            }
        }

        PowerManager powerManager = (PowerManager) getSystemService(POWER_SERVICE);
        PowerManager.WakeLock wakeLock = powerManager.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK,
                "com.asmtunis.procaissemobile:MyWakelockTag");
        wakeLock.acquire(30 * 60 * 1000L /*30 minutes*/);


        AlarmManager alarmManager = (AlarmManager) getSystemService(Context.ALARM_SERVICE);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (!alarmManager.canScheduleExactAlarms()) {
                // Permission not granted, request it
                requestScheduleExactAlarmPermission();
            } else {
                // Permission granted, schedule your alarm
             //   scheduleExactAlarm();
            }
        }

        if (extras != null) {
            // String intentFragment = String.valueOf(extras.getChar("frgmntToLoad"));
            String intentFragment = extras.getString("frgmntToLoad");
            if (intentFragment.equals("BL")) {
                try {
                    if (result != null) {
                        Objects.requireNonNull(getSupportActionBar()).setTitle(getString(R.string.tickets_title));
                        if (result.isDrawerOpen()) {
                            result.closeDrawer();

                            //   result.getDrawerItem(R.id.tickets_drawer_item).withSetSelected(true);
                        }
                    }
                } catch (Exception e) {
                    Toasty.info(context, Objects.requireNonNull(e.getMessage())).show();
                }
                Fragment fragment;
                fragment = new TicketsFragment();

                //  changeContainer(Globals.DRAWER_ITEMS.TICKETS.getFragment());
                changeContainerWithDetach(Globals.DRAWER_ITEMS.TICKETS.getFragment());
                //  DashboardDrawerItem.withSetSelected(false);
                ticketDrawerItem.withSetSelected(true);
            }
        }

    }

    private void requestScheduleExactAlarmPermission() {
        //Intent intent = new Intent(AlarmManager.ACTION_REQUEST_SCHEDULE_EXACT_ALARM);
        Intent intent = new Intent(ACTION_REQUEST_SCHEDULE_EXACT_ALARM);
        intent.setData(Uri.parse("package:" + getPackageName()));
        startActivityForResult(intent, REQUEST_SCHEDULE_EXACT_ALARM_PERMISSION);
    }
    private void listenToCountNoSyncDataUpdate() {
        try {
            ReglementCaisseViewModel.getInstance(this).getNoSyncCount().observeForever(integer -> {
                updateNoSyncCount();
            });
            TicketViewModel.getInstance(this).getNoSyncCount().observeForever(integer -> {
                updateNoSyncCount();
            });
            ClientViewModel.getInstance(this).getNoSyncCount().observeForever(integer -> {
                updateNoSyncCount();
            });

            ClientViewModel.getInstance(this).getNoSyncCountToUpdate().observeForever(integer -> {
                updateNoSyncCount();
            });

            BonRetourViewModel.getInstance(this).getNoSyncCount().observeForever(integer -> {
                updateNoSyncCount();
            });
            BonComandeViewModel.getInstance(this).getNoSyncCount().observeForever(integer -> {
                updateNoSyncCount();
            });


            DNVisiteViewModel.getInstance(this).getNoSyncCount().observeForever(integer -> {
                updateNoSyncCount();
            });


            VCPromoViewModel.getInstance(this).getNoSyncCountMubtale().observeForever(integer -> {
                updateNoSyncCount();
            });
            VCPrixViewModel.getInstance(this).getNoSyncCountMubtale().observeForever(integer -> {
                updateNoSyncCount();
            });

            VCNewProductViewModel.getInstance(this).getNoSyncCountMubtale().observeForever(integer -> {
                updateNoSyncCount();
            });
            VCAutreViewModel.getInstance(this).getNoSyncCountMubtale().observeForever(integer -> {
                updateNoSyncCount();
            });

            App.database.vcImageDAO().getNoSyncCountMubtale().observeForever(integer -> {
                updateNoSyncCount();
            });


            VCPromoViewModel.getInstance(this).getNoSyncCountMubtaleToDelete().observeForever(integer -> {
                updateNoSyncCount();
            });
            VCPrixViewModel.getInstance(this).getCountNoSyncedToDeleteMubtale().observeForever(integer -> {
                updateNoSyncCount();
            });
            VCNewProductViewModel.getInstance(this).getNoSyncCountMubtaleToDelete().observeForever(integer -> {
                updateNoSyncCount();
            });
            VCAutreViewModel.getInstance(this).getNoSyncCountMubtaleToDelete().observeForever(integer -> {
                updateNoSyncCount();
            });


            TicketViewModel.getInstance(this).getAllCountBySessionMutable(prefUtils.getSessionCaisseId(), prefUtils.getUserStationId()).observeForever(integer -> {
                updateBLCount();
            });
            ExpensesViewModel.getInstance(this).getAllCountBySessionMutable(prefUtils.getSessionCaisseId()).observeForever(integer -> {
                updateExpensesCount();

                updateNoSyncCount();
            });

            ExpensesViewModel.getInstance(this).getNoSyncCountDepenseType().observe(this, integer -> {
                updateNoSyncCount();
            });

            BonComandeViewModel.getInstance(this).getAllCountBySessionMutable(prefUtils.getUserStationId()).observeForever(integer -> {
                updateBCCount();
            });
            BonRetourViewModel.getInstance(this).getAllCountBySessionMutable(prefUtils.getUserStationId()).observeForever(integer -> {
                updateBRCount();
            });
            ClientViewModel.getInstance(this).getAllCountBySessionMutable(prefUtils.getUserStationId()).observeForever(integer -> {
                updatePDCount();
            });

        } catch (Exception e) {
            Toasty.info(context, e.getMessage()).show();
        }
    }

    private void updateNoSyncCount() {

        try {
            count = App.database.clientDAO().getNoSyncCount() +
                    App.database.clientDAO().getNoSyncCountToUpdate() +
                    App.database.ticketDAO().getNoSyncCount() +
                    App.database.reglementCaisseDAO().getNoSyncCount() +
                    App.database.bonCommandeDAO().getCountNonSync() +
                    App.database.bonRetourDAO().getCountNonSync() +
                    App.database.dnVisitesDAO().getCountNonSync() +
                    App.database.vcAutreDAO().getCountNonSync() +
                    App.database.vcPricesDAO().getCountNonSync() +
                    App.database.vcNewProductDAO().getCountNonSync() +
                    App.database.vcPromosDAO().getCountNonSync() +

                    App.database.vcImageDAO().getNoSynced().size() +

                    App.database.vcAutreDAO().getCountNoSyncedToDelete() +
                    App.database.vcPricesDAO().getCountNoSyncedToDelete() +
                    App.database.vcNewProductDAO().getCountNoSyncedToDelete() +
                    App.database.vcPromosDAO().getCountNoSyncedToDelete() +

                    App.database.depenceCaisseDAO().getNoSyncCount() +
                    App.database.depenceTypeDAO().getNoSyncCount();


            syncDrawerItem = new PrimaryDrawerItem().withName(R.string.sync_title)
                    .withIcon(FontAwesome.Icon.faw_sync_alt)
                    .withIdentifier(R.id.sync_drawer_item)
                    .withDescriptionTextColorRes(R.color.material_yellow700)
                    .withDescription(PrefUtils.isAutoSync() ? "Auto" : "")
                    .withBadge(count > 0 ? new StringHolder(String.valueOf(count)) : null)
                    .withEnabled(count > 0 && AppHelper.isConnected()).withBadgeStyle(new BadgeStyle()
                            .withTextColor(Color.WHITE).withColorRes(R.color.material_drawer_selected_text));
        } catch (Exception e) {
        }

        Log.d("jhhfdd","fff " + count);
    }

    private void updateBLCount() {
        try {
            BLcount = App.database.ticketDAO().getAllCountBySession(prefUtils.getSessionCaisseId(), prefUtils.getUserStationId());
            ticketDrawerItem = new PrimaryDrawerItem()
                    .withName(R.string.tickets_title).withIcon(FontAwesome.Icon.faw_ticket_alt)
                    .withBadge(BLcount)
                    .withBadgeStyle(new BadgeStyle()
                            .withTextColor(Color.WHITE).withColorRes(R.color.material_drawer_selected_text))
                    .withIdentifier(R.id.tickets_drawer_item);
        } catch (Exception e) {
        }
    }
    private void updateExpensesCount() {
        try {
            Expensescount = App.database.depenceCaisseDAO().getAllCountBySession(prefUtils.getSessionCaisseId());
            expenseDrawerItem = new PrimaryDrawerItem()
                 //   .withName(R.string.expense_label).withIcon(R.drawable.expenses)
                    .withName(R.string.expense_label).withIcon(R.drawable.print_icon)
                    .withBadge(Expensescount)
                    .withBadgeStyle(new BadgeStyle()
                            .withTextColor(Color.WHITE).withColorRes(R.color.material_drawer_selected_text))
                    .withIdentifier(R.id.expenses_drawer_item);
        } catch (Exception e) {
        }
    }
    private void updateSessionCaisseId() {


        try {

            SessionCaisseViewModel.getInstance(this).getAllSessionCaisseMutable().observeForever(sessionCaisses -> {
                if (!App.database.sessionCaisseDAO().getAll().isEmpty())
                    SessionCaiiseID = App.database.sessionCaisseDAO().getAll().get(0).getsCIdSCaisse();
                Log.d("ggfff", "eeee" + SessionCaiiseID);
                closeSessionDrawerItem = new PrimaryDrawerItem()
                        .withName(R.string.close_session_title)
                        .withIcon(FontAwesome.Icon.faw_comment_slash)
                        .withDescriptionTextColorRes(R.color.material_yellow700)
                        .withTextColorRes(R.color.material_orange800)
                        .withIdentifier(R.id.close_session_drawer_item)
                        .withSelectable(false)
                        .withDescription(SessionCaiiseID)
                        .withEnabled(count <= 0);
            });
        } catch (Exception e) {
        }
    }

    private void updateBCCount() {
        try {
            BCcount = App.database.bonCommandeDAO().getAllCountBySession(prefUtils.getUserStationId());
            commandDrawerItem = new PrimaryDrawerItem()
                    .withName(R.string.commande_title).withIcon(FontAwesome.Icon.faw_copy)
                    .withBadge(BCcount)
                    .withBadgeStyle(new BadgeStyle()
                            .withTextColor(Color.WHITE).withColorRes(R.color.material_drawer_selected_text))
                    .withIdentifier(R.id.commande_drawer_item);
        } catch (Exception e) {
        }
    }

    private void updateBRCount() {
        try {
            BRcount = App.database.bonRetourDAO().getAllCountBySession(prefUtils.getUserStationId());
            retourDrawerItem = new PrimaryDrawerItem()
                    .withName(R.string.retour_title).withIcon(FontAwesome.Icon.faw_undo)
                    .withBadge(BRcount > 0 ? new StringHolder(String.valueOf(BRcount)) : null)
                    .withBadgeStyle(new BadgeStyle()
                            .withTextColor(Color.WHITE).withColorRes(R.color.material_drawer_selected_text))
                    .withIdentifier(R.id.retour_drawer_item);
        } catch (Exception e) {
        }
    }

    private void updatePDCount() {
        ClientViewModel.getInstance(this).getAll().observe(this, clients -> {
            try {
                CLCount = clients.size();
                Log.d("vvcfv", String.valueOf(CLCount));
                clientsDrawerItem = new PrimaryDrawerItem()
                        .withName(R.string.clients_title).withIcon(FontAwesome.Icon.faw_users)
                        .withBadge(CLCount > 0 ? new StringHolder(String.valueOf(CLCount)) : null)
                        .withBadgeStyle(new BadgeStyle()
                                .withTextColor(Color.WHITE).withColorRes(R.color.material_drawer_selected_text))
                        .withIdentifier(R.id.clients_drawer_item);
            } catch (Exception e) {
            }

        });


    }

    private void updatednClientCount() {
        try {
            ClientViewModel.getInstance(this).getAllDNClient("Prospect", "Clients").observe(this, clients -> {
                if (!clients.isEmpty()) {
                    DNCLCount = clients.size();

                    // DNCLCount = App.database.clientDAO().getCount();
                    dnclientsDrawerItem = new SecondaryDrawerItem()
                            .withName(R.string.clients_title).withIcon(FontAwesome.Icon.faw_users)
                            .withBadge(DNCLCount > 0 ? new StringHolder(String.valueOf(DNCLCount)) : null)
                            .withBadgeStyle(new BadgeStyle()
                                    .withTextColor(Color.WHITE).withColorRes(R.color.material_drawer_selected_text))
                            .withIdentifier(R.id.clients_drawer_item);
                }
            });

        } catch (Exception e) {
            Log.e("exeption", e.getMessage());
        }
    }


    private void updatednProspectCount() {
        try {
            ClientViewModel.getInstance(this).getAllDNClient("Prospect", "Prospects").observe(this, clients -> {
                if (!clients.isEmpty()) {
                    DNprospectCount = clients.size();

                    // DNCLCount = App.database.clientDAO().getCount();
                    dnProspectDrawerItem = new SecondaryDrawerItem()
                            .withName(R.string.prospect_title).withIcon(FontAwesome.Icon.faw_users)
                            .withBadge(DNprospectCount > 0 ? new StringHolder(String.valueOf(DNprospectCount)) : null)
                            .withBadgeStyle(new BadgeStyle()
                                    .withTextColor(Color.WHITE).withColorRes(R.color.material_drawer_selected_text))
                            .withIdentifier(R.id.clients_drawer_item);
                }
            });

        } catch (Exception e) {
            Log.e("exeption", e.getMessage());
        }
    }


    private void createDrawerBuilder(Bundle savedInstanceState) {
        try {
            drawerBuilder = new DrawerBuilder()
                    .withActivity(this)
                    .withToolbar(toolbar)
                    .withHasStableIds(true)
                    .withFireOnInitialOnClick(true)
                    .withSelectedItem(R.id.dashboard_drawer_item)
                    .withShowDrawerOnFirstLaunch(true)
                    .withOnDrawerListener(new Drawer.OnDrawerListener() {
                        @Override
                        public void onDrawerOpened(View drawerView) {
                            updateDrawer();
                        }

                        @Override
                        public void onDrawerClosed(View drawerView) {

                        }

                        @Override
                        public void onDrawerSlide(View drawerView, float slideOffset) {

                        }
                    })
                    .withOnDrawerItemClickListener(this)
                    .withCloseOnClick(true)
                    .withSavedInstance(savedInstanceState)
                    .withShowDrawerOnFirstLaunch(true);
        } catch (Exception e) {
            Log.e("exeption", e.getMessage());
        }
    }

    void setDrawerHeader(Bundle bundle) {
        final IProfile profile = new ProfileDrawerItem()
                .withName(prefUtils.getUserName()).
                withEmail(
                        String.format("%s: %d %s", getString(R.string.remaining_days),
                                DateUtils.getRemainingTime(prefUtils.getExpirationDate()),
                                getString(R.string.days)))
                .withIcon(AvatarGenerator.Companion.avatarImage(context, 100, 0, prefUtils.getUserName()));
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            AccountHeader headerResult = new AccountHeaderBuilder()
                    .withActivity(this)
                    .withHeaderBackground(R.drawable.header)
                    .withTextColorRes(R.color.white)
                    .addProfiles(profile).withAlternativeProfileHeaderSwitching(false)
                    .withCloseDrawerOnProfileListClick(false)
                    .withSelectionListEnabled(false)
                    .withCompactStyle(true)
                    .withSavedInstance(bundle)
                    .withCloseDrawerOnProfileListClick(true)
                    .withDividerBelowHeader(true).withProfileImagesClickable(false)
                    .build();
            drawerBuilder
                    .withFooterDivider(true)
                    .withAccountHeader(headerResult);
        }
    }


    @Override
    protected void onStart() {
        super.onStart();
        PreferenceManager.getDefaultSharedPreferences(this)
                .registerOnSharedPreferenceChangeListener(this);
        requestPermissions();
    }

    @Override
    protected void onResume() {
        super.onResume();
        //   if (wasDisconected)
        startConnectivityChanged();

    }

    private void checkForUpdate() {
        appUpdateManager = AppUpdateManagerFactory.create(context);

// Returns an intent object that you use to check for an update.
        Task<AppUpdateInfo> appUpdateInfoTask = appUpdateManager.getAppUpdateInfo();

// Checks that the platform will allow the specified type of update.
        appUpdateInfoTask.addOnSuccessListener(appUpdateInfo -> {
            if (appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE
                    // This example applies an immediate update. To apply a flexible update
                    // instead, pass in AppUpdateType.FLEXIBLE
                    && appUpdateInfo.isUpdateTypeAllowed(IMMEDIATE)) {
                // Request the update.

                try {
                    appUpdateManager.startUpdateFlowForResult(
                            // Pass the intent that is returned by 'getAppUpdateInfo()'.
                            appUpdateInfo,
                            // The current activity making the update request.
                            this,
                            // Or pass 'AppUpdateType.FLEXIBLE' to newBuilder() for
                            // flexible updates.
                            AppUpdateOptions.newBuilder(IMMEDIATE)
                                    .setAllowAssetPackDeletion(true)
                                    .build(),
                            // Include a request code to later monitor this update request.
                            REQUEST_CODE_UPDATE);
                } catch (IntentSender.SendIntentException e) {
                    e.printStackTrace();
                }
            }
        });




/* AppUpdateManager appUpdateManager = AppUpdateManagerFactory.create(context);

// Returns an intent object that you use to check for an update.
        Task<AppUpdateInfo> appUpdateInfoTask = appUpdateManager.getAppUpdateInfo();

// Checks whether the platform allows the specified type of update,
// and current version staleness.
        appUpdateInfoTask.addOnSuccessListener(appUpdateInfo -> {
            if (appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE
                    && appUpdateInfo.clientVersionStalenessDays() != null
                    && appUpdateInfo.clientVersionStalenessDays() >= DAYS_FOR_FLEXIBLE_UPDATE
                    && appUpdateInfo.isUpdateTypeAllowed(AppUpdateType.FLEXIBLE)) {
                // Request the update.



            }
        });*/

    }


    @Override
    protected void onPause() {
        super.onPause();
    }

    @Override
    protected void onStop() {
        if (mBound) {
            unbindService(mServiceConnection);
            mBound = false;
        }
        PreferenceManager.getDefaultSharedPreferences(this)
                .unregisterOnSharedPreferenceChangeListener(this);
        super.onStop();
    }

    @Override
    protected void onDestroy() {
        stopConnectivityChanged();
        super.onDestroy();
    }

    private void startConnectivityChanged() {
        connectivityReceiver = new ConnectivityReceiver();
        IntentFilter filter = new IntentFilter();
        filter.addAction("android.net.conn.CONNECTIVITY_CHANGE");
        registerReceiver(connectivityReceiver, filter);
        connectivityReceiver.setOnConnectivityChanged(new ConnectivityReceiver.OnConnectivityChanged() {
            @Override
            public void onConnected() {

                /**
                 *to prevent multiple upload to back end
                 * like add new vc product
                 * we use wasDisconected
                 */
                long currentTimestamp = System.currentTimeMillis();

                long diff = (currentTimestamp - lastConnectedTimestamp)/ 1000;


                if (wasDisconected && diff > 5) {
                    lastConnectedTimestamp = currentTimestamp;
                    wasDisconected = false;
                    Snackbar snackbar = Snackbar.make(findViewById(android.R.id.content), getString(R.string.connected), Snackbar.LENGTH_LONG);
                    View view = snackbar.getView();
                    view.setBackgroundColor(ContextCompat.getColor(context, R.color.color_green));
                    snackbar.show();

                    if(count>0) DataSyncHelper.getInstance(context).syncData("1");


                }

            }

            @Override
            public void onNotConnected() {

                if(!wasDisconected) {
                    wasDisconected = true;
                    Snackbar snackbar = Snackbar.make(findViewById(android.R.id.content), getString(R.string.connection_failed_title), Snackbar.LENGTH_LONG);
                    View view = snackbar.getView();
                    view.setBackgroundColor(ContextCompat.getColor(context, R.color.md_red));
                    snackbar.show();
                }

            }
        });
    }

    private void stopConnectivityChanged() {
        if (connectivityReceiver != null) unregisterReceiver(connectivityReceiver);
    }

    void buildDrawer() {
        result = drawerBuilder
                .build();
    }

    void createDrawerItems() {
        try {
            syncDrawerItem = new PrimaryDrawerItem()
                    .withName(R.string.sync_title)
                    .withIcon(FontAwesome.Icon.faw_sync_alt)
                    .withIdentifier(R.id.sync_drawer_item)
                    .withDescriptionTextColorRes(R.color.material_yellow700)
                    .withDescription(PrefUtils.isAutoSync() ? "Auto" : "Manuelle ")
                    .withBadge(count > 0 ? new StringHolder(String.valueOf(count)) : null)
                    .withEnabled(count > 0 && AppHelper.isConnected()).withBadgeStyle(new BadgeStyle()
                            .withTextColor(Color.WHITE).withColorRes(R.color.material_drawer_selected_text));

            settingDrawerItem = new PrimaryDrawerItem()
                    .withName(R.string.parameters)
                    .withIcon(FontAwesome.Icon.faw_cogs)
                    .withIdentifier(R.id.settings_drawer_item)
                    .withSelectable(false)
                    .withDescriptionTextColorRes(R.color.material_yellow700);


            drawerBuilder.addDrawerItems(new PrimaryDrawerItem()
                    .withName(R.string.dashboard_title)
                    .withIcon(FontAwesome.Icon.faw_chart_area)
                    .withIdentifier(R.id.dashboard_drawer_item));


          /*  DashboardDrawerItem=    new PrimaryDrawerItem()
                    .withName(R.string.dashboard_title).withIcon(FontAwesome.Icon.faw_chart_area)
                    .withIdentifier(R.id.dashboard_drawer_item);*/

            ticketDrawerItem = new PrimaryDrawerItem()
                    .withName(R.string.tickets_title)
                    .withIcon(FontAwesome.Icon.faw_ticket_alt)
                    .withBadge(BLcount > 0 ? new StringHolder(String.valueOf(BLcount)) : null)
                    .withBadgeStyle(new BadgeStyle().withTextColor(Color.WHITE).withColorRes(R.color.material_drawer_selected_text))
                    .withIdentifier(R.id.tickets_drawer_item);

            expenseDrawerItem = new PrimaryDrawerItem()
                    //.withName(R.string.expense_label).withIcon(R.drawable.expenses)
                    .withName(R.string.expense_label).withIcon(R.drawable.print_icon)
                    .withBadge(Expensescount > 0 ? new StringHolder(String.valueOf(Expensescount)) : null)
                    .withBadgeStyle(new BadgeStyle()
                            .withTextColor(Color.WHITE).withColorRes(R.color.material_drawer_selected_text))
                    .withIdentifier(R.id.expenses_drawer_item);

            commandDrawerItem = new PrimaryDrawerItem()
                    .withName(R.string.commande_title).withIcon(FontAwesome.Icon.faw_copy)
                    .withBadge(BCcount > 0 ? new StringHolder(String.valueOf(BCcount)) : null)
                    .withBadgeStyle(new BadgeStyle().withTextColor(Color.WHITE).withColorRes(R.color.material_drawer_selected_text))
                    .withIdentifier(R.id.commande_drawer_item);

            retourDrawerItem = new PrimaryDrawerItem()
                    .withName(R.string.retour_title).withIcon(FontAwesome.Icon.faw_undo)
                    .withBadge(BRcount > 0 ? new StringHolder(String.valueOf(BRcount)) : null)
                    .withBadgeStyle(new BadgeStyle()
                            .withTextColor(Color.WHITE).withColorRes(R.color.material_drawer_selected_text))
                    .withIdentifier(R.id.retour_drawer_item);

            /*invPatrDrawerItem = new PrimaryDrawerItem()
                    .withName(R.string.patrimoine_title).withIcon(FontAwesome.Icon.faw_product_hunt)
                    .withBadge(BRcount > 0 ? new StringHolder(String.valueOf(BRcount)) : null)
                    .withBadgeStyle(new BadgeStyle()
                            .withTextColor(Color.WHITE).withColorRes(R.color.material_drawer_selected_text))
                    .withIdentifier(R.id.inv_drawer_item);*/



          /*  if ((prefUtils.getBlAuthorization() && !prefUtils.getInvPAuthorization()))
                drawerBuilder.addDrawerItems(new DividerDrawerItem());*/

            invPatrDrawerItem = new ExpandableDrawerItem().withName(R.string.patrimoine_title)
                    .withIcon(FontAwesome.Icon.faw_product_hunt)
                    .withIdentifier(R.id.inv_drawer_item)
                    .withSelectable(false)
                    .withSubItems(
                            invPatAffectationDrawerItem = new SecondaryDrawerItem().withName(R.string.affectation_title)
                                    .withDisabledTextColorRes(R.color.material_brown600)
                                    .withDisabledIconColorRes(R.color.material_brown600)
                                    .withIdentifier(R.id.inv_Affectation_drawer_subitem)
                                    .withIcon(FontAwesome.Icon.faw_arrow_down)
                                    .withLevel(2)
                                    .withBadgeStyle(new BadgeStyle()
                                            .withTextColor(Color.WHITE).withColorRes(R.color.material_drawer_selected_text))
                                    .withSelectable(true)
                                    .withEnabled(true),

                            invPatInventaireDrawerItem = new SecondaryDrawerItem().withName(R.string.patrimoineInventaire_title)
                                    .withDisabledTextColorRes(R.color.material_brown600)
                                    .withDisabledIconColorRes(R.color.material_brown600)
                                    .withIdentifier(R.id.inv_inventaire_drawer_subitem)
                                    .withIcon(FontAwesome.Icon.faw_plus)
                                    .withLevel(2)
                                    .withBadgeStyle(new BadgeStyle()
                                            .withTextColor(Color.WHITE).withColorRes(R.color.material_drawer_selected_text))
                                    .withSelectable(true)
                                    .withEnabled(true),


                            invPatDeplacementOutDrawerItem = new SecondaryDrawerItem().withName(R.string.patrimoine_deplacement_out_title)
                                    .withDisabledTextColorRes(R.color.material_brown600)
                                    .withDisabledIconColorRes(R.color.material_brown600)
                                    .withIdentifier(R.id.inv_deplacement_out_drawer_subitem)
                                    .withIcon(FontAwesome.Icon.faw_file)
                                    .withLevel(2)
                                    .withBadgeStyle(new BadgeStyle()
                                            .withTextColor(Color.WHITE).withColorRes(R.color.material_drawer_selected_text))
                                    .withSelectable(true)
                                    .withEnabled(true),

                            invPatDeplacementInDrawerItem = new SecondaryDrawerItem().withName(R.string.patrimoine_deplacement_in_title)
                                    .withDisabledTextColorRes(R.color.material_brown600)
                                    .withDisabledIconColorRes(R.color.material_brown600)
                                    .withIdentifier(R.id.inv_deplacement_in_drawer_subitem)
                                    .withIcon(FontAwesome.Icon.faw_file)
                                    .withLevel(2)
                                    .withBadgeStyle(new BadgeStyle()
                                            .withTextColor(Color.WHITE).withColorRes(R.color.material_drawer_selected_text))
                                    .withSelectable(true)
                                    .withEnabled(true)

                    );
            if (prefUtils.getInvPAuthorization()) drawerBuilder.addDrawerItems(invPatrDrawerItem);


            if (prefUtils.getBlAuthorization()) {
                drawerBuilder.addDrawerItems(ticketDrawerItem);
            }

     //    if (prefUtils.getDepenseAuthorization()) drawerBuilder.addDrawerItems(expenseDrawerItem);


            if (prefUtils.getBcAuthorization()) {
                drawerBuilder.addDrawerItems(commandDrawerItem);
            }


        //  if (prefUtils.getBrAuthorization()) {
            if (App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.br))) {
                drawerBuilder.addDrawerItems(retourDrawerItem);
            }

            recapTicketsDrawerItem = new PrimaryDrawerItem()
                    .withName(R.string.Recap_Current_Session_tickets_title)
                    .withIcon(FontAwesome.Icon.faw_dollar_sign)
                    // .withBadge(CLCount > 0 ? new StringHolder(String.valueOf(CLCount)) : null)
                    //.withBadgeStyle(new BadgeStyle().withTextColor(Color.WHITE).withColorRes(R.color.material_drawer_selected_text))
                    .withIdentifier(R.id.tickets_recap_drawer_item);


            if (prefUtils.getBlAuthorization()) drawerBuilder.addStickyDrawerItems(recapTicketsDrawerItem);


            if (prefUtils.getBlAuthorization()) {
                drawerBuilder.addDrawerItems(new PrimaryDrawerItem()
                        .withName(R.string.payments_title).withIcon(FontAwesome.Icon.faw_money_bill_alt)
                        .withIdentifier(R.id.payments_drawer_item));
            }


            clientsDrawerItem = new PrimaryDrawerItem()
                    .withName(R.string.clients_title).withIcon(FontAwesome.Icon.faw_users)
                    .withBadge(CLCount > 0 ? new StringHolder(String.valueOf(CLCount)) : null)
                    .withBadgeStyle(new BadgeStyle()
                            .withTextColor(Color.WHITE).withColorRes(R.color.material_drawer_selected_text))
                    .withIdentifier(R.id.clients_drawer_item);

            drawerBuilder.addDrawerItems(clientsDrawerItem);

            drawerBuilder.addDrawerItems(new PrimaryDrawerItem()
                    .withName(R.string.products_title).withIcon(FontAwesome.Icon.faw_cubes)
                    .withIdentifier(R.id.products_drawer_item));

            if (prefUtils.getTourAuthorization()) {
                drawerBuilder.addDrawerItems(new PrimaryDrawerItem()
                        .withName(R.string.tournee_title).withIcon(FontAwesome.Icon.faw_route)
                        .withIdentifier(R.id.tournee_drawer_item));
            }


          /*  if(prefUtils.getVeilleConcurentielle())     drawerBuilder.addDrawerItems(new PrimaryDrawerItem()
                    .withName(R.string.distribution_title).withIcon(FontAwesome.Icon.faw_cc_stripe)
                   .withIdentifier(R.id.distribution_drawer_item))*/


            if ((prefUtils.getBlAuthorization() && !prefUtils.getInvPAuthorization()))
                drawerBuilder.addDrawerItems(new DividerDrawerItem());

            distributionNumeriqueDrawerItem = new ExpandableDrawerItem().withName(R.string.distribution_title)
                    .withIcon(GoogleMaterial.Icon.gmd_directions)
                    .withIdentifier(R.id.distribution_drawer_item)
                    .withSelectable(false)
                    .withSubItems(
                            dnclientsDrawerItem = new SecondaryDrawerItem().withName(R.string.clients_title)
                                    .withDisabledTextColorRes(R.color.material_brown600)
                                    .withDisabledIconColorRes(R.color.material_brown600)
                                    .withIdentifier(R.id.dn_client)
                                    .withIcon(FontAwesome.Icon.faw_arrow_down).withLevel(2)
                                    .withBadgeStyle(new BadgeStyle()
                                            .withTextColor(Color.WHITE).withColorRes(R.color.material_drawer_selected_text))
                                    .withSelectable(true)
                                    .withEnabled(true),

                            dnProspectDrawerItem = new SecondaryDrawerItem().withName(R.string.prospect_title)
                                    .withDisabledTextColorRes(R.color.material_brown600)
                                    .withDisabledIconColorRes(R.color.material_brown600)
                                    .withIdentifier(R.id.dn_prospect)
                                    .withIcon(FontAwesome.Icon.faw_plus).withLevel(2)
                                    .withBadgeStyle(new BadgeStyle()
                                            .withTextColor(Color.WHITE).withColorRes(R.color.material_drawer_selected_text))
                                    .withSelectable(true)
                                    .withEnabled(true),


                            dnvisiteDrawerItem = new SecondaryDrawerItem().withName(R.string.visites)
                                    .withDisabledTextColorRes(R.color.material_brown600)
                                    .withDisabledIconColorRes(R.color.material_brown600)
                                    .withIdentifier(R.id.dn_visite)
                                    .withIcon(FontAwesome.Icon.faw_file).withLevel(2)
                                    .withBadgeStyle(new BadgeStyle()
                                            .withTextColor(Color.WHITE).withColorRes(R.color.material_drawer_selected_text))
                                    .withSelectable(true)
                                    .withEnabled(true)
                    );
            if (prefUtils.getVeilleConcurentielle())
                drawerBuilder.addDrawerItems(distributionNumeriqueDrawerItem);


            veilleDrawerItem = new ExpandableDrawerItem().withName(R.string.vc_label)
                    .withIcon(GoogleMaterial.Icon.gmd_show_chart)
                    .withIdentifier(R.id.vc_drawer_item)
                    .withSelectable(false)
                    .withSubItems(
                            new SecondaryDrawerItem().withName(R.string.new_product)
                                    .withDisabledTextColorRes(R.color.material_brown600)
                                    .withDisabledIconColorRes(R.color.material_brown600)
                                    .withIdentifier(R.id.vc_new_product)
                                    .withIcon(FontAwesome.Icon.faw_plus).withLevel(2)
                                    .withSelectable(true)
                                    .withEnabled(true),

                            new SecondaryDrawerItem().withName(R.string.promotion)
                                    .withDisabledTextColorRes(R.color.material_brown600)
                                    .withDisabledIconColorRes(R.color.material_brown600)
                                    .withIdentifier(R.id.vc_promo)
                                    .withIcon(FontAwesome.Icon.faw_arrow_down).withLevel(2)
                                    .withSelectable(true)
                                    .withEnabled(true),

                            new SecondaryDrawerItem().withName(R.string.price)
                                    .withDisabledTextColorRes(R.color.material_brown600)
                                    .withDisabledIconColorRes(R.color.material_brown600)
                                    .withIdentifier(R.id.vc_price)
                                    .withIcon(FontAwesome.Icon.faw_dollar_sign).withLevel(2)
                                    .withSelectable(true)
                                    .withEnabled(true),

                            new SecondaryDrawerItem().withName(R.string.others)
                                    .withDisabledTextColorRes(R.color.material_brown600)
                                    .withDisabledIconColorRes(R.color.material_brown600)
                                    .withIdentifier(R.id.vc_others)
                                    .withIcon(FontAwesome.Icon.faw_file).withLevel(2)
                                    .withSelectable(true)
                                    .withEnabled(true)
                    );
            if (prefUtils.getVeilleConcurentielle()) drawerBuilder.addDrawerItems(veilleDrawerItem);


            drawerBuilder.addDrawerItems((prefUtils.getBlAuthorization() /*&& !prefUtils.getInvPAuthorization()*/) ?
                    new ExpandableDrawerItem().withName(R.string.archive_label)
                            .withIcon(GoogleMaterial.Icon.gmd_archive)
                            .withIdentifier(R.id.archive_identifier)

                            .withSelectable(false)
                            .withSubItems(

                                    prefUtils.getBlAuthorization() ? new SecondaryDrawerItem().withName(R.string.tickets_title)
                                            .withDisabledTextColorRes(R.color.material_brown600)
                                            .withDisabledIconColorRes(R.color.material_brown600)
                                            .withIdentifier(R.id.archive_ticket)
                                            .withIcon(FontAwesome.Icon.faw_ticket_alt).withLevel(2)
                                            .withSelectable(true)
                                            .withEnabled(true) : new SecondaryDrawerItem().withName("").withEnabled(false),


                                    prefUtils.getBlAuthorization() ? new SecondaryDrawerItem().withName(R.string.Recaptickets_title)
                                            .withDisabledTextColorRes(R.color.material_brown600)
                                            .withDisabledIconColorRes(R.color.material_brown600)
                                            .withIdentifier(R.id.archive_Recap_ticket)
                                            .withIcon(FontAwesome.Icon.faw_ticket_alt).withLevel(2)
                                            .withSelectable(true)
                                            .withEnabled(true) : new SecondaryDrawerItem().withName("").withEnabled(false),


                                    !prefUtils.getInvPAuthorization() ? new SecondaryDrawerItem().withName(R.string.payments_title)
                                            .withDisabledTextColorRes(R.color.material_brown600)
                                            .withDisabledIconColorRes(R.color.material_brown600)
                                            .withIdentifier(R.id.archive_payment)
                                            .withIcon(FontAwesome.Icon.faw_money_bill_alt).withLevel(2)
                                            .withSelectable(true)
                                            .withEnabled(true) : new SecondaryDrawerItem().withName("").withEnabled(false)
                            ) : new SecondaryDrawerItem().withName("").withEnabled(false)) ;

          /* drawerBuilder.addDrawerItems(new ExpandableDrawerItem().withName(R.string.etat)
                    .withIcon(R.drawable.shopping)
                    .withIdentifier(R.id.etat_identifier)
                    .withSelectable(false)
                    .withSubItems(new SecondaryDrawerItem().withName(R.string.recap_recette)
                                    .withDisabledTextColorRes(R.color.material_brown600)
                                    .withDisabledIconColorRes(R.color.material_brown600)
                                    .withIdentifier(R.id.recipe_recap)
                                    .withIcon(R.drawable.ic_recipe).withLevel(2)
                                    .withSelectable(false)
                                    .withEnabled(true),
                            new SecondaryDrawerItem().withName(R.string.recap_details)
                                    .withDisabledTextColorRes(R.color.material_brown600)
                                    .withDisabledIconColorRes(R.color.material_brown600)
                                    .withIdentifier(R.id.recap_details)
                                    .withIcon(R.drawable.ic_recap_details).withLevel(2)
                                    .withSelectable(false)
                                    .withEnabled(true)));
*/


            drawerBuilder.addDrawerItems(new DividerDrawerItem());

            drawerBuilder.addDrawerItems(syncDrawerItem);


            drawerBuilder.addStickyDrawerItems(new PrimaryDrawerItem()
                    .withName(R.string.update_db_title)
                    .withIcon(FontAwesome.Icon.faw_database)
                    .withIdentifier(R.id.update_db_drawer_item)
                    .withSelectable(false));
            drawerBuilder.addStickyDrawerItems(settingDrawerItem);

            closeSessionDrawerItem = new PrimaryDrawerItem()
                    .withName(R.string.close_session_title)
                    .withIcon(FontAwesome.Icon.faw_comment_slash)
                    .withDescriptionTextColorRes(R.color.material_yellow700)
                    .withTextColorRes(R.color.material_orange800)
                    .withIdentifier(R.id.close_session_drawer_item)
                    .withSelectable(false)
                    //   .withDescription(prefUtils.getSessionCaisseId())
                    .withDescription(SessionCaiiseID)
                    .withEnabled(count <= 0);

            // if(!prefUtils.getClotSess())drawerBuilder.addDrawerItems(closeSessionDrawerItem);

            drawerBuilder.addStickyDrawerItems(new PrimaryDrawerItem()
                    .withName(R.string.logout_title).withIcon(FontAwesome.Icon.faw_sign_out_alt)
                    .withIdentifier(R.id.logout_drawer_item).withSelectable(false));

            drawerBuilder.addDrawerItems(new ExpandableDrawerItem().withName(R.string.info_label)
                    .withIcon(GoogleMaterial.Icon.gmd_info_outline)
                    .withIdentifier(19)
                    .withSelectable(false)
                    .withSubItems(
                            new SecondaryDrawerItem().withName(String.format("Exercice: %s", prefUtils.getExercice()))
                                    .withDisabledTextColorRes(R.color.material_drawer_selected_text)
                                    .withDisabledIconColorRes(R.color.material_drawer_selected_text)
                                    .withIdentifier(100000)
                                    .withIcon(FontAwesome.Icon.faw_calendar).withLevel(2)
                                    .withSelectable(false)
                                    .withEnabled(false),

                            new SecondaryDrawerItem().withName(String.format("Station: %s", prefUtils.getUserStationId()))
                                    .withDisabledTextColorRes(R.color.material_drawer_selected_text)
                                    .withDisabledIconColorRes(R.color.material_drawer_selected_text)
                                    .withIdentifier(122220000)
                                    .withIcon(FontAwesome.Icon.faw_home).withLevel(2)
                                    .withSelectable(false)
                                    .withEnabled(false),

                            prefUtils.getBlAuthorization() ? new SecondaryDrawerItem().withName(String.format("Session: %s", prefUtils.getSessionCaisseId()))
                                    .withDisabledTextColorRes(R.color.material_brown600)
                                    .withDisabledIconColorRes(R.color.material_brown600)
                                    .withIdentifier(100001)
                                    .withIcon(FontAwesome.Icon.faw_book)
                                    .withLevel(2)
                                    .withSelectable(false)
                                    .withEnabled(false) : new SecondaryDrawerItem().withName("").withEnabled(false),

                            new SecondaryDrawerItem().withName(String.format("BD: %s",
                                            HtmlCompat.fromHtml(prefUtils.getBaseConfigName(),
                                                    HtmlCompat.FROM_HTML_MODE_LEGACY))
                                    )
                                    .withDisabledTextColorRes(R.color.material_brown600)
                                    .withDisabledIconColorRes(R.color.material_brown600)
                                    .withIdentifier(100001)
                                    .withIcon(FontAwesome.Icon.faw_book)
                                    .withLevel(2)
                                    .withSelectable(false)
                                    .withEnabled(false),

                            new SecondaryDrawerItem().withName(String.format("NL: %s", DeviceUtils.getAndroidID()))
                                    .withDisabledTextColorRes(R.color.material_brown600)
                                    .withDisabledIconColorRes(R.color.material_brown600)
                                    .withIdentifier(100001)
                                    .withIcon(FontAwesome.Icon.faw_book)
                                    .withLevel(2)
                                    .withSelectable(false)
                                    .withEnabled(false)

                    ));

            drawerBuilder.addStickyDrawerItems(new PrimaryDrawerItem().withName(String.format("Version: %s", AppUtils.getAppVersionName()))
                    .withDisabledTextColorRes(R.color.material_grey500)
                    .withDisabledIconColorRes(R.color.material_grey500)
                    .withIdentifier(100)
                    .withIcon(FontAwesome.Icon.faw_code_branch)
                    .withSelectable(false)
                    .withEnabled(false));
        } catch (Exception e) {
            Toasty.info(context, e.getMessage()).show();
        }

    }

    @Override
    protected int setContentView() {
        return R.layout.activity_main;
    }


    private void handleIntent(Intent intent) {
        if (Intent.ACTION_SEARCH.equals(intent.getAction())) {
            String query = intent.getStringExtra(SearchManager.QUERY);
            //use the query to search your data somehow
        }
    }

    public void updateDrawer() {
        if (result.getDrawerItem(R.id.sync_drawer_item) != null && result.getDrawerItem(R.id.close_session_drawer_item) != null) {
            try {
                if (App.database.appPropertiesDAO() != null &&
                        App.database.appPropertiesDAO().getLastSync().getSynced_at() > 0) {
                    result.updateItem(syncDrawerItem.withDescription(DateTimeUtils
                            .getTimeAgo(context, new Date(App.database.appPropertiesDAO().getLastSync().getSynced_at()))));
                }
            } catch (Exception e) {
            }
        }
        result.getDrawerItem(R.id.sync_drawer_item).withEnabled(count > 0 && AppHelper.isConnected());
        // if(!Objects.equals(prefUtils.getSessionCaisseId(), DEFAULT_VALUE) /*&& !Objects.equals(App.prefUtils.getUserType(), "Commercial")*/){
        result.getDrawerItem(R.id.close_session_drawer_item);//.withEnabled(count <= 0);
        //   }

        result.updateBadge(R.id.sync_drawer_item, count > 0 ? new StringHolder(String.valueOf(count)) : (StringHolder) null);

        result.updateBadge(R.id.tickets_drawer_item, BLcount > 0 ? new StringHolder(String.valueOf(BLcount)) : (StringHolder) null);
        result.updateBadge(R.id.expenses_drawer_item, Expensescount > 0 ? new StringHolder(String.valueOf(Expensescount)) : (StringHolder) null);
        result.updateBadge(R.id.commande_drawer_item, BCcount > 0 ? new StringHolder(String.valueOf(BCcount)) : (StringHolder) null);
        result.updateBadge(R.id.retour_drawer_item, BRcount > 0 ? new StringHolder(String.valueOf(BRcount)) : (StringHolder) null);


        result.updateBadge(R.id.dn_client, DNCLCount > 0 ? new StringHolder(String.valueOf(DNCLCount)) : (StringHolder) null);
        result.updateBadge(R.id.dn_prospect, DNprospectCount > 0 ? new StringHolder(String.valueOf(DNprospectCount)) : (StringHolder) null);

        result.updateBadge(R.id.dn_visite, DNVisitCount > 0 ? new StringHolder(String.valueOf(DNVisitCount)) : (StringHolder) null);


        result.updateBadge(R.id.clients_drawer_item, CLCount > 0 ? new StringHolder(String.valueOf(CLCount)) : (StringHolder) null);


    }

    @Override
    protected void onSaveInstanceState(@NotNull Bundle outState) {
        //add the values which need to be saved from the drawer to the bundle
        outState = result.saveInstanceState(outState);
        super.onSaveInstanceState(outState);
    }

    @Override
    public boolean onItemClick(View view, int position, IDrawerItem drawerItem) {

        if (drawerItem instanceof Nameable) {

            switch ((int) drawerItem.getIdentifier()) {

                case R.id.reclamation_drawer_item:
                    startActivity(new Intent(this, ReclamationActivity.class));
                    break;

                case R.id.update_db_drawer_item:
                    if (NetworkUtils.isConnected()) {
                        if (count > 0) {
                            showRequesSynchronisationDialog(true, false)
                                    .show();
                        } else {
                            App.database.appPropertiesDAO().deleteAll();
                            //  Paper.book().destroy();
                            UpdateLocalData();
                        }

                    } else {
                        Toasty.info(context, R.string.no_connection).show();
                    }
                    break;

                case R.id.logout_drawer_item:

                    new MaterialDialog.Builder(context)
                            .title("Deconnexion ?")
                            .content("Êtes-vous sûr de vous déconnecter de l'application?")
                            .positiveText("Oui")
                            .negativeText("Non")
                            .onPositive((dialog, which) -> {
                                if (NetworkUtils.isConnected()) {
                                    if (count > 0) {
                                        showRequesSynchronisationDialog(false, false)
                                                .show();
                                    } else {
                                        // App.database.appPropertiesDAO().deleteAll();
                                        // Paper.book().destroy();
                                        logout();
                                    }

                                } else {
                                    Toasty.info(context, R.string.no_connection).show();
                                }

                            })
                            .show();

                    break;

                case R.id.settings_drawer_item:
                    startActivityForResult(new Intent(context, SettingActivity.class), 1);
                    break;

                case R.id.close_session_drawer_item:


                        if (!isFinishing() && !isDestroyed()) {
                            new MaterialDialog.Builder(context)
                                    .title("Fermer session ?")
                                    .content("Voulez-vous Fermer la session?")
                                    .positiveText("Oui")
                                    .negativeText("Non")
                                    .onPositive((dialog, which) -> {
                                        closeSession(false);
                                    })
                                    .show();
                            }


                    break;

                case R.id.archive_ticket: {
                    try {
                        if (result != null) {
                            getSupportActionBar()
                                    .setTitle(((Nameable) drawerItem)
                                            .getName()
                                            .getText(MainActivity.this));
                            if (result.isDrawerOpen()) {
                                result.closeDrawer();
                            }
                        }
                    } catch (Exception e) {
                        Toasty.info(context, e.getMessage()).show();
                    }
                    changeContainer(Globals.DRAWER_ITEMS.TICKETS_ARC.getFragment());
                }
                break;



                case R.id.tickets_recap_drawer_item:    {
                    try {
                        if (result != null) {
                            getSupportActionBar()
                                    .setTitle(((Nameable) drawerItem).getName().getText(MainActivity.this));
                            if (result.isDrawerOpen()) {
                                result.closeDrawer();
                            }
                        }
                    } catch (Exception e) {
                        Toasty.info(context, e.getMessage()).show();
                    }
                    changeContainerWithDetach(Globals.DRAWER_ITEMS.TICKETS_RECAP_ARC.getFragment());


                    Bundle bundle = new Bundle();
                    bundle.putString(Globals.TICKETS_RECAP_FROM, ((Nameable) drawerItem).getName().getText(MainActivity.this));
                    // set Fragmentclass Arguments
                    Globals.DRAWER_ITEMS.TICKETS_RECAP_ARC.getFragment().setArguments(bundle);

                }
                break;
                case R.id.archive_Recap_ticket:    {
                    try {
                        if (result != null) {
                            getSupportActionBar()
                                    .setTitle(((Nameable) drawerItem).getName().getText(MainActivity.this));
                            if (result.isDrawerOpen()) {
                                result.closeDrawer();
                            }
                        }
                    } catch (Exception e) {
                        Toasty.info(context, e.getMessage()).show();
                    }
                    changeContainerWithDetach(Globals.DRAWER_ITEMS.RECAP_TICKETS.getFragment());


                    Bundle bundle = new Bundle();
                    bundle.putString(Globals.TICKETS_RECAP_FROM, ((Nameable) drawerItem).getName().getText(MainActivity.this));
                    // set Fragmentclass Arguments
                    Globals.DRAWER_ITEMS.RECAP_TICKETS.getFragment().setArguments(bundle);

                }
                break;



                case R.id.archive_payment: {
                    try {
                        if (result != null) {
                            getSupportActionBar()
                                    .setTitle(((Nameable) drawerItem)
                                            .getName()
                                            .getText(MainActivity.this));
                            if (result.isDrawerOpen()) {
                                result.closeDrawer();
                            }
                        }
                    } catch (Exception e) {
                        Toasty.info(context, e.getMessage()).show();
                    }
                    changeContainer(Globals.DRAWER_ITEMS.PAYMENT_ARC.getFragment());
                }
                break;

                case R.id.recipe_recap: {
                    try {
                        if (result != null) {
                            getSupportActionBar()
                                    .setTitle(((Nameable) drawerItem)
                                            .getName()
                                            .getText(MainActivity.this));
                            if (result.isDrawerOpen()) {
                                result.closeDrawer();
                            }
                        }
                    } catch (Exception e) {
                        Toasty.info(context, e.getMessage()).show();
                    }
                    changeContainer(Globals.DRAWER_ITEMS.RECIPE_RECAP.getFragment());
                }
                break;

                case R.id.recap_details: {
                    try {
                        if (result != null) {
                            getSupportActionBar()
                                    .setTitle(((Nameable) drawerItem)
                                            .getName()
                                            .getText(MainActivity.this));
                            if (result.isDrawerOpen()) {
                                result.closeDrawer();
                            }
                        }
                    } catch (Exception e) {
                        Toasty.info(context, e.getMessage()).show();
                    }
                    changeContainer(Globals.DRAWER_ITEMS.RECAP_DETAILS.getFragment());
                }
                break;

                case R.id.expense_ticket: {
                    try {
                        if (result != null) {
                            getSupportActionBar()
                                    .setTitle(((Nameable) drawerItem)
                                            .getName()
                                            .getText(MainActivity.this));
                            if (result.isDrawerOpen()) {
                                result.closeDrawer();
                            }
                        }
                    } catch (Exception e) {
                        Toasty.info(context, e.getMessage()).show();
                    }
                    changeContainer(Globals.DRAWER_ITEMS.EXPENSE_TICKET_ARC.getFragment());
                }
                break;

                case R.id.expense_list: {
                    try {
                        if (result != null) {
                            getSupportActionBar()
                                    .setTitle(((Nameable) drawerItem)
                                            .getName()
                                            .getText(MainActivity.this));
                            if (result.isDrawerOpen()) {
                                result.closeDrawer();
                            }
                        }
                    } catch (Exception e) {
                        Toasty.info(context, e.getMessage()).show();
                    }
                    changeContainer(Globals.DRAWER_ITEMS.EXPENSE_LIST_ARC.getFragment());
                }
                break;

                case R.id.archive_command: {
                    try {
                        if (result != null) {
                            getSupportActionBar()
                                    .setTitle(((Nameable) drawerItem)
                                            .getName()
                                            .getText(MainActivity.this));
                            if (result.isDrawerOpen()) {
                                result.closeDrawer();
                            }
                        }
                    } catch (Exception e) {
                        Toasty.info(context, e.getMessage()).show();
                    }
                    changeContainer(Globals.DRAWER_ITEMS.COMMAND_ARC.getFragment());
                }
                break;

                case R.id.archive_retour: {
                    try {
                        if (result != null) {
                            getSupportActionBar()
                                    .setTitle(((Nameable) drawerItem)
                                            .getName()
                                            .getText(MainActivity.this));
                            if (result.isDrawerOpen()) {
                                result.closeDrawer();
                            }
                        }
                    } catch (Exception e) {
                        Toasty.info(context, e.getMessage()).show();
                    }
                    changeContainer(Globals.DRAWER_ITEMS.RETOUR_ARC.getFragment());
                }
                break;


                case R.id.vc_promo: {
                    try {
                        if (result != null) {
                            getSupportActionBar()
                                    .setTitle(((Nameable) drawerItem)
                                            .getName()
                                            .getText(MainActivity.this));
                            if (result.isDrawerOpen()) {
                                result.closeDrawer();
                            }
                        }
                    } catch (Exception e) {
                        Toasty.info(context, e.getMessage()).show();
                    }
                    changeContainer(Globals.DRAWER_ITEMS.VC_PROMO.getFragment());
                }
                break;
                case R.id.vc_price: {
                    try {
                        if (result != null) {
                            getSupportActionBar()
                                    .setTitle(((Nameable) drawerItem)
                                            .getName()
                                            .getText(MainActivity.this));
                            if (result.isDrawerOpen()) {
                                result.closeDrawer();
                            }
                        }
                    } catch (Exception e) {
                        Toasty.info(context, e.getMessage()).show();
                    }
                    changeContainer(Globals.DRAWER_ITEMS.PRICE.getFragment());
                }
                break;
                case R.id.vc_others: {
                    try {
                        if (result != null) {
                            getSupportActionBar()
                                    .setTitle(((Nameable) drawerItem)
                                            .getName()
                                            .getText(MainActivity.this));
                            if (result.isDrawerOpen()) {
                                result.closeDrawer();
                            }
                        }
                    } catch (Exception e) {
                        Toasty.info(context, e.getMessage()).show();
                    }
                    changeContainer(Globals.DRAWER_ITEMS.OTHERS.getFragment());
                }
                break;

                case R.id.dn_prospect:
                case R.id.dn_client: {
                    try {
                        if (result != null) {
                            getSupportActionBar()
                                    .setTitle(((Nameable) drawerItem)
                                            .getName()
                                            .getText(MainActivity.this));
                            if (result.isDrawerOpen()) {
                                result.closeDrawer();
                            }
                        }
                    } catch (Exception e) {
                        Toasty.info(context, e.getMessage()).show();
                    }

                    changeContainerWithDetach(Globals.DRAWER_ITEMS.DNClients.getFragment());

                    Bundle bundle = new Bundle();
                    bundle.putString("DNfrom", ((Nameable) drawerItem).getName().getText(MainActivity.this));

                    Globals.DRAWER_ITEMS.DNClients.getFragment().setArguments(bundle);

                }
                break;


          /*       case R.id.dn_prospect: {
                    try {
                        if (result != null) {
                            getSupportActionBar()
                                    .setTitle(((Nameable) drawerItem)
                                            .getName()
                                            .getText(MainActivity.this));
                            if (result.isDrawerOpen()) {
                                result.closeDrawer();
                            }
                        }
                    } catch (Exception e) {
                        Toasty.info(context, e.getMessage()).show();
                    }
                    changeContainer(Globals.DRAWER_ITEMS.DNProspect.getFragment());
                }
                break;
*/

                case R.id.dn_visite: {
                    try {
                        if (result != null) {
                            getSupportActionBar()
                                    .setTitle(((Nameable) drawerItem)
                                            .getName()
                                            .getText(MainActivity.this));
                            if (result.isDrawerOpen()) {
                                result.closeDrawer();
                            }
                        }
                    } catch (Exception e) {
                        Toasty.info(context, e.getMessage()).show();
                    }
                    changeContainer(Globals.DRAWER_ITEMS.DNVisiteFragment.getFragment());
                }
                break;


               /* case R.id.inv_drawer_item: {
                    try {
                        if (result != null) {
                            getSupportActionBar()
                                    .setTitle(((Nameable) drawerItem)
                                            .getName()
                                            .getText(MainActivity.this));
                            if (result.isDrawerOpen()) {
                                result.closeDrawer();
                            }
                        }
                    } catch (Exception e) {
                        Toasty.info(context, e.getMessage()).show();
                    }
                    changeContainer(Globals.DRAWER_ITEMS.INVPATRIMOINE.getFragment());
                }
                 break;
                 */


                case R.id.inv_Affectation_drawer_subitem:{
                    try {
                        if (result != null) {
                            getSupportActionBar()
                                    .setTitle(((Nameable) drawerItem).getName().getText(MainActivity.this));
                            if (result.isDrawerOpen()) {
                                result.closeDrawer();
                            }
                        }
                    } catch (Exception e) {
                        Toasty.info(context, e.getMessage()).show();
                    }
                    changeContainerWithDetach(Globals.DRAWER_ITEMS.AFFECTATIONPATRIMOINE.getFragment());


                    Bundle bundle = new Bundle();
                    //bundle.putString(Globals.INV_FROM,((Nameable) drawerItem).getName().getText(MainActivity.this));
                    // set Fragmentclass Arguments
                    Globals.DRAWER_ITEMS.AFFECTATIONPATRIMOINE.getFragment().setArguments(bundle);

                }
                break;
                case R.id.inv_inventaire_drawer_subitem:{
                    try {
                        if (result != null) {
                            getSupportActionBar()
                                    .setTitle(((Nameable) drawerItem).getName().getText(MainActivity.this));
                            if (result.isDrawerOpen()) {
                                result.closeDrawer();
                            }
                        }
                    } catch (Exception e) {
                        Toasty.info(context, e.getMessage()).show();
                    }
                    changeContainerWithDetach(Globals.DRAWER_ITEMS.INVPATRIMOINE.getFragment());


                    Bundle bundle = new Bundle();
                    //  bundle.putString(Globals.INV_FROM,((Nameable) drawerItem).getName().getText(MainActivity.this));
                    // set Fragmentclass Arguments
                    Globals.DRAWER_ITEMS.INVPATRIMOINE.getFragment().setArguments(bundle);

                }
                break;
                case R.id.inv_deplacement_in_drawer_subitem:{
                    try {
                        if (result != null) {
                            getSupportActionBar()
                                    .setTitle(((Nameable) drawerItem).getName().getText(MainActivity.this));
                            if (result.isDrawerOpen()) {
                                result.closeDrawer();
                            }
                        }
                    } catch (Exception e) {
                        Toasty.info(context, e.getMessage()).show();
                    }
                    changeContainerWithDetach(Globals.DRAWER_ITEMS.DEPINPATRIMOINE.getFragment());


                    Bundle bundle = new Bundle();
                    // bundle.putString(Globals.INV_FROM,((Nameable) drawerItem).getName().getText(MainActivity.this));
                    // set Fragmentclass Arguments
                    Globals.DRAWER_ITEMS.DEPINPATRIMOINE.getFragment().setArguments(bundle);

                }
                break;
                case R.id.inv_deplacement_out_drawer_subitem: {
                    try {
                        if (result != null) {
                            getSupportActionBar()
                                    .setTitle(((Nameable) drawerItem).getName().getText(MainActivity.this));
                            if (result.isDrawerOpen()) {
                                result.closeDrawer();
                            }
                        }
                    } catch (Exception e) {
                        Toasty.info(context, e.getMessage()).show();
                    }
                    changeContainerWithDetach(Globals.DRAWER_ITEMS.DEPOUTPATRIMOINE.getFragment());


                    Bundle bundle = new Bundle();
                    //   bundle.putString(Globals.INV_FROM,((Nameable) drawerItem).getName().getText(MainActivity.this));
                    // set Fragmentclass Arguments
                    Globals.DRAWER_ITEMS.DEPOUTPATRIMOINE.getFragment().setArguments(bundle);

                }
                break;


                default:
                    if (drawerItem.isSelectable()) {
                        onChangeFragment(drawerItem);
                    }
                    break;

            }
        }
        return false;

    }

    public MaterialDialog.Builder showRequesSynchronisationDialog(Boolean onUpdate, Boolean fromOtherFragment) {
        Log.d("nbbbnnc", "showRequesSynchronisationDialog");
        return new MaterialDialog.Builder(context)
                .title("Synchronisation ?")
                .content(onUpdate ? R.string.sync_before_u : R.string.sync_before_q)
                .positiveText("Oui")
                .negativeText("Non")
                .onPositive((dialog, which) -> {
                    DataSyncHelper.getInstance(context, new IDataSyncListener() {
                        @Override
                        public void onLoading(String msg) {
                            runOnUiThread(() -> {
                                mprogress.show();
                                mprogress.setMessage(msg);
                                mprogress.setCancelable(false);
                            });
                        }

                        @Override
                        public void onFailure(String msg, DataSyncStatus syncStatus) {
                            mprogress.hide();
                        }

                        @Override
                        public void onComplete() {
                            //System.out.println("onComplete");
                            Log.d("nbbbnnc", "cccc " + String.valueOf(count));
                            Log.d("nbbbnnc", "onComplete");

                            if (!fromOtherFragment) {
                                Log.d("nbbbnnc", "!fromOtherFragment");
                                runOnUiThread(() -> {
                                    Log.d("nbbbnnc", "dismiss");
                                    mprogress.dismiss();
                                    App.database.appPropertiesDAO().deleteAll();
                                    if (onUpdate && count == 0) {
                                        Log.d("nbbbnnc", "onUpdate");
                                        UpdateLocalData();
                                    }
                                 /*   else {
                                        if (onUpdate) {
                                            dialog.dismiss();
                                        } else {
                                            logout();
                                        }
                                    }*/


                                });
                            } else {
                                Log.d("nbbbnnc", "closeSession");
                                closeSession(true);
                            }
                        }

                        @Override
                        public void onComplete(Object object) {
                            Log.d("nbbbnnc", "object");
                        }
                    }).syncData("2");

                })
                .onNegative(((dialog, which) -> {
                    if (!fromOtherFragment) {
                        if (onUpdate) {
                            dialog.dismiss();
                        } else {
                            logout();
                        }
                    } else {
                        dialog.dismiss();
                    }
                }));
    }


    /**
     * delete all the data from local database
     */
    void purgeData() {
        App.database.trakingDAO().deleteAll();
        App.database.clientArticlePrixDAO().deleteAll();
        App.database.etablisementDAO().deleteAll();
        App.database.deviseDAO().deleteAll();
        App.database.caisseDAO().deleteAll();
        App.database.carteRestoDAO().deleteAll();
        App.database.bonRetourDAO().deleteAll();
        App.database.ligneBonRetourDAO().deleteAll();
        App.database.appPropertiesDAO().deleteAll();
        App.database.chequeCaisseDAO().deleteAll();
        App.database.articleCodeBarDAO().deleteAll();
        App.database.bonCommandeDAO().deleteAll();
        App.database.deviseDAO().deleteAll();
        App.database.etablisementDAO().deleteAll();
        App.database.etatOrdreMissionDAO().deleteAll();
        App.database.reglementCaisseDAO().deleteAll();
        App.database.familleDAO().deleteAll();
        App.database.fournisseurDAO().deleteAll();
        App.database.ligneBonCommandeDAO().deleteAll();
        App.database.ordreMissionDAO().deleteAll();
        App.database.pricePerStationDAO().deleteAll();
        App.database.sessionCaisseDAO().deleteAll();
        App.database.stationDAO().deleteAll();
        App.database.stationStockDAO().deleteAll();
        App.database.timbreDAO().deleteAll();
        App.database.trakingAlarmDAO().deleteAll();
        App.database.villeDAO().deleteAll();
        App.database.ligneTicketDAO().deleteAll();
        App.database.ticketDAO().deleteAll();
        App.database.articleDAO().deleteAll();
        App.database.clientDAO().deleteAll();
        App.database.banqueDAO().deleteAll();
        App.database.carteRestoDAO().deleteAll();
        App.database.prefixeDAO().deleteAll();
        App.database.authorizationDAO().deleteAll();
        App.database.dnFamilleVisite().deleteAll();
        App.database.dnLigneVisiteDAO().deleteAll();
        App.database.dnVisitesDAO().deleteAll();
        App.database.dnSuperficieDAO().deleteAll();
        App.database.dnTypePVenteSDAO().deleteAll();
        App.database.dnTypeServicesDAO().deleteAll();
        App.database.vcPromosDAO().deleteAll();
        App.database.vcNewProductDAO().deleteAll();
        App.database.vcPricesDAO().deleteAll();
        App.database.vcAutreDAO().deleteAll();
        App.database.vcImageDAO().deleteAll();
        App.database.vcListeConcurrentDAO().deleteAll();
        App.database.vcTypeCommunicationDAO().deleteAll();
        App.database.depenceTypeDAO().deleteAll();
        App.database.depenceCaisseDAO().deleteAll();

    }

    private void closeSessionAndLogout() {
        //prefUtils.clearSharedPreferences();
        //Paper.book().destroy();
        prefUtils.deleteUserAccount();
        prefUtils.setLoadData("");
        //App.database.clearAllTables();
        gotoSplash();
    }

    private void logout() {
        //  prefUtils.clearSharedPreferences();
        //Paper.book().destroy();
       /* prefUtils.deleteUserAccount();
        prefUtils.setIsConnected(false);
        App.database.appPropertiesDAO().deleteAll();
        purgeData();*/
        //App.database.clearAllTables();


        prefUtils.setIsConnected(false);
        prefUtils.clearSharedPreferences();
        Paper.book().destroy();
        App.database.appPropertiesDAO().deleteAll();
        purgeData();
        gotoSplash();

    }


    private void gotoSplash() {
        startActivity(new Intent(this, SplashscreenActivity.class));
        finish();
    }

    void getSessionCaisseByUser() {
        Utilisateur utilisateur = prefUtils.getUserAccount();
        SessionCaisseDataManager.getInstance().getSessionCaisseByUser(new GenericObject(App.prefUtils.getBaseConfig(),
                utilisateur.getCodeUt()), new RemoteCallback<SessionCaisse>(context, false, getString(R.string.custErrirmsgSessionCreation)) {
            @Override
            public void onSuccess(SessionCaisse response) {
                if (response != null && response.getsCIdSCaisse() != null) {
                    App.prefUtils.setSessionCaisse(response);
                    Toasty.success(context, "Nouvelle session crée avec succés : " + prefUtils.getSessionCaisseId()).show();

                    App.database.sessionCaisseDAO().insert(response);
                    //getExercice();
                } else {
                    // Set an EditText view to get user fond caisse input
                    final EditText input = new EditText(context);
                    input.setInputType(TYPE_CLASS_NUMBER | TYPE_NUMBER_FLAG_DECIMAL);
                    AlertDialog dialog = new AlertDialog.Builder(context)
                            .setTitle(R.string.cash_fund)
                            .setMessage(R.string.cash_fund_request)
                            .setView(input)
                            .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                @Override
                                public void onClick(DialogInterface dialogInterface, int i) {
                                    String editTextInput = input.getText().toString();
                                 //   Log.d("onclick","editext value is: "+ editTextInput);
                          if(editTextInput.isEmpty()){
                              Toasty.error(context, "Please enter a valid number").show();
                              return;
                          }

                                    if (Double.parseDouble(editTextInput) >= 0) {
                                        SessionCaisseDataManager.getInstance()
                                                .addSessionVendeur(
                                                        new GenericObject(App.prefUtils.getBaseConfig(),
                                                                new SessionActivation(
                                                                        utilisateur.getCodeCaisse(),
                                                                        utilisateur.getCodeUt(),
                                                                        utilisateur.getCodeCarnet(),
                                                                        utilisateur.getStation(),
                                                                        editTextInput,
                                                                        DeviceUtils.getUniqueDeviceId())),
                                                        new RemoteCallback<SessionCaisse>(context, true) {
                                                            @RequiresApi(api = Build.VERSION_CODES.N)
                                                            @Override
                                                            public void onSuccess(SessionCaisse response) {
                                                                //   prefUtils.setSessionDateOuv(returnCurrentDate());
                                                                if (response != null) {
                                                                    if (response != null && response.getsCIdSCaisse() != null) {
                                                                        App.prefUtils.setSessionCaisse(response);
                                                                        Toasty.success(context, "Nouvelle session crée avec succés : " + prefUtils.getSessionCaisseId() +" Fond Caisse : "+ decimalFormat(Double.parseDouble(response.sCFondCaisse)) + "DT").show();

                                                                        App.database.sessionCaisseDAO().insert(response);
                                                                        //getExercice();
                                                                    }
                                                                    else {
                                                                        Toasty.error(context, "Erreur Creation session").show();
                                                                    }
                                                                    //  getSessionCaisseByUser();
                                                                } else {
                                                                    Toasty.error(context, "Cannot create a session").show();
                                                                }
                                                            }

                                                            @Override
                                                            public void onUnauthorized() {

                                                            }

                                                            @Override
                                                            public void onFailed(Throwable throwable) {
                                                                Toasty.error(context, "Carnet invalide, veuillez contacter votre administrateur").show();
                                                            }
                                                        });
                                    } else {
                                        Toasty.error(context, "Please enter a valid number").show();
                                    }
                                }
                            })
                            .setNegativeButton("Cancel", null)
                            .create();
                    dialog.show();
                   /* new MaterialDialog.Builder(context)
                            .title(R.string.cash_fund)
                            .content(R.string.cash_fund_request)
                            .inputType(InputType.TYPE_NUMBER_FLAG_DECIMAL | InputType.TYPE_CLASS_NUMBER)
                            .input("", "",
                                    (dialog1, input) -> {
                                        try {
                                            if (input != null && Double.parseDouble(input.toString()) >= 0) {
                                                SessionCaisseDataManager.getInstance()
                                                        .addSessionVendeur(
                                                                new GenericObject(App.prefUtils.getBaseConfig(),
                                                                        new SessionActivation(
                                                                                utilisateur.getCodeCaisse(),
                                                                                utilisateur.getCodeUt(),
                                                                                utilisateur.getCodeCarnet(),
                                                                                utilisateur.getStation(),
                                                                                input.toString(),
                                                                                DeviceUtils.getUniqueDeviceId())),
                                                                new RemoteCallback<SessionCaisse>(context, true) {
                                                                    @RequiresApi(api = Build.VERSION_CODES.N)
                                                                    @Override
                                                                    public void onSuccess(SessionCaisse response) {
                                                                        //   prefUtils.setSessionDateOuv(returnCurrentDate());
                                                                        if (response != null) {
                                                                            if (response != null && response.getsCIdSCaisse() != null) {
                                                                                App.prefUtils.setSessionCaisse(response);
                                                                                Toasty.success(context, "Nouvelle session crée avec succés : " + prefUtils.getSessionCaisseId()).show();

                                                                                App.database.sessionCaisseDAO().insert(response);
                                                                                //getExercice();
                                                                            }
                                                                            else {
                                                                                Toasty.error(context, "Erreur Creation session").show();
                                                                            }
                                                                          //  getSessionCaisseByUser();
                                                                        } else {
                                                                            Toasty.error(context, "Cannot create a session").show();
                                                                        }
                                                                    }

                                                                    @Override
                                                                    public void onUnauthorized() {

                                                                    }

                                                                    @Override
                                                                    public void onFailed(Throwable throwable) {
                                                                        Toasty.error(context, "Carnet invalide, veuillez contacter votre administrateur").show();
                                                                    }
                                                                });
                                            } else {
                                                Toasty.error(context, "Please enter a valid number").show();
                                            }
                                        } catch (NumberFormatException ignored) {
                                            Toasty.error(context, "Please enter a valid number").show();
                                        }
                                        // Do something
                                    }).show();*/
                }


            }

            @Override
            public void onUnauthorized() {
            }

            @Override
            public void onFailed(Throwable throwable) {

            }
        });

    }

    public void closeSession(Boolean fromOtherFrsgment) {
        SessionCaisseDataManager.getInstance().closeSessionVendeur(new GenericObject(App.prefUtils.getBaseConfig(),
                prefUtils.getSessionCaisseId()), new RemoteCallback<Boolean>(context, true, getString(R.string.custErrirmsgSessionCreation)) {
            @Override
            public void onSuccess(Boolean response) {
                //   prefUtils.setClotSess(false);
                App.database.trakingAlarmDAO().deleteAll();
                if (ServiceUtils.mService != null) {
                    ServiceUtils.mService.removeLocationUpdates();


                }
                //   if (!fromOtherFrsgment) closeSessionAndLogout();
                //  else {

                  /*  if(!Objects.equals(App.prefUtils.getUserType(), "Commercial")
                            && !Objects.equals(App.prefUtils.getUserType(), "Operateur patrimoine") ){

                        getCaisses();
                    }

                    else   getExercice();*/
                getSessionCaisseByUser();
                //   }


            }

            @Override
            public void onUnauthorized() {

            }

            @Override
            public void onFailed(Throwable throwable) {

            }
        });
    }


    /**
     * get caisse data from server
     */
    public void getCaisses() {
        if (!App.prefUtils.getUserId().equals(DEFAULT_VALUE)) {
            SessionCaisseDataManager.getInstance().getSessionCaisseByUser(new GenericObject(App.prefUtils.getBaseConfig(),
                            App.prefUtils.getUserId()),
                    new RemoteCallback<SessionCaisse>(context, false) {
                        @Override
                        public void onSuccess(SessionCaisse response) {

                            if (response.getsCIdSCaisse() != null) {
                                App.prefUtils.setSessionCaisse(response);
                                getExercice();
                                if (!App.prefUtils.getUserId().equals(DEFAULT_VALUE))
                                    getSessionCaisseByUser();
                            } else {
                                if (!App.prefUtils.getUserId().equals(DEFAULT_VALUE)) {
                                    getSessionCaisseByUser();

                                } else {
                                    if (!StringUtils.isEmptyString(new PrefUtils(context).getSerialKey())) {
                                        startActivity(LoginActivity.class);
                                    }
                                }
                            }
                        }

                        @Override
                        public void onUnauthorized() {
                            mprogress.dismiss();
                        }

                        @Override
                        public void onFailed(Throwable throwable) {
                            mprogress.dismiss();
                        }
                    });
        } else {
            startActivity(LoginActivity.class);

        }
    }

    /**
     * open a new activity
     */
    void startActivity(Class aClass) {
        startActivity(new Intent(this, aClass));
        finish();
    }

    /**
     * get exercice data from server
     */
    void getExercice() {
        ExerciceDataManager.getInstance().getExercice(new GenericObject(App.prefUtils.getBaseConfig()), new RemoteCallback<Exercice>(context, false) {
            @Override
            public void onSuccess(Exercice response) {

                if (response.getExerciceCode() != null) {
                    App.prefUtils.setExercice(response.getExerciceCode());
                    getUser();

                } else {
                    UIUtils.showDialog(context, getString(R.string.error), getString(R.string.exrcice_not_found), (dialog, which) -> finish());
                }

            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();

            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();
            }
        });
    }

    /**
     * get connected user data from server
     */
    void getUser() {
        UtilisateurDataManager.getInstance().authentification(new GenericObject(App.prefUtils.getBaseConfig(), new Utilisateur(App.prefUtils.getUserLogin(), App.prefUtils.getUserPassword())),
                new RemoteCallback<Utilisateur>(context, false) {
                    @Override
                    public void onSuccess(Utilisateur response) {
                        if (response != null) {
                            try {

                                App.prefUtils.setUserAccount(response);
                                if (App.prefUtils.getCrtourAuto() /*&& newSession*/) {
                                    addOrdreMission();
                                } else {
                                    UpdateLocalData();
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                                UIUtils.showDialogWithoutChoice(context, context.getString(R.string.error), context.getString(R.string.aut_error), (dialog, which) -> {
                                    dialog.dismiss();
                                    startActivity(LoginActivity.class);
                                });
                            }

                        } else {
                            startActivity(LoginActivity.class);
                        }

                    }

                    @Override
                    public void onUnauthorized() {
                        mprogress.dismiss();

                    }

                    @Override
                    public void onFailed(Throwable throwable) {
                        mprogress.dismiss();

                    }
                });
    }

    /**
     * add ordre mission
     */
    void addOrdreMission() {
        List<OrdreMission> ordreMissionList = new ArrayList<>();
        OrdreMission ordreMission = new OrdreMission("",
                prefUtils.getExercice(), "", "", "", "", prefUtils.getUserId(), "", "", prefUtils.getUserStationId(),
                0, "", 0, "", "", "", "", prefUtils.getSessionCaisseId()
        );
        ordreMissionList.add(ordreMission);
        OrdreMissionDataManager.getInstance().addOrdreMission(new GenericObject(App.prefUtils.getBaseConfig(), ordreMissionList),
                new RemoteCallback<Boolean>(context, false) {
                    @Override
                    public void onSuccess(Boolean response) {
                        UpdateLocalData();
                    }

                    @Override
                    public void onUnauthorized() {

                    }

                    @Override
                    public void onFailed(Throwable throwable) {

                    }
                });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {

        super.onActivityResult(requestCode, resultCode, data);
        ActivityResultBus.getInstance().postQueue(
                new ActivityResultEvent(requestCode, resultCode, data));


        if (requestCode == REQUEST_CODE_UPDATE) {
            if (resultCode != RESULT_OK) {
                Toasty.error(context,"Erreur Mise à jour !").show();
                // log("Update flow failed! Result code: " + resultCode);
                // If the update is cancelled or fails,
                // you can request to start the update again.
            }
        }

        if (requestCode == REQUEST_SCHEDULE_EXACT_ALARM_PERMISSION) {
            if (resultCode == RESULT_OK) {
                // Permission granted, schedule your alarm
                //scheduleExactAlarm();
            } else {
                // Permission denied, handle the situation appropriately
                // Inform the user that the app cannot function without the permission
            }
        }

    }

    /**
     * detect drawer item click and open a new fragment
     */
    void onChangeFragment(IDrawerItem drawerItem) {
        try {
            if (result != null) {
                getSupportActionBar()
                        .setTitle(((Nameable) drawerItem)
                                .getName()
                                .getText(MainActivity.this));
                if (result.isDrawerOpen()) {
                    result.closeDrawer();
                }
            }
        } catch (Exception e) {
            Toasty.info(context, e.getMessage()).show();
        }
        try {
            switch ((int) drawerItem.getIdentifier()) {

                case R.id.dashboard_drawer_item:
                    changeContainer(Globals.DRAWER_ITEMS.DASHBOARD.getFragment());
                    break;
                case R.id.sync_drawer_item:
                    if (NetworkUtils.isConnected()) {
                        changeContainer(Globals.DRAWER_ITEMS.SYNC.getFragment());
                    } else {
                        Toasty.info(context, R.string.no_connection).show();
                    }
                    break;
                case R.id.products_drawer_item:
                    changeContainer(Globals.DRAWER_ITEMS.PRODUCTS.getFragment());
                    break;
                case R.id.clients_drawer_item:
                    changeContainer(Globals.DRAWER_ITEMS.CLIENTS.getFragment());
                    break;
                case R.id.commande_drawer_item:
                    changeContainer(Globals.DRAWER_ITEMS.COMMANDE.getFragment());
                    break;
                case R.id.retour_drawer_item:
                    changeContainer(Globals.DRAWER_ITEMS.RETOUR.getFragment());
                    break;

                case R.id.payments_drawer_item:
                    changeContainer(Globals.DRAWER_ITEMS.PAYMENTS.getFragment());
                    break;
                case R.id.settings_drawer_item:
                    changeContainer(new SettingsFragment());
                    break;
                case R.id.tickets_drawer_item:
                    changeContainer(Globals.DRAWER_ITEMS.TICKETS.getFragment());
                    break;
                case R.id.expenses_drawer_item:
                    changeContainer(Globals.DRAWER_ITEMS.EXPENSES.getFragment());
                    break;

                case R.id.orders_drawer_item:
                    changeContainer(Globals.DRAWER_ITEMS.ORDERS.getFragment());
                    break;

                case R.id.tournee_drawer_item: {
                    changeContainer(Globals.DRAWER_ITEMS.TOURNEE.getFragment());
                }
                break;

                case R.id.vc_new_product: {
                    changeContainer(Globals.DRAWER_ITEMS.VC_NEWPRODUCT.getFragment());
                }
                break;


            }
        } catch (Exception e) {
            Toasty.info(context, e.getMessage()).show();
        }
    }

    /**
     * show the chosen fragment inside the container
     */
    void changeContainer(Fragment fragment) {

        getSupportFragmentManager()
                .beginTransaction()
                .replace(R.id.frame_container, fragment)
                //.addToBackStack(null)
                .commit();

    }


    /**
     * show the chosen fragment inside the container
     */
    void changeContainerWithDetach(Fragment fragment) {
        getSupportFragmentManager()
                .beginTransaction()
                .detach(fragment)
                .commit();


        getSupportFragmentManager()
                .beginTransaction()
                .attach(fragment)
                .commit();


        getSupportFragmentManager()
                .beginTransaction()
                .replace(R.id.frame_container, fragment)
                .commit();

    }

    @SuppressLint("WrongConstant")
    @Override
    public void onBackPressed() {
        if (result != null && result.isDrawerOpen()) {
            result.closeDrawer();
        } else if (mLayoutBottomSheet != null) {
            if (mBottomSheetBehavior.getState() == ExpandableLayout.State.EXPANDED) {
                mBottomSheetBehavior.setState(ExpandableLayout.State.COLLAPSED);
            }
        } else {
            if (doubleBackToExitPressedOnce) {
                super.onBackPressed();
                finishAffinity();
                return;
            }

            this.doubleBackToExitPressedOnce = true;
            Toasty.info(this, R.string.press_back, Toast.LENGTH_SHORT).show();
            new Handler().postDelayed(() -> doubleBackToExitPressedOnce = false, 2000);
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        handleIntent(intent);
    }

    /**
     * load the data from server to the local database
     */
    private void UpdateLocalData() {
        mprogress.show();
        mprogress.setCancelable(false);


        LoadBackUpLicensesUrl();
    }


    /**
     * get prefixes data from server
     */
    void getPrices() {

        mprogress.setMessage("Chargement des données prix ...");
        PricePerStationDataManager.getInstance().getPricesByStation(new GenericObject(prefUtils.getBaseConfig()),
                new RemoteCallback<List<PricePerStation>>(context, false) {
                    @Override
                    public void onSuccess(List<PricePerStation> response) {
                        App.database.pricePerStationDAO().insertAll(response);
                        //   getBanques();
                        getStations();
                    }

                    @Override
                    public void onUnauthorized() {
                        mprogress.dismiss();
                    }

                    @Override
                    public void onFailed(Throwable throwable) {
                        mprogress.dismiss();
                    }
                });

    }

    /**
     * get stations data from server
     */
    void getStations() {
        mprogress.setMessage("Chargement des données stations ...");
        StationDataManager.getInstance().getAllStations(new GenericObject(App.prefUtils.getBaseConfig()), new RemoteCallback<List<Station>>(context, false) {
            @Override
            public void onSuccess(List<Station> response) {
                App.database.stationDAO().insertAll(response);
                Station userStat = App.database.stationDAO().getOneByCode(prefUtils.getUserStationId());
                if (userStat != null)
                    prefUtils.setGPSParams(userStat.getStatMetrageM(), userStat.getStatSecondeM());
                getFornisseurs();
            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();

            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();
            }
        });
    }

    void getBanques() {
        mprogress.setMessage("Chargement des données banque ...");
        BanqueDataManager.getInstance().getBanques(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<ArrayList<Banque>>(context, false) {
            @Override
            public void onSuccess(ArrayList<Banque> response) {
                App.database.banqueDAO().insertAll(response);
                getClients();
            }

            @Override
            public void onUnauthorized() {

                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {

                mprogress.dismiss();
            }
        });
    }

    private void getVCNewProducts() {
        mprogress.setMessage("Chargement des données veuille concurrentielle ...");
        VeuilleConcurrentielleDataManager.getInstance().getVCNewProducts(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<VCNewProduct>>(context, false) {
            @Override
            public void onSuccess(List<VCNewProduct> response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    App.database.vcNewProductDAO().deleteAll();
                    App.database.vcNewProductDAO().insertAll(response);
                }
                getVCPromos();
            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();
                Toasty.info(context, throwable.getMessage()).show();
            }
        });
    }

    private void getVCPromos() {
        mprogress.setMessage("Chargement des données veuille concurrentielle ...");
        VeuilleConcurrentielleDataManager.getInstance().getVCPromos(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<VCPromo>>(context, false) {
            @Override
            public void onSuccess(List<VCPromo> response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    App.database.vcPromosDAO().deleteAll();
                    App.database.vcPromosDAO().insertAll(response);
                }
                getVCPrix();
            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();
                Toasty.info(context, throwable.getMessage()).show();
            }
        });
    }

    private void getVCPrix() {
        mprogress.setMessage("Chargement des données veuille concurrentielle ...");
        VeuilleConcurrentielleDataManager.getInstance().getVCPrices(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<VCPrix>>(context, false) {
            @Override
            public void onSuccess(List<VCPrix> response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    App.database.vcPricesDAO().deleteAll();
                    App.database.vcPricesDAO().insertAll(response);
                }
                getVCAutre();
            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();
                Toasty.info(context, throwable.getMessage()).show();
            }
        });
    }

    private void getVCAutre() {
        mprogress.setMessage("Chargement des données veuille concurrentielle ...");
        VeuilleConcurrentielleDataManager.getInstance().getVCautres(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<VCAutre>>(context, false) {
            @Override
            public void onSuccess(List<VCAutre> response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    App.database.vcAutreDAO().deleteAll();
                    App.database.vcAutreDAO().insertAll(response);
                }
                getVCTypeCommunication();
            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();
                Toasty.info(context, throwable.getMessage()).show();
            }
        });
    }

    private void getVCTypeCommunication() {
        mprogress.setMessage("Chargement des données veuille concurrentielle ...");
        VeuilleConcurrentielleDataManager.getInstance().getVCTypeCommunications(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<VCTypeCommunication>>(context, false) {
            @Override
            public void onSuccess(List<VCTypeCommunication> response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    App.database.vcTypeCommunicationDAO().insertAll(response);
                }
                getVCListConcurrent();
            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();
                Toasty.info(context, throwable.getMessage()).show();
            }
        });
    }

    private void getVCImages() {
        mprogress.setMessage("Chargement des données veuille concurrentielle ...");
        VeuilleConcurrentielleDataManager.getInstance().getVCImages(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<VCImage>>(context, false) {
            @Override
            public void onSuccess(List<VCImage> response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    for (VCImage vcImage : response) {
                        vcImage.isSync = true;
                        vcImage.status = Globals.ITEM_STATUS.SELECTED.getStatus();
                    }
                    App.database.vcImageDAO().deleteAll();
                    App.database.vcImageDAO().insertAll(response);
                }
                getAllTypeServices();
            }

            @Override
            public void onUnauthorized() {

            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();
                Toasty.info(context, throwable.getMessage()).show();
            }
        });
    }

    private void getVCListConcurrent() {
        mprogress.setMessage("Chargement des données veuille concurrentielle ...");
        VeuilleConcurrentielleDataManager.getInstance().getVCListeConcurrents(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<VCListeConcurrent>>(context, false) {
            @Override
            public void onSuccess(List<VCListeConcurrent> response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    App.database.vcListeConcurrentDAO().insertAll(response);
                }
                getVCImages();
            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();
                Toasty.info(context, throwable.getMessage()).show();
            }
        });
    }

    void getClients() {
        mprogress.setMessage("Chargement des données client ...");
        List<String> zones = prefUtils.getUserAccount().getZone();
        ClientDataManager.getInstance().getClients(new GenericObject(prefUtils.getBaseConfig(), null), new RemoteCallback<List<Client>>(context, false) {
            @Override
            public void onSuccess(List<Client> response) {
                App.database.clientDAO().insertAll(response);
              //  getImmobilisation();
                getArticles();
            }

            @Override
            public void onUnauthorized() {

                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();

            }
        }, prefUtils.getUserAccount().getTypeUser().equals("Client") ? prefUtils.getUserAccount().getCltEquivalent() : null);
    }




    /**
     * get Immobilisation data from server
     */

    void getImmobilisation() {

        mprogress.setMessage("Chargement des données immobilisation ...");
        List<String> zones = prefUtils.getUserAccount().getZone();

        ImmobilisationDataManager.getInstance().getImmobilisation(new GenericObject(prefUtils.getBaseConfig(), null), new RemoteCallback<List<Immobilisation>>(context, false) {
                    @Override
                    public void onSuccess(List<Immobilisation> response) throws NoSuchFieldException, InstantiationException, IllegalAccessException {
                        for (Immobilisation immobilisation : response) {
                            Immobilisation im = new Immobilisation(
                                    immobilisation.getcLICode(),
                                    immobilisation.cLINomPren,
                                    immobilisation.getCliImmo(),
                                    immobilisation.getCliImoCodeP(),
                                    immobilisation.getCliImoTypEmp(),
                                    immobilisation.getCliImoCB(),
                                    immobilisation.getTyEmpImNom().replace(" ",""));




                            App.database.clientImooDAO().insert(im);
                        }

                        getArticles();
                    }

                    @Override
                    public void onUnauthorized() {
                        Toasty.error(context, " getArticles onUnauthorized").show();

                        mprogress.dismiss();
                    }

                    @Override
                    public void onFailed(Throwable throwable) {
                        Toasty.error(context, " getImmobilisation : " + throwable.getMessage()).show();

                        mprogress.dismiss();
                    }
                }
        );
    }

    void getArticles() {
        mprogress.setMessage("Chargement des données article ...");
        ArticleDataManager.getInstance().getArticles(new GenericObject(prefUtils.getBaseConfig(), null), new RemoteCallback<List<Article>>(context, false) {
            @Override
            public void onSuccess(List<Article> response) {

                if (!response.isEmpty()) {
                    App.database.articleDAO().deleteAll();
                    App.database.articleDAO().insertAll(response);
                    //App.database.articleDAO().insertAll(response);
                    getArticleCodeBare();
                } else {
                    com.asmtunis.procaissemobility.helper.utils.UIUtils.showDialog(context, "erreur", "0 produit trouvé", (dialog, which) -> finish());
                }
            }

            @Override
            public void onUnauthorized() {
                Toasty.error(context, " getArticles onUnauthorized").show();

                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {
                Toasty.error(context, " getArticles : " + throwable.getMessage()).show();

                mprogress.dismiss();

            }
        });
    }

    private void getArticleCodeBare() {
        mprogress.setMessage("Chargement des données code à barres ..");
        CodeBareDataManager.getInstance().getCodeBares(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<ArticleCodeBar>>(context, false) {

            @Override
            public void onSuccess(List<ArticleCodeBar> response) {

                if (ObjectUtils.isNotEmpty(response)) {
                    App.database.articleCodeBarDAO().insertAll(response);
                }
                getArticlesStock();

            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();
                Toasty.info(context, throwable.getMessage()).show();
            }
        });
    }


    void getArticlesStock() {

        mprogress.setMessage("Chargement des données stock ...");
        StationStockDataManager.getInstance().getstockArticle(new GenericObject(prefUtils.getBaseConfig(), prefUtils.getUserStationId()), new RemoteCallback<List<StationStock>>(context, false) {
            @Override
            public void onSuccess(List<StationStock> response) {

                if (response != null) {

                    App.database.stationStockDAO().deleteAll();
                    App.database.stationStockDAO().insertAll(response);
                    for (StationStock stationStock : response) {
                        if (stationStock.getSARTCodeSatation().equals(App.prefUtils.getCaisseStationId())) {
                            Article article = App.database.articleDAO().getOneByCode(stationStock.getSARTCodeArt());
                            if (article != null) {
                                try {
                                    article.setaRTQteStock((stationStock.getSARTQte()));
                                    article.setsARTCodeSatation(stationStock.getSARTCodeSatation());
                                } catch (Exception e) {
                                    Log.d("err", e.getMessage());
                                }

                                App.database.articleDAO().insert(article);
                            }
                        }
                    }
                } else {
                    com.asmtunis.procaissemobility.helper.utils.UIUtils.showDialog(context, "erreur", "0 produit trouvé", (dialog, which) -> finish());
                }
                if (App.prefUtils.getVeilleConcurentielle())
                    getVCNewProducts();
                else getReclamations();

                // getReclamations();

            }

            @Override
            public void onUnauthorized() {

                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();

            }
        });
    }

    /**
     * get Marques data from server
     */
    void getMarque() {
        App.database.marqueDAO().deleteAll();
        mprogress.setMessage("Chargement des données Marques. ...");
        MarqueDataManager.getInstance().getMarques(new GenericObject(App.prefUtils.getBaseConfig(), null), new RemoteCallback<List<Marque>>(context, false) {
            @Override
            public void onSuccess(List<Marque> response) {

                if (!response.isEmpty()) {

                    App.database.marqueDAO().insertAll(response);
                }


                getTickets();
            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();
            }
        });
    }

    void getReclamations() {

        mprogress.setMessage("Chargement des données reclamations ...");

        ReclamationDataManager.getInstance().getReclamations(new GenericObject(prefUtils.getBaseConfig(), prefUtils.getCaisseId()), new RemoteCallback<List<Reclamation>>(context, false) {
            @Override
            public void onSuccess(List<Reclamation> response) {
                List<Reclamation> reclamations = new ArrayList<>();
                if (response != null || !response.isEmpty()) {

                    if (!reclamations.isEmpty()) {
                        App.database.reclamationDAO().insertAll(reclamations);
                    }

                }
                //   getTickets();
                getBonRetour();
            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();

            }

            @Override
            public void onFailed(Throwable throwable) {

                mprogress.dismiss();
            }
        });
    }

    void getBonRetour() {
        if (App.database.authorizationDAO().hasAuth(context.getResources().getInteger(R.integer.br))) {
            mprogress.setMessage("Chargement des données Bon retour ...");
            BonRetourDataManager.getInstance().getBonRetours(new GenericObject(App.prefUtils.getBaseConfig(), App.prefUtils.getCaisseId()), new RemoteCallback<List<BonRetour>>(context, false) {
                @Override
                public void onSuccess(List<BonRetour> response) {
                    if (response != null || !response.isEmpty()) {

                        if (!response.isEmpty()) {
                            for (BonRetour bonRetour : response) {
                                bonRetour.isSync = true;
                                bonRetour.setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());
                            }
                            App.database.bonRetourDAO().insertAll(response);
                        }
                    }
                    getLigneBonRetour();

                }

                @Override
                public void onUnauthorized() {
                    mprogress.dismiss();

                }

                @Override
                public void onFailed(Throwable throwable) {
                    mprogress.dismiss();

                }
            });
        } else {
            getBonCommande();
        }
    }

    void getLigneBonRetour() {

        mprogress.setMessage("Chargement des données lignes bon retour ...");

        LigneBonRetourDataManager.getInstance().getLigneBonRetours(new GenericObject(App.prefUtils.getBaseConfig(), App.prefUtils.getCaisseId()), new RemoteCallback<List<LigneBonRetour>>(context, false) {
            @Override
            public void onSuccess(List<LigneBonRetour> response) {
                if (response != null || !response.isEmpty()) {

                    if (!response.isEmpty()) {
                        for (LigneBonRetour bonRetour : response) {
                            bonRetour.isSync = true;
                            bonRetour.setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());
                        }
                        App.database.ligneBonRetourDAO().insertAll(response);
                    }


                }
                getBonCommande();

            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();

            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();

            }
        });
    }

    void getBonCommande() {
        if (prefUtils.getBcAuthorization() || prefUtils.getInvPAuthorization()) {
            mprogress.setMessage("Chargement des données lignes bon commande ...");
            BonCommandeDataManager.getInstance().getBonCommande(new GenericObject(App.prefUtils.getBaseConfig(), App.prefUtils.getCaisseId()), new RemoteCallback<List<BonCommande>>(context, false) {
                @Override
                public void onSuccess(List<BonCommande> response) {
                    if (response != null || !response.isEmpty()) {

                        if (!response.isEmpty()) {
                            for (BonCommande bonRetour : response) {
                                bonRetour.isSync = true;
                                bonRetour.setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());
                            }
                            App.database.bonCommandeDAO().insertAll(response);
                        }
                    }
                    getLigneBonCommande();
                }

                @Override
                public void onUnauthorized() {
                    mprogress.dismiss();

                }

                @Override
                public void onFailed(Throwable throwable) {
                    mprogress.dismiss();

                }
            });
        } else {
            getEtatOrdreMission();
        }
    }

    void getLigneBonCommande() {
        mprogress.setMessage("Chargement des données Bon commande ...");
        LigneBonCommandeDataManager.getInstance().getLigneBonCommande(new GenericObject(App.prefUtils.getBaseConfig(), App.prefUtils.getCaisseId()), new RemoteCallback<List<LigneBonCommande>>(context, false) {
            @Override
            public void onSuccess(List<LigneBonCommande> response) {
                if (response != null && !response.isEmpty()) {
                    for (LigneBonCommande bonRetour : response) {
                        bonRetour.isSync = true;
                        bonRetour.setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());
                    }
                    App.database.ligneBonCommandeDAO().insertAll(response);
                }
                getEtatOrdreMission();

            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();
            }
        });
    }

    /**
     * get EtatOrdreMission data from server
     */
    void getEtatOrdreMission() {
        if (prefUtils.getTourAuthorization()) {
            mprogress.setMessage("Chargement des données Etat Ordre Missions. ...");
            EtatOrdreMissionDataManager.getInstance().getEtatOrdreMission(
                    new GenericObject(App.prefUtils.getBaseConfig(), null),
                    new RemoteCallback<List<EtatOrdreMission>>(context, false) {
                        @Override
                        public void onSuccess(List<EtatOrdreMission> response) {
                            if (!response.isEmpty() && response != null) {
                                App.database.etatOrdreMissionDAO().insertAll(response);
                            }
                            //   new OrdreMissionUtils(context).getOrdreWithLines();
                            getOrdreWithLines();


                        }


                        @Override
                        public void onUnauthorized() {
                            mprogress.dismiss();
                        }

                        @Override
                        public void onFailed(Throwable throwable) {
                            mprogress.dismiss();
                        }
                    }
            );
        } else {
            if (App.prefUtils.getBlAuthorization()) getTimbres();
            else getFamilles();
        }
    }

    /**
     * /**
     * get OrdreWithLines data from server
     */
    void getOrdreWithLines() {
        Vendeur vendeur = new Vendeur(App.prefUtils.getUserId(), getCurrentDate());
        mprogress.setMessage("Chargement des données Ordre Missions. ...");
        OrdreMissionDataManager.getInstance().getOrdreMessionWithLines(
                new GenericObject(App.prefUtils.getBaseConfig(), vendeur),
                new RemoteCallback<List<OrdreWithLines>>(context, false) {
                    @Override
                    public void onSuccess(List<OrdreWithLines> response) {
                        if (!response.isEmpty() && response != null) {
                            App.database.ordreMissionDAO().deleteAll();
                            App.database.ligneOrdreMissionDAO().deleteAll();
                            App.database.trakingAlarmDAO().deleteAll();
                            for (OrdreWithLines ordreWithLines : response) {
                                App.database.ordreMissionDAO().insertOne(ordreWithLines.ordreMission);
                                App.database.ligneOrdreMissionDAO().insertAll(ordreWithLines.ligneOrdreMission);
                                TrakingAlarm trakingAlarm = App.database.trakingAlarmDAO().getWithOrdreMission(ordreWithLines.ordreMission.oRDCode);
                                if (!(ordreWithLines.ordreMission.oRD_dateDebut == null) && !(ordreWithLines.ordreMission.oRD_dateFin == null) && trakingAlarm == null) {
                                    trakingAlarm
                                            = new TrakingAlarm(
                                            ordreWithLines.ordreMission.oRDCode,
                                            ordreWithLines.ordreMission.oRD_dateDebut,
                                            ordreWithLines.ordreMission.oRD_dateFin,
                                            false,
                                            false,
                                            false
                                    );
                                    App.database.trakingAlarmDAO().insertTrAlarm(trakingAlarm);
                                }
                            }
                        }
                        if (App.prefUtils.getBlAuthorization()) getTimbres();
                        else getFamilles();
                    }

                    @Override
                    public void onUnauthorized() {
                        mprogress.dismiss();
                    }

                    @Override
                    public void onFailed(Throwable throwable) {
                        mprogress.dismiss();
                    }
                }
        );
    }

    /**
     * get timbre data from server
     */
    void getFamilles() {
        mprogress.setMessage("Chargement des données Familles. ...");
        if (!App.prefUtils.getUserId().equals(DEFAULT_VALUE)) {
            FamilleDataManager.getInstance().getFamilles(new GenericObject(App.prefUtils.getBaseConfig(),
                            App.prefUtils.getUserId()),
                    new RemoteCallback<List<Famille>>(context, false) {
                        @Override
                        public void onSuccess(List<Famille> response) {
                            if (response != null) {
                                App.database.familleDAO().insertAll(response);
                            }
                            getFacture();
                        }

                        @Override
                        public void onUnauthorized() {

                        }

                        @Override
                        public void onFailed(Throwable throwable) {

                        }
                    });
        }
    }

    /**
     * get facture data from server
     */
    void getFacture() {
        mprogress.setMessage("Chargement des données Facture. ...");
        if (!App.prefUtils.getUserId().equals(DEFAULT_VALUE)) {
            FactureDataManager.getInstance().getFacture(new GenericObject(App.prefUtils.getBaseConfig(), null),
                    new RemoteCallback<List<Facture>>(context, false) {
                        @Override
                        public void onSuccess(List<Facture> response) {
                            if (response != null) {
                                App.database.factureDAO().insertAll(response);
                            }
                            getClientsArticlePrix();
                        }

                        @Override
                        public void onUnauthorized() {

                        }

                        @Override
                        public void onFailed(Throwable throwable) {

                        }
                    });
        }
    }

    /**
     * get timbre data from server
     */
    void getTimbres() {
        mprogress.setMessage("Chargement des données Timbres. ...");
        if (!App.prefUtils.getUserId().equals(DEFAULT_VALUE)) {
            TimbreDataManager.getInstance().getTimbres(new GenericObject(App.prefUtils.getBaseConfig(),
                            App.prefUtils.getUserId()),
                    new RemoteCallback<List<Timbre>>(context, false) {
                        @Override
                        public void onSuccess(List<Timbre> response) {
                            if (response.size() != 0) {
                                App.database.timbreDAO().insertAll(response);
                            }
                            getFamilles();
                        }

                        @Override
                        public void onUnauthorized() {
                            mprogress.dismiss();
                        }

                        @Override
                        public void onFailed(Throwable throwable) {
                            mprogress.dismiss();
                        }
                    });
        }
    }

    void getClientsArticlePrix() {
        mprogress.setMessage("Chargement des données clients articles prix ...");
        ClientArticlePrixDataManager.getInstance().getClientArticlePrix(
                new GenericObject(App.prefUtils.getBaseConfig()),
                new RemoteCallback<List<ClientArticlePrix>>(context, false) {
                    @Override
                    public void onSuccess(List<ClientArticlePrix> response) {
                        App.database.clientArticlePrixDAO().insertAll(response);
                        getVilles();
                    }

                    @Override
                    public void onUnauthorized() {
                        mprogress.dismiss();
                    }

                    @Override
                    public void onFailed(Throwable throwable) {
                        mprogress.dismiss();
                    }
                });
    }

    /**
     * get villes data from server
     */
    void getVilles() {
        mprogress.setMessage("Chargement des données Villes. ...");
        VilleDataManager.getInstance().getVilles(new GenericObject(App.prefUtils.getBaseConfig(), App.prefUtils.getCaisseId()), new RemoteCallback<List<Ville>>(context, false) {
            @Override
            public void onSuccess(List<Ville> response) {
                if (!response.isEmpty()) {
                    App.database.villeDAO().insertAll(response);
                    prefUtils.setMaxPassager(response.get(0));
                }

                if(prefUtils.getInvPAuthorization())
                    getMarque();

                else
                    getTickets();
            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();
            }
        });
    }

    /**
     * get tickets data from server
     */
    void getTickets() {
        if (prefUtils.getBlAuthorization()) {
            mprogress.setMessage("Chargement des données B.L. ...");
            TicketDataManager.getInstance().getTicketsByCaisseId(new GenericObject(App.prefUtils.getBaseConfig(), App.prefUtils.getCaisseId()), new RemoteCallback<List<Ticket>>(context, false) {
                @Override
                public void onSuccess(List<Ticket> response) {
                    List<Ticket> tickets = new ArrayList<>();
                    if (response != null || !response.isEmpty()) {
                        for (Ticket ticket : response) {
                            if (ticket.gettIKMtTTC() > 0 ) {

                                if( ticket.tikNumTicketM == null)
                                    ticket.setTikNumTicketM(String.valueOf(ticket.gettIKNumTicket()));

                                tickets.add(ticket);
                            }
                        }
                        if (!tickets.isEmpty()) {
                            App.database.ticketDAO().insertAll(tickets);
                            getLigneTickets(tickets);
                        } else {
                            App.prefUtils.setIsInitiated(false);
                            prefUtils.setLoadData("full");
                            App.database.appPropertiesDAO().insert(new AppProperties(new Date().getTime(), 0L));
                            mprogress.dismiss();
                        }
                    }
                }

                @Override
                public void onUnauthorized() {
                    mprogress.dismiss();
                }

                @Override
                public void onFailed(Throwable throwable) {
                    mprogress.dismiss();
                }
            }, false, true);
        } else {
            App.prefUtils.setIsInitiated(false);
            prefUtils.setLoadData("full");
            //startActivity(MainActivity.class);
            mprogress.dismiss();
            App.database.appPropertiesDAO().insert(new AppProperties(new Date().getTime(), 0L));
        }
    }
  /*  void getTickets() {
        if (prefUtils.getBlAuthorization()) {
            mprogress.setMessage("Chargement des données B.L. ...");
            TicketDataManager.getInstance().getTicketsByCaisseId(new GenericObject(prefUtils.getBaseConfig(), prefUtils.getCaisseId()), new RemoteCallback<List<Ticket>>(context, false) {
                @Override
                public void onSuccess(List<Ticket> response) {
                    List<Ticket> tickets = new ArrayList<>();
                    if (response != null || !response.isEmpty()) {
                        for (Ticket ticket : response) {
                            if (ticket.gettIKMtTTC() > 0) {
                                tickets.add(ticket);
                            }
                        }
                        if (!tickets.isEmpty()) {
                            App.database.ticketDAO().insertAll(tickets);
                            getLigneTickets(tickets);
                        } else {
                            // startActivity(MainActivity.class);
                            mprogress.dismiss();
                        }

                    } else {
                        //  startActivity(MainActivity.class);
                        mprogress.dismiss();

                    }
                }

                @Override
                public void onUnauthorized() {
                    mprogress.dismiss();

                }

                @Override
                public void onFailed(Throwable throwable) {

                    mprogress.dismiss();
                }
            }, false, true);

        }
        else {
            App.prefUtils.setIsInitiated(false);
            prefUtils.setLoadData("full");
        //    startActivity(MainActivity.class);
            App.database.appPropertiesDAO().insert(new AppProperties(new Date().getTime(), 0L));
        }


    }*/

    void getLigneTickets(List<Ticket> tickets) {
        mprogress.setMessage("Chargement des données lignes des B.L. ...");
        LigneTicketDataManager.getInstance().getLigneTicketByTickets(new GenericObject(prefUtils.getBaseConfig(), tickets), new RemoteCallback<List<List<LigneTicket>>>(context, false) {
            @Override
            public void onSuccess(List<List<LigneTicket>> response) {

                List<LigneTicket> finalList = new ArrayList<>();

                if (response.get(0) != null) {
                    for (List<LigneTicket> list : response) {
                        finalList.addAll(list);
                    }

                    if (!finalList.isEmpty()) {
                        App.database.ligneTicketDAO().insertAll(finalList);
                        getReglementsCaisse(tickets);
                    } else {
                        App.prefUtils.setIsInitiated(false);
                        prefUtils.setLoadData("full");
                        App.database.appPropertiesDAO().insert(new AppProperties(new Date().getTime(), 0L));
                        mprogress.dismiss();
                    }


                } else {
                    mprogress.dismiss();
                }

            }

            @Override
            public void onUnauthorized() {

                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {

                mprogress.dismiss();
            }
        });


    }

    void getReglementsCaisse(List<Ticket> tickets) {
        mprogress.setMessage("Chargement des données réglements (1)...");


       /* ReglementCaisseDataManager.getInstance().getReglementCaisseByTickets(new GenericObject(prefUtils.getBaseConfig(), tickets), new RemoteCallback<List<List<ReglementCaisse>>>(context, false) {
            @Override
            public void onSuccess(List<List<ReglementCaisse>> response) {


                if (response != null) {

                    List<ReglementCaisse> finalList = new ArrayList<>();

                    for (List<ReglementCaisse> list : response) {

                        if (!list.isEmpty()) {
                            for (int i = 0; i < response.size(); i++) {
                                if(list.size()>=i){
                                    if (list.get(i).getrEGCCode_M() == null) {
                                        list.get(i).setrEGCCode_M(list.get(i).rEGCCode);
                                    }
                                    if (list.get(i).REGC_MntCheque == null) {
                                        list.get(i).setrEGCMntChQue(0.0);
                                    }


                                    list.get(i).setrEGCDateReg(DateUtils.dateToStr(
                                            DateUtils.strToDate(list.get(i).getrEGCDateReg().replaceAll("/", "-"),
                                                    "yyyy-MM-dd HH:mm:ss"), "yyyy-MM-dd HH:mm:ss"));
                                }

                            }

                            finalList.addAll(list);

                        }


                    }
                    if (!finalList.isEmpty()) {
                        App.database.reglementCaisseDAO().deleteAll();

                        App.database.reglementCaisseDAO().insertAll(finalList);
                        getChequesCaisse(finalList);

                    } else {
                        App.prefUtils.setIsInitiated(false);
                        prefUtils.setLoadData("full");
                        App.database.appPropertiesDAO().insert(new AppProperties(new Date().getTime(), 0L));
                        mprogress.dismiss();
                    }
                }

            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();

            }

            @Override
            public void onFailed(Throwable throwable) {

                mprogress.dismiss();
            }
        });
*/


        ReglementCaisseDataManager.getInstance().getReglementCaisseBySession(new GenericObject(App.prefUtils.getBaseConfig(), App.prefUtils.getCaisseId()), new RemoteCallback<List<ReglementCaisse>>(context, false) {
            @Override
            public void onSuccess(List<ReglementCaisse> response) {

                if (response != null) {
                    if (!response.isEmpty()) {

                        for (int i = 0; i < response.size(); i++) {
                            if (response.get(i).getrEGCCode_M() == null) {
                                response.get(i).setrEGCCode_M(response.get(i).rEGCCode);
                            }

                            response.get(i).setrEGCDateReg(DateUtils.dateToStr(
                                    DateUtils.strToDate(response.get(i).getrEGCDateReg().replaceAll("/", "-"),
                                            "yyyy-MM-dd HH:mm:ss"), "yyyy-MM-dd HH:mm:ss"));
                        }

                    }
                    App.database.reglementCaisseDAO().deleteAll();
                    App.database.reglementCaisseDAO().insertAll(response);

                    getChequesCaisse(response);

                } else {
                    App.prefUtils.setIsInitiated(false);
                    prefUtils.setLoadData("full");
                    startActivity(MainActivity.class);
                    App.database.appPropertiesDAO().insert(new AppProperties(new Date().getTime(), 0L));
                }
            }


            @Override
            public void onUnauthorized() {
                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {
                Toasty.info(context, throwable.getMessage()).show();
                mprogress.dismiss();
            }
        }, false);

    }

    void getChequesCaisse(List<ReglementCaisse> reglementsCaisse) {
        mprogress.setMessage("Chargement des données réglements (2)...");

        if (!reglementsCaisse.isEmpty()) {
            ChequeCaisseDataManager.getInstance().getChequeCaisseByReglements(new GenericObject(prefUtils.getBaseConfig(), reglementsCaisse), new RemoteCallback<List<List<ChequeCaisse>>>(context, false) {
                @Override
                public void onSuccess(List<List<ChequeCaisse>> response) {
                    if (response != null) {
                        App.database.chequeCaisseDAO().insertAll(response.get(0));
                        getTraitesCaisse(reglementsCaisse);
                    } else {
                        mprogress.dismiss();

                    }

                }

                @Override
                public void onUnauthorized() {
                    mprogress.dismiss();

                }

                @Override
                public void onFailed(Throwable throwable) {
                    mprogress.dismiss();

                }
            });
        } else {
            mprogress.dismiss();
        }

    }
    void getDepenceCaisseByCaisseId(){
        mprogress.setMessage("Chargement des données Depence ...");
        String ddm = "";
        ddm = App.database.depenceCaisseDAO().getDDM();
        DepenceCaisseDataManager.getInstance().getDepenceCaisseByCaisseId(new GenericObject(prefUtils.getBaseConfig(), new DepenceCaisse(App.prefUtils.getCaisseCode(), ddm)),
                new RemoteCallback<List<DepenceCaisse>>(context, false) {
                    @Override
                    public void onSuccess(List<DepenceCaisse> response) {
                        if (!response.isEmpty()) {
                            App.database.depenceCaisseDAO().deleteAll();
                            for (DepenceCaisse depenceCaisse : response) {

                                if(depenceCaisse.getDepCodeM()==null)
                                    depenceCaisse.setDepCodeM(depenceCaisse.getDepCode());
                                App.database.depenceCaisseDAO().insert(depenceCaisse);
                            }
                            //App.database.depenceCaisseDAO().insertAll(response);


                        }


                            App.prefUtils.setIsInitiated(false);
                            prefUtils.setLoadData("full");
                            mprogress.dismiss();
                            App.database.appPropertiesDAO().insert(new AppProperties(new Date().getTime(), 0L));

                    }

                    @Override
                    public void onUnauthorized() {
                        mprogress.dismiss();
                    }

                    @Override
                    public void onFailed(Throwable throwable) {
                        mprogress.dismiss();
                    }
                });
    }
    void getTraitesCaisse(List<ReglementCaisse> reglementsCaisse) {
        mprogress.setMessage("Chargement des données réglements (3)...");
        TraiteCaisseDataManager.getInstance().getTraiteCaisseByReglements(new GenericObject(prefUtils.getBaseConfig(), reglementsCaisse), new RemoteCallback<List<List<TraiteCaisse>>>(context, false) {
            @Override
            public void onSuccess(List<List<TraiteCaisse>> response) {



               if (!response.isEmpty()) {
                    App.database.traiteCaisseDAO().insertAll(response.get(0));

               }
                 if (prefUtils.getDepenseAuthorization()) getExpenses();

                else {
                    App.prefUtils.setIsInitiated(false);
                    prefUtils.setLoadData("full");
                    startActivity(MainActivity.class);
                    App.database.appPropertiesDAO().insert(new AppProperties(new Date().getTime(), 0L));
                }


                // getAllTypeServices();

            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();

            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();
            }
        });


    }

    void getDevises() {
        mprogress.setMessage("Chargement des données devises ...");
        DeviseDataManager.getInstance().getDevises(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<Devise>>(context, false) {
            @Override
            public void onSuccess(List<Devise> response) {
                App.database.deviseDAO().insertAll(response);
                Devise devise = App.database.deviseDAO().getActiveOne();
                if (devise != null) {
                    prefUtils.setCurrency(devise.getSymbole());
                    prefUtils.setDecimalCount(devise.getNbreChiffreVirgule());
                }
                getStatistics();
            }

            @Override
            public void onUnauthorized() {

                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();

            }
        });
    }

    /**
     * load the list of  BackUp Licenses Url
     */
    public void LoadBackUpLicensesUrl() {
        //  if(progress != null)  progress.setProgress(3);
        mprogress.setMessage("Chargement des données URL ...");
        LicenseDataManager.getInstance().getLicenseUrl(new GenericObject(App.prefUtils.getBaseConfig()),
                new RemoteCallback<LicenseResponse>(context, false) {
                    @Override
                    public void onSuccess(LicenseResponse response) {


                        App.database.backUpLicenseUrlDAO().insertAll(response);

                        getParametrages();

                    }

                    @Override
                    public void onUnauthorized() {
                        mprogress.dismiss();
                    }

                    @Override
                    public void onFailed(Throwable throwable) {
                        mprogress.dismiss();
                    }
                });

    }


    void getParametrages() {
        mprogress.setMessage("Chargement logos ...");
        if (!App.prefUtils.getUserId().equals(DEFAULT_VALUE)) {
            ParametragesDataManager.getInstance().getParametrage(new GenericObject(App.prefUtils.getBaseConfig()),
                    new RemoteCallback<Parametrages>(context, false) {
                        @Override
                        public void onSuccess(Parametrages response) {
                            if (response != null) {
                                //App.database.familleDAO().insertAll(response);

                                App.prefUtils.setEntrepriseIcon(response.getPARAM_Logo());

                            }

                            getPrefixes();
                        }

                        @Override
                        public void onUnauthorized() {
                            mprogress.dismiss();
                        }

                        @Override
                        public void onFailed(Throwable throwable) {
                            mprogress.dismiss();
                        }
                    });
        }
    }

    /**
     * get depenses data from server
     */
    void getExpenses() {
        mprogress.setMessage("Chargement des données depenses ...");


        DepenceDataManager.getInstance().getDepence(new GenericObject(App.prefUtils.getBaseConfig()), new RemoteCallback<List<DepenceType>>(context, false) {
            @Override
            public void onSuccess(List<DepenceType> response) {
                getDepenceCaisseByCaisseId();




                //getExpensesCaisse();
            }

            @Override
            public void onUnauthorized() {

            }

            @Override
            public void onFailed(Throwable throwable) {

            }
        });

    }

    /**
     * get depenses data from server
     */
  /*  void getExpensesCaisse() {
        progress.setProgress(23);
        if (loading!=null)

            loading.setText("Chargement des données depenses caisse ...");

        DepenceCaisseDataManager.getInstance().getDepenceCaisse(new GenericObject(App.prefUtils.getBaseConfig()), new RemoteCallback<>(context, false) {
            @Override
            public void onSuccess(List<DepenceCaisse> response) {

                App.database.depenceCaisseDAO().insertAll(response);

                getStatistics();
            }

            @Override
            public void onUnauthorized() {

            }

            @Override
            public void onFailed(Throwable throwable) {

            }
        });

    }
*/
    void getPrefixes() {
        mprogress.setMessage("Chargement des données prefixes ...");
        PrefixeDataManager.getInstance().getPrefixes(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<Prefixe>>(context, false) {
            @Override
            public void onSuccess(List<Prefixe> response) {
                App.database.prefixeDAO().insertAll(response);
                //   new OrdreMissionUtils(context).getOrdreWithLines();
                getMaxTikNum();
                //  getDevises();
            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {

                mprogress.dismiss();
            }
        });
    }

    /**
     * get num ticket from server
     */
    void getMaxTikNum() {
        if (prefUtils.getBlAuthorization()) {
            mprogress.setMessage("Chargement des données numéro ticket ...");
            TicketDataManager.getInstance().getMaxTikNum(new GenericObject(App.prefUtils.getBaseConfig()), new RemoteCallback<Integer>(context, false) {
                @Override
                public void onSuccess(Integer response) {
                    if (response != null) {
                        prefUtils.setMaxNumTicket(response);
                    }
                    getSessionCaisses();
                }

                @Override
                public void onUnauthorized() {
                    mprogress.dismiss();

                }

                @Override
                public void onFailed(Throwable throwable) {
                    mprogress.dismiss();

                }
            });
        } else {
            getSessionCaisses();
        }
    }

    /**
     * get all caisse sessions
     */
    void getSessionCaisses() {
        SessionCaisseDataManager.getInstance().getSessionCaisses(new GenericObject(App.prefUtils.getBaseConfig()), new RemoteCallback<List<SessionCaisse>>(context, false) {
            @Override
            public void onSuccess(List<SessionCaisse> response) {
                if (response != null) {
                    App.database.sessionCaisseDAO().insertAll(response);
                }

                getPrices();

            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();

            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();

            }
        });
    }

    /**
     * get distribution numérique AllTypeServices data from server
     */
    private void getAllTypeServices() {

        mprogress.setMessage("Chargement des données distribution numérique 1 ...");
        DistributionNumeriqueDataManager.getInstance().getAllTypeService(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<DNTypeServices>>(context, false) {
            @Override
            public void onSuccess(List<DNTypeServices> response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    App.database.dnTypeServicesDAO().insertAll(response);
                }
                getAllTypeService();
            }

            @Override
            public void onUnauthorized() {

            }

            @Override
            public void onFailed(Throwable throwable) {
                Toasty.info(context, throwable.getMessage()).show();
                getAllTypeService();
            }
        });
    }

    /**
     * get distribution numérique AllTypeServices data from server
     */
    private void getAllTypeService() {

        mprogress.setMessage("Chargement des données distribution numérique 2 ...");
        DistributionNumeriqueDataManager.getInstance().getAllSuperficies(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<DNSuperficie>>(context, false) {
            @Override
            public void onSuccess(List<DNSuperficie> response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    App.database.dnSuperficieDAO().insertAll(response);
                }

                getAllTypePVentes();

            }

            @Override
            public void onUnauthorized() {

            }

            @Override
            public void onFailed(Throwable throwable) {
                Toasty.info(context, throwable.getMessage()).show();
                getAllTypePVentes();
            }
        });
    }


    /**
     * get distribution numérique  TypePVentes()  data from server
     */
    private void getAllTypePVentes() {

        mprogress.setMessage("Chargement des données distribution numérique 3 ...");
        DistributionNumeriqueDataManager.getInstance().getAllTypePVentes(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<DNTypePVente>>(context, false) {
            @Override
            public void onSuccess(List<DNTypePVente> response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    App.database.dnTypePVenteSDAO().insertAll(response);
                }
                getAllDNVIsites();
            }

            @Override
            public void onUnauthorized() {

            }

            @Override
            public void onFailed(Throwable throwable) {
                Toasty.info(context, throwable.getMessage()).show();
                getAllDNVIsites();
            }
        });
    }

    /**
     * get distribution numérique  DNVIsiteS  data from server
     */
    private void getAllDNVIsites() {
        mprogress.setMessage("Chargement des données distribution numérique 4 ...");
        DistributionNumeriqueDataManager.getInstance().getDNVIsitesByuser(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<DNVIsite>>(context, false) {
            @Override
            public void onSuccess(List<DNVIsite> response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    int i = 0;
                    for (DNVIsite dNVIsite : response) {

                        if (response.get(i).getVIS_Code_M() == null) {
                            dNVIsite.setVIS_Code_M(response.get(i).getVIS_Num());
                        }
                        App.database.dnVisitesDAO().insert(dNVIsite);
                        i++;

                    }
                }
                //getVisitebyCode();
                getAllLignevisitebyUser();
            }

            @Override
            public void onUnauthorized() {

            }

            @Override
            public void onFailed(Throwable throwable) {
                Toasty.info(context, throwable.getMessage()).show();
                getallDNFamille();
            }
        });
    }


    /**
     * get distribution numérique  DNLigne VIsiteS  by user data from server
     */
    private void getAllLignevisitebyUser() {

        mprogress.setMessage("Chargement des données distribution numérique 5 ...");
        DistributionNumeriqueDataManager.getInstance().getAllLignevisiteByUser(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<DN_LigneVisite>>(context, false) {
            @Override
            public void onSuccess(List<DN_LigneVisite> response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    App.database.dnLigneVisiteDAO().deleteAll();
                    App.database.dnLigneVisiteDAO().insertAll(response);


                }
                //getVisitebyCode();
                getallDNFamille();

            }

            @Override
            public void onUnauthorized() {

            }

            @Override
            public void onFailed(Throwable throwable) {
                Toasty.info(context, throwable.getMessage()).show();
                // getallDNFamille();
            }
        });
    }


    /**
     * get distribution numérique  DNfamille  data from server
     */
    private void getallDNFamille() {
        mprogress.setMessage("Chargement des données distribution numérique 6 ...");
        DistributionNumeriqueDataManager.getInstance().getalldnFamille(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<DNFamille>>(context, false) {
            @Override
            public void onSuccess(List<DNFamille> response) {
                if (ObjectUtils.isNotEmpty(response)) {
                    App.database.dnFamilleVisite().insertAll(response);
                }
                getReclamations();
                //getVisitebyCode();
              /*  App.prefUtils.setIsInitiated(false);
                prefUtils.setLoadData("full");
                App.database.appPropertiesDAO().insert(new AppProperties(new Date().getTime(), 0L));
                mprogress.dismiss();*/
            }

            @Override
            public void onUnauthorized() {

            }

            @Override
            public void onFailed(Throwable throwable) {
                Toasty.info(context, throwable.getMessage()).show();
            }
        });
    }


    void getStatistics() {
        mprogress.setMessage("Chargement des données statistiques ...");

        MiscDataManager.getInstance().getStatistics(new GenericObject(prefUtils.getBaseConfig(), prefUtils.getSessionCaisseId()), new RemoteCallback<Statistics>(context, false) {
            @Override
            public void onSuccess(Statistics response) {
                response.setUpdated_at(new Date().getTime());
                Paper.book().write(STATISTICS_DB_KEY, response);
                //   getFornisseurs();
                getCartesResto();
            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();
            }
        });
    }

    void getFornisseurs() {
        mprogress.setMessage("Chargement des données fornisseurs ...");
        FournisseurDataManager.getInstance().getFournisseurs(new GenericObject(App.prefUtils.getBaseConfig()), new RemoteCallback<List<Fournisseur>>(context, false) {
            @Override
            public void onSuccess(List<Fournisseur> response) {
                App.database.fournisseurDAO().insertAll(response);
                //    getCartesResto();
                getEtablisements();
            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();
            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();

            }
        });
    }

    /**
     * get etablisments data from server
     */
    void getEtablisements() {
        mprogress.setMessage("Chargement des données Etablisement ...");
        EtablisementDataManager.getInstance().getEtablisements(new GenericObject(App.prefUtils.getBaseConfig()), new RemoteCallback<Etablisement>(context, false) {
            @Override
            public void onSuccess(Etablisement response) {
                App.database.etablisementDAO().insertAll(response);
                getDevises();
            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();

            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();

            }
        });
    }


    void getCartesResto() {
        mprogress.setMessage("Chargement des données carte resto ...");
        CarteRestoDataManager.getInstance().getCartesResto(new GenericObject(prefUtils.getBaseConfig()), new RemoteCallback<List<CarteResto>>(context, false) {
            @Override
            public void onSuccess(List<CarteResto> response) {
                App.database.carteRestoDAO().insertAll(response);
                //  getPrices();
                getBanques();
            }

            @Override
            public void onUnauthorized() {
                mprogress.dismiss();

            }

            @Override
            public void onFailed(Throwable throwable) {
                mprogress.dismiss();

            }
        });
    }


    @Override
    public void onSharedPreferenceChanged(SharedPreferences sharedPreferences, String key) {
    }

    @Override
    public void onPointerCaptureChanged(boolean hasCapture) {
    }

    /**
     * Returns the current state of the permissions needed.
     */
    private boolean checkPermissions() {
        return PackageManager.PERMISSION_GRANTED == ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION);
    }

    private void requestPermissions() {
        boolean shouldProvideRationale = ActivityCompat.shouldShowRequestPermissionRationale(this, Manifest.permission.ACCESS_FINE_LOCATION);

        // Provide an additional rationale to the user. This would happen if the user denied the
        // request previously, but didn't check the "Don't ask again" checkbox.
        if (shouldProvideRationale) {
            Log.i(TAG, "Displaying permission rationale to provide additional context.");
            Snackbar.make(
                            findViewById(R.id.activity_main),
                            R.string.permission_rationale,
                            Snackbar.LENGTH_INDEFINITE)
                    .setAction(R.string.valid, new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            // Request permission
                            ActivityCompat.requestPermissions(MainActivity.this,
                                    new String[]{Manifest.permission.ACCESS_FINE_LOCATION},
                                    REQUEST_PERMISSIONS_REQUEST_CODE);
                        }
                    })
                    .show();
        } else {
            Log.i(TAG, "Requesting permission");
            // Request permission. It's possible this can be auto answered if device policy
            // sets the permission in a given state or the user denied the permission
            // previously and checked "Never ask again".
            ActivityCompat.requestPermissions(MainActivity.this,
                    new String[]{Manifest.permission.ACCESS_FINE_LOCATION},
                    REQUEST_PERMISSIONS_REQUEST_CODE);
        }
    }

    /**
     * Callback received when a permissions request has been completed.
     */
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        Log.i(TAG, "onRequestPermissionResult");
        if (requestCode == REQUEST_PERMISSIONS_REQUEST_CODE) {
            if (grantResults.length <= 0) {
                // If user interaction was interrupted, the permission request is cancelled and you
                // receive empty arrays.
                Log.i(TAG, "User interaction was cancelled.");
            } else if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                // Permission was granted.
                //mService.requestLocationUpdates();
            } else {
                // Permission denied.
                Snackbar.make(
                                findViewById(R.id.activity_main),
                                R.string.permission_denied_explanation,
                                Snackbar.LENGTH_INDEFINITE)
                        .setAction(R.string.settings, new View.OnClickListener() {
                            @Override
                            public void onClick(View view) {
                                // Build intent that displays the App settings screen.
                                Intent intent = new Intent();
                                intent.setAction(
                                        Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                                Uri uri = Uri.fromParts("package",
                                        getApplicationContext().getPackageName(), null);
                                intent.setData(uri);
                                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                startActivity(intent);
                            }
                        })
                        .show();
            }
        }
    }


    public void setActionBarTitle(String title) {

        Objects.requireNonNull(getSupportActionBar()).setTitle(title);


        //  ActionBar bar = getSupportActionBar();
        // if(bar!=null){
        //    TextView tv = new TextView(getApplicationContext());
            /*RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(
                    LayoutParams.MATCH_PARENT, // Width of TextView
                    LayoutParams.WRAP_CONTENT);*/ // Height of TextView
        //   tv.setLayoutParams(lp);
        //   tv.setText(bar.getTitle());
        //  tv.setText(title);
        //  tv.setGravity(Gravity.CENTER);
        //  tv.setTextColor(Color.WHITE);
        //   tv.setTextSize(TypedValue.COMPLEX_UNIT_SP, 15);
        // bar.setDisplayOptions(ActionBar.DISPLAY_SHOW_CUSTOM);
        //    bar.setCustomView(tv);
        //  }
    }




    public boolean allowSync(int integer) {
        return PrefUtils.isAutoSync() && NetworkUtils.isConnected() && integer!=0 && ObjectUtils.isNotEmpty(PrefUtils.getServerIPAddress()) &&
                !PrefUtils.getServerIPAddress().equalsIgnoreCase(DEFAULT_VALUE);
    }
    private void observeForNewObject() {
        App.database.depenceCaisseDAO().getNoSyncCountMutable().observeForever(integer -> {
            //    syncDataAuto();
            if (allowSync(integer)) {
                try {
                    DataSyncHelper.getInstance(context).syncExpenses(null);
                } catch (Exception e) {
                    Log.d("base ac", e.getMessage());
                }
            }
        });

        App.database.depenceTypeDAO().getNoSyncCountMutable().observeForever(integer -> {
            //    syncDataAuto();
            Log.d("dsffdsd","integer " + integer);

            if (allowSync(integer)) {
                try {
                    DataSyncHelper.getInstance(context).syncExpensesType(null);
                } catch (Exception e) {
                    Log.d("base ac", e.getMessage());
                }
            }
        });


        App.database.ticketDAO().getNoSyncCountMutable().observeForever(new Observer<Integer>() {
            private boolean isSyncTriggered = false; // Flag to ensure sync is triggered only once
            private final Handler handler = new Handler(Looper.getMainLooper()); // Handler to manage the timer
            private final long resetDelay = 5000; // Delay in milliseconds (e.g., 60000ms = 1 minute)

            @Override
            public void onChanged(Integer integer) {
                if (allowSync(integer) && !isSyncTriggered) {
                    try {
                        DataSyncHelper.getInstance(context).syncTickets(null, "2");
                        isSyncTriggered = true; // Set the flag to true after sync is triggered

                        // Schedule the reset of isSyncTriggered after the specified delay
                        handler.postDelayed(() -> {
                            isSyncTriggered = false;
                        }, resetDelay);
                    } catch (Exception e) {
                     //   Log.d("kklnjbhvg", e.getMessage());
                    }
                }
            }
        });
        App.database.bonCommandeDAO().getNoSyncCountMubtale().observeForever(integer -> {
            Log.d("base ac","dd "+ String.valueOf(integer));
            //   syncDataAuto();
            if (allowSync(integer)) {
                Log.d("base ac","cc "+ String.valueOf(integer));
                try {
                    DataSyncHelper.getInstance(context).syncBonCommandes();
                } catch (Exception e) {
                    Log.d("base ac", e.getMessage());
                }
            }
        });
        App.database.bonRetourDAO().getNoSyncCountMutable().observeForever(integer -> {
            //  syncDataAuto();
            if (allowSync(integer)) {
                try {
                    DataSyncHelper.getInstance(context).syncBonRetour();
                } catch (Exception e) {
                    Log.d("base ac", e.getMessage());
                }
            }
        });
        App.database.clientDAO().getNoSyncCountMutable().observeForever(integer -> {
            //   syncDataAuto();
            if (allowSync(integer)) {
                try {
                    DataSyncHelper.getInstance(context).syncClients();
                } catch (Exception e) {
                    Log.d("base ac", e.getMessage());
                }
            }

        });
        App.database.clientDAO().getNoSyncToUpdateCountMutable().observeForever(integer -> {
            //  syncDataAuto();
            if (allowSync(integer)) {
                try {
                    DataSyncHelper.getInstance(context).updateClient();
                } catch (Exception e) {
                    Log.d("base ac", e.getMessage());
                }
            }

        });

        App.database.ligneOrdreMissionDAO().getNoSyncCountMutable().observeForever(integer -> {
            //    syncDataAuto();

            if (allowSync(integer)) {
                try {
                    DataSyncHelper.getInstance(context).syncLigneOrdreMission();
                } catch (Exception e) {
                    Log.d("base ac", e.getMessage());
                }
            }

        });

        ReglementCaisseViewModel.getInstance(this).getNoSyncCount().observeForever( integer ->{
            if (allowSync(integer)) {
                try {

                    /**
                     *
                     *this delay because when pass reg partiel and  sync payment and ticket the same time
                     * sync payment dont sync !!
                     */
                    Handler handler = new Handler();
                    handler.postDelayed(() -> {
                        DataSyncHelper.getInstance(context).syncPayments();

                    }, 10000);

                } catch (Exception e) {
                    Log.d("base ac", e.getMessage());
                }
            }
        });
        App.database.vcNewProductDAO().getCountNoSyncedToDeleteMubtale().observeForever(integer -> {
            if (allowSync(integer)) {
                try {
                    DataSyncHelper.getInstance(context).syncVCNewProductToDelete();
                } catch (Exception e) {
                    Log.d("base ac", e.getMessage());
                }
            }

            //    syncDataAuto();
        });
        App.database.vcNewProductDAO().getNoSyncCountMubtale().observeForever(integer -> {
            if (allowSync(integer)) {
                try {
                    DataSyncHelper.getInstance(context).syncVCNewProduct();
                } catch (Exception e) {
                    Log.d("base ac", e.getMessage());
                }
            }

            //    syncDataAuto();
        });
        App.database.vcPromosDAO().getCountNoSyncedToDeleteMubtale().observeForever(integer -> {
            if (allowSync(integer)) {
                try {
                    DataSyncHelper.getInstance(context).syncVCPromosToDelete();
                } catch (Exception e) {
                    Log.d("base ac", e.getMessage());
                }
            }
            //    syncDataAuto();
        });
        App.database.vcPromosDAO().getNoSyncCountMubtale().observeForever(integer -> {
            if (allowSync(integer)) {
                try {
                    DataSyncHelper.getInstance(context).syncVCPromos();
                } catch (Exception e) {
                    Log.d("base ac", e.getMessage());
                }
            }
            //    syncDataAuto();
        });
        App.database.vcPricesDAO().getCountNoSyncedToDeleteMubtale().observeForever(integer -> {
            if (allowSync(integer)) {
                try {
                    DataSyncHelper.getInstance(context).syncVCPrixToDelete();
                } catch (Exception e) {
                    Log.d("base ac", e.getMessage());
                }
            }
            //  syncDataAuto();
        });
        App.database.vcPricesDAO().getNoSyncCountMubtale().observeForever(integer -> {
            if (allowSync(integer)) {
                try {
                    DataSyncHelper.getInstance(context).syncVCPrix();
                } catch (Exception e) {
                    Log.d("base ac", e.getMessage());
                }
            }
            //  syncDataAuto();
        });
        App.database.vcAutreDAO().getCountNoSyncedToDeleteMubtale().observeForever(integer -> {
            if (allowSync(integer)) {
                try {
                    DataSyncHelper.getInstance(context).syncVcAutreToDelete();
                } catch (Exception e) {
                    Log.d("base ac", e.getMessage());
                }
            }
            //   syncDataAuto();
        });
        App.database.vcAutreDAO().getNoSyncCountMubtale().observeForever(integer -> {
            if (allowSync(integer)) {
                try {
                    DataSyncHelper.getInstance(context).syncVcAutre();
                } catch (Exception e) {
                    Log.d("base ac", e.getMessage());
                }
            }
            //   syncDataAuto();
        });
        App.database.vcImageDAO().getNoSyncCountMubtale().observeForever(integer -> {
            if (allowSync(integer)) {
                try {
                    DataSyncHelper.getInstance(context).uploadImages();
                } catch (Exception e) {
                    Log.d("base ac", e.getMessage());
                }
            }
            //   syncDataAuto();
        });

        App.database.dnVisitesDAO().getNoSyncCountMubtale().observeForever(integer -> {
            if (allowSync(integer)) {
                try {
                    Log.d("kkkdds",integer+"cc");
                    DataSyncHelper.getInstance(context).getNonSyncDNVisites();

                } catch (Exception e) {
                    Log.d("base ac", e.getMessage());
                }
            }
            //   syncDataAuto();
        });
        App.database.dnVisitesDAO().getNoSynctoDeleteCountMubtale().observeForever(integer -> {
            if (allowSync(integer)) {
                try {
                    DataSyncHelper.getInstance(context).getNonSyncDNVisitesTodelete();
                } catch (Exception e) {
                    Log.d("base ac", e.getMessage());
                }
            }
            //   syncDataAuto();
        });


       /* App.database.dnVisitesDAO().getNoSyncCountMubtale().observeForever(integer -> {
            DataSyncHelper.getInstance(context).getNonSyncDNVisites();
            //   syncDataAuto();
        });*/
    }

}
