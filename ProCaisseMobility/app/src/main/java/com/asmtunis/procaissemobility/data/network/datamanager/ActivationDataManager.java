package com.asmtunis.procaissemobility.data.network.datamanager;

import static com.asmtunis.procaissemobility.helper.Globals.CHECK_LICENCE_BASEURL;
import static com.asmtunis.procaissemobility.helper.Globals.CHECK_LICENCE_URL;

import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.ActivationService;

import license.model.Activation;
import license.model.Licence;

//import com.blankj.utilcode.util.AppUtils;

/**
 * Created by PC on 11/13/2017.
 */

public class ActivationDataManager {

    private static ActivationDataManager sInstance;

    private final ActivationService mActivationService;

    public ActivationDataManager() {
        if(CHECK_LICENCE_URL.equals(""))
        mActivationService = new ServiceFactory<>(ActivationService.class,  "http://as.asmhost.net/licence/public/api/").makeService();
       else mActivationService = new ServiceFactory<>(ActivationService.class, CHECK_LICENCE_BASEURL).makeService();
    }


    public static ActivationDataManager getInstance( ) {
        if (sInstance == null) {
            sInstance = new ActivationDataManager();
        }
        return sInstance;
    }


    public void checkActivation(Activation activation,
                                RemoteCallback<Licence> listener) {
        if(CHECK_LICENCE_URL.equals(""))
        mActivationService.CheckActivation(activation.getIdDevice(), "ProCaisse Mobility").enqueue(listener);
   else mActivationService.CheckActivationBackUp(CHECK_LICENCE_URL,activation.getIdDevice(), "ProCaisse Mobility").enqueue(listener);
    }


}
