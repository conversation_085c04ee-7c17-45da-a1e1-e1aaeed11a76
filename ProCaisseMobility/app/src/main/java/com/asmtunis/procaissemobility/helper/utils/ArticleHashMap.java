package com.asmtunis.procaissemobility.helper.utils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Article;
import com.asmtunis.procaissemobility.data.models.LigneBonCommande;
import com.asmtunis.procaissemobility.data.models.LigneTicket;
import com.asmtunis.procaissemobility.data.models.Ticket;
import com.asmtunis.procaissemobility.helper.Globals;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Created by me on 10/15/2017.
 */

public class ArticleHashMap extends LinkedHashMap<Article, LigneTicket> {


    @Override
    public LigneTicket put(Article key, LigneTicket value) {
        LigneTicket result = super.put(key, value);
        this.order();
        return result;
    }

    public LigneTicket put(Article key, double quantity, double discount, boolean isOrder) {
        LigneTicket value = new LigneTicket();
        value.setlTCommande(isOrder);
        value.setlTAnnuler(Globals.Boolean.FALSE.getValue());
        value.setlTQte(quantity);
        value.setlTQtePiece(key.getuNITEARTICLEQtePiece());
        value.setlTTauxRemise(discount);
        value.setlTTVA(key.getaRTTVA());
        value.setlTUnite(key.getuNITEARTICLECodeUnite());
        value.setlTPrixEncaisse(Calculator.calculateAmountTTCNet(key.getPvttc(), discount));
        value.setlTPrixVente(key.getPvttc());
        value.setlTPACHAT(key.getaRTPrixUnitaireHT());
        value.setlTMtTTC(Calculator.calculateAmountTTC(
                value.getlTPrixEncaisse(),
                quantity));
        value.setlTMtHT(Calculator.calculateAmountHT(value.getlTMtTTC(), key.getaRTTVA()));
        value.setlTRemise(Calculator.calculateAmountExcludingTax(value.getlTPrixVente(), value.getlTQte(), value.getlTTVA()) -
                Calculator.calculateAmountExcludingTax(value.getlTPrixEncaisse(), value.getlTQte(), value.getlTTVA()));
        value.setlTCodArt(key.getaRTCode());
        value.setArticle(key);
        LigneTicket result = super.put(key, value);
        if (isOrder) {
            order();
        }
        return result;
    }

    public LigneTicket put(Ticket ticket,Article key, LigneBonCommande lb, double quantity, double discount, boolean isOrder) {
        LigneTicket value = new LigneTicket();
        value.setlTCommande(isOrder);
        value.setlTAnnuler(Globals.Boolean.FALSE.getValue());
        value.setlTQte(quantity);
        value.setlTQtePiece(key.getuNITEARTICLEQtePiece());
        value.setlTTauxRemise(discount);
        value.setlTTVA(key.getaRTTVA());
        value.setlTUnite(key.getuNITEARTICLECodeUnite());
        //value.setlTPrixEncaisse(Calculator.calculateAmountTTCNet(key.getPvttc(), discount));
        value.setlTPrixEncaisse(Calculator.calculateAmountTTCNet(Double.parseDouble(lb.getLGDEVPUTTC()), discount));
       // value.setlTPrixVente(key.getPvttc());
        value.setlTPrixVente(Double.parseDouble(lb.getLGDEVPUTTC()));
       // value.setlTPACHAT(key.getaRTPrixUnitaireHT());
        value.setlTPACHAT(Double.parseDouble(lb.getLGDEVPUHT()));
        value.setlTMtTTC(Calculator.calculateAmountTTC(
                value.getlTPrixEncaisse(),
                quantity));
        value.setlTMtHT(Calculator.calculateAmountHT(value.getlTMtTTC(), key.getaRTTVA()));
        value.setlTRemise(Calculator.calculateAmountExcludingTax(value.getlTPrixVente(), value.getlTQte(), value.getlTTVA()) -
                Calculator.calculateAmountExcludingTax(value.getlTPrixEncaisse(), value.getlTQte(), value.getlTTVA()));
        value.setlTCodArt(key.getaRTCode());
        value.setArticle(key);

        value.setlTCodArt(key.getaRTCode());
        //value.setCodeBarFils(lb.getLGDEVCodeArt());
       // value.setlTNumTicketM(lb.getLGDEVNumBon());
        value.setlTNumTicket(ticket.tIKNumTicket);
       // value.setLtTarif(String.valueOf(key.aRTPrixUnitaireHT));
        value.setLtTarif(String.valueOf(lb.getLGDEVPUHT()));
        value.setlTCodArt(lb.getLGDEVCodeArt());
        value.setlTIdCarnet(ticket.tIKIdCarnet);
        value.setlTExerc(lb.getLGDEVExerc());
        LigneTicket result = super.put(key, value);
        if (isOrder) {
            order();
        }
        return result;
    }



    @Override
    public void putAll(@NonNull Map<? extends Article, ? extends LigneTicket> m) {
        super.putAll(m);
        this.order();
    }

    @Override
    public boolean remove(@Nullable Object key, @Nullable Object value) {
        boolean result = super.remove(key, value);
        this.order();
        return result;
    }

    @Nullable
    @Override
    public LigneTicket remove(@Nullable Object key) {
        LigneTicket result = super.remove(key);
        this.order();
        return result;
    }

    void order() {
        int i = 1;
        for (Entry<Article, LigneTicket> entry : this.entrySet()) {
            entry.getValue().lTNumOrdre = i;
            i++;
        }
    }

    @Override
    public LigneTicket get(Object key) {
        return super.get(key);
    }

    public ArrayList<LigneTicket> getLigneTickets(Ticket ticket) {
        for (Article article : this.keySet()) {
            LigneTicket ligneTicket = this.get(article);
            assert ligneTicket != null;
            ligneTicket.setlTIdCarnet(ticket.gettIKIdCarnet());
            ligneTicket.setlTNumTicket(ticket.gettIKNumTicket());
            ligneTicket.setLtTarif(String.valueOf(article.aRTPrixUnitaireHT));
            ligneTicket.setlTExerc(ticket.gettIKExerc());
            ligneTicket.setlTNumOrdre(new ArrayList<>(this.keySet()).indexOf(article) + 1);
            ligneTicket.setArticle(article);
            ligneTicket.setSync(ticket.isSync());
            ligneTicket.setStatus(ticket.getStatus());
            ligneTicket.codeBarFils = article.getaRTCodeBar();
            ligneTicket.lTNumTicketM = Utils.generateMobileLigneTicketCode(ticket.tIKStation, String.valueOf(ticket.tIKNumTicket),String.valueOf(ligneTicket.lTNumOrdre));
            this.put(article, ligneTicket);
        }

        return new ArrayList<>(this.values());
    }


    public ArrayList<LigneTicket> getLigneTickets() {
        for (Article article : this.keySet()) {
            LigneTicket ligneTicket = this.get(article);
            ligneTicket.setSync(false);
            ligneTicket.setStatus(Globals.ITEM_STATUS.INSERTED.getStatus());
            //   ligneTicket.setlTNumOrdre(new ArrayList<>(this.keySet()).indexOf(article));
            ligneTicket.setArticle(article);
            this.put(article, ligneTicket);
        }
        return new ArrayList<>(this.values());
    }

    public double getAmount() {
        try {
            return Calculator.calculateTicketAmount(getLigneTickets(), R.id.calculateAmountTTC);
        } catch (Exception e) {
            return 0;
        }
    }

    public double getAmountWithDiscount() {
        return Calculator.calculateTicketAmount(getLigneTickets(), R.id.calculateAmountTTCNet);
    }

    public double getAmountHT() {
        return Calculator.calculateTicketAmount(getLigneTickets(), R.id.calculateAmountHT);
    }

    public double getVATAmount() {
        return Calculator.calculateTicketAmount(getLigneTickets(), R.id.calculateAmountVAT);
    }

    public ArticleHashMap setDiscount(String discount) {
        for (LigneTicket ligneTicket : getLigneTickets()) {
            this.put(ligneTicket.getArticle(), ligneTicket.getlTQte(), StringUtils.decimalFormat(StringUtils.parseDouble(discount, ligneTicket.getlTTauxRemise())), ligneTicket.getlTCommande());
        }
        return this;
    }


}
