package com.asmtunis.procaissemobility.ui.activities;

import static com.asmtunis.procaissemobility.App.prefUtils;
import static com.asmtunis.procaissemobility.helper.AppHelper.isPOINTMOBILE;
import static com.asmtunis.procaissemobility.helper.Globals.CLIENT_INTENT_ID_KEY;
import static com.asmtunis.procaissemobility.helper.Globals.DEFAULT_ENCODING;
import static com.asmtunis.procaissemobility.helper.Globals.TICKET_WITH_LINES_KEY;
import static com.asmtunis.procaissemobility.helper.utils.UIUtils.getIconicsDrawable;
import static com.asmtunis.procaissemobility.helper.utils.UIUtils.setDefaultAutoScanColor;
import static com.asmtunis.procaissemobility.helper.utils.UIUtils.switchAutoScanColor;
import static com.asmtunis.procaissemobility.helper.utils.Utils.indexOfClient;
import static com.honeywell.aidc.BarcodeReader.PROPERTY_CODE_128_ENABLED;
import static com.honeywell.aidc.BarcodeReader.PROPERTY_EAN_13_CHECK_DIGIT_TRANSMIT_ENABLED;
import static com.honeywell.aidc.BarcodeReader.PROPERTY_EAN_13_ENABLED;

import static java.lang.Math.abs;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.Gravity;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.appcompat.widget.Toolbar;
import androidx.core.content.res.ResourcesCompat;
import androidx.fragment.app.FragmentActivity;

import com.afollestad.materialdialogs.MaterialDialog;
import com.asmtunis.procaissemobility.BuildConfig;
import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.adapters.spinners.ClientSpinnerAdapter;
import com.asmtunis.procaissemobility.adapters.tables.LigneTicketTableDataAdapter;
import com.asmtunis.procaissemobility.data.models.Article;
import com.asmtunis.procaissemobility.data.models.BonRetour;
import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.data.models.LigneBonRetour;
import com.asmtunis.procaissemobility.data.models.LigneTicket;
import com.asmtunis.procaissemobility.data.models.StationStock;
import com.asmtunis.procaissemobility.data.models.Ticket;
import com.asmtunis.procaissemobility.data.models.TicketWithLines;
import com.asmtunis.procaissemobility.data.models.TicketWithLinesAndPayments;
import com.asmtunis.procaissemobility.data.viewModels.ArticleViewModel;
import com.asmtunis.procaissemobility.data.viewModels.ClientViewModel;
import com.asmtunis.procaissemobility.helper.BluetoothService;
import com.asmtunis.procaissemobility.helper.GPSTracker;
import com.asmtunis.procaissemobility.helper.Globals;
import com.asmtunis.procaissemobility.helper.PrinterHelper;
import com.asmtunis.procaissemobility.helper.utils.DateUtils;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.helper.utils.UIUtils;
import com.asmtunis.procaissemobility.helper.utils.Utils;
import com.asmtunis.procaissemobility.helper.wifi_print.Comman;
import com.asmtunis.procaissemobility.helper.wifi_print.WifiPrint;
import com.asmtunis.procaissemobility.listener.DialogClickInterface;
import com.asmtunis.procaissemobility.ui.components.SortableLigneTicketTableView;
import com.asmtunis.procaissemobility.ui.dialogs.ArticleDialog;
import com.asmtunis.procaissemobility.ui.dialogs.ArticleListDialog;
import com.asmtunis.procaissemobility.ui.dialogs.BarCodeDialogueFragment;
import com.asmtunis.procaissemobility.ui.dialogs.BarcodeScannerListener;
import com.asmtunis.procaissemobility.ui.dialogs.LigneTicketDialog;
import com.blankj.utilcode.util.ObjectUtils;
import com.example.barecodereader.barcode.BarCodeReaderManager;
import com.example.barecodereader.barcode.listener.BarcodeListener;
import com.example.barecodereader.barcode.readers.EdaReader;
import com.example.barecodereader.barcode.readers.PM80Reader;
import com.google.android.material.snackbar.Snackbar;
import com.mikepenz.fontawesome_typeface_library.FontAwesome;
import com.mikepenz.google_material_typeface_library.GoogleMaterial;
import com.mikepenz.iconics.IconicsDrawable;
import com.mobsandgeeks.saripaar.ValidationError;
import com.mobsandgeeks.saripaar.Validator;
import com.rengwuxian.materialedittext.MaterialEditText;
import com.transitionseverywhere.Slide;
import com.transitionseverywhere.TransitionManager;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;
import device.common.DecodeResult;
import device.common.ScanConst;
import device.sdk.ScanManager;
import es.dmoral.toasty.Toasty;
import searchablespinner.interfaces.OnItemSelectedListener;
import timber.log.Timber;

public class BonRetourActivity extends BaseActivity implements DialogClickInterface {
    private static final int REQUEST_CLIENT_CODE = 101;
    public static final int REQUEST_PAYEMENT_CODE = 111;
    public static final int REQUEST_CONNECT_DEVICE = 845;
    public static final int REQUEST_ENABLE_BT = 875;
    int index_prod_intable = 0;

    double amountToSet = 0.0;
    public static BluetoothDevice con_dev = null;
    public BluetoothService mService = null;
    static ArticleDialog articleDialog;
    private static final String TAG = "Ticket activity";
    @BindView(R.id.DateInputField)
    TextView dateInputField;
    static Validator.ValidationListener validationListener;

    static EditText amountInputField;

    static EditText amountwithDiscountInputField;


    static EditText discountInputField;

    @BindView(R.id.addItemButton)
    com.mikepenz.iconics.view.IconicsButton addItemButton;


    static SortableLigneTicketTableView tableView;

    @BindView(R.id.SearchableClientSpinner)
    searchablespinner.SearchableSpinner searchableSpinner;
     View footerTicketView;



    View dataTableView;
    TextView tableTitle;

    @BindView(R.id.buttons_layout)
    LinearLayoutCompat linearLayoutCompat;

    @BindView(R.id.headerTicketView)
    View headerTicketView;

    @BindView(R.id.societe)
    TextView text;

    @BindView(R.id.linear)
    LinearLayout linear;


    @BindView(R.id.dividerView1)
    View dividerView1;
    @BindView(R.id.dividerView2)
    View dividerView2;

    @BindView(R.id.discountLayout)
    RelativeLayout discountLayout;


    @BindView(R.id.AmountwithDiscountLayout)
    RelativeLayout AmountwithDiscountLayout;

    @BindView(R.id.addClientButton)
    com.mikepenz.iconics.view.IconicsButton addClientButton;

    @BindView(R.id.autoscan)
    com.mikepenz.iconics.view.IconicsButton autoScanButton;

    @BindView(R.id.scan)
    com.mikepenz.iconics.view.IconicsButton scanArt;

    MenuItem save, scan;

    static ViewGroup content;

    ClientSpinnerAdapter simpleListAdapter;

    Client client;

    static boolean dialogShown = false;
    static LigneTicketDialog ligneTicketDialog;

    Bundle savedInstanceState;
    static ArticleListDialog articleListDialog;
    static LigneTicketTableDataAdapter tableDataAdapter = null;
    ArrayList<LigneTicket> selectedArticles;

    static double amount;

    static double amountWithDiscount;

    double made;

    static double discount;

    double received;

    Date date;

    int ticketNumber;

    @BindView(R.id.toolbar)
    Toolbar toolbar;

    static FragmentActivity context;
    Intent intent;
    Unbinder unbinder;
    GPSTracker gPSTracker;
    double entreLongitude, entreAltitude;
    private int mBackupResultType = ScanConst.ResultType.DCD_RESULT_COPYPASTE;
    static boolean isShowing = false;
    static boolean isInventory = false;
    protected static ScanManager mScanner = null;
    protected static DecodeResult mDecodeResult;
    BarCodeReaderManager barCodeReaderManager;
    BonRetour bonRetour;
    Ticket ticket;
    TicketWithLines ticketWithLines;
    List<LigneBonRetour> ligneBonRetours;
    private Boolean isUpdate;
    private String bonRtNum;
    String ddm = "";

    @SuppressLint("StringFormatMatches")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        unbinder = ButterKnife.bind(this);
        initComposants();
        autoScanButton.setDrawableBottom(getIconicsDrawable(GoogleMaterial.Icon.gmd_wb_auto, R.color.material_drawer_accent, this));
        setDefaultAutoScanColor(autoScanButton, this);
        scanArt.setDrawableBottom(getIconicsDrawable(GoogleMaterial.Icon.gmd_flip, R.color.material_drawer_accent, this));
        isUpdate = getIntent().getBooleanExtra(Globals.UPDATE_TICKET, false);
        bonRetour = (BonRetour) getIntent().getSerializableExtra(Globals.TICKET_TO_UPDATE);
        /**
         * BOR_NumeroM in BonRetour class dont exist in remote db
         */
       // if (isUpdate) ddm = bonRetour.borNUMM;
        this.savedInstanceState = savedInstanceState;
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setDisplayShowCustomEnabled(true);
        getSupportActionBar().setDisplayUseLogoEnabled(false);
        context = this;
        mScanner = new ScanManager();
        mDecodeResult = new DecodeResult();
        tableDataAdapter = null;

        gPSTracker = new GPSTracker(context);
        entreLongitude = gPSTracker.getLongitude();
        entreAltitude = gPSTracker.getLatitude();
        discountLayout.setVisibility(View.GONE);
        dividerView1.setVisibility(View.GONE);
        dividerView2.setVisibility(View.GONE);
        AmountwithDiscountLayout.setVisibility(View.GONE);

        if ((gPSTracker.getLatitude() > 0) && (gPSTracker.getLongitude() > 0)) {
            getSupportActionBar().setSubtitle(String.format(context.getResources().getString(R.string.lat_log), gPSTracker.getLatitude(), gPSTracker.getLongitude()));
        }
        startBarcode();
     /*   barCodeReader = new BarCodeReaderManager();

        barCodeReader.addReader(new EdaReader(this, new BarcodeListener() {
            @Override
            public void onSuccess(String event) {
                showArticleFromScan(event);
            }

            @Override
            public void onFail(String event) {
                Toasty.info(context, R.string.error_read_codebare).show();
            }
        }, null)).addReader(new PM80Reader(new BarcodeListener() {
            @Override
            public void onSuccess(String event) {
                showArticleFromScan(event);
            }

            @Override
            public void onFail(String event) {
                Toasty.info(context, R.string.error_read_codebare).show();
            }
        }));
        barCodeReader.startListener();*/
        getData();
        validationListener = new Validator.ValidationListener() {
            @Override
            public void onValidationSucceeded() {
            }

            @Override
            public void onValidationFailed(List<ValidationError> errors) {
                for (ValidationError error : errors) {
                    View view = error.getView();
                    String message = error.getCollatedErrorMessage(context);
                    // Display error messages ;)
                    if (view instanceof EditText) {
                        ((MaterialEditText) view).setError(message);
                    } else {
                        Toast.makeText(context, message, Toast.LENGTH_LONG).show();
                    }
                }
            }
        };
        //chahia
        addClientButton.setVisibility(View.GONE);
        if (isUpdate) {
            bonRtNum = bonRetour.getBORNumero();
            client = App.database.clientDAO().getOneByCode(bonRetour.getBORCodefrs());
        } else {
            bonRtNum = String.valueOf(App.database.bonRetourDAO().getCount() + 1);
        }
        getSupportActionBar().setTitle(String.format(getString(R.string.br_number_field), bonRtNum));
    }
    void startBarcode() {
        barCodeReaderManager = new BarCodeReaderManager();
        Map<String, Object> properties = new HashMap();
        properties.put(PROPERTY_CODE_128_ENABLED, true);
        properties.put(PROPERTY_EAN_13_ENABLED, true);
        properties.put(PROPERTY_EAN_13_CHECK_DIGIT_TRANSMIT_ENABLED, prefUtils.getEan13CheckDigitEnabled());
        EdaReader edaReader = new EdaReader(this, new BarcodeListener() {
            @Override
            public void onSuccess(String event) {
                Toasty.info(context, "" + event).show();
                showArticleFromScan(event);
            }

            @Override
            public void onFail(String event) {
                Toasty.info(context, R.string.error_read_codebare).show();
            }
        }, properties);
        barCodeReaderManager.addReader(edaReader).addReader(new PM80Reader(new BarcodeListener() {
            @Override
            public void onSuccess(String event) {
                Toasty.info(context, "" + event).show();
                showArticleFromScan(event);
            }

            @Override
            public void onFail(String event) {
                Log.d(TAG, "event : " + event);
                Toasty.info(context, R.string.error_read_codebare).show();
            }
        }));
        barCodeReaderManager.startListener();

    }


    /**
     * load view from xml
     */
    private void initComposants() {
        amountInputField = findViewById(R.id.AmountInputField);
        amountwithDiscountInputField = findViewById(R.id.AmountwithDiscountInputField);
        discountInputField = findViewById(R.id.DiscountInputField);
        tableView = findViewById(R.id.tableView);
        dataTableView = findViewById(R.id.dataTableView);
        tableTitle = findViewById(R.id.produitTxtVw);
        footerTicketView = findViewById(R.id.footerTicketView);
        content = findViewById(android.R.id.content);
        linearLayoutCompat.setVisibility(View.GONE);
    }


    void setupView(List<Article> allArticles, List<Article> stockableArticles) {
        setArticlesListView(allArticles, stockableArticles);
        setTicketHeader();
        setTicketFooter();
    }

    /**
     * get articles list form local database
     */

        /**
         * get articles list form local database
         */
        private void getData() {
            ArticleViewModel.getInstance(this).getAllNotPat().observe(this, articles -> {
                List<Article> allArticles = articles;
                ArticleViewModel.getInstance(this).getAllNotPatStockable().observe(this, articles1 -> {
                    List<Article> articlesStockables = articles1;
                    if (allArticles.size() > 0) {
                        date = new Date();
                        setupView(allArticles, articlesStockables);
                    } else {
                        Snackbar snackbar = Snackbar.make(linearLayoutCompat, "La liste est vide ! ", Snackbar.LENGTH_INDEFINITE)
                                .setAction("quitter", v -> {

                                    finish();

                                });
                        snackbar.show();
                    }
                });
            });
        }


    /**
     * send articles list to dialog
     */
    void setArticlesListView(List<Article> allArticles, List<Article> stockableArticles) {
        articleListDialog = new ArticleListDialog(allArticles, stockableArticles, false, 2, true, false, false, (item, quantity) -> {
            amountToSet += item.getPvttc() * quantity;
            if (ObjectUtils.isNotEmpty(client.getcLIType()) && client.getcLIType().equalsIgnoreCase("Passager")) {
                ArticleListDialog.passagerBlocked = (amountToSet + amount > prefUtils.getMaxPassager()) && prefUtils.getMaxPassager() != 0;
            } else ArticleListDialog.passagerBlocked = false;
        });
    }

    /**
     * load the spinner with clients list
     */
    void setSearchableSpinner(List<Client> clients) {

        simpleListAdapter = new ClientSpinnerAdapter(this, (ArrayList<Client>) clients);
        searchableSpinner.setAdapter(simpleListAdapter);
        if (isUpdate) {
            searchableSpinner.setSelectedItem(indexOfClient((ArrayList<Client>) clients, client) + 1);
            TransitionManager.beginDelayedTransition(content, new Slide(Gravity.BOTTOM));
            dataTableView.setVisibility(View.VISIBLE);
            List<LigneBonRetour> ligneBonRetours = App.database.ligneBonRetourDAO().getByBRNum(bonRetour.getBORNumero());
            for (LigneBonRetour lr : ligneBonRetours) {
                Article article = App.database.articleDAO().getOneByCode(lr.getLIGBonEntreeCodeArt());
                articleListDialog.getList().put(article, Double.parseDouble(lr.getLIGBonEntreeQte()), Double.parseDouble(lr.getLIGBonEntreeRemise()), false);
            }
            setTicketDataTable(true);
            setFooter();
          //  text.setText(client.cLINomPren);
          //  linear.setVisibility(View.GONE);
        }
     //   else{

            searchableSpinner.setOnItemSelectedListener(new OnItemSelectedListener() {
                @Override
                public void onItemSelected(View view, int position, long id) {
                    //  Client client1 = simpleListAdapter.getItem(position);
                    client = simpleListAdapter.getItem(position);
                    if (client != null) {
                        if (ObjectUtils.isNotEmpty(client.getcLIType()) && client.getcLIType().equalsIgnoreCase("Passager")) {
                            if ((amount > prefUtils.getMaxPassager()) && prefUtils.getMaxPassager() != 0) {
                                searchableSpinner.setSelectedItem(client);
                                Toast.makeText(context, "Impossible d'effectuer l'opération", Toast.LENGTH_LONG).show();
                            } else {
                                TransitionManager.beginDelayedTransition(content, new Slide(Gravity.BOTTOM));
                                dataTableView.setVisibility(position > 0 ? View.VISIBLE : View.GONE);
                                client = simpleListAdapter.getItem(position);

                            }
                        } else {
                            TransitionManager.beginDelayedTransition(content, new Slide(Gravity.BOTTOM));
                            dataTableView.setVisibility(position > 0 ? View.VISIBLE : View.GONE);
                            client = simpleListAdapter.getItem(position);
                        }
                    }else searchableSpinner.setSelectedItem(0);

                }

                @Override
                public void onNothingSelected() {

                }
            });
    //    }

    }


    void showArticlesListDialog() {
        articleListDialog.showConfirmDialog(this, 0, savedInstanceState);
        if (tableDataAdapter != null && !tableDataAdapter.getData().isEmpty()) {
            for (LigneTicket ligneTicket : tableDataAdapter.getData()) {
                updateListArticleQuantity(ligneTicket.getArticle(), ligneTicket.lTQte);
            }
        }
    }

    private void updateListArticleQuantity(Article article, double qty) {
        for (Article article1 : articleListDialog.articles)
            if (article.aRTCode.equals(article1.aRTCode))
                article1.setCount(qty);
        if (!articleListDialog.getList().isEmpty())
            for (Article article1 : articleListDialog.getList().keySet())
                if (article.aRTCode.equals(article1.aRTCode))
                    article1.setCount(qty);
        if (!articleListDialog.getCurrenSelection().isEmpty())
            for (Article article1 : articleListDialog.getCurrenSelection().keySet())
                if (article.aRTCode.equals(article1.aRTCode))
                    article1.setCount(qty);
    }

    void setTicketHeader() {
        dateInputField.setText(DateUtils.dateToStr(date, "EEEE, dd MMMM yyyy HH:mm"));
        ClientViewModel.getInstance(this).getByStation(prefUtils.getFiltreCltAuthorization(), prefUtils.getUserStationId()).observe(this, clients -> {
            if (clients != null)
                setSearchableSpinner(clients);
        });
    }

    void setTicketFooter() {

        discountInputField.addTextChangedListener(new TextWatcher() {

            @Override
            public void onTextChanged(CharSequence cs, int arg1, int arg2, int arg3) {

            }

            @Override
            public void beforeTextChanged(CharSequence s, int arg1, int arg2, int arg3) {

            }

            @Override
            public void afterTextChanged(Editable arg0) {
                amountWithDiscount = articleListDialog.setList(articleListDialog.getList().setDiscount(discountInputField.getText().toString())).getAmountWithDiscount();
                setTicketDataTable(true);
                setFooter();
            }

        });


    }

    /**
     * load table adapter with tickets
     */
    void setTicketDataTable(boolean changeFromGlobal) {

        tableView.setSwipeToRefreshEnabled(false);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            tableView.setElevation(10);
        }
        tableView.setHeaderBackground(R.color.material_teal500);
        if (changeFromGlobal) {
            amount = articleListDialog.setList(articleListDialog.getList().setDiscount(discountInputField.getText().toString())).getAmount();
            amountWithDiscount = articleListDialog.setList(articleListDialog.getList().setDiscount(discountInputField.getText().toString())).getAmountWithDiscount();
        } else {
            amount = articleListDialog.getList().getAmount();
            amountWithDiscount = articleListDialog.getList().getAmountWithDiscount();
        }
        tableDataAdapter = new LigneTicketTableDataAdapter(context, articleListDialog.getList().getLigneTickets(), tableView);
        tableDataAdapter.setNotifyOnChange(true);
        if(tableTitle!=null)
        tableTitle.setText(context.getString(R.string.product_title ,String.valueOf(tableDataAdapter.getData().size())));


        tableView.setDataAdapter(tableDataAdapter);

        tableView.addDataLongClickListener((rowIndex, clickedData) -> {
            if (!dialogShown) {
                dialogShown = true;
                new MaterialDialog.Builder(context)
                        .title(R.string.confirmation)
                        .content(R.string.delete_confirmation_msg)
                        .positiveText(R.string.yes)
                        .negativeText(R.string.no)
                        .onPositive((dialog, which) -> {
                                    try {
                                        if (tableDataAdapter.getData() != null) {
                                            tableDataAdapter.getData().get(rowIndex).getArticle().setCount(0);
                                            articleListDialog.getList().remove(tableDataAdapter.getData().get(rowIndex).getArticle());
                                            tableDataAdapter.getData().remove(rowIndex);
                                            tableDataAdapter.notifyDataSetChanged();
                                            amount = articleListDialog.setList(articleListDialog.getList().setDiscount(discountInputField.getText().toString())).getAmount();
                                            amountWithDiscount = articleListDialog.setList(articleListDialog.getList().setDiscount(discountInputField.getText().toString())).getAmountWithDiscount();
                                            setFooter();
                                            TransitionManager.beginDelayedTransition(content, new Slide(Gravity.BOTTOM));
                                            footerTicketView.setVisibility(!tableDataAdapter.getData().isEmpty() ? View.VISIBLE : View.GONE);
                                        }
                                    } catch (NullPointerException ignored) {
                                    }
                                    dialogShown = false;
                                }

                        ).onNegative((dialog, which) -> dialogShown = false)
                        .show();
            }
            return false;
        });

        TransitionManager.beginDelayedTransition(content, new Slide(Gravity.BOTTOM));
        footerTicketView.setVisibility(!tableDataAdapter.getData().isEmpty() ? View.VISIBLE : View.GONE);
        tableView.addDataClickListener((rowIndex, clickedData) -> {
            if (!dialogShown) {
                dialogShown = true;
                openModifyArticleDialog((LigneTicket) clickedData, false);
            }
        });
    }

    /**
     * modify ticket from table option
     */
  private void openModifyArticleDialog(LigneTicket clickedData, Boolean scanned) {
        ligneTicketDialog = new LigneTicketDialog(context, scanned, clickedData, 2, false,
                (dialog, which) -> ligneTicketDialog.validator.validate(), (dialog, which) -> {
            dialog.dismiss();
            dialogShown = false;
        }, new Validator.ValidationListener() {
            @Override
            public void onValidationSucceeded() {
                if (ligneTicketDialog.validate()) {
                    double quantity = StringUtils.parseDouble(ligneTicketDialog.getQuantityInputField().getText().toString(), 0);
                    double discount1 = StringUtils.parseDouble(ligneTicketDialog.getDiscountInputField().getText().toString(), 0);
                    double price = StringUtils.parseDouble(ligneTicketDialog.getUnitPriceInputField().getText().toString(), 0);
                    clickedData.getArticle().setPvttc(price);
                    clickedData.getArticle().setCount(quantity);
                    articleListDialog.getList().put(clickedData.getArticle(), quantity, discount1, false);
                    setTicketDataTable(false);
                    setFooter();
                    ligneTicketDialog.dismiss();
                    dialogShown = false;

                }
            }

            @Override
            public void onValidationFailed(List<ValidationError> errors) {

                for (ValidationError error : errors) {
                    View view = error.getView();
                    String message = error.getCollatedErrorMessage(context);

                    if (view instanceof EditText) {
                        ((MaterialEditText) view).setError(message);
                    } else {
                        Toast.makeText(context, message, Toast.LENGTH_LONG).show();
                    }
                }
            }
        });
        ligneTicketDialog.show(context.getFragmentManager(), StringUtils.upper);
    }

/*
    private void openModifyArticleDialog(LigneTicket clickedData, Boolean scanned) {
        if (prefUtils.getIsAutoScan() && scanned) {
            addArticleToTable("scan", clickedData, -11.1);
        } else {
            ligneTicketDialog = new LigneTicketDialog(context, scanned, clickedData, 2, false,
                    (dialog, which) -> ligneTicketDialog.validator.validate(), (dialog, which) -> {
                dialog.dismiss();
                dialogShown = false;
            }, new Validator.ValidationListener() {
                @Override
                public void onValidationSucceeded() {
                    if (ligneTicketDialog.validate()) {
                        if (!ArticleListDialog.passagerBlocked) {
                            double quantity = StringUtils.parseDouble(ligneTicketDialog.getQuantityInputField().getText().toString(), 0);
                            double discount1 = StringUtils.parseDouble(ligneTicketDialog.getDiscountInputField().getText().toString(), 0);
                            double price = StringUtils.parseDouble(ligneTicketDialog.getUnitPriceInputField().getText().toString(), 0);
                            if (tableDataAdapter != null) {
                                List<LigneTicket> ligneTickets = tableDataAdapter.getData();


                                if (ligneTickets != null) {
                                    for (int rowIndex = 0; rowIndex < ligneTickets.size(); rowIndex++) {
                                        if (ligneTickets.get(rowIndex).getArticle().getaRTCodeBar().equals(clickedData.article.getaRTCodeBar())) {

                                            if (scanned)
                                                quantity += ligneTickets.get(rowIndex).article.getCount(); // if from scan increment quantity else if from modify then replace with user input quantity

                                            articleListDialog.getList().remove(tableDataAdapter.getData().get(rowIndex).getArticle());
                                            articleListDialog.getCurrenSelection().remove(tableDataAdapter.getData().get(rowIndex).getArticle());
                                            tableDataAdapter.getData().remove(rowIndex);
                                            tableDataAdapter.notifyDataSetChanged();
                                        }
                                    }
                                }
                            }

                            clickedData.getArticle().setPvttc(price);
                            clickedData.getArticle().setCount(quantity);
                            articleListDialog.getList().put(clickedData.getArticle(), quantity, discount1, false);
                            setTicketDataTable(false);
                            setFooter();
                            ligneTicketDialog.dismiss();
                            dialogShown = false;
                        }
                    }

                }

                @Override
                public void onValidationFailed(List<ValidationError> errors) {
                    for (ValidationError error : errors) {
                        View view = error.getView();
                        String message = error.getCollatedErrorMessage(context);

                        if (view instanceof EditText) {
                            ((MaterialEditText) view).setError(message);
                        } else {
                            Toast.makeText(context, message, Toast.LENGTH_LONG).show();
                        }
                    }
                }
            }, client);

            ligneTicketDialog.show(context.getFragmentManager(), StringUtils.upper);


        }
    }


    private void addArticleToTable(String from, LigneTicket clickedData, double quant) {
        double quantity = 0.0;
        Log.d("lxolk", "cc " + String.valueOf(quant));

        if (tableDataAdapter != null) {
            List<LigneTicket> ligneTickets = tableDataAdapter.getData();
            if (ligneTickets != null) {
                for (int rowIndex = 0; rowIndex < ligneTickets.size(); rowIndex++) {
                    //  Log.d("lxolk","cc "+ String.valueOf(  isExistArticle(clickedData.getArticle(),ligneTickets)));
                    if (ligneTickets.get(rowIndex).getArticle().getaRTCodeBar().equals(clickedData.article.getaRTCodeBar())) {
                        if (from.equals("scan")) {
                            quantity = clickedData.getlTQte() + ligneTickets.get(rowIndex).article.getCount(); // if from scan increment quantity else if from modify then replace with user input quantity
                        } else if (from.equals("prodexist")) {
                            Log.d("lxolk", "ff " + String.valueOf(clickedData.getlTQte()));
                            quantity = quant + clickedData.getlTQte();
                        }
                        Log.d("lxolk", "tt " + String.valueOf(quantity));

                        articleListDialog.getList().remove(tableDataAdapter.getData().get(rowIndex).getArticle());
                        articleListDialog.getCurrenSelection().remove(tableDataAdapter.getData().get(rowIndex).getArticle());
                        tableDataAdapter.getData().remove(rowIndex);
                        tableDataAdapter.notifyDataSetChanged();
                    }
                }
            }
        }


        double discount1;
        if (Double.parseDouble(clickedData.getArticle().getTauxSolde()) != clickedData.getlTTauxRemise())
            discount1 = clickedData.getlTTauxRemise();
        else discount1 = Double.parseDouble(clickedData.getArticle().getTauxSolde());


        clickedData.getArticle().setPvttc(clickedData.getArticle().getPvttc());
        if (quantity < 1) quantity = 1.0;
        clickedData.getArticle().setCount(quantity);
        articleListDialog.getList().put(clickedData.getArticle(), quantity, discount1, false);
        setTicketDataTable(false);
        setFooter();

    }
*/
    /**
     * add new client button
     */
    @OnClick(R.id.addClientButton)
    void addClient() {
        addClientButton.setEnabled(false);
        addClientButton.setClickable(false);
        intent = new Intent(context, AddClientActivity.class);
        startActivityForResult(intent, REQUEST_CLIENT_CODE);
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {

        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case REQUEST_ENABLE_BT:
                if (resultCode == Activity.RESULT_OK) {
                    Toast.makeText(context, "Bluetooth open successful", Toast.LENGTH_LONG).show();
                } else {
                    Toast.makeText(context, "Bluetooth failed to connect", Toast.LENGTH_LONG).show();

                }
                break;
            case REQUEST_CONNECT_DEVICE:
                if (resultCode == Activity.RESULT_OK) {
                    String address = data.getExtras()
                            .getString(DeviceListActivity.EXTRA_DEVICE_ADDRESS);
                    try {
                        con_dev = mService.getDevByMac(address);

                        mService.connect(con_dev);
                    } catch (Exception e) {
                        Log.d("erro", e.getMessage());
                    }
                }
            case Activity.RESULT_CANCELED:
                finish();
                break;
        }
        if (requestCode == REQUEST_CLIENT_CODE) {
            if (resultCode == RESULT_OK) {
                final Client client1 = (Client) data.getSerializableExtra(CLIENT_INTENT_ID_KEY);
                if (client1 != null) {
                    simpleListAdapter.addItem(client1);
                    searchableSpinner.setSelectedItem(simpleListAdapter.getCount() - 1);
                }
            }
            addClientButton.setEnabled(true);
            addClientButton.setClickable(true);
        }

        if (requestCode == REQUEST_PAYEMENT_CODE) {
            if (resultCode == RESULT_OK) {
                //   TicketWithLinesAndPayments ticketWithLinesAndPayments = (TicketWithLinesAndPayments) data.getParcelableExtra(TICKET_WITH_LINES_AND_PAYEMENT_KEY);
                Toast.makeText(context, "Enregistrement a été effectué avec succès", Toast.LENGTH_LONG).show();
             /*   if (ticketWithLinesAndPayments != null) {
                    //saveData(ticketWithLinesAndPayments);
                }*/
                setResult(Activity.RESULT_OK, new Intent());
                finish();
            } else {
                save.setEnabled(true);
            }

        }
    }


    /*void saveData(TicketWithLinesAndPayments ticketWithLinesAndPayments) {
        App.database.ticketDAO().insert(ticketWithLinesAndPayments.getTicket());
        App.database.ligneTicketDAO().insertAll(ticketWithLinesAndPayments.getLigneTicket());
        App.database.reglementCaisseDAO().insert(ticketWithLinesAndPayments.getReglement());
        App.database.traiteCaisseDAO().insertAll(ticketWithLinesAndPayments.getTraites());
        App.database.chequeCaisseDAO().insertAll(ticketWithLinesAndPayments.getCheques());
    }
     */

    @OnClick(R.id.addItemButton)
    void addItems() {
        showArticlesListDialog();
    }

    @OnClick(R.id.autoscan)
    void autoScan() {
        switchAutoScanColor(autoScanButton, context);
    }

    @OnClick(R.id.scan)
    void scanArt() {
        scanArt.setEnabled(false);
        showScanner();
    }

    @Override
    protected int setContentView() {
        return R.layout.activity_ticket;
    }

    @Override
    public void onClickPositiveButton(DialogInterface pDialog) {
        articleListDialog.getList().putAll(articleListDialog.getCurrentSelection());
        selectedArticles = articleListDialog.getList().getLigneTickets();
        articleListDialog.generateView();
        setTicketDataTable(false);
        setFooter();
        pDialog.dismiss();
    }

 /*   @Override
    public void onClickPositiveButton(DialogInterface pDialog) {
        index_prod_intable = 0;
        boolean prodexist = false;
        Article articl = null;

        if (tableDataAdapter == null) {
            tableDataAdapter = new LigneTicketTableDataAdapter(context, articleListDialog.getList().getLigneTickets(), tableView);
        } else {
            tableDataAdapter.clear();
            tableDataAdapter.addAll(articleListDialog.getList().getLigneTickets());
        }

        List<LigneTicket> ligneTickets = tableDataAdapter.getData();


        for (Article article : articleListDialog.getCurrenSelection().keySet()) {
            if (isExistArticle(article, ligneTickets)) {
                articl = article;
                prodexist = true;
                break;
            }
            //  articleListDialog.getList().remove(article);
        }

        if (!prodexist) {
            articleListDialog.getList().putAll(articleListDialog.getCurrentSelection());
            articleListDialog.getCurrentSelection().clear();
            articleListDialog.generateView();
            selectedArticles = articleListDialog.getList().getLigneTickets();
            setTicketDataTable(false);
            setFooter();

        } else
            addArticleToTable("prodexist", Objects.requireNonNull(articleListDialog.getCurrenSelection().get(articl)),
                    ligneTickets.get(index_prod_intable).lTQte);


        pDialog.dismiss();
    }
    */
    @Override
    public void onClickNegativeButton(DialogInterface pDialog) {

        articleListDialog.getCurrentSelection().clear();
        articleListDialog.generateView();
        pDialog.dismiss();
    }

    private boolean isExistArticle(Article selectedArticle, List<LigneTicket> ligneTickets) {
        boolean exist = false;
        for (LigneTicket ligneTicket : ligneTickets) {
            if (ligneTicket.getArticle().getaRTCodeBar().equals(selectedArticle.getaRTCodeBar())) {
                exist = true;
                break;
            }
            index_prod_intable++;
        }
        return exist;
    }
    void setFooter() {
        setText(amountInputField, StringUtils.priceFormat(amount));
        setText(amountwithDiscountInputField, StringUtils.priceFormat(amountWithDiscount));
        //AmountwithDiscountLayout.setVisibility(View.VISIBLE);
    }


    @Override
    public void onBackPressed() {
        new MaterialDialog.Builder(this).onPositive((dialog, which) -> finish()).onNegative((dialog, which) -> {
                }).title("Confirmation")
                .content("Êtes-vous sûr de vouloir quitter?")
                .positiveText(R.string.yes)
                .negativeText(R.string.no)
                .show();


    }


    @Override
    public boolean onOptionsItemSelected(MenuItem menuItem) {
        switch (menuItem.getItemId()) {
            case android.R.id.home:
                onBackPressed();
                break;
        }
        return super.onOptionsItemSelected(menuItem);
    }


    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.add_ticket_menu, menu);
        save = menu.findItem(R.id.save);
        scan = menu.findItem(R.id.scan);

        scan.setVisible(Utils.hasCamera(context));
        scan.setIcon(new IconicsDrawable(context).icon(FontAwesome.Icon.faw_barcode)
                .color(Color.WHITE).sizeDp(20));
        scan.setOnMenuItemClickListener(item -> {
            scan.setEnabled(false);
            showScanner();
            return false;
        });

        save.setIcon(new IconicsDrawable(context).icon(GoogleMaterial.Icon.gmd_send)
                .color(ResourcesCompat.getColor(getResources(), R.color.material_white, null)).sizeDp(24));
        save.setOnMenuItemClickListener(item -> {
            if ((tableView.getVisibility() == View.VISIBLE) && (searchableSpinner.getSelectedPosition() > 0)) {
                new MaterialDialog.Builder(this)
                        .title(R.string.confirmation)
                        .content(getString(R.string.sauv_label))
                        .negativeText(R.string.cancel)
                        .positiveText(R.string.dialog_ok)
                        .cancelable(false)
                        .onPositive((dialog, which) -> sendData())
                        .onNegative((dialog, which) -> save.setEnabled(true))
                        .show();
            } else {
                UIUtils.showDialog(context, R.string.fields_error);
                save.setEnabled(true);
            }

            return false;
        });
        return super.onCreateOptionsMenu(menu);
    }

    private void showScanner() {
//        new BarcodeScannerDialog(context, new BarcodeScannerListener() {
//            @Override
//            public void onDismiss(DialogInterface pDialog) {
//                //  item.setEnabled(true);
//                if (scanArt != null) {
//                    scanArt.setEnabled(true);
//                }
//            }
//
//            @Override
//            public void onDecoded(DialogInterface pDialog, Result result) {
//                showArticleFromScan(result.getText());
//                pDialog.dismiss();
//            }
//        }).show();


        BarCodeDialogueFragment dialog = new BarCodeDialogueFragment(new BarcodeScannerListener() {


            @Override
            public void onDecoded(String result) {

                showArticleFromScan(result);
            }
        });

        dialog.show(getSupportFragmentManager(), "MyDialogFragment");
    }

    public void showArticleFromScan(String code) {
        Article selectedArticle = App.database.articleDAO().getByCodeBarAndSation("%" + code + "%", App.prefUtils.getUserStationId());
        if (selectedArticle != null && !isDestroyed()) {
            dataTableView.setVisibility(View.VISIBLE);
            if (tableDataAdapter != null) {
                List<LigneTicket> ligneTickets = tableDataAdapter.getData();
                if (ObjectUtils.isNotEmpty(ligneTickets)) {
                    boolean exist = false;
                    for (LigneTicket ligneTicket : ligneTickets) {
                        if (ligneTicket.getArticle().getaRTCodeBar().equals(selectedArticle.getaRTCodeBar())) {
                            openModifyArticleDialog(ligneTicket, true);
                            exist = true;
                        }
                    }
                    if (!exist) {
                        showArticleDialog(selectedArticle);
                    }
                } else {
                    showArticleDialog(selectedArticle);
                }
            } else {
                showArticleDialog(selectedArticle);
            }

        } else
            Toast.makeText(BonRetourActivity.this, "article introuvable", Toast.LENGTH_SHORT).show();
    }


    void sendData() {
        createBonRetour();
        if (articleListDialog.getList().getLigneTickets().isEmpty()) {
            bonRetour = null;
            ticket = null;
            save.setEnabled(true);
            Toasty.info(context, R.string.no_article_selected).show();
        } else {
            ticketWithLines = new TicketWithLines(ticket, articleListDialog.getList().getLigneTickets());

            try {
                new MaterialDialog.Builder(context)
                        .title("Imprimer ?")
                        .content("Voulez-vous imprimer le B.R.?")
                        .positiveText("Oui")
                        .negativeText("Non")
                        .onNegative((dialog, which1) -> {
                            setResult(Activity.RESULT_OK, new Intent().putExtra(TICKET_WITH_LINES_KEY, ticketWithLines));
                            finish();
                        })
                        .onPositive((dialog, which2) -> {
                            TicketWithLinesAndPayments ticketWithLinesAndPayments = new TicketWithLinesAndPayments();
                            ticketWithLinesAndPayments.setTicket(ticketWithLines.getTicket());
                            ticketWithLinesAndPayments.setLigneTicket(ticketWithLines.getLigneTicket());
                            if (ligneBonRetours != null && ligneBonRetours.size() > 0) {
                                putArticleInLignes(ligneBonRetours);
                            }

                            if(prefUtils.getPrintA4Enabled()){
                                new File(Objects.requireNonNull(Comman.Companion.getAppPath(context))).mkdirs();
                                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                                    if (ligneBonRetours != null && ligneBonRetours.size() > 0) {

                                        WifiPrint.Companion.createBonRetourPDFFile(context, bonRetour, ligneBonRetours,() -> {
                                            setResult(Activity.RESULT_OK, new Intent());
                                            finish();
                                        });

                                    }
                                }

                            }
                            else {
                                printTicket(bonRetour, ligneBonRetours);
                            }



                        })
                        .show();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void createBonRetour() {
        if (bonRetour == null) {
              int versionCode = BuildConfig.VERSION_CODE;

            bonRtNum = App.database.prefixeDAO().getOneById("Bon_Retour").getpREPrefixe() +"_"+versionCode+"_"+
                    App.database.bonRetourDAO().getNewCode(App.database.prefixeDAO().getOneById("Bon_Retour").getpREPrefixe());
        }
        bonRetour = new BonRetour();
        bonRetour.setBORNumero(bonRtNum);
        bonRetour.setBORExercice(App.prefUtils.getExercice());
        bonRetour.setObservation(App.prefUtils.getUserId());
        bonRetour.setBORCodefrs(client.getcLICode());
        bonRetour.setBORStation(App.prefUtils.getUserStationId());
        bonRetour.setBORType("RetourTick");
        bonRetour.setBORMntFodec("0.0");
        bonRetour.setBONENTMntDC("0.0");
        bonRetour.setBORMntTTC(String.valueOf(0 - articleListDialog.getList().getAmountWithDiscount()));
        bonRetour.setBORMntTva(String.valueOf(abs(articleListDialog.getList().getVATAmount())));
        bonRetour.setBORMntHT(String.valueOf(0 - articleListDialog.getList().getAmountHT()));
        bonRetour.setStatus(Globals.ITEM_STATUS.INSERTED.getStatus());
        bonRetour.setBORDate(DateUtils.dateToStr(DateUtils.strToDate(dateInputField.getText().toString(), "EEEE, dd MMMM yyyy HH:mm"), Globals.DATE_PATTERN));
        bonRetour.isSync = false;
        ligneBonRetours = new ArrayList<>();
        /**
         * BOR_NumeroM in BonRetour class dont exist in remote db
         */
      //  if (isUpdate) bonRetour.borNUMM = ddm;
      //  else bonRetour.borNUMM = "empty";
        App.database.bonRetourDAO().insert(bonRetour);

        /**update sold client **/
        Utils.setClientSoldBnRetour(client, Double.parseDouble(bonRetour.getBORMntTTC()));


        for (LigneTicket ligneTicket : articleListDialog.getList().getLigneTickets()) {
            LigneBonRetour ligneBonRetour = new LigneBonRetour();
            ligneBonRetour.setNumBonRetour(bonRtNum);
            ligneBonRetour.setLIGBonEntreeDDm(DateUtils.dateToStr(DateUtils.strToDate(dateInputField.getText().toString(), "EEEE, dd MMMM yyyy HH:mm"), Globals.DATE_PATTERN));
            ligneBonRetour.setLIGBonEntreeExerc(App.prefUtils.getExercice());
            ligneBonRetour.setLIGBonEntreeStation(App.prefUtils.getUserStationId());
            ligneBonRetour.setLIGBonEntreeCodeArt(ligneTicket.getlTCodArt());
            ligneBonRetour.setLIGBonEntreeUnite("Pièce");
            ligneBonRetour.setLIGBonEntreeMntNetHt(String.valueOf(ligneTicket.getlTMtHT()));
            ligneBonRetour.setLIGBonEntreeMntTTC(String.valueOf(ligneTicket.getlTMtTTC()));
            ligneBonRetour.setLIGBonEntreeMntTva(String.valueOf(bonRetour.getBORMntTva()));
            ligneBonRetour.setLIGBonEntreeQte(String.valueOf(ligneTicket.getlTQte()));
            ligneBonRetour.setLIGBonEntreeRemise(String.valueOf(ligneTicket.getlTRemise()));
            ligneBonRetour.isSync = false;
            ligneBonRetour.setStatus(Globals.ITEM_STATUS.INSERTED.getStatus());
            App.database.ligneBonRetourDAO().insert(ligneBonRetour);
            ligneBonRetours.add(ligneBonRetour);
            Article article = App.database.articleDAO().getOneByCodeAndStation(ligneTicket.getlTCodArt(), App.prefUtils.getUserStationId());
        if(article!=null){
            StationStock stationStock = App.database.stationStockDAO().getOneByCode(article.getaRTCode(), prefUtils.getUserStationId());
            stationStock.setSARTQte(((stationStock.getSARTQte()) +abs(ligneTicket.getlTQte()) ));
            article.setaRTQteStock((stationStock.getSARTQte()+abs(ligneTicket.getlTQte())));
            App.database.stationStockDAO().insertOne(stationStock);
            App.database.articleDAO().insert(article);
        }

        }

    }

    private void printTicket(BonRetour item, List<LigneBonRetour> ligneBonRetours) {
        if (ligneBonRetours != null && ligneBonRetours.size() > 0) {

         //   calcuteTotalAndRemisePrices(item, ligneBonRetours);
            mService = new BluetoothService(getApplication(), new Handler() {
                @Override
                public void handleMessage(Message msg2) {
                    switch (msg2.what) {
                        case BluetoothService.MESSAGE_STATE_CHANGE:
                            switch (msg2.arg1) {
                                case BluetoothService.STATE_CONNECTED:
                                    Toast.makeText(getApplication(), "Connect successful",
                                            Toast.LENGTH_SHORT).show();

                                    try {
                                        new PrinterHelper(mService, DEFAULT_ENCODING).printBonRetour(context, item, ligneBonRetours, false);
                                        finish();

                                    } catch (IOException e) {
                                        Toasty.error(getApplication(), e.getMessage()).show();
                                    }

                                    break;
                                case BluetoothService.STATE_CONNECTING:
                                    break;
                                case BluetoothService.STATE_LISTEN:
                                case BluetoothService.STATE_NONE:
                                    break;
                            }
                            break;
                        case BluetoothService.MESSAGE_CONNECTION_LOST:
                            Toast.makeText(getApplication(), "Device connection was lost",
                                    Toast.LENGTH_SHORT).show();

                            break;
                        case BluetoothService.MESSAGE_UNABLE_CONNECT:
                            Toast.makeText(getApplication(), "Unable to connect device",
                                    Toast.LENGTH_SHORT).show();
                        case Activity.RESULT_CANCELED:
                            finish();
                            break;
                    }
                }

            });

            if (!mService.isBTopen()) {
                Intent enableIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
                startActivityForResult(enableIntent, REQUEST_ENABLE_BT);
            }
            if (!mService.isAvailable()) {
                Toast.makeText(context, "Bluetooth is not available", Toast.LENGTH_LONG).show();
            } else {
                Intent serverIntent = new Intent(context, DeviceListActivity.class);
                startActivityForResult(serverIntent, REQUEST_CONNECT_DEVICE);
            }
        } else {
            Toasty.info(context, "Aucune ligne à été trouvée pour cette B.C!!").show();
        }
    }

    private void calcuteTotalAndRemisePrices(BonRetour item, List<LigneBonRetour> ligneBonRetours) {
        double mntRemise = 0.0;
        double total = 0.0;
        for (LigneBonRetour ligneTicket : ligneBonRetours
        ) {
            try {
                total += Double.parseDouble(ligneTicket.getLIGBonEntreeQte()) * ligneTicket.getArticle().pvttc;
            } catch (Exception e) {
                Timber.tag("errcoammabdefragment").d(e);
            }


        }
        item.setBORMntRemise("0");
        item.setBORMntTTC(total + "");
    }

    private void putArticleInLignes(List<LigneBonRetour> bonRetourList) {
        for (int i = 0; i < bonRetourList.size(); i++) {
            bonRetourList.get(i).setArticle(App.database.articleDAO().getOneByCodeAndStation(
                    bonRetourList.get(i).getLIGBonEntreeCodeArt(), bonRetourList.get(i).getLIGBonEntreeStation()));
        }
    }

    static void setText(EditText text, String value) {
        text.setText(value);
    }


    @Override
    protected void onResume() {
        startBarcode();
        if (isPOINTMOBILE()) {
            if (mScanner != null) {
                if (mScanner.aDecodeGetDecodeEnable() == 1) {
                    initScanner();
                }
            } else {
                mScanner = new ScanManager();
                mDecodeResult = new DecodeResult();
            }
        }
        isInventory = true;
        super.onResume();
    }

    @Override
    protected void onPause() {
        if (mScanner != null) {
            if (isPOINTMOBILE()) {
                mScanner.aDecodeSetResultType(mBackupResultType);
            }
        }
        super.onPause();
        isShowing = false;
        isInventory = false;
    }

    @Override
    protected void onDestroy() {
        barCodeReaderManager.destroy();
        if (mScanner != null) {
            if (isPOINTMOBILE())
                mScanner.aDecodeSetResultType(mBackupResultType);
        }
        mScanner = null;
        isShowing = false;
        isInventory = false;
        unbinder.unbind();
        super.onDestroy();
    }


    @Override
    protected void onStop() {
        super.onStop();
        barCodeReaderManager.destroy();


        mScanner = null;
        mDecodeResult = null;
        isShowing = false;
        isInventory = false;
    }

    @Override
    protected void onPostResume() {
        super.onPostResume();
        barCodeReaderManager.resume();

        mScanner = new ScanManager();
        mDecodeResult = new DecodeResult();
    }











    private void initScanner() {
        if (mScanner != null) {
            mBackupResultType = mScanner.aDecodeGetResultType();
            mScanner.aDecodeSetResultType(ScanConst.ResultType.DCD_RESULT_USERMSG);
        }
    }



    /**
     * /**
     * show the dialog which contains all the articles to chose from
     */
    private void showArticleDialog(Article selectedArticle) {
        if(prefUtils.getIsAutoScan()) addPurchaseLineFromAutoScan(selectedArticle);
        else {
            boolean isScanSource = true;
            // validationListener=new Va
            articleDialog = new ArticleDialog(context, false, selectedArticle, 2,true, isScanSource, true, (dialog, which) -> {
                articleDialog.validator.validate();
                selectedArticle.setSync(false);
                selectedArticle.setStatus(Globals.ITEM_STATUS.INSERTED.getStatus());
                selectedArticle.setCount(StringUtils.parseDouble(articleDialog.getQuantityInputField().getText().toString(), 0));
                articleListDialog.getList().put(selectedArticle, Double.parseDouble(articleDialog.getQuantityInputField().getText().toString()), 0, false);
                setTicketDataTable(false);
                setFooter();
                articleDialog.dismiss();
                //App.database.articleDAO().insert(ArticleDialog.article);
            }, (dialog, which) -> {
                if (ArticleDialog.article != null)
                    ArticleDialog.article.setPvttc(App.database.articleDAO().getOneByCodeAndStation(ArticleDialog.article.getaRTCode(),
                            App.prefUtils.getUserStationId()).getPvttc());
                dialog.dismiss();

            }, validationListener);
            articleDialog.show(context.getSupportFragmentManager(), StringUtils.digits);
        }
    }

    private void addPurchaseLineFromAutoScan(Article selectedArticle) {
        selectedArticle.setSync(false);
        selectedArticle.setStatus(Globals.ITEM_STATUS.INSERTED.getStatus());
        selectedArticle.setCount(StringUtils.parseDouble("1", 1));
        articleListDialog.getList().put(selectedArticle, Double.parseDouble("1"), 0, false);
        setTicketDataTable(false);
        setFooter();
        App.database.articleDAO().insert(ArticleDialog.article);
    }
}
