package com.asmtunis.procaissemobility.data.viewModels;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelProviders;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.dao.ReglementCaisseDAO;
import com.asmtunis.procaissemobility.data.models.ReglementCaisse;

import java.util.List;

public class ReglementCaisseViewModel extends ViewModel {

    public ReglementCaisseDAO dao;
    private static ReglementCaisseViewModel instance;


    public static ReglementCaisseViewModel getInstance(Fragment activity) {
        if (instance == null)
            instance = new ViewModelProvider(activity).get(ReglementCaisseViewModel.class);
        instance.dao = App.database.reglementCaisseDAO();
        return instance;
    }
    public static ReglementCaisseViewModel getInstance(FragmentActivity activity) {
        if (instance == null)
            instance = new ViewModelProvider(activity).get(ReglementCaisseViewModel.class);
        instance.dao = App.database.reglementCaisseDAO();
        return instance;
    }

    public LiveData<List<ReglementCaisse>> getAll() {
        return dao.getAllMutableBySession(App.prefUtils.getSessionCaisseId());
    }

    public LiveData<List<ReglementCaisse>> getAllBySession(String session) {
        return dao.getAllMutableBySession(session);
    }

    public LiveData<Integer> getNoSyncCount() {
        return dao.getNoSyncCountMuable();
    }
    public Integer getNoSyncNonMutable() {
        return dao.getNoSyncCountNonMutable();
    }
}
