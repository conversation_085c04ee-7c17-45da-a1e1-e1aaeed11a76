package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * Created by PC on 26/01/2018.
 */
@Entity
public class Fournisseur extends BaseModel {


    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "FRS_codef")
    @SerializedName("FRS_codef")
    @Expose
    private String fRSCodef;
    @ColumnInfo(name = "FRS_Nomf")
    @SerializedName("FRS_Nomf")
    @Expose
    private String fRSNomf;
    @ColumnInfo(name = "FRS_adrf")
    @SerializedName("FRS_adrf")
    @Expose
    private String fRSAdrf;
    @ColumnInfo(name = "FRS_villef")
    @SerializedName("FRS_villef")
    @Expose
    private String fRSVillef;

    @ColumnInfo(name = "FRS_paysf")
    @SerializedName("FRS_paysf")
    @Expose
    private String fRSPaysf;
    @ColumnInfo(name = "FRS_Codepf")
    @SerializedName("FRS_Codepf")
    @Expose
    private String fRSCodepf;
    @ColumnInfo(name = "FRS_telf")
    @SerializedName("FRS_telf")
    @Expose
    private String fRSTelf;
    @ColumnInfo(name = "FRS_Gsm")
    @SerializedName("FRS_Gsm")
    @Expose
    private String fRSGsm;
    @ColumnInfo(name = "FRS_faxf")
    @SerializedName("FRS_faxf")
    @Expose
    private String fRSFaxf;
    @ColumnInfo(name = "FRS_codeTVAf")
    @SerializedName("FRS_codeTVAf")
    @Expose
    private String fRSCodeTVAf;
    @ColumnInfo(name = "FRS_assujette")
    @SerializedName("FRS_assujette")
    @Expose
    private String fRSAssujette;
    @ColumnInfo(name = "FRS_fodec")
    @SerializedName("FRS_fodec")
    @Expose
    private String fRSFodec;
    @ColumnInfo(name = "FRS_DC")
    @SerializedName("FRS_DC")
    @Expose
    private String fRSDC;

    @ColumnInfo(name = "FRS_forfetaire")
    @SerializedName("FRS_forfetaire")
    @Expose
    private String fRSForfetaire;
    @ColumnInfo(name = "FRS_timber")
    @SerializedName("FRS_timber")
    @Expose
    private String fRSTimber;
    @ColumnInfo(name = "FRS_remarque")
    @SerializedName("FRS_remarque")
    @Expose
    private String fRSRemarque;
    @ColumnInfo(name = "FRS_User")
    @SerializedName("FRS_User")
    @Expose
    private String fRSUser;
    @ColumnInfo(name = "FRS_Station")
    @SerializedName("FRS_Station")
    @Expose
    private String fRSStation;
    @ColumnInfo(name = "FRS_Solde")
    @SerializedName("FRS_Solde")
    @Expose
    private String fRSSolde;

    @ColumnInfo(name = "FRS_export")
    @SerializedName("FRS_export")
    @Expose
    private String fRSExport;
    @ColumnInfo(name = "FRS_DDm")
    @SerializedName("FRS_DDm")
    @Expose
    private String fRSDDm;
    @ColumnInfo(name = "FRS_Atelier")
    @SerializedName("FRS_Atelier")
    @Expose
    private String fRSAtelier;
    @ColumnInfo(name = "FRS_Debit")
    @SerializedName("FRS_Debit")
    @Expose
    private String fRSDebit;
    @ColumnInfo(name = "FRS_Credit")

    @SerializedName("FRS_Credit")
    @Expose
    private String fRSCredit;
    @ColumnInfo(name = "FRS_EX")
    @SerializedName("FRS_EX")
    @Expose
    private String fRSEX;
    @ColumnInfo(name = "Type")
    @SerializedName("Type")
    @Expose
    private String type;

    public Fournisseur() {
    }

    public String getFRSCodef() {
        return fRSCodef;
    }

    public void setFRSCodef(String fRSCodef) {
        this.fRSCodef = fRSCodef;
    }

    public String getFRSNomf() {
        return fRSNomf;
    }

    public void setFRSNomf(String fRSNomf) {
        this.fRSNomf = fRSNomf;
    }

    public String getFRSAdrf() {
        return fRSAdrf;
    }

    public void setFRSAdrf(String fRSAdrf) {
        this.fRSAdrf = fRSAdrf;
    }

    public String getFRSVillef() {
        return fRSVillef;
    }

    public void setFRSVillef(String fRSVillef) {
        this.fRSVillef = fRSVillef;
    }

    public String getFRSPaysf() {
        return fRSPaysf;
    }

    public void setFRSPaysf(String fRSPaysf) {
        this.fRSPaysf = fRSPaysf;
    }

    public String getFRSCodepf() {
        return fRSCodepf;
    }

    public void setFRSCodepf(String fRSCodepf) {
        this.fRSCodepf = fRSCodepf;
    }

    public String getFRSTelf() {
        return fRSTelf;
    }

    public void setFRSTelf(String fRSTelf) {
        this.fRSTelf = fRSTelf;
    }

    public String getFRSGsm() {
        return fRSGsm;
    }

    public void setFRSGsm(String fRSGsm) {
        this.fRSGsm = fRSGsm;
    }

    public String getFRSFaxf() {
        return fRSFaxf;
    }

    public void setFRSFaxf(String fRSFaxf) {
        this.fRSFaxf = fRSFaxf;
    }

    public String getFRSCodeTVAf() {
        return fRSCodeTVAf;
    }

    public void setFRSCodeTVAf(String fRSCodeTVAf) {
        this.fRSCodeTVAf = fRSCodeTVAf;
    }

    public String getFRSAssujette() {
        return fRSAssujette;
    }

    public void setFRSAssujette(String fRSAssujette) {
        this.fRSAssujette = fRSAssujette;
    }

    public String getFRSFodec() {
        return fRSFodec;
    }

    public void setFRSFodec(String fRSFodec) {
        this.fRSFodec = fRSFodec;
    }

    public String getFRSDC() {
        return fRSDC;
    }

    public void setFRSDC(String fRSDC) {
        this.fRSDC = fRSDC;
    }

    public String getFRSForfetaire() {
        return fRSForfetaire;
    }

    public void setFRSForfetaire(String fRSForfetaire) {
        this.fRSForfetaire = fRSForfetaire;
    }

    public String getFRSTimber() {
        return fRSTimber;
    }

    public void setFRSTimber(String fRSTimber) {
        this.fRSTimber = fRSTimber;
    }

    public String getFRSRemarque() {
        return fRSRemarque;
    }

    public void setFRSRemarque(String fRSRemarque) {
        this.fRSRemarque = fRSRemarque;
    }

    public String getFRSUser() {
        return fRSUser;
    }

    public void setFRSUser(String fRSUser) {
        this.fRSUser = fRSUser;
    }

    public String getFRSStation() {
        return fRSStation;
    }

    public void setFRSStation(String fRSStation) {
        this.fRSStation = fRSStation;
    }

    public String getFRSSolde() {
        return fRSSolde;
    }

    public void setFRSSolde(String fRSSolde) {
        this.fRSSolde = fRSSolde;
    }

    public String getFRSExport() {
        return fRSExport;
    }

    public void setFRSExport(String fRSExport) {
        this.fRSExport = fRSExport;
    }

    public String getFRSDDm() {
        return fRSDDm;
    }

    public void setFRSDDm(String fRSDDm) {
        this.fRSDDm = fRSDDm;
    }

    public String getFRSAtelier() {
        return fRSAtelier;
    }

    public void setFRSAtelier(String fRSAtelier) {
        this.fRSAtelier = fRSAtelier;
    }

    public String getFRSDebit() {
        return fRSDebit;
    }

    public void setFRSDebit(String fRSDebit) {
        this.fRSDebit = fRSDebit;
    }

    public String getFRSCredit() {
        return fRSCredit;
    }

    public void setFRSCredit(String fRSCredit) {
        this.fRSCredit = fRSCredit;
    }

    public String getFRSEX() {
        return fRSEX;
    }

    public void setFRSEX(String fRSEX) {
        this.fRSEX = fRSEX;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

}
    
    
    

