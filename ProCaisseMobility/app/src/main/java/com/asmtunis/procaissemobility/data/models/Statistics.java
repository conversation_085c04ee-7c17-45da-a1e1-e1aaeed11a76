package com.asmtunis.procaissemobility.data.models;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * Created by PC on 11/23/2017.
 */
public class Statistics {

    @SerializedName("REGC_IdSCaisse")
    @Expose
    public String rEGCIdSCaisse;
    @SerializedName("Mnt_Espece")
    @Expose
    public String mntEspece;
    @SerializedName("Mnt_Carte_Bancaire")
    @Expose
    public String mntCarteBancaire;
    @SerializedName("Mnt_Cheque")
    @Expose
    public String mntCheque;
    @SerializedName("Mnt_Traite")
    @Expose
    public String mntTraite;
    @SerializedName("total")
    @Expose
    public String total;
    @SerializedName("Mnt_Bonus")
    @Expose
    public String mntBonus;
    @SerializedName("Fond_Caisse")
    @Expose
    public String fondCaisse;
    @SerializedName("Dep_Caisse")
    @Expose
    public String depCaisse;
    @SerializedName("MntCarte_prepayee")
    @Expose
    public String mntCartePrepayee;
    @SerializedName("Mnt_PointMerci")
    @Expose
    public String mntPointMerci;
    @SerializedName("MntBonAchat")
    @Expose
    public String mntBonAchat;
    @SerializedName("NomPrenUtil")
    @Expose
    public String nomPrenUtil;
    @SerializedName("CA")
    @Expose
    public String cA;
    @SerializedName("Nbr_Client")
    @Expose
    public String nbrClient;
    @SerializedName("NbreTicket")
    @Expose
    public String nbreTicket;
    @SerializedName("LT_Qte")
    @Expose
    public String lTQte;
    @SerializedName("TopClients")
    @Expose
    public List<com.asmtunis.procaissemobility.data.models.custom.Client> topClients;

    @SerializedName("Mnt_Credit")
    @Expose
    public String mntCredit;

    public Long updated_at;




    public Statistics() {
    }

    public String getMntCredit() {
        return mntCredit;
    }

    public void setMntCredit(String mntCredit) {
        this.mntCredit = mntCredit;
    }

    public Long getUpdated_at() {
        return updated_at;
    }

    public void setUpdated_at(Long updated_at) {
        this.updated_at = updated_at;
    }

    public String getMntEspece() {
        return mntEspece;
    }

    public void setMntEspece(String mntEspece) {
        this.mntEspece = mntEspece;
    }

    public String getMntCarteBancaire() {
        return mntCarteBancaire;
    }

    public void setMntCarteBancaire(String mntCarteBancaire) {
        this.mntCarteBancaire = mntCarteBancaire;
    }

    public String getrEGCIdSCaisse() {
        return rEGCIdSCaisse;
    }

    public void setrEGCIdSCaisse(String rEGCIdSCaisse) {
        this.rEGCIdSCaisse = rEGCIdSCaisse;
    }

    public String getcA() {
        return cA;
    }

    public void setcA(String cA) {
        this.cA = cA;
    }

    public String getlTQte() {
        return lTQte;
    }

    public void setlTQte(String lTQte) {
        this.lTQte = lTQte;
    }

    public List<com.asmtunis.procaissemobility.data.models.custom.Client> getTopClients() {
        return topClients;
    }

    public void setTopClients(List<com.asmtunis.procaissemobility.data.models.custom.Client> topClients) {
        this.topClients = topClients;
    }

    public String getMntCheque() {
        return mntCheque;
    }

    public void setMntCheque(String mntCheque) {
        this.mntCheque = mntCheque;
    }

    public String getMntTraite() {
        return mntTraite;
    }

    public void setMntTraite(String mntTraite) {
        this.mntTraite = mntTraite;
    }

    public String getTotal() {
        return total;
    }

    public void setTotal(String total) {
        this.total = total;
    }

    public String getMntBonus() {
        return mntBonus;
    }

    public void setMntBonus(String mntBonus) {
        this.mntBonus = mntBonus;
    }



    public String getFondCaisse() {
        return fondCaisse;
    }

    public void setFondCaisse(String fondCaisse) {
        this.fondCaisse = fondCaisse;
    }

    public String getDepCaisse() {
        return depCaisse;
    }

    public void setDepCaisse(String depCaisse) {
        this.depCaisse = depCaisse;
    }

    public String getMntCartePrepayee() {
        return mntCartePrepayee;
    }

    public void setMntCartePrepayee(String mntCartePrepayee) {
        this.mntCartePrepayee = mntCartePrepayee;
    }

    public String getMntPointMerci() {
        return mntPointMerci;
    }

    public void setMntPointMerci(String mntPointMerci) {
        this.mntPointMerci = mntPointMerci;
    }

    public String getMntBonAchat() {
        return mntBonAchat;
    }

    public void setMntBonAchat(String mntBonAchat) {
        this.mntBonAchat = mntBonAchat;
    }

    public String getNomPrenUtil() {
        return nomPrenUtil;
    }

    public void setNomPrenUtil(String nomPrenUtil) {
        this.nomPrenUtil = nomPrenUtil;
    }


    public String getCA() {
        return cA;
    }

    public void setCA(String cA) {
        this.cA = cA;
    }

    public String getNbrClient() {
        return nbrClient;
    }

    public void setNbrClient(String nbrClient) {
        this.nbrClient = nbrClient;
    }

    public String getNbreTicket() {
        return nbreTicket;
    }

    public void setNbreTicket(String nbreTicket) {
        this.nbreTicket = nbreTicket;
    }

    public String getLTQte() {
        return lTQte;
    }

    public void setLTQte(String lTQte) {
        this.lTQte = lTQte;
    }

    @Override
    public String toString() {
        return "Statistics{" +
                "rEGCIdSCaisse='" + rEGCIdSCaisse + '\'' +
                ", mntEspece='" + mntEspece + '\'' +
                ", mntCarteBancaire='" + mntCarteBancaire + '\'' +
                ", mntCheque='" + mntCheque + '\'' +
                ", mntTraite='" + mntTraite + '\'' +
                ", total='" + total + '\'' +
                ", mntBonus='" + mntBonus + '\'' +
                ", fondCaisse='" + fondCaisse + '\'' +
                ", depCaisse='" + depCaisse + '\'' +
                ", mntCartePrepayee='" + mntCartePrepayee + '\'' +
                ", mntPointMerci='" + mntPointMerci + '\'' +
                ", mntBonAchat='" + mntBonAchat + '\'' +
                ", nomPrenUtil='" + nomPrenUtil + '\'' +
                ", cA='" + cA + '\'' +
                ", nbrClient='" + nbrClient + '\'' +
                ", nbreTicket='" + nbreTicket + '\'' +
                ", lTQte='" + lTQte + '\'' +
                ", topClients=" + topClients +
                '}';
    }
}
