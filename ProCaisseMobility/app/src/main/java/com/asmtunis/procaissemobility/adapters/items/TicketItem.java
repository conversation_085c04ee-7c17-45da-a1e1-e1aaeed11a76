package com.asmtunis.procaissemobility.adapters.items;

import android.content.Context;
import android.graphics.Paint;
import android.graphics.Typeface;
import android.util.Log;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.RecyclerView;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Facture;
import com.asmtunis.procaissemobility.data.models.LigneTicket;
import com.asmtunis.procaissemobility.data.models.Ticket;
import com.asmtunis.procaissemobility.helper.Globals;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.listener.ItemCallback;
import com.asmtunis.procaissemobility.listener.MenuItemClickListener;
import com.asmtunis.procaissemobility.listener.MenuItemsAction;
import com.mikepenz.fastadapter.items.AbstractItem;
import com.mikepenz.fontawesome_typeface_library.FontAwesome;
import com.mikepenz.iconics.IconicsDrawable;

import java.util.List;
import java.util.Objects;

/**
 * Created by PC on 10/11/2017.
 */

public class TicketItem extends AbstractItem<TicketItem, TicketItem.ViewHolder> {

    private final int UNSELECTED = -1;
    private Context context;
    public Ticket ticket;
    private List<LigneTicket> ligneTickets;
    private int selectedItem = UNSELECTED;
    MenuItemsAction menuItemsAction;
    protected ItemCallback itemCallback;
    boolean isOrder;
    boolean invoiced = false;
    int menuId;

    public TicketItem(Context context, boolean isOrder, Ticket ticket, List<LigneTicket> ligneTickets, int menuId, MenuItemsAction
            menuItemsAction, ItemCallback itemCallback) {
        this.ticket = ticket;
        this.context = context;
        this.menuItemsAction = menuItemsAction;
        this.menuId = menuId;
        this.itemCallback = itemCallback;
        this.isOrder = isOrder;
        this.ligneTickets = ligneTickets;
    }

    public Ticket getTicket() {
        return ticket;
    }

    public void setTicket(Ticket ticket) {
        this.ticket = ticket;
    }

    //The unique ID for this type of item
    @Override
    public int getType() {
        return R.id.fastadapter_ticket_item_id;
    }

    //The unit_price_dialog to be used for this type of item
    @Override
    public int getLayoutRes() {
        return R.layout.ticket_item;
    }

    //The logic to bind your data to the view
    @Override
    public void bindView(final ViewHolder viewHolder, List<Object> payloads) {
        //call super so the selection is already handled for you
        super.bindView(viewHolder, payloads);

      Facture facture = App.database.factureDAO().getByTicket(String.valueOf(ticket.tIKNumTicket), ticket.tIKUser, ticket.tIKStation);


        String numFact = "";
        if (ticket.tIKNumeroBL != null) {
            invoiced = true;
            numFact = ticket.tIKNumeroBL;
        }

        viewHolder.setIsRecyclable(false);
        viewHolder.toolbar.inflateMenu(menuId);
        String displayedNumTicket = "";

        if(ticket.isSync) {
            displayedNumTicket = String.valueOf(ticket.gettIKNumTicket());
        }
        else {
            if (ticket.tikNumTicketM.contains("_")) {
                String[] numTicket = ticket.tikNumTicketM.split("_");
                displayedNumTicket = numTicket[2] + "_" + numTicket[3] + "_" + numTicket[4];
            } else displayedNumTicket = ticket.tikNumTicketM;
        }


     viewHolder.ticketNumber.setText(String.format(context.getString(isOrder ? R.string.order_number_field : R.string.ticket_number_field), displayedNumTicket/*ticket.tikNumTicketM*/));

        viewHolder.ticketUser.setText(ticket.gettIKNomClient());

        String tiketEtat = "";
        if (!numFact.contains(context.getString(R.string.Nombre_des_tickets))) {
            if (ticket.gettIKEtat().equals(Globals.TICKET_STATE.PAYED.getValue()))
                tiketEtat = " (" + context.getString(R.string.payment_label) + " )";
            else tiketEtat = " (" + ticket.gettIKEtat() + " )";
        } else {
            viewHolder.ticketUserLL.setVisibility(View.GONE);
            viewHolder.toolbar.getLayoutParams().height = LinearLayout.LayoutParams.WRAP_CONTENT;


        }
      double tikMttc = 0.0;

        if(facture != null) {
            tikMttc = Math.max(Double.parseDouble(Objects.requireNonNull(facture.getFACT_MntTTC())), ticket.gettIKMtTTC());
        }
        else tikMttc = ticket.gettIKMtTTC();


        // viewHolder.typeReglement.setText(" (" +tiketEtat+" )");
      /*  if (timbre == null) {
            viewHolder.price.setText(String.format("%s %s", StringUtils.priceFormat((ticket.gettIKMtTTC())), new PrefUtils(context).getCurrency()) + tiketEtat);
        } else if (!ticket.isSync) {
            viewHolder.price.setText(String.format("%s %s", StringUtils.priceFormat((ticket.gettIKMtTTC())), new PrefUtils(context).getCurrency()) + tiketEtat);
        } else*/

        viewHolder.price.setText(String.format("%s %s", StringUtils.priceFormat(tikMttc), new PrefUtils(context).getCurrency()) + tiketEtat);


        if (numFact.contains(context.getString(R.string.Nombre_des_tickets)))
        viewHolder.dateCreation.setVisibility(View.INVISIBLE);
        viewHolder.dateCreation.setText(ticket.gettIKDateHeureTicket().replace(".000", ""));
        viewHolder.toolbar.setTag("toolbar_" + ticket.gettIKNumTicket());
        viewHolder.itemStatusLabel.setTag("itemStatusLabel_" + ticket.gettIKNumTicket());

        viewHolder.price.setVisibility(View.VISIBLE);
        viewHolder.price.setTypeface(viewHolder.price.getTypeface(), Typeface.BOLD);
        viewHolder.price.setTextColor(context.getResources().getColor(R.color.successColor));

//        for (LigneTicket ligneTicket : ligneTickets) {
//            System.out.println(ligneTicket);
//            if (ligneTicket.lTnumFacture != null) {
//                invoiced = true;
//                numFact = ligneTicket.lTnumFacture;
//            }
//        }


        if (ticket.tIKAnnuler == 1) {
            viewHolder.state.setText("A");
            viewHolder.price.setPaintFlags(viewHolder.price.getPaintFlags() & (~Paint.STRIKE_THRU_TEXT_FLAG));
            viewHolder.state.setBackground(context.getResources().getDrawable(R.drawable.round_corner_a));
            viewHolder.price.setTextColor(context.getResources().getColor(R.color.errorColor));

            if (!numFact.contains(context.getString(R.string.Nombre_des_tickets)))
                setTriangleView(viewHolder.itemStatusLabel, 3);

        }

        if (!ticket.isSync) {
            viewHolder.state.setText("NOT SYNC");
            viewHolder.price.setPaintFlags(viewHolder.price.getPaintFlags() & (~Paint.STRIKE_THRU_TEXT_FLAG));
            viewHolder.state.setBackground(context.getResources().getDrawable(R.drawable.round_corner_c));
            if (!numFact.contains(context.getString(R.string.Nombre_des_tickets)))
                setTriangleView(viewHolder.itemStatusLabel, 0);

            viewHolder.price.setTextColor(context.getResources().getColor(R.color.warningColor));
            viewHolder.toolbar.getMenu().findItem(R.id.invoice_item).setVisible(false);
            if (!PrefUtils.isAutoSync()) {
                if (ticket.tIKAnnuler != 1 && !numFact.contains(context.getString(R.string.Nombre_des_tickets)))
                    viewHolder.toolbar.getMenu().findItem(R.id.edit_item).setVisible(true);
                else viewHolder.toolbar.getMenu().findItem(R.id.edit_item).setVisible(false);

            } else {
                viewHolder.toolbar.getMenu().findItem(R.id.edit_item).setVisible(false);
            }
        }
        else {
            setTriangleView(viewHolder.itemStatusLabel, 4);
            viewHolder.dateCreation.setTextColor(context.getResources().getColor(R.color.successColor));

            viewHolder.toolbar.getMenu().findItem(R.id.edit_item).setVisible(false);

        }

        if (menuId != 0) {
            viewHolder.toolbar.setOnMenuItemClickListener(new MenuItemClickListener<Ticket>(ticket, menuItemsAction));
            viewHolder.toolbar.getMenu().findItem(R.id.print_item).setIcon(new IconicsDrawable(context)
                    .icon(FontAwesome.Icon.faw_print)
                    .color(context.getResources().getColor(R.color.material_teal700))
                    .sizeDp(20));
            //if(ticket.tIK_Source == null) viewHolder.toolbar.getMenu().findItem(R.id.edit_item).setVisible(false);
            viewHolder.toolbar.getMenu().findItem(R.id.print_item).setVisible(ticket.tIKAnnuler != 1);
            if (!invoiced) {
                //viewHolder.toolbar.getMenu().findItem(R.id.edit_item).setVisible(true);
                viewHolder.toolbar.getMenu().findItem(R.id.invoice_item).setVisible(ticket.tIKAnnuler != 1);
            } else {
                viewHolder.toolbar.getMenu().findItem(R.id.edit_item).setVisible(false);
                viewHolder.toolbar.getMenu().findItem(R.id.invoice_item).setVisible(false);
                if (!numFact.contains(context.getString(R.string.Nombre_des_tickets))) {
                    setTriangleView(viewHolder.itemStatusLabel, 1);
                    viewHolder.ticketNumber.setText(String.format(context.getString(R.string.fact), numFact));
                }

                else viewHolder.ticketNumber.setText(numFact);

            }
        }


        if (itemCallback != null) {
            viewHolder.itemView.setOnClickListener(v -> onViewClick(viewHolder));
            viewHolder.toolbar.setOnClickListener(v -> onViewClick(viewHolder));
        }

        if (numFact.contains(context.getString(R.string.Nombre_des_tickets)))
            viewHolder.itemStatusLabel.setVisibility(View.GONE);
    }


    void onViewClick(TicketItem.ViewHolder viewHolder) {
        if (ticket != null) {
            if (itemCallback == null) {
                return;
            } else {
                itemCallback.onItemClicked(viewHolder, ticket);
            }

        }
    }

    //reset the view here (this is an optional method, but recommended)
    @Override
    public void unbindView(final ViewHolder holder) {
        super.unbindView(holder);
      /*  holder.toolbar.setTitle(null);

        holder.toolbar.setSubtitle(null);
        holder.dateCreation.setText(null);
        holder.price.setText(null);*/
      /*  holder.tel1.setText(null);
        holder.address.setText(null);*/
//        holder.count.setText(item.getDateCreation());

    }

    //Init the viewHolder for this Item
    @Override
    public ViewHolder getViewHolder(View v) {
        return new ViewHolder(v);
    }


    void setTriangleView(jp.shts.android.library.TriangleLabelView labelView, int status) {
        labelView.setVisibility(View.VISIBLE);
        switch (status) {
            case 0:
                labelView.setTriangleBackgroundColorResource(R.color.warningColor);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.notSync);
                // labelView.setPrimaryTextColorResource(R.color.md_red_100);
                labelView.setPrimaryTextColorResource(R.color.black);

                break;

            case 1:
                labelView.setTriangleBackgroundColorResource(R.color.md_red_900);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.invoiced);
                labelView.setPrimaryTextColorResource(R.color.md_red_100);


                break;

            case 2:
                labelView.setTriangleBackgroundColorResource(R.color.md_green_800);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.new_label);
                labelView.setPrimaryTextColorResource(R.color.md_green_100);
                break;


            case 3:
                labelView.setTriangleBackgroundColorResource(R.color.black);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.cancel);
                labelView.setPrimaryTextColorResource(R.color.md_green_100);
                break;

            case 4:
                labelView.setTriangleBackgroundColorResource(R.color.md_green_800);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.Sync);
                labelView.setPrimaryTextColorResource(R.color.md_green_100);
                break;
            default:
                labelView.setVisibility(View.GONE);

                break;
        }

    }


    //The viewHolder used for this item. This viewHolder is always reused by the RecyclerView so scrolling is blazing fast
    public static class ViewHolder extends RecyclerView.ViewHolder {

        //    @BindView(R.id.expandable_layout)
        com.asmtunis.procaissemobility.ui.components.TicketView ticketView;
        LinearLayout footerLayout;
        //public ExpandableLayout expandableLayout;
        // @BindView(R.id.toolbar)
        public Toolbar toolbar;
        // @BindView(R.id.dateCreation)
        TextView dateCreation;
        //  @BindView(R.id.price)
        TextView price;
        //  @BindView(R.id.content_layout)
        FrameLayout content;
        TextView state;
        ImageView tiketuserImV;
        LinearLayout ticketUserLL;

        TextView ticketNumber;
        TextView ticketUser;
        jp.shts.android.library.TriangleLabelView itemStatusLabel;
        Ticket ticket;
        LinearLayout linearLayout;

        public ViewHolder(View view) {
            super(view);
            //       ButterKnife.bind(this, view);

            ticketView = view.findViewById(R.id.layout_ticket);
            ticketUserLL = view.findViewById(R.id.ticketUserLL);
            tiketuserImV = view.findViewById(R.id.tiketuserImV);
            footerLayout = view.findViewById(R.id.footer_layout);
            //    expandableLayout = view.findViewById(R.id.expandable_layout);
            toolbar = view.findViewById(R.id.toolbar);
            price = view.findViewById(R.id.price);
            dateCreation = view.findViewById(R.id.dateCreation);
            content = view.findViewById(R.id.content_layout);
            itemStatusLabel = view.findViewById(R.id.item_status_label);
            ticketNumber = view.findViewById(R.id.ticketNumber);
            ticketUser = view.findViewById(R.id.ticketUser);
            state = view.findViewById(R.id.state);
            linearLayout = view.findViewById(R.id.linearLayout);

        }
    }


}
