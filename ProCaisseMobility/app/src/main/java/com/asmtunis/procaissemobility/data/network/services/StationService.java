package com.asmtunis.procaissemobility.data.network.services;

import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Station;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

public interface StationService {
    @POST("getStations")
    Call<List<Station>> getAllStations(@Body GenericObject genericObject);
}
