package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * Created by Oussama AZIZI on 6/24/22.
 */

@Entity
public class VCTypeCommunication {

    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "CodeTypeCom")
    @SerializedName("CodeTypeCom")
    @Expose
    private String codeTypeCom;

    @ColumnInfo(name = "TypeCommunication")
    @SerializedName("TypeCommunication")
    @Expose
    private String typeCommunication;

    @ColumnInfo(name = "NoteComm")
    @SerializedName("NoteComm")
    @Expose
    private String noteComm;

    public String getCodeTypeCom() {
        return codeTypeCom;
    }

    public void setCodeTypeCom(String codeTypeCom) {
        this.codeTypeCom = codeTypeCom;
    }

    public String getTypeCommunication() {
        return typeCommunication;
    }

    public void setTypeCommunication(String typeCommunication) {
        this.typeCommunication = typeCommunication;
    }

    public String getNoteComm() {
        return noteComm;
    }

    public void setNoteComm(String noteComm) {
        this.noteComm = noteComm;
    }

    @Override
    public String toString() {
        return typeCommunication;
    }
}
