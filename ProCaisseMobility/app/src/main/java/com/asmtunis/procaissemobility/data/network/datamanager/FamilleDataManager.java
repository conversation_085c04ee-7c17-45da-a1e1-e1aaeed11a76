package com.asmtunis.procaissemobility.data.network.datamanager;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Famille;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.FamilleService;

import java.util.List;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

/**
 * Created by PC on 10/4/2017.
 */

public class FamilleDataManager {
    private static FamilleDataManager sInstance;

    private final FamilleService mFamilleService;

    public FamilleDataManager( ) {
        mFamilleService = new ServiceFactory<>(FamilleService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Famille")).makeService();

    }

    public static FamilleDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new FamilleDataManager();
        }
        return sInstance;
    }

    public void getFamilles(GenericObject genericObject,
                            RemoteCallback<List<Famille>> listener) {
        mFamilleService.getFamilles(genericObject)
                .enqueue(listener);
    }

}

