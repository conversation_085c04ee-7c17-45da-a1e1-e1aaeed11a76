package com.asmtunis.procaissemobility.data.models;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.annotation.NonNull;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * Created by PC on 31/01/2018.
 */

@Entity
public class DepenceType extends BaseModel implements Serializable {
    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "DEP_Code")
    @SerializedName("DEP_Code")
    @Expose
    private String depCode;
    @ColumnInfo(name = "DEP_Lib")
    @SerializedName("DEP_Lib")
    @Expose
    private String depLib;
    @ColumnInfo(name = "DEP_IsTactile")
    @SerializedName("DEP_IsTactile")
    @Expose
    private String depIsTactile;
    @ColumnInfo(name = "DEP_user")
    @SerializedName("DEP_user")
    @Expose
    private String depUser;
    @ColumnInfo(name = "DEP_station")
    @SerializedName("DEP_station")
    @Expose
    private String depStation;
    @ColumnInfo(name = "DEP_export")
    @SerializedName("DEP_export")
    @Expose
    private String depExport;
    @ColumnInfo(name = "DEP_DDm")
    @SerializedName("DEP_DDm")
    @Expose
    private String depDDm;
    @ColumnInfo(name = "DEP_Code_M")
    @SerializedName("DEP_Code_M")
    @Expose
    private String depCodeM;

    public DepenceType() {
    }

    public DepenceType(@NonNull String depCode, String depLib, String depIsTactile, String depUser, String depStation, String depExport, String depDDm, String depCodeM, boolean isSync, String status) {
        this.depCode = depCode;
        this.depLib = depLib;
        this.depIsTactile = depIsTactile;
        this.depUser = depUser;
        this.depStation = depStation;
        this.depExport = depExport;
        this.depDDm = depDDm;
        this.depCodeM = depCodeM;
        this.isSync = isSync;
        this.status = status;
    }

    @NonNull
    public String getDepCode() {
        return depCode;
    }

    public void setDepCode(@NonNull String depCode) {
        this.depCode = depCode;
    }

    public String getDepLib() {
        return depLib;
    }

    public void setDepLib(String depLib) {
        this.depLib = depLib;
    }

    public String getDepIsTactile() {
        return depIsTactile;
    }

    public void setDepIsTactile(String depIsTactile) {
        this.depIsTactile = depIsTactile;
    }

    public String getDepUser() {
        return depUser;
    }

    public void setDepUser(String depUser) {
        this.depUser = depUser;
    }

    public String getDepStation() {
        return depStation;
    }

    public void setDepStation(String depStation) {
        this.depStation = depStation;
    }

    public String getDepExport() {
        return depExport;
    }

    public void setDepExport(String depExport) {
        this.depExport = depExport;
    }

    public String getDepDDm() {
        return depDDm;
    }

    public void setDepDDm(String depDDm) {
        this.depDDm = depDDm;
    }

    @NonNull
    public String getDepCodeM() {
        return depCodeM;
    }

    public void setDepCodeM(@NonNull String depCodeM) {
        this.depCodeM = depCodeM;
    }

    @Override
    public String toString() {
        return "DEPENCE{" +
                "depCode='" + depCode + '\'' +
                ", depLib='" + depLib + '\'' +
                ", depIsTactile='" + depIsTactile + '\'' +
                ", depUser='" + depUser + '\'' +
                ", depStation='" + depStation + '\'' +
                ", depExport='" + depExport + '\'' +
                ", depDDm=" + depDDm +
                '}';
    }
}