package com.asmtunis.procaissemobility.data.network.datamanager;


import static com.asmtunis.procaissemobility.helper.Globals.CHECK_LICENCE_BASEURL;
import static com.asmtunis.procaissemobility.helper.Globals.GET_BASE_CONFIG_BASEURL;
import static com.asmtunis.procaissemobility.helper.Globals.GET_BASE_CONFIG_URL;

import com.asmtunis.procaissemobility.data.models.Connexion;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.BaseConfigService;

import java.util.List;

import license.model.Activation;

//import com.blankj.utilcode.util.AppUtils;

/**
 * Created by Achraf on 25/09/2017.
 */

public class BaseConfigDataManager {

    private static BaseConfigDataManager sInstance;
    public static String ASM_BASE_URL = "http://as.asmhost.net/licence/public/api/";

    private final BaseConfigService baseConfigService;

    public BaseConfigDataManager() {
        if(GET_BASE_CONFIG_URL.equals(""))   baseConfigService = new ServiceFactory<>(BaseConfigService.class, "https://as.asmhost.net/licence/reclamation/WS/").makeService();
        else  baseConfigService = new ServiceFactory<>(BaseConfigService.class, GET_BASE_CONFIG_BASEURL).makeService();

    }

    public static BaseConfigDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new BaseConfigDataManager();
        }
        return sInstance;
    }

    public void getBaseConfig(Activation activation, RemoteCallback<List<Connexion>> listener) {

        if(GET_BASE_CONFIG_URL.equals(""))    baseConfigService.getBaseConfig(activation.getIdDevice(), "ProCaisse Mobility")
                .enqueue(listener);
        else baseConfigService.getBaseConfigBackUp(GET_BASE_CONFIG_URL,activation.getIdDevice(), "ProCaisse Mobility")
                .enqueue(listener);
    }

    public void getBaseConfigById(Connexion connexion, RemoteCallback<Connexion> listener) {
    BaseConfigService baseConfigService = new ServiceFactory<>(BaseConfigService.class, ASM_BASE_URL).makeService();
        baseConfigService.getBaseConfigsById(connexion)
                .enqueue(listener);
    }

}
