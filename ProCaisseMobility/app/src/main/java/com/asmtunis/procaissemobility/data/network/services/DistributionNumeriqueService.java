package com.asmtunis.procaissemobility.data.network.services;

import com.asmtunis.procaissemobility.data.models.DNFamille;
import com.asmtunis.procaissemobility.data.models.DNResponseBatchData;
import com.asmtunis.procaissemobility.data.models.DNSuperficie;
import com.asmtunis.procaissemobility.data.models.DNTypePVente;
import com.asmtunis.procaissemobility.data.models.DNTypeServices;
import com.asmtunis.procaissemobility.data.models.DNVIsite;
import com.asmtunis.procaissemobility.data.models.DN_LigneVisite;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Ticket;
import com.asmtunis.procaissemobility.data.models.TicketUpdate;
import com.asmtunis.procaissemobility.data.models.VCNewProduct;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.HTTP;
import retrofit2.http.Headers;
import retrofit2.http.POST;
import retrofit2.http.Query;

public interface DistributionNumeriqueService {


    @POST("getAllTypeService")
    Call<List<DNTypeServices>> getAllTypeService(@Body GenericObject genericObject);

    @POST("getAllSuperficie")
    Call<List<DNSuperficie>>  getAllSuperficie(@Body GenericObject genericObject);


    @POST("getAllTypePVente")
    Call<List<DNTypePVente>> getAllTypePVente(@Body GenericObject genericObject);

    @POST("getAllVisite")
    Call<List<DNVIsite>> getAllVisite(@Body GenericObject genericObject);

    @POST("getAllVisite")
    Call<List<DNVIsite>> getAllVisiteByUser(@Body GenericObject genericObject, @Query("user") String user);



    @POST("getAllLigneVisite")
    Call<List<DN_LigneVisite>> getAllLigneVisiteByUser(@Body GenericObject genericObject, @Query("user") String user);


    @POST("getVisiteByCode")
    Call<DNVIsite> getVisiteByCode(@Body GenericObject visiteBycode);


    @POST("getAllFamille")
    Call<List<DNFamille>> getAllFamille(@Body GenericObject dnfamille);


    @POST("addBatchVisite")
    Call<List<DNResponseBatchData>> addBatchVisite(@Body GenericObject dnvsite);



    @HTTP(method = "DELETE", path = "deleteVisite", hasBody = true)
    Call<List<DNResponseBatchData>> deleteVisite(@Body GenericObject object);







}
