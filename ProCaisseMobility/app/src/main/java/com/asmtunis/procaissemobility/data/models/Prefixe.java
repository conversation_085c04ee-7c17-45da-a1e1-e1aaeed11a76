package com.asmtunis.procaissemobility.data.models;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;
import androidx.annotation.NonNull;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

@Entity
public class Prefixe extends BaseModel{
	@PrimaryKey
	@NonNull
	@ColumnInfo(name = "PRE_Id_table")
	@SerializedName("PRE_Id_table")
	@Expose
	public String pREIdTable;
	@ColumnInfo(name = "PRE_Designation")

	@SerializedName("PRE_Prefixe")
	@Expose
	public String pREPrefixe;
	@ColumnInfo(name = "PRE_Prefixe")

	@SerializedName("PRE_Designation")
	@Expose
	public String pREDesignation;




	public Prefixe() {
	}


	@Ignore
	public Prefixe(@NonNull String pREIdTable, String pREDesignation, String pREPrefixe) {
		this.pREIdTable = pREIdTable;
		this.pREDesignation = pREDesignation;
		this.pREPrefixe = pREPrefixe;
	}



	@NonNull
	public String getpREIdTable() {
		return pREIdTable;
	}

	public void setpREIdTable(@NonNull String pREIdTable) {
		this.pREIdTable = pREIdTable;
	}

	public String getpREPrefixe() {
		return pREPrefixe;
	}

	public void setpREPrefixe(String pREPrefixe) {
		this.pREPrefixe = pREPrefixe;
	}

	public String getpREDesignation() {
		return pREDesignation;
	}

	public void setpREDesignation(String pREDesignation) {
		this.pREDesignation = pREDesignation;
	}
}