package com.asmtunis.procaissemobility.data.network.services;

import com.asmtunis.procaissemobility.data.models.CarteResto;
import com.asmtunis.procaissemobility.data.models.GenericObject;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * Created by PC on 10/12/2017.
 */

public interface CarteRestoService {

    @POST("getCartesResto")
    Call<List<CarteResto>> getCartesResto(@Body GenericObject genericObject);

    @POST("getCarteRestoByCode")
    Call<CarteResto> getCarteRestoByCode(@Body GenericObject genericObject);

}
