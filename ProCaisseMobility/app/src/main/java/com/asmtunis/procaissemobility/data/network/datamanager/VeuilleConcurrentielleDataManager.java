package com.asmtunis.procaissemobility.data.network.datamanager;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.VCAutre;
import com.asmtunis.procaissemobility.data.models.VCAutreDeleteResponse;
import com.asmtunis.procaissemobility.data.models.VCImage;
import com.asmtunis.procaissemobility.data.models.VCListeConcurrent;
import com.asmtunis.procaissemobility.data.models.VCNewProduct;
import com.asmtunis.procaissemobility.data.models.VCPrix;
import com.asmtunis.procaissemobility.data.models.VCPromo;
import com.asmtunis.procaissemobility.data.models.VCTypeCommunication;
import com.asmtunis.procaissemobility.data.models.VcResponseBatchData;
import com.asmtunis.procaissemobility.data.models.uploadImageResponse;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.VeuilleConcurrentielleService;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;

import java.util.List;

/**
 * Created by Oussama AZIZI on 6/24/22.
 */

public class VeuilleConcurrentielleDataManager {

    private static VeuilleConcurrentielleDataManager sInstance;
    private final VeuilleConcurrentielleService veuilleConcurrentielleService;

    public VeuilleConcurrentielleDataManager() {
        veuilleConcurrentielleService = new ServiceFactory<>(VeuilleConcurrentielleService.class,
                String.format(BASE_URL, PrefUtils.getServerIPAddress(), PrefUtils.getServerPort(),"VConcu")).makeService();
    }

    public static VeuilleConcurrentielleDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new VeuilleConcurrentielleDataManager();
        }
        return sInstance;
    }

    public void getVCNewProducts(GenericObject genericObject,
                                     RemoteCallback<List<VCNewProduct>> listener) {
        veuilleConcurrentielleService.getVCNewProduct(genericObject, App.prefUtils.getUserId())
                .enqueue(listener);
    }

    public void getVCPromos(GenericObject genericObject,
                                 RemoteCallback<List<VCPromo>> listener) {
        veuilleConcurrentielleService.getVCPromo(genericObject, App.prefUtils.getUserId())
                .enqueue(listener);
    }

    public void getVCPrices(GenericObject genericObject,
                            RemoteCallback<List<VCPrix>> listener) {
        veuilleConcurrentielleService.getVCPrix(genericObject, App.prefUtils.getUserId())
                .enqueue(listener);
    }

    public void getVCautres(GenericObject genericObject,
                            RemoteCallback<List<VCAutre>> listener) {
        veuilleConcurrentielleService.getVCAutre(genericObject, App.prefUtils.getUserId())
                .enqueue(listener);
    }

    public void getVCListeConcurrents(GenericObject genericObject,
                            RemoteCallback<List<VCListeConcurrent>> listener) {
        veuilleConcurrentielleService.getVCListeConcurrent(genericObject)
                .enqueue(listener);
    }

    public void getVCTypeCommunications(GenericObject genericObject,
                                      RemoteCallback<List<VCTypeCommunication>> listener) {
        veuilleConcurrentielleService.getVCTypeCommunication(genericObject)
                .enqueue(listener);
    }

    public void getVCImages(GenericObject genericObject,
                                        RemoteCallback<List<VCImage>> listener) {
        veuilleConcurrentielleService.getVCImage(genericObject)
                .enqueue(listener);
    }
    public void addVCImages(GenericObject genericObject,RemoteCallback <List<uploadImageResponse>> listener){
        veuilleConcurrentielleService.addBatchVCImage(genericObject).enqueue(listener);
    }

    public void addBatchVC(GenericObject genericObject, RemoteCallback <List<VcResponseBatchData>> listener, String table){
        veuilleConcurrentielleService.addBatchDataVConcu(genericObject,table).enqueue(listener);
    }

    public void deleteBatchVCAutre(GenericObject genericObject, RemoteCallback <List<VCAutreDeleteResponse>> listener, String table){
        veuilleConcurrentielleService.deleteBatchDataVConcuAutre(genericObject,table).enqueue(listener);
    }



    public void deleteBatchVCPrix(GenericObject genericObject, RemoteCallback <List<VCAutreDeleteResponse>> listener, String table){
        veuilleConcurrentielleService.deleteBatchDataVConcuPrix(genericObject,table).enqueue(listener);
    }


    public void deleteBatchVCPromotion(GenericObject genericObject, RemoteCallback <List<VCAutreDeleteResponse>> listener, String table){
        veuilleConcurrentielleService.deleteBatchDataVConcuPromotion(genericObject,table).enqueue(listener);
    }


    public void deleteBatchVCNewProduct(GenericObject genericObject, RemoteCallback <List<VCAutreDeleteResponse>> listener, String table){
        veuilleConcurrentielleService.deleteBatchDataVConcuNewProduct(genericObject,table).enqueue(listener);
    }

}
