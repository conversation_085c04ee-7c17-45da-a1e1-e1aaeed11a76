package com.asmtunis.procaissemobility.helper;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.BonRetour;
import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.data.models.Ticket;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;

import java.util.List;

public class ClientHelper {

    public static double calculateSold(Client client, String exercie) {
        double res = 0.0;
        res -= getCreditMnt(client, exercie);
        res += getToRetunrnMnt(client, exercie);
        return StringUtils.decimalFormat(res);
    }

    public static double getToRetunrnMnt(Client client, String exercie) {
        double res = 0.0;
        List<BonRetour> retours = App.database.bonRetourDAO().getClientSolde(client.cLICode, exercie);
        for (BonRetour bonRetour : retours) {
            res += App.database.ligneBonRetourDAO().getSumPriceByBr(bonRetour.getBORNumero(), exercie);
        }
        return StringUtils.decimalFormat(res + App.database.reglementCaisseDAO().getSumMnttcByClient(client.cLICode, exercie));
    }

    public static double getCreditMnt(Client client, String exercie) {
        double res = 0.0;
        List<Ticket> tickets = App.database.ticketDAO().getClientCredit(client.cLICode, exercie);
        for (Ticket ticket : tickets) {
            res += ticket.tIKMtTTC;
        }
        return StringUtils.decimalFormat(res);
    }

}
