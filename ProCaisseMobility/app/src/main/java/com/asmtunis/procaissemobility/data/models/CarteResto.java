package com.asmtunis.procaissemobility.data.models;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;
import androidx.annotation.NonNull;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * Created by PC on 10/25/2017.
 */
@Entity
public class CarteResto extends BaseModel{
    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "Code")
    @SerializedName("Code")
    @Expose
    public String code;
    @ColumnInfo(name = "Societe")
    @SerializedName("Societe")
    @Expose
    public String societe;

    public CarteResto() {
    }
    @Ignore
    public CarteResto(String code, String societe) {
        this.code = code;
        this.societe = societe;
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getSociete() {
        return societe;
    }

    public void setSociete(String societe) {
        this.societe = societe;
    }

    @Override
    public String toString() {
        return "CarteResto{" +
                "code='" + code + '\'' +
                ", societe='" + societe + '\'' +
                '}';
    }
}
