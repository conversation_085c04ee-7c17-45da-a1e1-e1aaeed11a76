package com.asmtunis.procaissemobility.data.models;

import androidx.room.Entity;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 14/09/2017.
 */
@Entity
public class UniteArticle extends BaseModel{

    @SerializedName("UNITE_ARTICLE_CodeUnite")
    @Expose
    private String uNITEARTICLECodeUnite;
    @SerializedName("UNITE_ARTICLE_CodeArt")
    @Expose
    private String uNITEARTICLECodeArt;
    @SerializedName("UNITE_ARTICLE_QtePiece")
    @Expose
    private String uNITEARTICLEQtePiece;
    @SerializedName("UNITE_ARTICLE_IsUnitaire")
    @Expose
    private String uNITEARTICLEIsUnitaire;
    @SerializedName("UNITE_ARTICLE_PrixVenteTTC")
    @Expose
    private String uNITEARTICLEPrixVenteTTC;
    @SerializedName("UNITE_ARTICLE_TypePrixVente")
    @Expose
    private Object uNITEARTICLETypePrixVente;
    @SerializedName("UNITE_ARTICLE_user")
    @Expose
    private Object uNITEARTICLEUser;
    @SerializedName("UNITE_ARTICLE_station")
    @Expose
    private Object uNITEARTICLEStation;
    @SerializedName("UNITE_ARTICLE_export")
    @Expose
    private String uNITEARTICLEExport;
    @SerializedName("UNITE_ARTICLE_DDm")
    @Expose
    private String uNITEARTICLEDDm;
    @SerializedName("exportM")
    @Expose
    private Object exportM;
    @SerializedName("DDmM")
    @Expose
    private Object dDmM;

    public String getUNITEARTICLECodeUnite() {
        return uNITEARTICLECodeUnite;
    }

    public void setUNITEARTICLECodeUnite(String uNITEARTICLECodeUnite) {
        this.uNITEARTICLECodeUnite = uNITEARTICLECodeUnite;
    }

    public String getUNITEARTICLECodeArt() {
        return uNITEARTICLECodeArt;
    }

    public void setUNITEARTICLECodeArt(String uNITEARTICLECodeArt) {
        this.uNITEARTICLECodeArt = uNITEARTICLECodeArt;
    }

    public String getUNITEARTICLEQtePiece() {
        return uNITEARTICLEQtePiece;
    }

    public void setUNITEARTICLEQtePiece(String uNITEARTICLEQtePiece) {
        this.uNITEARTICLEQtePiece = uNITEARTICLEQtePiece;
    }

    public String getUNITEARTICLEIsUnitaire() {
        return uNITEARTICLEIsUnitaire;
    }

    public void setUNITEARTICLEIsUnitaire(String uNITEARTICLEIsUnitaire) {
        this.uNITEARTICLEIsUnitaire = uNITEARTICLEIsUnitaire;
    }

    public String getUNITEARTICLEPrixVenteTTC() {
        return uNITEARTICLEPrixVenteTTC;
    }

    public void setUNITEARTICLEPrixVenteTTC(String uNITEARTICLEPrixVenteTTC) {
        this.uNITEARTICLEPrixVenteTTC = uNITEARTICLEPrixVenteTTC;
    }

    public Object getUNITEARTICLETypePrixVente() {
        return uNITEARTICLETypePrixVente;
    }

    public void setUNITEARTICLETypePrixVente(Object uNITEARTICLETypePrixVente) {
        this.uNITEARTICLETypePrixVente = uNITEARTICLETypePrixVente;
    }

    public Object getUNITEARTICLEUser() {
        return uNITEARTICLEUser;
    }

    public void setUNITEARTICLEUser(Object uNITEARTICLEUser) {
        this.uNITEARTICLEUser = uNITEARTICLEUser;
    }

    public Object getUNITEARTICLEStation() {
        return uNITEARTICLEStation;
    }

    public void setUNITEARTICLEStation(Object uNITEARTICLEStation) {
        this.uNITEARTICLEStation = uNITEARTICLEStation;
    }

    public String getUNITEARTICLEExport() {
        return uNITEARTICLEExport;
    }

    public void setUNITEARTICLEExport(String uNITEARTICLEExport) {
        this.uNITEARTICLEExport = uNITEARTICLEExport;
    }

    public String getUNITEARTICLEDDm() {
        return uNITEARTICLEDDm;
    }

    public void setUNITEARTICLEDDm(String uNITEARTICLEDDm) {
        this.uNITEARTICLEDDm = uNITEARTICLEDDm;
    }

    public Object getExportM() {
        return exportM;
    }

    public void setExportM(Object exportM) {
        this.exportM = exportM;
    }

    public Object getDDmM() {
        return dDmM;
    }

    public void setDDmM(Object dDmM) {
        this.dDmM = dDmM;
    }
}
