package com.asmtunis.procaissemobility.data.viewModels;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelProviders;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.dao.VCPromosDAO;
import com.asmtunis.procaissemobility.data.models.VCPromo;

import java.util.List;

public class VCPromoViewModel extends ViewModel {
    public VCPromosDAO vcPromosDAO;
    private static VCPromoViewModel instance;

    public static VCPromoViewModel getInstance(Fragment fragment){
        if(instance==null){
            instance = new ViewModelProvider(fragment).get(VCPromoViewModel.class);
            instance.vcPromosDAO= App.database.vcPromosDAO();

        }
        return instance;
    }


     /*  public static DNVisiteViewModel getInstance(Fragment activity) {
        if (instance == null)
            instance = ViewModelProviders.of(activity).get(DNVisiteViewModel.class);
        instance.dnVisiteDAO = App.database.dnVisitesDAO();
        return instance;
    }*/

    public static VCPromoViewModel getInstance(FragmentActivity activity) {
        if (instance == null)
            instance = new ViewModelProvider(activity).get(VCPromoViewModel.class);
        instance.vcPromosDAO = App.database.vcPromosDAO();
        return instance;
    }

    public LiveData<Integer> getNoSyncCountMubtale(){
        return vcPromosDAO.getNoSyncCountMubtale();
    }

    public LiveData<Integer> getNoSyncCountMubtaleToDelete(){
        return vcPromosDAO.getCountNoSyncedToDeleteMubtale();
    }


    public LiveData<List<VCPromo>> getAllPromos(){
        return vcPromosDAO.getAll();
    }
}
