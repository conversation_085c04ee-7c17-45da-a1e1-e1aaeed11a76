package com.asmtunis.procaissemobility.listener;

import androidx.appcompat.widget.PopupMenu;
import androidx.appcompat.widget.Toolbar;
import android.view.MenuItem;

import com.asmtunis.procaissemobility.listener.MenuItemsAction;

/**
 * Created by PC on 11/29/2017.
 */

public class MenuItemClickListener<T> implements PopupMenu.OnMenuItemClickListener, Toolbar
        .OnMenuItemClickListener {

    int position;
    MenuItemsAction menuItemsAction;
    T item;

    public MenuItemClickListener(int position, MenuItemsAction menuItemsAction) {
        this.position = position;
        this.menuItemsAction = menuItemsAction;
    }

    public MenuItemClickListener(T item,MenuItemsAction menuItemsAction) {
        this.item = item;
        this.menuItemsAction = menuItemsAction;
    }


    @Override
    public boolean onMenuItemClick(MenuItem menuItem) {
       return menuItemsAction.itemsAction(menuItem, item);
    }


}
