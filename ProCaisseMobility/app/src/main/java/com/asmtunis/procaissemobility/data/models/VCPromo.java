package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * Created by Oussama AZIZI on 6/24/22.
 */

@Entity
public class VCPromo extends BaseModel{

    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "CodeVCPromo")
    @SerializedName("CodeVCPromo")
    @Expose
    private String codeVCPromo;

    @ColumnInfo(name = "CodeVCPromoM")
    @SerializedName("CodeVCPromoM")
    @Expose
    private String codeVCPromoM;

    @ColumnInfo(name = "CodeArtLocal")
    @SerializedName("CodeArtLocal")
    @Expose
    private String codeArtLocal;

    @ColumnInfo(name = "ArticleConcur")
    @SerializedName("ArticleConcur")
    @Expose
    private String articleConcur;

    @ColumnInfo(name = "DateOp")
    @SerializedName("DateOp")
    @Expose
    private String dateOp;

    @ColumnInfo(name = "CodeConcur")
    @SerializedName("CodeConcur")
    @Expose
    private String codeConcur;

    @ColumnInfo(name = "NoteOp")
    @SerializedName("NoteOp")
    @Expose
    private String noteOp;

    @ColumnInfo(name = "PrixConcur")
    @SerializedName("PrixConcur")
    @Expose
    private Double prixConcur;

    @ColumnInfo(name = "TauxPromo")
    @SerializedName("TauxPromo")
    @Expose
    private Double tauxPromo;

    @ColumnInfo(name = "CodeUser")
    @SerializedName("CodeUser")
    @Expose
    private Integer codeUser;

    @ColumnInfo(name = "InfoOp1")
    @SerializedName("InfoOp1")
    @Expose
    private String infoOp1;

    @ColumnInfo(name = "CodeTypeCom")
    @SerializedName("CodeTypeCom")
    @Expose
    private String codeTypeCom;

    public String getCodeVCPromo() {
        return codeVCPromo;
    }

    public void setCodeVCPromo(String codeVCPromo) {
        this.codeVCPromo = codeVCPromo;
    }

    public String getCodeVCPromoM() {
        return codeVCPromoM;
    }

    public void setCodeVCPromoM(String codeVCPromoM) {
        this.codeVCPromoM = codeVCPromoM;
    }

    public String getCodeArtLocal() {
        return codeArtLocal;
    }

    public void setCodeArtLocal(String codeArtLocal) {
        this.codeArtLocal = codeArtLocal;
    }

    public String getArticleConcur() {
        return articleConcur;
    }

    public void setArticleConcur(String articleConcur) {
        this.articleConcur = articleConcur;
    }

    public String getDateOp() {
        return dateOp;
    }

    public void setDateOp(String dateOp) {
        this.dateOp = dateOp;
    }

    public String getCodeConcur() {
        return codeConcur;
    }

    public void setCodeConcur(String codeConcur) {
        this.codeConcur = codeConcur;
    }

    public String getNoteOp() {
        return noteOp;
    }

    public void setNoteOp(String noteOp) {
        this.noteOp = noteOp;
    }

    public Double getPrixConcur() {
        return prixConcur;
    }

    public void setPrixConcur(Double prixConcur) {
        this.prixConcur = prixConcur;
    }

    public Double getTauxPromo() {
        return tauxPromo;
    }

    public void setTauxPromo(Double tauxPromo) {
        this.tauxPromo = tauxPromo;
    }

    public int getCodeUser() {
        return codeUser;
    }

    public void setCodeUser(int codeUser) {
        this.codeUser = codeUser;
    }

    public String getInfoOp1() {
        return infoOp1;
    }

    public void setInfoOp1(String infoOp1) {
        this.infoOp1 = infoOp1;
    }

    public String getCodeTypeCom() {
        return codeTypeCom;
    }

    public void setCodeTypeCom(String codeTypeCom) {
        this.codeTypeCom = codeTypeCom;
    }



    public VCPromo(String codeVCPromo) {
        this.codeVCPromo = codeVCPromo;


    }

    public VCPromo() {


    }
}
