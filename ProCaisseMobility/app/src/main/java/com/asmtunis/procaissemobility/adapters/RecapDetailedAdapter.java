package com.asmtunis.procaissemobility.adapters;

import android.annotation.SuppressLint;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.data.models.LigneTicket;
import com.asmtunis.procaissemobility.data.models.Ticket;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;

import java.util.List;

/**
 * Created by WAEL on 9/8/22.
 */
public class RecapDetailedAdapter extends RecyclerView.Adapter<RecapDetailedAdapter.ViewHolder>{
    private List<Ticket> ticketList;
    private List<List<LigneTicket>> ligneTicketList;
    boolean invoiced = false;
    String numFact = "";

   // RecyclerView recyclerView;
    public RecapDetailedAdapter(List<Ticket> ticketList, List<List<LigneTicket>> ligneTicketList) {
        this.ticketList = ticketList;
        this.ligneTicketList = ligneTicketList;
    }
    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        LayoutInflater layoutInflater = LayoutInflater.from(parent.getContext());
        View listItem= layoutInflater.inflate(R.layout.recap_detailed_list_item, parent, false);
        ViewHolder viewHolder = new ViewHolder(listItem);
        return viewHolder;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {
 /*       for (LigneTicket ligneTicket: ligneTicketList.get(position)) {
            if(ligneTicket.lTnumFacture != null) {
                invoiced = true;
                numFact = ligneTicket.lTnumFacture;
            }
        }
*/
        if (ticketList.get(position).tIKNumeroBL!=null){
            invoiced = true;
            numFact = ticketList.get(position).tIKNumeroBL;
        }
        int nbCharAllowed = 48;
        String num = holder.itemView.getResources().getString(R.string.number_field_recap) + " " + (!invoiced ? ticketList.get(position).tikNumTicketM : numFact);

        holder.blFactRecap.setText(!invoiced ? holder.itemView.getResources().getString(R.string.bon_livraison_recap) : holder.itemView.getResources().getString(R.string.facture_recap));
        holder.numRecap.setText(num);

       /* Date today = DateUtils.strToDate(ticketList.get(position).gettIKDateHeureTicket(), "yyyy-MM-dd HH:mm");
        DateFormat date = new SimpleDateFormat("dd/MM/yyyy");
        DateFormat time = new SimpleDateFormat("HH:mm");

        if (etablisement != null) {
            printText("Date: " + date.format(today) + " " + time.format(today) + " ", 0, 1, false, false, 0);
        }*/
        holder.dateRecap.setText(holder.itemView.getResources().getString(R.string.date_recap) + " " + ticketList.get(position).gettIKDateHeureTicket());
        holder.clientRecap.setText(holder.itemView.getResources().getString(R.string.client_recap) + " " + ticketList.get(position).gettIKNomClient());
        holder.codeClientRecap.setText(holder.itemView.getResources().getString(R.string.code_client_recap) + " " + ticketList.get(position).gettIKCodClt());
        holder.amountTTcRecap.setText(holder.itemView.getResources().getString(R.string.amount_ttc_recap) + " " + StringUtils.priceFormat(ticketList.get(position).gettIKMtTTC()) +  " ");
    }
//
//
    @Override
    public int getItemCount() {
        return ticketList.size();
    }
//
    public static class ViewHolder extends RecyclerView.ViewHolder {
        public TextView blFactRecap;
        public TextView numRecap;
        public TextView dateRecap;
        public TextView clientRecap;
        public TextView codeClientRecap;
        public TextView amountTTcRecap;
        public ViewHolder(View itemView) {
            super(itemView);
            this.blFactRecap = (TextView) itemView.findViewById(R.id.blFactRecap);
            this.numRecap = (TextView) itemView.findViewById(R.id.numRecap);
            this.dateRecap = (TextView) itemView.findViewById(R.id.dateRecap);
            this.clientRecap = (TextView) itemView.findViewById(R.id.clientRecap);
            this.codeClientRecap = (TextView) itemView.findViewById(R.id.codeClientRecap);
            this.amountTTcRecap = (TextView) itemView.findViewById(R.id.amountTTcRecap);
        }
    }
}