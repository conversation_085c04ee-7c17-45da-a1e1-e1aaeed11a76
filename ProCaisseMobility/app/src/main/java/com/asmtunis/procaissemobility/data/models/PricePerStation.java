package com.asmtunis.procaissemobility.data.models;

import androidx.room.ColumnInfo;
import androidx.room.Entity;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import org.jetbrains.annotations.NotNull;

@Entity(primaryKeys = {"UNITE_ARTICLE_CodeUnite", "UNITE_ARTICLE_CodeArt", "UNITE_ARTICLE_station"})
public class PricePerStation {

    @SerializedName("UNITE_ARTICLE_CodeUnite")
    @ColumnInfo(name = "UNITE_ARTICLE_CodeUnite")
    @Expose
    @NotNull
    private String uNITEARTICLECodeUnite;
    @SerializedName("UNITE_ARTICLE_CodeArt")
    @ColumnInfo(name = "UNITE_ARTICLE_CodeArt")
    @Expose
    @NotNull
    private String uNITEARTICLECodeArt;
    @SerializedName("UNITE_ARTICLE_QtePiece")
    @ColumnInfo(name = "UNITE_ARTICLE_QtePiece")
    @Expose
    private String uNITEARTICLEQtePiece;
    @SerializedName("UNITE_ARTICLE_PrixVenteTTCs")
    @ColumnInfo(name = "UNITE_ARTICLE_PrixVenteTTCs")
    @Expose
    private String uNITEARTICLEPrixVenteTTCs;
    @SerializedName("UNITE_ARTICLE_user")
    @ColumnInfo(name = "UNITE_ARTICLE_user")
    @Expose
    private String uNITEARTICLEUser;
    @SerializedName("UNITE_ARTICLE_station")
    @ColumnInfo(name = "UNITE_ARTICLE_station")
    @Expose
    @NotNull
    private String uNITEARTICLEStation;
    @SerializedName("UNITE_ARTICLE_export")
    @ColumnInfo(name = "UNITE_ARTICLE_export")
    @Expose
    private String uNITEARTICLEExport;
    @SerializedName("UNITE_ARTICLE_DDm")
    @ColumnInfo(name = "UNITE_ARTICLE_DDm")
    @Expose
    private String uNITEARTICLEDDm;
    @SerializedName("prix_is_unitaire")
    @ColumnInfo(name = "prix_is_unitaire")
    @Expose
    private String prixIsUnitaire;
    @SerializedName("PrixG1")
    @ColumnInfo(name = "PrixG1")
    @Expose
    private double prixG1;
    @SerializedName("PrixG2")
    @ColumnInfo(name = "PrixG2")
    @Expose
    private double prixG2;
    @SerializedName("PrixG3")
    @ColumnInfo(name = "PrixG3")
    @Expose
    private double prixG3;
    @SerializedName("Taux_remise")
    @ColumnInfo(name = "Taux_remise")
    @Expose
    private double tauxRemise;
    @SerializedName("Taux_Promo")
    @ColumnInfo(name = "Taux_Promo")
    @Expose
    private double tauxPromo;

    public String getUNITEARTICLECodeUnite() {
        return uNITEARTICLECodeUnite;
    }

    public void setUNITEARTICLECodeUnite(String uNITEARTICLECodeUnite) {
        this.uNITEARTICLECodeUnite = uNITEARTICLECodeUnite;
    }

    public String getUNITEARTICLECodeArt() {
        return uNITEARTICLECodeArt;
    }

    public void setUNITEARTICLECodeArt(String uNITEARTICLECodeArt) {
        this.uNITEARTICLECodeArt = uNITEARTICLECodeArt;
    }

    public String getUNITEARTICLEQtePiece() {
        return uNITEARTICLEQtePiece;
    }

    public void setUNITEARTICLEQtePiece(String uNITEARTICLEQtePiece) {
        this.uNITEARTICLEQtePiece = uNITEARTICLEQtePiece;
    }

    public String getUNITEARTICLEPrixVenteTTCs() {
        return uNITEARTICLEPrixVenteTTCs;
    }

    public void setUNITEARTICLEPrixVenteTTCs(String uNITEARTICLEPrixVenteTTCs) {
        this.uNITEARTICLEPrixVenteTTCs = uNITEARTICLEPrixVenteTTCs;
    }

    public String getUNITEARTICLEUser() {
        return uNITEARTICLEUser;
    }

    public void setUNITEARTICLEUser(String uNITEARTICLEUser) {
        this.uNITEARTICLEUser = uNITEARTICLEUser;
    }

    public String getUNITEARTICLEStation() {
        return uNITEARTICLEStation;
    }

    public void setUNITEARTICLEStation(String uNITEARTICLEStation) {
        this.uNITEARTICLEStation = uNITEARTICLEStation;
    }

    public String getUNITEARTICLEExport() {
        return uNITEARTICLEExport;
    }

    public void setUNITEARTICLEExport(String uNITEARTICLEExport) {
        this.uNITEARTICLEExport = uNITEARTICLEExport;
    }

    public String getUNITEARTICLEDDm() {
        return uNITEARTICLEDDm;
    }

    public void setUNITEARTICLEDDm(String uNITEARTICLEDDm) {
        this.uNITEARTICLEDDm = uNITEARTICLEDDm;
    }

    public String getPrixIsUnitaire() {
        return prixIsUnitaire;
    }

    public void setPrixIsUnitaire(String prixIsUnitaire) {
        this.prixIsUnitaire = prixIsUnitaire;
    }

    public double getPrixG1() {
        return prixG1;
    }

    public void setPrixG1(double prixG1) {
        this.prixG1 = prixG1;
    }

    public double getPrixG2() {
        return prixG2;
    }

    public void setPrixG2(double prixG2) {
        this.prixG2 = prixG2;
    }

    public double getPrixG3() {
        return prixG3;
    }

    public void setPrixG3(double prixG3) {
        this.prixG3 = prixG3;
    }

    public double getTauxRemise() {
        return tauxRemise;
    }

    public void setTauxRemise(double tauxRemise) {
        this.tauxRemise = tauxRemise;
    }

    public double getTauxPromo() {
        return tauxPromo;
    }

    public void setTauxPromo(double tauxPromo) {
        this.tauxPromo = tauxPromo;
    }

    @Override
    public String toString() {
        return "PricePerStation{" +
                "uNITEARTICLECodeUnite='" + uNITEARTICLECodeUnite + '\'' +
                ", uNITEARTICLECodeArt='" + uNITEARTICLECodeArt + '\'' +
                ", uNITEARTICLEQtePiece='" + uNITEARTICLEQtePiece + '\'' +
                ", uNITEARTICLEPrixVenteTTCs='" + uNITEARTICLEPrixVenteTTCs + '\'' +
                ", uNITEARTICLEUser='" + uNITEARTICLEUser + '\'' +
                ", uNITEARTICLEStation='" + uNITEARTICLEStation + '\'' +
                ", uNITEARTICLEExport='" + uNITEARTICLEExport + '\'' +
                ", uNITEARTICLEDDm='" + uNITEARTICLEDDm + '\'' +
                ", prixIsUnitaire='" + prixIsUnitaire + '\'' +
                ", prixG1=" + prixG1 +
                ", prixG2=" + prixG2 +
                ", prixG3=" + prixG3 +
                ", tauxRemise=" + tauxRemise +
                ", tauxPromo=" + tauxPromo +
                '}';
    }
}
