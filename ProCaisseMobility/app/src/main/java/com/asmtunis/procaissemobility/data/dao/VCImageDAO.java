package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.VCImage;
import com.asmtunis.procaissemobility.data.models.VCNewProduct;

import java.util.List;

/**
 * Created by Oussama AZIZI on 6/24/22.
 */

@Dao
public interface VCImageDAO {
    @Query("SELECT * FROM VCImage order by strftime('%Y-%m-%d %H-%M',DateOp) desc")
    LiveData<List<VCImage>> getAll();

    @Query("SELECT * FROM VCImage WHERE Code_TypeVC =:code order by strftime('%Y-%m-%d %H-%M',DateOp) desc")
    LiveData<List<VCImage>> getImageByCode(String code);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<VCImage> vcImages);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(VCImage vcImages);

    @Query("SELECT count(*) FROM VCImage where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    LiveData<Integer> getNoSyncCountMubtale();

    @Query("SELECT * FROM VCImage where isSync=0 and  (Status='INSERTED'  or Status='UPDATED') ")
    List<VCImage> getNoSynced();


    @Query("UPDATE VCImage SET Status = 'SELECTED', isSync = 1 where Code_Mob  = :codeMob")
    void setSynced(String codeMob);

    @Query("delete from VCImage")
    void deleteAll();
}
