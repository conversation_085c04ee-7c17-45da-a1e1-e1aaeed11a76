package com.asmtunis.procaissemobility.adapters.items;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.RecyclerView;
import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.data.models.Caisse;
import com.mikepenz.fastadapter.items.AbstractItem;
import com.rengwuxian.materialedittext.MaterialEditText;
import net.cachapa.expandablelayout.ExpandableLayout;
import java.util.List;
import butterknife.BindView;
import butterknife.ButterKnife;
import static android.content.Context.LAYOUT_INFLATER_SERVICE;

/**
 * Created by PC on 10/11/2017.
 */
public class CaisseItem extends AbstractItem<CaisseItem, CaisseItem.ViewHolder> {

    private  Context context;
    public Caisse caisse;

    public CaisseItem(Context context, Caisse caisse) {
        this.caisse = caisse;
        this.context = context;
    }

    //The unique ID for this type of item
    @Override
    public int getType() {
        return R.id.fastadapter_caisse_item_id;
    }

    //The unit_price_dialog to be used for this type of item
    @Override
    public int getLayoutRes() {
        return R.layout.list_item;
    }

    //The logic to bind your data to the view
    @Override
    public void bindView(final ViewHolder viewHolder, List<Object> payloads) {
        //call super so the selection is already handled for you
        super.bindView(viewHolder, payloads);
        //bind our data
        //set the text for the name
        viewHolder.toolbar.setTitle(caisse.getcAIDesCaisse());
        //set the text for the description or hide
        //  viewHolder.caisse.setText(description);
        viewHolder.toolbar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.d("logexpend", "test");
            }
        });
    }

    //reset the view here (this is an optional method, but recommended)
    @Override
    public void unbindView(final ViewHolder holder) {
        super.unbindView(holder);
        holder.toolbar.setTitle(null);
        holder.toolbar.setSubtitle(null);
    }

    //Init the viewHolder for this Item
    @Override
    public ViewHolder getViewHolder(View v) {
        return new ViewHolder(v);
    }

    //The viewHolder used for this item. This viewHolder is always reused by the RecyclerView so scrolling is blazing fast
    protected  class ViewHolder extends RecyclerView.ViewHolder {

        @BindView(R.id.expandable_layout)
        ExpandableLayout expandableLayout;
        @BindView(R.id.toolbar)
        Toolbar toolbar;


        public ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
            LayoutInflater inflater = (LayoutInflater) context.getSystemService(LAYOUT_INFLATER_SERVICE);
            expandableLayout.addView(inflater.inflate(R.layout.ticket_list_item_details_view, null));
        }
    }

    //        holder.count.setText(item.getDateCreation());


}
