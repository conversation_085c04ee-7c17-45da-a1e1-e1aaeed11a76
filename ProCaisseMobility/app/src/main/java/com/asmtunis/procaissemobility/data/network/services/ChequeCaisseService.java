package com.asmtunis.procaissemobility.data.network.services;


import com.asmtunis.procaissemobility.data.models.ChequeCaisse;
import com.asmtunis.procaissemobility.data.models.GenericObject;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Headers;
import retrofit2.http.POST;

/**
 * Created by Achraf on 29/09/2017.
 */

public interface ChequeCaisseService {

/*
    @Headers("User-Agent: android-api-client")
    @POST("getLigneTicketByCarnetId")
    Call<List<LigneTicket>> getChequeCaisse(@Body GenericObject genericObject);
*/

    @Headers("User-Agent: android-api-client")
    @POST("getChequeCaisseByReglement")
    Call<List<ChequeCaisse>> getChequeCaisseByReglement(@Body GenericObject genericObject);

    @Headers("User-Agent: android-api-client")
    @POST("getChequeCaisseByReglements")
    Call<List<List<ChequeCaisse>>> getChequeCaisseByReglements(@Body GenericObject genericObject);


}
