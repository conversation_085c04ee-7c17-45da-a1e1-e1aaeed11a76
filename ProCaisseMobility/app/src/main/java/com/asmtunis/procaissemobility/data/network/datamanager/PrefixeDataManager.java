package com.asmtunis.procaissemobility.data.network.datamanager;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Prefixe;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.OrdreMissionService;
import com.asmtunis.procaissemobility.data.network.services.PrefixeService;

import java.util.List;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

/**
 * Created by Achraf on 29/09/2017.
 */

public class PrefixeDataManager   {

    private static PrefixeDataManager sInstance;

    private final PrefixeService mPrefixeService;

    public PrefixeDataManager( ) {
        mPrefixeService = new ServiceFactory<>(PrefixeService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Prefixe")).makeService();
    }

    public static PrefixeDataManager getInstance( ) {
        if (sInstance == null) {
            sInstance = new PrefixeDataManager();
        }
        return sInstance;
    }

    public void getPrefixes(GenericObject genericObject,
                           RemoteCallback<List<Prefixe>> listener) {
        mPrefixeService.getPrefixes(genericObject)
                .enqueue(listener);
    }


}


