package com.asmtunis.procaissemobility.helper;

import static com.asmtunis.procaissemobility.helper.Globals.DEFAULT_VALUE;

import android.app.ProgressDialog;
import android.content.Context;
import android.os.Build;
import android.util.Log;
import android.widget.Toast;

import com.asmtunis.procaissemobility.data.models.DepenceType;
import com.asmtunis.procaissemobility.data.models.DepenceCaisse;
import androidx.annotation.RequiresApi;

import com.asmtunis.procaissemobility.data.models.ChequeCaisse;
import com.asmtunis.procaissemobility.data.models.DNResponseBatchData;
import com.asmtunis.procaissemobility.data.models.DNVIsite;
import com.asmtunis.procaissemobility.data.models.DNVisteWithLines;
import com.asmtunis.procaissemobility.data.models.DN_LigneVisite;
import com.asmtunis.procaissemobility.data.models.Facture;
import com.asmtunis.procaissemobility.data.models.InvPatBatchResponse;
import com.asmtunis.procaissemobility.data.models.LigneOrdreMission;
import com.asmtunis.procaissemobility.data.models.ReglementUpdate;
import com.asmtunis.procaissemobility.data.models.TicketUpdate;
import com.asmtunis.procaissemobility.data.models.Timbre;
import com.asmtunis.procaissemobility.data.models.TraiteCaisse;
import com.asmtunis.procaissemobility.data.models.Traking;
import com.asmtunis.procaissemobility.data.models.VCAutre;
import com.asmtunis.procaissemobility.data.models.VCAutreDeleteResponse;
import com.asmtunis.procaissemobility.data.models.VCImage;
import com.asmtunis.procaissemobility.data.models.VCNewProduct;
import com.asmtunis.procaissemobility.data.models.VCPrix;
import com.asmtunis.procaissemobility.data.models.VCPromo;
import com.asmtunis.procaissemobility.data.models.VcResponseBatchData;
import com.asmtunis.procaissemobility.data.models.uploadImageResponse;
import com.asmtunis.procaissemobility.data.network.datamanager.DepenceCaisseDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.DistributionNumeriqueDataManager;
import com.asmtunis.procaissemobility.data.models.custom.AddBatchDepenseModel;
import com.asmtunis.procaissemobility.data.network.datamanager.DepenceDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.FactureDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.OrdreMissionDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.TrakingDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.VeuilleConcurrentielleDataManager;
import com.asmtunis.procaissemobility.enums.DataSyncStatus;
import com.asmtunis.procaissemobility.helper.utils.ChequeCaisseList;
import com.asmtunis.procaissemobility.helper.utils.DateUtils;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.helper.utils.TraiteCaisseList;
import com.asmtunis.procaissemobility.helper.utils.Utils;
import com.asmtunis.procaissemobility.listener.IDataSyncListener;
import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.AppProperties;
import com.asmtunis.procaissemobility.data.models.BonCommande;
import com.asmtunis.procaissemobility.data.models.BonRetour;
import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.LigneBonCommande;
import com.asmtunis.procaissemobility.data.models.LigneBonRetour;
import com.asmtunis.procaissemobility.data.models.LigneTicket;
import com.asmtunis.procaissemobility.data.models.NestedItem;
import com.asmtunis.procaissemobility.data.models.ReglementCaisse;
import com.asmtunis.procaissemobility.data.models.Ticket;
import com.asmtunis.procaissemobility.data.models.TicketWithLines;
import com.asmtunis.procaissemobility.data.models.TicketWithLinesAndPayments;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.datamanager.BonCommandeDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.BonRetourDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.ClientDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.ReglementCaisseDataManager;
import com.asmtunis.procaissemobility.data.network.datamanager.TicketDataManager;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;
import com.blankj.utilcode.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import es.dmoral.toasty.Toasty;
import okhttp3.ResponseBody;

public class DataSyncHelper {
    Context context;
    static DataSyncHelper instance;
    PrefUtils prefUtils;
    private IDataSyncListener listener;
    Thread thread;
    public DataSyncStatus dataSyncStatus;
    List<TicketWithLinesAndPayments> ticketWithLinesAndPayments;
    List<DepenceCaisse> depenceCaissesList;
    List<DepenceType> depenceTypeTypeList;
    ProgressDialog mprogress;
    Boolean synTicket = true;


    public DataSyncHelper(Context context) {
        this.context = context;
        prefUtils = new PrefUtils(context);
    }

    public static DataSyncHelper getInstance(Context context) {
        if (instance == null)
            instance = new DataSyncHelper(context);
        return instance;
    }

    public static DataSyncHelper getInstance(Context context, IDataSyncListener iDataSyncListener) {
        if (instance == null)
            instance = new DataSyncHelper(context);
        instance.listener = iDataSyncListener;
        return instance;
    }

    public void syncData(String from) {

        Log.d("sdfqfsqffqf", "syncData "+ from);
        thread = new Thread(() -> {
            if(!PrefUtils.isAutoSync() ) return;

            
            syncExpenses(null);
            syncExpensesType(null);
            syncClients();
             updateClient();
            syncLigneOrdreMission();
            syncTickets(null, "1"+ from);
            getNonSyncDNVisites();
            getNonSyncDNVisitesTodelete();
            syncVcAutre();
            uploadImages();
            syncVCPrix();
            syncVCPromos();
            syncBonCommandes();
            syncInvPat();
            syncBonRetour();
            syncPayments();
            syncTraking();
            syncVCNewProduct();
          //  updateClient();
        }


        );
        thread.start();
    }

    void setTimbreBl(Ticket ticket, ReglementCaisse reglement) {
        Timbre timbre = App.database.timbreDAO().getActif();
        if (ticket != null && timbre != null) {
            Client client = App.database.clientDAO().getOneByCode(ticket.tIKCodClt);
            if (client != null) {
                if (client.getcLITimbre() == 0) ticket.setTimbre(0.0);
                else if (!PrefUtils.getInstance(context).getAutoBLTimbreEnabled() && ticket.timbre == 0.0)
                    //   ticket.setTimbre(Double.parseDouble(timbre.tIMBCode));
                    ticket.setTimbre(Double.parseDouble(timbre.tIMBCode));

            }


            if (ticket.timbre != 0.0) {
                ticket.settIKMtTTC(Utils.round(ticket.gettIKMtTTC() + Double.parseDouble(timbre.tIMBValue), 3));
                reglement.setrEGCMontant(ticket.gettIKMtTTC());
            }
        }


    }

    void addTimbretoTicketPayment(Ticket ticket,Timbre timbre) {
        /*if(ticket.gettIKMtEspece()!=0.0 && ticket.gettIKMtrecue()==0.0 && Double.parseDouble(ticket.gettIKMtCheque())==0.0)
            ticket.settIKMtEspece(Utils.round(ticket.gettIKMtEspece()+Double.parseDouble(timbre.tIMBValue), 3));
        else if(ticket.gettIKMtEspece()==0.0 && ticket.gettIKMtrecue()!=0.0 && Double.parseDouble(ticket.gettIKMtCheque())==0.0)
            ticket.settIKMtrecue(Utils.round(ticket.gettIKMtrecue()+Double.parseDouble(timbre.tIMBValue), 3));
        else if(ticket.gettIKMtEspece()==0.0 && ticket.gettIKMtrecue()==0.0 && Double.parseDouble(ticket.gettIKMtCheque())!=0.0)
            ticket.settIKMtCheque(String.valueOf(Utils.round(Double.parseDouble(ticket.gettIKMtCheque())+Double.parseDouble(timbre.tIMBValue), 3)));
        else ticket.settIKMtEspece(Utils.round(ticket.gettIKMtEspece()+Double.parseDouble(timbre.tIMBValue), 3));
*/
        ticket.settIKMtEspece(Utils.round(ticket.gettIKMtEspece()+Double.parseDouble(timbre.tIMBValue), 3));
    }


//    void setMntRevImp(Ticket ticket, Client client) {
//      if(ticket.timbre != 0.0)  ticket.settIKMtTTC(ticket.tIKMtTTC - ticket.timbre);
//        if(client != null && client.cltMntRevImp!= null) {
//            if(client.cltMntRevImp>0) {
//
//
//
//                ticket.setMntRevImp(ticket.tIKMtTTC * (client.cltMntRevImp/ 100));
//                ticket.settIKMtTTC(ticket.tIKMtTTC * (1 +(client.cltMntRevImp / 100)));
//            }
//            else  ticket.setMntRevImp(0.0);
//        }
//
//
//        if(ticket.timbre != 0.0)  ticket.settIKMtTTC(ticket.tIKMtTTC + ticket.timbre);
//    }
    void setTimbreCredit(Ticket ticket) {
        if(ticket.mntRevImp == null || ticket.mntRevImp == 0.0) {

            Client client = App.database.clientDAO().getOneByCode(ticket.tIKCodClt);

           // setMntRevImp(ticket, client);


            Timbre timbre = App.database.timbreDAO().getActif();
            if (timbre != null) {
                // Client client = App.database.clientDAO().getOneByCode(ticket.tIKCodClt);
                if (client != null) {
                    if (client.getcLITimbre() == 0) {
                        ticket.setTimbre(0.0);
                    }
                    else if (!PrefUtils.getInstance(context).getAutoBLTimbreEnabled() && ticket.timbre == 0.0){
                        ticket.setTimbre(Double.parseDouble(timbre.tIMBCode));
                        ticket.settIKMtTTC(Utils.round(ticket.gettIKMtTTC() + Double.parseDouble(timbre.tIMBValue), 3));

                    }





                }
            }
            App.database.ticketDAO().insert(ticket);
        }


    }

    void setTimbreFact(Ticket ticket, ReglementCaisse reglement) {


 if(ticket.mntRevImp == null || ticket.mntRevImp == 0.0) {
     Log.d("fdsqffsds", "ticket.tIKMtTTC " + ticket.tIKMtTTC);
     Client client = App.database.clientDAO().getOneByCode(ticket.tIKCodClt);

    // setMntRevImp(ticket, client);

     Log.d("fdsqffsds", "ticket.tIKMtTTC 2 " + ticket.tIKMtTTC);


     Timbre timbre = App.database.timbreDAO().getActif();
     if (timbre != null) {
         if (client != null) {
             if (client.getcLITimbre() == 0){
                 ticket.setTimbre(0.0);
             }
             else if (!PrefUtils.getInstance(context).getAutoBLTimbreEnabled() && ticket.timbre == 0.0){
                 ticket.setTimbre(Double.parseDouble(timbre.tIMBCode));
                 ticket.settIKMtTTC(Utils.round(ticket.gettIKMtTTC() + Double.parseDouble(timbre.tIMBValue), 3));

                 //      addTimbretoTicketPayment(ticket,timbre);


                 reglement.setrEGCMntChQue(Double.parseDouble(ticket.gettIKMtCheque()));
                 reglement.setrEGCMntTraite(ticket.gettIKMtrecue());
                 reglement.setrEGCMntEspeceRecue(ticket.gettIKMtEspece());
                 reglement.setrEGCMontant(ticket.gettIKMtTTC());
             }


         }
         else if (!PrefUtils.getInstance(context).getAutoBLTimbreEnabled() && ticket.timbre == 0.0){
             Log.d("podgfdsgsgsg", "6");
             ticket.setTimbre(Double.parseDouble(timbre.tIMBCode));
             ticket.settIKMtTTC(Utils.round(ticket.gettIKMtTTC() + Double.parseDouble(timbre.tIMBValue), 3));

             //      addTimbretoTicketPayment(ticket,timbre);


             reglement.setrEGCMntChQue(Double.parseDouble(ticket.gettIKMtCheque()));
             reglement.setrEGCMntTraite(ticket.gettIKMtrecue());
             reglement.setrEGCMntEspeceRecue(ticket.gettIKMtEspece());
             reglement.setrEGCMontant(ticket.gettIKMtTTC());


         }


         App.database.ticketDAO().insert(ticket);
         App.database.reglementCaisseDAO().insert(reglement);
     }

 }


    }

    public void syncFacture(int numTicket, Context mContext) {
        mprogress = new ProgressDialog(mContext);
        mprogress.setMessage(mContext.getString(R.string.fact_en_cours));
        mprogress.setCancelable(false);
        mprogress.show();

        //dataSyncStatus = DataSyncStatus.Loading;

        ticketWithLinesAndPayments = new ArrayList<>();
        if (!initFacturedTickets(numTicket).isEmpty()) {

            FactureDataManager.getInstance().addBatchFactureWithLines(new GenericObject(new PrefUtils(context).getBaseConfig(), ticketWithLinesAndPayments),
                    new RemoteCallback<List<TicketUpdate>>(context, false) {
                        @Override
                        public void onSuccess(List<TicketUpdate> response) {
                            if (response.size() > 0) {
                                /*for (TicketWithLinesAndPayments ticketWithLinesAndPayment : ticketWithLinesAndPayments) {
                            App.database.ticketDAO().delete(ticketWithLinesAndPayment.getTicket());
                        }
                         */
                                for (TicketUpdate ticketUpdate : response) {
                                    if (ticketUpdate.getCode().equals("10200") || ticketUpdate.getCode().equals("10201")|| ticketUpdate.getCode().equals("10304")) {

                                        App.database.ticketDAO().updateTicketNumber(ticketUpdate.getTIKNumeroBL(), ticketUpdate.getTIKNumTicket(), ticketUpdate.getTIKNumTicketM());
                                        // Ticket ticket = App.database.ticketDAO().getOneByCode(ticketUpdate.getTIKNumTicket(), ticketUpdate.getTIKExerc());

                                        Ticket ticket = App.database.ticketDAO().getOneByCodeM(ticketUpdate.getTIKNumTicketM(), ticketUpdate.getTIKExerc());
                                     //   addTimbreValueInLocalDB(ticket);
                                        ticket.setSync(true);
                                        ticket.settIKNumFact(ticketUpdate.getTIKNumTicket());
                                        ticket.setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());

                                        //  Timbre timbre = App.database.timbreDAO().getActif();
                                        // setTimbreFact(ticket,timbre);
                                        App.database.ticketDAO().insert(ticket);
                                        if(ticketUpdate.getFacture()!= null) {
                                            App.database.factureDAO().insert(ticketUpdate.getFacture());
                                        }


                                        App.database.appPropertiesDAO().insert(new AppProperties(0L, new Date().getTime()));


                                     //   Log.d("kkkmmm","cccc  c "+ticketUpdate.getObservation());
                                        if(ticketUpdate.getObservation()!=null){
                                            if(!ticketUpdate.getObservation().equals("")){
                                                BonCommande bonCommande=  App.database.bonCommandeDAO().getByCode(ticketUpdate.getTIKNumTicketM(),ticketUpdate.getTIKExerc());
                                                if (bonCommande!=null){
                                                    App.database.bonCommandeDAO().updateObservation(ticketUpdate.getObservation(),ticketUpdate.getTIKNumTicketM(),ticketUpdate.getTIKExerc());
                                                }
                                            }
                                        }


                                        if(ticketUpdate.getMessage()!=null){
                                            if(ticketUpdate.getMessage().equals("TICKET FACTURE DEJA")){
                                                BonCommande bonCommande=  App.database.bonCommandeDAO().getByCode(ticketUpdate.getTIKNumTicketM(),ticketUpdate.getTIKExerc());
                                                if (bonCommande!=null){
                                                    App.database.bonCommandeDAO().updateObservation(ticketUpdate.getMessage(),ticketUpdate.getTIKNumTicketM(),ticketUpdate.getTIKExerc());
                                                }
                                            }
                                        }



                                        setClientCredit(ticketUpdate);
                                    }


//                            for (TicketWithLinesAndPayments ticketWithLinesAndPayments1 : response) {
//                                /*App.database.ligneTicketDAO().deleteByTicket(ticketWithLinesAndPayments1.getTicket().gettIKNumTicket(),
//                                        ticketWithLinesAndPayments1.getTicket().tIKIdCarnet,
//                                        ticketWithLinesAndPayments1.getTicket().tIKExerc
//                                );
//                                 */
//
//                                App.database.ligneTicketDAO().insertAll(ticketWithLinesAndPayments1.getLigneTicket());
//                            }
                                }

                              //  getFacture();

                            }
                            mprogress.dismiss();
                        }

                        @Override
                        public void onUnauthorized() {
                            if (listener != null) {
                                listener.onFailure("Non Autorisé", dataSyncStatus);
                            }
                            mprogress.dismiss();

                        }

                        @Override
                        public void onFailed(Throwable throwable) {
                            if (listener != null) {
                                listener.onFailure(throwable.getMessage(), dataSyncStatus);
                            }
                            mprogress.dismiss();
                        }
                    });
        }
    }

    public void syncTickets(List<TicketWithLinesAndPayments> ticketWithLinesAndPayment, String from) {
        if (listener != null) {
            listener.onLoading("syncTickets...");
        }
        ticketWithLinesAndPayments = new ArrayList<>();

       if(ticketWithLinesAndPayments !=null){
           ticketWithLinesAndPayments.clear();
       }
        Log.d("sdfqfsqffqf", "xsd from "+ from);



        if (ticketWithLinesAndPayment == null) {
            //ticketWithLinesAndPayments = initNonSyncTickets();

            if(App.prefUtils.getAutoFactureEnabled()) {
                for (TicketWithLinesAndPayments ticketWithLinesPayment : initNonSyncTickets()) {
                    Log.d("sdfqfsqffqf", "xxx  ");
                    initFacturedTickets(ticketWithLinesPayment.getTicket().tIKNumTicket);
                }

            }
            else  ticketWithLinesAndPayments = initNonSyncTickets();
        } else {
         //   ticketWithLinesAndPayments = ticketWithLinesAndPayment;
            if(App.prefUtils.getAutoFactureEnabled()) {
                for (TicketWithLinesAndPayments ticketWithLinesPayment : ticketWithLinesAndPayment) {
                    Log.d("sdfqfsqffqf", "cccc  ");
                    initFacturedTickets(ticketWithLinesPayment.getTicket().tIKNumTicket);
                }

            } else {
                ticketWithLinesAndPayments = ticketWithLinesAndPayment;
            }
        }
        dataSyncStatus = DataSyncStatus.Loading;


        Log.d("sdfqfsqffqf", "ticketWithLinesAndPayments "+ ticketWithLinesAndPayments.size());
         if (!ticketWithLinesAndPayments.isEmpty()) {
            TicketDataManager.getInstance().addBatchTicketWithLignesTicketAndPayment(
                    new GenericObject(new PrefUtils(context).getBaseConfig(), ticketWithLinesAndPayments),
                    new RemoteCallback<List<TicketUpdate>>(context, false) {
                        @Override
                        public void onSuccess(List<TicketUpdate> response) {
                            Log.d("sdfqfsqffqf", "onSuccess  ");
                            if (!response.isEmpty()) {
                                try {
                                    for (TicketUpdate ticketUpdate : response) {

                                        try {
                                            if (ticketUpdate.getCode().equals("10200") || ticketUpdate.getCode().equals("10201") ||ticketUpdate.getCode().equals("10304")) {
                                                Log.d("sdfqfsqffqf", "rrrrrrrr  ");

                                                int OldNumTicket = App.database.ticketDAO().getOneByMCode(ticketUpdate.getTIKNumTicketM());

                                                if(ticketUpdate.getFacture() != null) {
                                                    App.database.factureDAO().insert(ticketUpdate.getFacture());
                                                }

                                                List<LigneTicket> ligneTickets = App.database.ligneTicketDAO().getByTicket(OldNumTicket, ticketUpdate.getTIKIdCarnet(), ticketUpdate.getTIKExerc());

                                                for (LigneTicket ligneTicket : ligneTickets) {

                                                    ligneTicket.lTNumTicket = ticketUpdate.getTIKNumTicket();
                                                    ligneTicket.isSync = true;
                                                    ligneTicket.status = Globals.ITEM_STATUS.SELECTED.getStatus();
                                                    App.database.ligneTicketDAO().insert(ligneTicket);
                                                }

                                                if (ticketUpdate.getTIKNumeroBL() == null) {

                                                    App.database.ticketDAO().updateBLNumber(ticketUpdate.getTIKNumTicket(), ticketUpdate.getTIKNumTicketM());
                                                    // Ticket ticket = App.database.ticketDAO().getOneByCode(ticketUpdate.getTIKNumTicket(), ticketUpdate.getTIKExerc());
                                                    Ticket ticket = App.database.ticketDAO().getOneByCodeM(ticketUpdate.getTIKNumTicketM(), ticketUpdate.getTIKExerc());


                                                    ticket.setSync(true);
                                                    ticket.setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());

                                                 //   addTimbreValueInLocalDB(ticket);

                                                    Log.d("sdfqfsqffqf", "frty  ");
                                                    App.database.ticketDAO().insert(ticket);

                                                }
                                                else {

                                                    App.database.ticketDAO().updateTicketNumber(ticketUpdate.getTIKNumeroBL(), ticketUpdate.getTIKNumTicket(), ticketUpdate.getTIKNumTicketM());
                                                    Ticket ticket = App.database.ticketDAO().getOneByCode(ticketUpdate.getTIKNumTicket(), ticketUpdate.getTIKExerc());

                                                 //   addTimbreValueInLocalDB(ticket);
                                                    Log.d("sdfqfsqffqf", "kloi  ");
                                                    ticket.setSync(true);
                                                    ticket.setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());
                                                    App.database.ticketDAO().insert(ticket);


                                                }


                                               if(ticketUpdate.getObservation()!=null){
                                                    if(!ticketUpdate.getObservation().isEmpty()){
                                                  BonCommande bonCommande=  App.database.bonCommandeDAO().getByCode(ticketUpdate.getTIKNumTicketM(),ticketUpdate.getTIKExerc());
                                                    if (bonCommande!=null){
                                                        App.database.bonCommandeDAO().updateObservation(ticketUpdate.getObservation(),ticketUpdate.getTIKNumTicketM(),ticketUpdate.getTIKExerc());
                                                    }
                                                    }
                                                }

                                                if(ticketUpdate.getMessage()!=null){
                                                    if(ticketUpdate.getMessage().equals("TICKET FACTURE DEJA")){
                                                        BonCommande bonCommande=  App.database.bonCommandeDAO().getByCode(ticketUpdate.getTIKNumTicketM(),ticketUpdate.getTIKExerc());
                                                        if (bonCommande!=null){
                                                            App.database.bonCommandeDAO().updateObservation(ticketUpdate.getMessage(),ticketUpdate.getTIKNumTicketM(),ticketUpdate.getTIKExerc());
                                                        }



                                                    }
                                                    else if(ticketUpdate.getMessage().equals("TICKET EXISTE")){
                                                        Log.d("sdfqfsqffqf", "TICKET EXISTE  ");
                                                        Ticket ticket = App.database.ticketDAO().getOneByCodeM(ticketUpdate.getTIKNumTicketM(), ticketUpdate.getTIKExerc());
                                                        ticket.setSync(true);
                                                        ticket.setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());
                                                    }
                                                }

                                                setClientCredit(ticketUpdate);
                                                synTicket = true;


                                                for (TicketWithLinesAndPayments ticketWithLinesAndPayments : ticketWithLinesAndPayments) {
                                                    Log.d("dgfdfddqsf", "fffffff");
                                                    if (ticketWithLinesAndPayments.getReglement() != null) {
                                                        Log.d("dgfdfddqsf", "DDDDD");
                                                        ticketWithLinesAndPayments.getReglement().setSync(true);
                                                        ticketWithLinesAndPayments.getReglement().setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());


                                                       // if (!ticketWithLinesAndPayments.getTicket().gettIKEtat().equals(Globals.TICKET_STATE.CREDIT.getValue())) {
                                                            App.database.reglementCaisseDAO().insert(ticketWithLinesAndPayments.getReglement());
                                                            App.database.chequeCaisseDAO().insertAll(ticketWithLinesAndPayments.getCheques());
                                                            App.database.traiteCaisseDAO().insertAll(ticketWithLinesAndPayments.getTraites());
                                                      //  }



                                                    }
                                                }

                                            }

                                        } catch (Exception e) {
                                            Log.d("sdfqfsqffqf", "e  "+ e.getMessage());
                                        }


                                    }
                                } catch (Exception e) {
                                }
                            }
                          //  if (ticketWithLinesAndPayment == null ) syncBonCommandes();
                        }

                        @Override
                        public void onUnauthorized() {
                            if (listener != null) {
                                listener.onFailure("onUnauthorized syncTickets", dataSyncStatus);
                            }
                            synTicket = true;
                           // if (ticketWithLinesAndPayment == null ) syncBonCommandes();
                        }

                        @Override
                        public void onFailed(Throwable throwable) {
                            if (listener != null) {
                                listener.onFailure(throwable.getMessage(), dataSyncStatus);
                            }
                            synTicket = true;
                          //  if (ticketWithLinesAndPayment == null ) syncBonCommandes();
                        }
                    }
                    );
        }


    }




    void setClientCredit(TicketUpdate ticketUpdate) {
        App.database.clientDAO().updateSoldClient(Double.valueOf(ticketUpdate.getSoldeClient()), ticketUpdate.getCodeClient());
        App.database.clientDAO().updateSoldCredit(Double.valueOf(ticketUpdate.getCredit()), ticketUpdate.getCodeClient());
        App.database.clientDAO().updateSoldDebit(Double.valueOf(ticketUpdate.getDebit()), ticketUpdate.getCodeClient());
    }

    void setClientCreditReglement(ReglementUpdate reglementUpdate) {
        App.database.clientDAO().updateSoldClient(Double.valueOf(reglementUpdate.getSoldeClient()), reglementUpdate.getCodeClient());
        App.database.clientDAO().updateSoldCredit(Double.valueOf(reglementUpdate.getCredit()), reglementUpdate.getCodeClient());
        App.database.clientDAO().updateSoldDebit(Double.valueOf(reglementUpdate.getDebit()), reglementUpdate.getCodeClient());
    }

    public void getNonSyncDNVisites() {
        List<DNVIsite> dnNonSyncList = App.database.dnVisitesDAO().getNoSyncedToAddOrUpdate();
        ArrayList<DN_LigneVisite> dnListLigneVisite;
        ArrayList<DNVisteWithLines> ListdnBatchVisites = new ArrayList<DNVisteWithLines>();

        if (!dnNonSyncList.isEmpty()  ) {
            if (listener != null) {
                listener.onLoading("sync Distribution Num ...");
            }
            for (int i = 0; i < dnNonSyncList.size(); i++) {
                Client client=  App.database.clientDAO().getOneByCodeM(dnNonSyncList.get(i).getVIS_CodeClient());
                if(client != null) {
                    String CltCodeM =  client.getcLICodeM();
                    if(CltCodeM.equals(dnNonSyncList.get(i).getVIS_CodeClient()) && client.isSync){
                        App.database.dnVisitesDAO().updateCodeClient(client.cLICode, dnNonSyncList.get(i).getVIS_CodeClient());
                    }


                }else {
                    dnListLigneVisite = (ArrayList<DN_LigneVisite>) App.database.dnLigneVisiteDAO().getListByCode(dnNonSyncList.get(i).getVIS_Code_M());

                    if (dnListLigneVisite.isEmpty())
                        dnListLigneVisite = (ArrayList<DN_LigneVisite>) App.database.dnLigneVisiteDAO().getListByCode(dnNonSyncList.get(i).getVIS_Num());

                    ArrayList<DN_LigneVisite> dnLigneVisites = new ArrayList<>();
                    for (int j= 0; j < dnListLigneVisite.size(); j++) {
                        String info1 = "";
                        if(dnListLigneVisite.get(j).getLgVISInfo1()!= null && dnListLigneVisite.get(j).getLgVISInfo1().isEmpty())
                            info1 =  dnListLigneVisite.get(j).getLgVISInfo1(); else info1 = "";
                        dnLigneVisites.add( new DN_LigneVisite(
                                dnListLigneVisite.get(j).getLgVISNum(),
                                dnListLigneVisite.get(j).getLgVISExerc(),
                                dnListLigneVisite.get(j).getLgVISFamille(),
                                dnListLigneVisite.get(j).getLgVISTier(),
                                info1  ,
                                "",
                                "",
                                dnListLigneVisite.get(j).getLgVISDDM()));
                    }

                    ListdnBatchVisites.add(new DNVisteWithLines(dnLigneVisites, dnNonSyncList.get(i)));

                }



            }
              syncDNVisites(ListdnBatchVisites);

        }
        else {
            if (listener != null) {
                listener.onComplete();
            }

            dataSyncStatus = DataSyncStatus.Finish;
        }

    }


    public void getNonSyncDNVisitesTodelete() {
        List<DNVIsite> dnNonSyncListToDelete = App.database.dnVisitesDAO().getNoSyncedToDelete();

        if (!dnNonSyncListToDelete.isEmpty()) {
            if (listener != null) {
                listener.onLoading("sync DN VisitesTodelete...");
            }
      /*      for (int i = 0; i < dnNonSyncListToDelete.size(); i++) {



                int finalI = i;
                DistributionNumeriqueDataManager.getInstance().deleteVisites(new GenericObject(prefUtils.getBaseConfig(), new DNVIsite(
                        dnNonSyncListToDelete.get(i).getVIS_Num() ,
                        dnNonSyncListToDelete.get(i).getVIS_Exerc(),
                        dnNonSyncListToDelete.get(i).getVIS_Code_M()

                )), new RemoteCallback<Boolean>(context, false) {
                    @Override
                    public void onSuccess(Boolean response) {
                        if (response != null) {
                            if (response) {

                                App.database.dnVisitesDAO().deleteById(dnNonSyncListToDelete.get(finalI).getVIS_Code_M());
                                App.database.dnLigneVisiteDAO().deleteById(dnNonSyncListToDelete.get(finalI).getVIS_Code_M());
                            } else {
                                if (dnNonSyncListToDelete.get(finalI).getVIS_Code_M().equals(dnNonSyncListToDelete.get(finalI).getVIS_Num())) { // item is not sync with backend
                                    App.database.dnVisitesDAO().deleteById(dnNonSyncListToDelete.get(finalI).getVIS_Code_M());
                                    App.database.dnLigneVisiteDAO().deleteById(dnNonSyncListToDelete.get(finalI).getVIS_Code_M());

                                }
                            }

                            getNonSyncDNVisites();
                        } else
                            Toasty.error(context, "Erreur Suppression ! --> response == null").show();


                    }

                    @Override
                    public void onUnauthorized() {
                        System.out.println("errorr onUnauthorized DELETE VC AUTRE");
                    }

                    @Override
                    public void onFailed(Throwable throwable) {
                        System.out.println("errorr" + throwable.getMessage());
                    }
                });
            }
*/
            List<DNVIsite>  dnVIsiteNonSyncListToDelete = new ArrayList();
            for (int i = 0; i < dnNonSyncListToDelete.size(); i++) {

                dnVIsiteNonSyncListToDelete.add( new DNVIsite(
                        dnNonSyncListToDelete.get(i).getVIS_Num() ,
                        dnNonSyncListToDelete.get(i).getVIS_Exerc(),
                        dnNonSyncListToDelete.get(i).getVIS_Code_M()));


            }


                 DistributionNumeriqueDataManager.getInstance().deleteVisites(new GenericObject(prefUtils.getBaseConfig(),
                         dnVIsiteNonSyncListToDelete

                 ), new RemoteCallback<List<DNResponseBatchData>>(context, false) {
                    @Override
                    public void onSuccess(List<DNResponseBatchData> response) {
                        if (response != null) {
                            if (!response.isEmpty()) {
                                for (int i = 0; i < response.size(); i++) {
                                    if (response.get(i).getCode().equals("10200")) {
                                        App.database.dnVisitesDAO().deleteById(response.get(i).getVIS_Code_M());
                                        App.database.dnLigneVisiteDAO().deleteById(response.get(i).getVIS_Code_M());
                                    }
                                    else {
                                        if (dnNonSyncListToDelete.get(i).getVIS_Code_M().equals(dnNonSyncListToDelete.get(i).getVIS_Num())) { // item is not sync with backend
                                            App.database.dnVisitesDAO().deleteById(dnNonSyncListToDelete.get(i).getVIS_Code_M());
                                            App.database.dnLigneVisiteDAO().deleteById(dnNonSyncListToDelete.get(i).getVIS_Code_M());
                                        }
                                    }

                                }

                            }
                            else {
                                for (int i = 0; i < dnNonSyncListToDelete.size(); i++) {
                                    if (dnNonSyncListToDelete.get(i).getVIS_Code_M().equals(dnNonSyncListToDelete.get(i).getVIS_Num())) { // item is not sync with backend
                                        App.database.dnVisitesDAO().deleteById(dnNonSyncListToDelete.get(i).getVIS_Code_M());
                                        App.database.dnLigneVisiteDAO().deleteById(dnNonSyncListToDelete.get(i).getVIS_Code_M());
                                    }
                                }

                            }

                         //   getNonSyncDNVisites();
                        } else
                            Toasty.error(context, "Erreur Suppression ! --> response == null").show();


                    }

                    @Override
                    public void onUnauthorized() {
                        System.out.println("errorr onUnauthorized DELETE VC AUTRE");
                    }

                    @Override
                    public void onFailed(Throwable throwable) {
                        System.out.println("errorr" + throwable.getMessage());
                    }
                });

        }
       // else  getNonSyncDNVisites();
    }

    public void syncDNVisites(ArrayList<DNVisteWithLines> ListdnBatchVisites) {
        Log.d("kkkdds","ddssss");
        DistributionNumeriqueDataManager.getInstance().addBatchVisites(
                new GenericObject(App.prefUtils.getBaseConfig(), ListdnBatchVisites),
                new RemoteCallback<List<DNResponseBatchData>>(context, false) {


                    @Override
                    public void onUnauthorized() {
                        if (listener != null) {
                            listener.onComplete();
                        }

                        dataSyncStatus = DataSyncStatus.Finish;
                    }

                    @Override
                    public void onFailed(Throwable throwable) {
                        // Toast.makeText(context, throwable.getMessage(), Toast.LENGTH_LONG).show();

                        if (listener != null) {
                            listener.onComplete();
                        }

                        dataSyncStatus = DataSyncStatus.Finish;
                    }

                    @Override
                    public void onSuccess(List<DNResponseBatchData> response) {
                        if (ObjectUtils.isNotEmpty(response)) {
                            //   App.database.dnVisitesDAO().insert(response);

                            for (int i = 0; i < response.size(); i++) {

                                if (response.get(i).getCode().equals("10200")) {
                                    if (response.get(i) != null) {
                                        DNVIsite dnVisites = App.database.dnVisitesDAO().getByCodeM(response.get(i).getVIS_Code_M());
                                        List<DN_LigneVisite> dn_ligneVisite = App.database.dnLigneVisiteDAO().getListByCode(response.get(i).getVIS_Code_M());

                                        for (int j = 0; j < dn_ligneVisite.size(); j++) {
                                            /* DN_LigneVisite dn_ligneVisit = dn_ligneVisite.get(j);
                                             App.database.dnLigneVisiteDAO().delete(dn_ligneVisit);
                                            dn_ligneVisit.isSync = true;
                                            dn_ligneVisit.status = Globals.ITEM_STATUS.SELECTED.status;
                                            dn_ligneVisit.setLgVISNum(response.get(i).getVIS_Num());
                                            App.database.dnLigneVisiteDAO().insert(dn_ligneVisit);*/
                                         // App.database.dnLigneVisiteDAO().updateCloud(response.get(i).getVIS_Num(),Globals.ITEM_STATUS.SELECTED.status,true,dn_ligneVisite.get(j).getLgVISNum());
                                          App.database.dnLigneVisiteDAO().updateCloud(response.get(i).getVIS_Num(),dn_ligneVisite.get(j).getLgVISNum());
                                        }


                                        if (dnVisites != null) {
                                            dnVisites.setVIS_Num(response.get(i).getVIS_Num());
                                            dnVisites.isSync = true;
                                            dnVisites.status = Globals.ITEM_STATUS.SELECTED.status;
                                            App.database.dnVisitesDAO().insert(dnVisites);
                                        }
                                    }

                                }

                            }

                            if (listener != null) {
                                listener.onComplete();
                            }

                            dataSyncStatus = DataSyncStatus.Finish;
                        }
                    }
                });

    }


    public void syncExpenses(List<DepenceCaisse> depenceCaisses) {
        if (listener != null) {
            listener.onLoading("syncExpenses...");
        }

        if (depenceCaisses == null) {
            depenceCaissesList = initNonSyncExpenses();
        } else {
            depenceCaissesList = depenceCaisses;
        }
        dataSyncStatus = DataSyncStatus.Loading;

        if (!depenceCaissesList.isEmpty()) {

            DepenceCaisseDataManager.getInstance().addBatchDepense(
                    new GenericObject(new PrefUtils(context).getBaseConfig(), depenceCaissesList), new RemoteCallback<ResponseBody>(context, false)  {
                        @Override
                        public void onSuccess(ResponseBody response) {


                            if (!depenceCaissesList.isEmpty()) {
                                for (DepenceCaisse depenceCaisseItem : depenceCaissesList) {
                                    App.database.depenceCaisseDAO().updateDepenseCaisseStatus(depenceCaisseItem.getDepCode());
                                }
                            }




                         //   if (depenceCaissesList == null) syncExpensesType(null);

                       //     syncExpensesType(null);
                        }

                        @Override
                        public void onUnauthorized() {
                            if (listener != null) {
                                listener.onFailure("onUnauthorized syncExpenses", dataSyncStatus);
                            }

                        }

                        @Override
                        public void onFailed(Throwable throwable) {
                            if (listener != null) {
                                listener.onFailure(throwable.getMessage(), dataSyncStatus);
                            }


                        }
                    }

            );
        }
    }

    public void syncExpensesType(List<DepenceType> depenceTypeTypes) {
        if (listener != null) {
            listener.onLoading("syncExpensesType...");
        }

        if (depenceTypeTypes == null) {
            depenceTypeTypeList = initNonSyncExpensesType();
        } else {
            depenceTypeTypeList = depenceTypeTypes;
        }
        dataSyncStatus = DataSyncStatus.Loading;

        if (!depenceTypeTypeList.isEmpty()) {

            DepenceDataManager.getInstance().addBatchDepenseType(new GenericObject(new PrefUtils(context).getBaseConfig(), depenceTypeTypeList), new RemoteCallback<List<AddBatchDepenseModel>>(context,false) {
                @Override
                public void onSuccess(List<AddBatchDepenseModel> response) {
                    if (!response.isEmpty()) {
                        try {

                            for (int i = 0; i < response.size(); i++) {
                                App.database.depenceTypeDAO().updateDepenseStatusByCodeM(response.get(i).getDEPCode(),
                                        response.get(i).getDEPCodeM());

                                Log.d("dsffdsd","getDEPCode() " + response.get(i).getDEPCode());
                                Log.d("dsffdsd","getDEPCodeM() " + response.get(i).getDEPCodeM());

                            }



                        } catch (Exception ignored) {
                        }
                    }

                }

                @Override
                public void onUnauthorized() {
                    if (listener != null) {
                        listener.onFailure("onUnauthorized syncExpenses", dataSyncStatus);
                    }


                }

                @Override
                public void onFailed(Throwable throwable) {
                    if (listener != null) {
                        listener.onFailure(throwable.getMessage(), dataSyncStatus);
                    }


                }
            });
        }
    }

    public void syncLigneOrdreMission() {
        List<LigneOrdreMission> ligneOrdreMissions = App.database.ligneOrdreMissionDAO().getNotSync();
        if(!ligneOrdreMissions.isEmpty()){
            if (listener != null) {
                listener.onLoading("sync LigneOrdreMission...");
            }

            OrdreMissionDataManager.getInstance().batchUpdateLigneOrdreMission(
                    new GenericObject(new PrefUtils(context).getBaseConfig(), ligneOrdreMissions),
                    new RemoteCallback<Boolean>(context, false) {
                        @Override
                        public void onSuccess(Boolean response) {

                            if(response) {
                                for (LigneOrdreMission ligneOrdreMission : ligneOrdreMissions) {
                                    ligneOrdreMission.isSync = true;
                                }
                                App.database.ligneOrdreMissionDAO().insertAll(ligneOrdreMissions);

                            }
                          
                        }

                        @Override
                        public void onUnauthorized() {
                        }

                        @Override
                        public void onFailed(Throwable throwable) {
                            Toast.makeText(context, throwable.getMessage(), Toast.LENGTH_LONG).show();

                            if (listener != null) {
                                listener.onFailure(throwable.getMessage(), dataSyncStatus);
                            }
                        }
                    }
            );
        }


       // else {
          //  if (synTicket) {
           //     synTicket = false;
           //     syncTickets(null);

          //  }
       // }
    }


    public void syncClients() {
        Log.d("gcgc", "cc  oldClients " );
        dataSyncStatus = DataSyncStatus.Loading;
        List<Client> oldClients = App.database.clientDAO().getByQuery();

       /* for (int i = 0; i < oldClients.size(); i++) {
            if(!oldClients.get(i).getcLICodeM().equals(oldClients.get(i).cLICode) && !oldClients.get(i).isSync){
                oldClients.get(i).setSync(true);
                oldClients.get(i).setExercice("");
                oldClients.get(i).setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());
                App.database.clientDAO().insert(oldClients.get(i));
            }
        }*/
        if (!oldClients.isEmpty()){
            Log.d("gcgc", "oldClients  oldClients " );
            if (listener != null) {
                listener.onLoading("syncClients...");
            }
            ClientDataManager.getInstance().addBatchClient(new GenericObject(prefUtils.getBaseConfig(), App.database.clientDAO().getByQuery()), new RemoteCallback<List<Client>>
                    (context, false) {
                @Override
                public void onSuccess(List<Client> response) {
                    if (response != null) {

                      /*  for (int i = 0; i < response.size(); i++) {
                            App.database.ticketDAO().updateCodeClient(response.get(i).cLICode, oldClients.get(i).getcLICodeM());
                            App.database.reglementCaisseDAO().updateCodeClient(response.get(i).cLICode, oldClients.get(i).getcLICodeM());
                            App.database.bonCommandeDAO().updateCodeClient(response.get(i).cLICode, oldClients.get(i).getcLICodeM());
                            App.database.bonRetourDAO().updateCodeClient(response.get(i).cLICode, oldClients.get(i).getcLICodeM());
                            App.database.dnVisitesDAO().updateCodeClient(response.get(i).cLICode, oldClients.get(i).getcLICodeM());
                            App.database.clientDAO().deleteById(oldClients.get(i).cLICode);

                        }*/

                        for (Client client : response) {
                     if(client.getcLICodeM() != null){
                         App.database.ticketDAO().updateCodeClient(client.cLICode, client.getcLICodeM());
                         App.database.reglementCaisseDAO().updateCodeClient(client.cLICode, client.getcLICodeM());
                         App.database.bonCommandeDAO().updateCodeClient(client.cLICode, client.getcLICodeM());
                         App.database.bonRetourDAO().updateCodeClient(client.cLICode, client.getcLICodeM());
                         App.database.dnVisitesDAO().updateCodeClient(client.cLICode, client.getcLICodeM());
                         //App.database.clientDAO().deleteById(client.cLICode);


                         if(!client.getcLICodeM().equals(client.getcLICode()))     {
                             App.database.clientDAO().deleteBycodeM(client.getcLICodeM());
                         }
                         client.setSync(true);
                         client.setExercice("");
                         client.setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());
                         if(!client.getcLICodeM().equals(client.getcLICode())) {

                             App.database.clientDAO().insert(client);
                     }


                            }
                        }

                        App.database.appPropertiesDAO().insert(
                                new AppProperties(App.database.appPropertiesDAO().getOne()
                                        != null ? App.database.appPropertiesDAO().getOne().getUpdated_at() : new Date().getTime(), new Date().getTime()));
                    }
                //   syncLigneOrdreMission();
                }

                @Override
                public void onUnauthorized() {
                    if (listener != null) {
                        listener.onFailure("onUnauthorized syncClients...", dataSyncStatus);
                    }
                    //syncTickets();
                }

                @Override
                public void onFailed(Throwable throwable) {
                    if (listener != null) {
                        listener.onFailure(throwable.getMessage(), dataSyncStatus);
                    }
                    //syncTickets();
                }
            });
        }

      //  else {
          // syncLigneOrdreMission();
      //  }
    }


  public   void updateClient() {
        Client clientToUpdate = App.database.clientDAO().getClientToUpdateByQuery();

        List<Client> listclt = new ArrayList<>();
        listclt.add(clientToUpdate);
        if (clientToUpdate != null) {
            ClientDataManager.getInstance().updatClient(new GenericObject(new PrefUtils(context).getBaseConfig(), clientToUpdate), new RemoteCallback<Boolean>
                    (context, false) {
                @Override
                public void onSuccess(Boolean response) {
                    if (response != null) {
                        if (response) {
                            clientToUpdate.setSync(true);
                            clientToUpdate.setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());
                            App.database.clientDAO().insert(clientToUpdate);
                        }


                    }
                }

                @Override
                public void onUnauthorized() {
                }

                @Override
                public void onFailed(Throwable throwable) {
                }
            });
        }
    }


    private List<TicketWithLinesAndPayments> initFacturedTickets(int numTicket) {

        dataSyncStatus = DataSyncStatus.Loading;


        Ticket ticket = App.database.ticketDAO().getOneByCode(numTicket);


        ticket.settIKStation(prefUtils.getUserStationId());
        ReglementCaisse reglement = App.database.reglementCaisseDAO().getByTicket(ticket.gettIKNumTicket());

        List<LigneTicket> ligneTickets = App.database.ligneTicketDAO().getByTicket(ticket.gettIKNumTicket(), ticket.tIKIdCarnet,
                ticket.tIKExerc);



        Client client=  App.database.clientDAO().getOneByCodeM(ticket.gettIKCodClt());
        if(client != null) {
            String CltCodeM =  client.getcLICodeM();
            if(CltCodeM.equals(ticket.gettIKCodClt()) && client.isSync){
                App.database.ticketDAO().updateCodeClient(client.cLICode, ticket.gettIKCodClt());
                App.database.reglementCaisseDAO().updateCodeClient(client.cLICode, ticket.gettIKCodClt());
            }

        }

        Log.d("fdsqffsds", "ticket "+ ticket.tIKMtTTC);

        ticketWithLinesAndPayments.clear();
        if (reglement != null) {
            setTimbreFact(ticket, reglement);

            // reglement.setrEGCMntEspece(ticket.gettIKMtEspece());
            ticketWithLinesAndPayments.add(new
                    TicketWithLinesAndPayments(new TicketWithLines(ticket,
                    ligneTickets),
                    reglement,
                    App.database.chequeCaisseDAO().getByReglementM(reglement.getrEGCCode_M()),
                    App.database.traiteCaisseDAO().getByReglementM(reglement.getrEGCCode_M())
            )); //App.database.traiteCaisseDAO().getByReglement(reglement.getrEGCCode())));
        } else {
            setTimbreCredit(ticket);
            ticketWithLinesAndPayments.add(new TicketWithLinesAndPayments(new TicketWithLines(ticket, ligneTickets),
                    new ReglementCaisse(),
                    new ChequeCaisseList(),
                    new TraiteCaisseList()));

        }

        return ticketWithLinesAndPayments;
    }


    private List<TicketWithLinesAndPayments> initNonSyncTickets() {

        ticketWithLinesAndPayments = new ArrayList<>();
        dataSyncStatus = DataSyncStatus.Loading;
        List<Ticket> tickets = App.database.ticketDAO().getNonSync();


        for (Ticket ticket : tickets) {
            Log.d("kklnjbdhvg","ff ");
            ticket.settIKStation(prefUtils.getUserStationId());
            ReglementCaisse reglement = null;
            if(!ticket.gettIKEtat().equals(Globals.TICKET_STATE.CREDIT.getValue()))
              reglement = App.database.reglementCaisseDAO().getByTicket(ticket.gettIKNumTicket());

            List<LigneTicket> ligneTickets = App.database.ligneTicketDAO().getByTicket(ticket.gettIKNumTicket(), ticket.tIKIdCarnet, ticket.tIKExerc);


            Client client=  App.database.clientDAO().getOneByCodeM(ticket.gettIKCodClt());
            if(client != null) {
                String CltCodeM =  client.getcLICodeM();
                if(CltCodeM.equals(ticket.gettIKCodClt()) && client.isSync){
                    App.database.ticketDAO().updateCodeClient(client.cLICode, ticket.gettIKCodClt());
                    App.database.reglementCaisseDAO().updateCodeClient(client.cLICode, ticket.gettIKCodClt());
                }
            }

            else {

                    if (reglement != null) {
                        ticketWithLinesAndPayments.add(new
                                TicketWithLinesAndPayments(new TicketWithLines(ticket,
                                ligneTickets),
                                reglement,
                                App.database.chequeCaisseDAO().getByReglementM(reglement.getrEGCCode_M()),
                                App.database.traiteCaisseDAO().getByReglementM(reglement.getrEGCCode_M())));

                    }
                    else {
                        // setTimbreCredit(ticket);
                        ticketWithLinesAndPayments.add(
                                new TicketWithLinesAndPayments(new TicketWithLines(ticket, ligneTickets),
                                        new ReglementCaisse(),
                                        new ChequeCaisseList(),
                                        new TraiteCaisseList())
                        );
                    }
            }
        }

        return ticketWithLinesAndPayments;
    }

    private List<DepenceCaisse> initNonSyncExpenses() {
        depenceCaissesList = new ArrayList<>();
        dataSyncStatus = DataSyncStatus.Loading;

        depenceCaissesList = App.database.depenceCaisseDAO().getNonSync();


/*
        for (DepenceCaisse depenceCaisse : depenceCaissesList) {

            DepenceType depenceType = App.database.depenceTypeDAO().getOneByCodeM(depenceCaisse.getDepCode());
            if (depenceType != null) {
               // String CltCodeM = depence.getDepCodeM();
                 if(depenceType.getDepCode().equals(depenceCaisse.getDepCode())){
                     depenceCaissesList.remove(depenceCaisse);
                     Log.d("dsffdsd", depenceType.getDepCode());
                 }




            }

        }*/
        return depenceCaissesList;
    }

    private List<DepenceType> initNonSyncExpensesType() {
        depenceTypeTypeList = new ArrayList<>();
        dataSyncStatus = DataSyncStatus.Loading;

        depenceTypeTypeList = App.database.depenceTypeDAO().getNonSync();

        return depenceTypeTypeList;
    }

    public void syncBonCommandes() {
        dataSyncStatus = DataSyncStatus.Loading;
        List<BonCommande> nonSyncComandes = App.database.bonCommandeDAO().getNoSynced();

        List<NestedItem<BonCommande, List<LigneBonCommande>>> nestedItemList = new ArrayList<>();
        for (BonCommande bonCommande : nonSyncComandes) {

            Client client=  App.database.clientDAO().getOneByCodeM(bonCommande.getDEVCodeClient());
            if(client != null) {
                String CltCodeM =  client.getcLICodeM();
                if(CltCodeM.equals(bonCommande.getDEVCodeClient()) && client.isSync){
                    App.database.bonCommandeDAO().updateCodeClient(client.cLICode, bonCommande.getDEVCodeClient());
                }


            }else {
                setNestedItemlist(bonCommande,nestedItemList );
            /*    if(bonCommande.getDEV_info3()==null){
                    setNestedItemlist(bonCommande,nestedItemList );

                }
                else if( !bonCommande.getDEV_info3().equals(Globals.TYPE_PATRIMOINE.AFFECTATION.getTypePat())&&
                        !bonCommande.getDEV_info3().equals(Globals.TYPE_PATRIMOINE.SORTIE.getTypePat())&&
                        ! bonCommande.getDEV_info3().equals(Globals.TYPE_PATRIMOINE.ENTREE.getTypePat())&&
                        ! bonCommande.getDEV_info3().equals(Globals.TYPE_PATRIMOINE.INVENTAIRE.getTypePat())
                ){
                    setNestedItemlist(bonCommande,nestedItemList );
                }*/
            }



        }


        if (!nestedItemList.isEmpty()){
            if (listener != null) {
                listener.onLoading("syncBonCommandes ...");
            }
            BonCommandeDataManager.getInstance().addBatchBonCommande(new GenericObject(prefUtils.getBaseConfig(), nestedItemList), new RemoteCallback<InvPatBatchResponse>
                    (context, false) {
                @RequiresApi(api = Build.VERSION_CODES.O)
                @Override
                public void onSuccess(InvPatBatchResponse response) {
                    if (response != null) {
                     /*   for (int i = 0; i < response.size(); i++) {
                            App.database.ligneBonCommandeDAO().updateCodeBonCommande(response.get(i).getDEVNum(), nonSyncComandes.get(i).getDEVNum());
                            App.database.bonCommandeDAO().deleteById(nonSyncComandes.get(i).getDEVNum(), response.get(i).getBONLIVExerc());
                            response.get(i).setSync(true);
                            response.get(i).setDEVDate(DateUtils.dateToStr(DateUtils.strToDate(response.get(i).getDEVDate(), "dd-MM-yyyy hh:mm:ss"), "MM/dd/yyyy hh:mm:ss"));
                            response.get(i).setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());
                            App.database.bonCommandeDAO().insert(response.get(i));
                        }*/

                        for (int i = 0; i < response.size(); i++) {
                            if (response.get(i).getCode() == 10200  || response.get(i).getCode()==10701) {
                                BonCommande bonCammand = App.database.bonCommandeDAO().getListByCodeM(response.get(i).getDEV_Code_M(), response.get(i).getDEV_Exerc());
                                //  for (int t = 0; t < bonCammand.size(); t++) {
                                //      BonCommande bonCammand = bonCammand.get(t);
                                if(bonCammand!=null){
                                    List<LigneBonCommande> lignBnCommandList = App.database.ligneBonCommandeDAO().getByBCCode(response.get(i).getDEV_Code_M());
                                    for (int j = 0; j < lignBnCommandList.size(); j++) {
                                        LigneBonCommande lignBnCommand = lignBnCommandList.get(j);
                                        //  App.database.ligneBonCommandeDAO().deleteById(response.get(i).getDEV_Num(),App.prefUtils.getExercice());
                                       // App.database.ligneBonCommandeDAO().deleteByCmd(lignBnCommand.getLGDEVNumBon());
                                        //App.database.ligneBonCommandeDAO().deleteByCmd(lignBnCommand.getlGDEVCodeM());
                                        App.database.ligneBonCommandeDAO().deleteByCmd(response.get(i).getDEV_Code_M());
                                        lignBnCommand.isSync = true;
                                        lignBnCommand.setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());
                                        lignBnCommand.setLGDEVNumBon(response.get(i).getDEV_Num());
                                        App.database.ligneBonCommandeDAO().insert(lignBnCommand);
                                    }




                                //    App.database.bonCommandeDAO().deleteByIdM(response.get(i).getDEV_Code_M());
                                    App.database.bonCommandeDAO().deleteByIdM(bonCammand.devCodeM);
                                    bonCammand.isSync = true;
                                    bonCammand.setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());
                                    bonCammand.setDEVNum(response.get(i).getDEV_Num());

                                    bonCammand.setDEVDate(DateUtils.dateToStr(new Date(), Globals.DATE_PATTERN));
                                    bonCammand.setDEVDDm(DateUtils.dateToStr(new Date(), Globals.DATE_PATTERN));
                                    App.database.bonCommandeDAO().insert(bonCammand);


                               //     App.database.clientDAO().updateSoldClient(Double.valueOf(response.get(i).getSoldeClient()), bonCammand.getDEVClient());

                                    /*
                            const Error_article_pat_affecte = 10704;
                            const Error_article_out = 10705;
                            const Error_article_in = 10706;
                             */


                                }

                                //  }
                            }




                        }
                        //    Toast.makeText(context, R.string.added_succesfully, Toast.LENGTH_SHORT).show();
                    }
                    App.database.appPropertiesDAO().insert(new AppProperties(
                            App.database.appPropertiesDAO().getOne() == null ? new Date().getTime() :
                                    App.database.appPropertiesDAO().getOne().getUpdated_at(), new Date().getTime()));

                   // syncInvPat();

                }

                @Override
                public void onUnauthorized() {
                    Log.d("bvnchd","onUnauthorized");
                    if (listener != null) {
                        listener.onFailure("onUnauthorized BonCommandes ...", dataSyncStatus);
                    }
                     //  syncInvPat();
                }

                @Override
                public void onFailed(Throwable throwable) {
                    Log.d("bvnchd","onFailed");
                    if (listener != null) {
                        listener.onFailure(throwable.getMessage(), dataSyncStatus);
                    }
                   //    syncInvPat();
                }
            });
        }
       // else {
        //    syncInvPat();
        //}

    }

    public void setNestedItemlist(BonCommande bonCommande, List<NestedItem<BonCommande, List<LigneBonCommande>>> nestedItemList){


        Log.d("base ac","getDevCodeM " +bonCommande.getDevCodeM());

        NestedItem<BonCommande, List<LigneBonCommande>> nestedItem = new NestedItem<>();
        nestedItem.setParent(bonCommande);
        nestedItem.setChildren(App.database.ligneBonCommandeDAO().getByBCCode(bonCommande.getDevCodeM()));
        nestedItemList.add(nestedItem);
    }

    public void syncInvPat() {

        dataSyncStatus = DataSyncStatus.Loading;
        List<BonCommande> nonSyncComandes = App.database.bonCommandeDAO().getNoSynced();

        List<NestedItem<BonCommande, List<LigneBonCommande>>> nestedItemList = new ArrayList<>();
        for (BonCommande bonCommande : nonSyncComandes) {
            if (bonCommande.getDEV_info3()!=null){
                if(bonCommande.getDEV_info3().equals(Globals.TYPE_PATRIMOINE.AFFECTATION.getTypePat())||
                        bonCommande.getDEV_info3().equals(Globals.TYPE_PATRIMOINE.SORTIE.getTypePat())||
                        bonCommande.getDEV_info3().equals(Globals.TYPE_PATRIMOINE.ENTREE.getTypePat())||
                        bonCommande.getDEV_info3().equals(Globals.TYPE_PATRIMOINE.INVENTAIRE.getTypePat())
                ){
                    NestedItem<BonCommande, List<LigneBonCommande>> nestedItem = new NestedItem<>();
                    nestedItem.setParent(bonCommande);
                    nestedItem.setChildren(App.database.ligneBonCommandeDAO().getByBCCode(bonCommande.getDevCodeM()));
                    nestedItemList.add(nestedItem);
                }
            }


        }
        if (!nestedItemList.isEmpty()){
            if (listener != null) {
                listener.onLoading("syncBonCommandes ...");
            }
            BonCommandeDataManager.getInstance().addBatchInvPat(new GenericObject(prefUtils.getBaseConfig(), nestedItemList), new RemoteCallback<InvPatBatchResponse>
                    (context, false) {
                @RequiresApi(api = Build.VERSION_CODES.O)
                @Override
                public void onSuccess(InvPatBatchResponse response) {
                    if (response != null) {

                     /*   for (int i = 0; i < response.size(); i++) {
                            App.database.ligneBonCommandeDAO().updateCodeBonCommande(response.get(i).getDEVNum(), nonSyncComandes.get(i).getDEVNum());
                            App.database.bonCommandeDAO().deleteById(nonSyncComandes.get(i).getDEVNum(), response.get(i).getBONLIVExerc());
                            response.get(i).setSync(true);
                            response.get(i).setDEVDate(DateUtils.dateToStr(DateUtils.strToDate(response.get(i).getDEVDate(), "dd-MM-yyyy hh:mm:ss"), "MM/dd/yyyy hh:mm:ss"));
                            response.get(i).setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());
                            App.database.bonCommandeDAO().insert(response.get(i));
                        }*/

                        for (int i = 0; i < response.size(); i++) {

                            if (response.get(i).getCode() ==10200 ||response.get(i).getCode() == 10701) {

                                BonCommande bonCammand = App.database.bonCommandeDAO().getListByCodeM(response.get(i).getDEV_Code_M(), response.get(i).getDEV_Exerc());
                                //  for (int t = 0; t < bonCammand.size(); t++) {
                                //      BonCommande bonCammand = bonCammand.get(t);
                                if(bonCammand!=null){

                                  //  List<LigneBonCommande> lignBnCommandList = App.database.ligneBonCommandeDAO().getByBCCode(response.get(i).getDEV_Code_M());


                                        List<LigneBonCommande> lignBnCommandList = App.database.ligneBonCommandeDAO().getByBCCode(response.get(i).getDEV_Code_M());
                                        for (int j = 0; j < lignBnCommandList.size(); j++) {
                                            LigneBonCommande lignBnCommand = lignBnCommandList.get(j);

                                            //  App.database.ligneBonCommandeDAO().deleteById(response.get(i).getDEV_Num(),App.prefUtils.getExercice());
                                             App.database.ligneBonCommandeDAO().deleteByCmd(lignBnCommand.getLGDEVNumBon());
                                            App.database.ligneBonCommandeDAO().deleteByCmd(lignBnCommand.getlGDEVCodeM());
                                            lignBnCommand.isSync = true;
                                            lignBnCommand.setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());
                                            lignBnCommand.setLGDEVNumBon(response.get(i).getDEV_Num());


                                            App.database.ligneBonCommandeDAO().insert(lignBnCommand);
                                        }


                                    /*    for (int j = 0; j <  response.get(i).lignes.size(); j++) {
                                            LigneBonCommande lignBnCommand =  App.database.ligneBonCommandeDAO().getOneByBCCodeM(response.get(i).lignes.get(j).getLG_DEV_Code_M());
                                            //  App.database.ligneBonCommandeDAO().deleteById(response.get(i).getDEV_Num(),App.prefUtils.getExercice());
                                            // App.database.ligneBonCommandeDAO().deleteByCmd(lignBnCommand.getLGDEVNumBon());
                                            App.database.ligneBonCommandeDAO().deleteByCmd(lignBnCommand.getlGDEVCodeM());
                                            lignBnCommand.isSync = true;
                                            lignBnCommand.setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());
                                            lignBnCommand.setLGDEVNumBon(response.get(i).getDEV_Num());
                                            App.database.ligneBonCommandeDAO().insert(lignBnCommand);
                                        }
                                   */






                                    App.database.bonCommandeDAO().deleteByIdM(response.get(i).getDEV_Code_M());
                                    bonCammand.isSync = true;
                                    bonCammand.setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());
                                    bonCammand.setDEVNum(response.get(i).getDEV_Num());

                                    bonCammand.setDEVDate(DateUtils.dateToStr(new Date(), Globals.DATE_PATTERN));
                                    bonCammand.setDEVDDm(DateUtils.dateToStr(new Date(), Globals.DATE_PATTERN));
                                    App.database.bonCommandeDAO().insert(bonCammand);

                                    /*
                            const Error_article_pat_affecte = 10704;
                            const Error_article_out = 10705;
                            const Error_article_in = 10706;
                             */


                                }

                                //  }
                            }

                            else if (response.get(i).getCode() ==10707||
                                    response.get(i).getCode() ==10706||
                                    response.get(i).getCode() ==10705||
                                    response.get(i).getCode() ==10704){
/**
 * "message": "patrimoine rejected",
 *         "code": 10707,
 */


                                BonCommande bonCammand = App.database.bonCommandeDAO().getListByCodeM(response.get(i).getDEV_Code_M(), response.get(i).getDEV_Exerc());
                                //  for (int t = 0; t < bonCammand.size(); t++) {
                                //      BonCommande bonCammand = bonCammand.get(t);
                                if(bonCammand!=null){
                                    //  List<LigneBonCommande> lignBnCommandList = App.database.ligneBonCommandeDAO().getByBCCode(response.get(i).getDEV_Code_M());
                                    for (int j = 0; j <  response.get(i).lignes.size(); j++) {
                                        LigneBonCommande lignBnCommand =  App.database.ligneBonCommandeDAO().getOneByBCCodeM(response.get(i).lignes.get(j).getLG_DEV_Code_M());
                                        //  App.database.ligneBonCommandeDAO().deleteById(response.get(i).getDEV_Num(),App.prefUtils.getExercice());
                                         App.database.ligneBonCommandeDAO().deleteByCmd(lignBnCommand.getLGDEVNumBon());

                                        if(!response.get(i).lignes.isEmpty()){
                                            if(response.get(i).lignes.get(j).getLG_DEV_Code_M().equals(lignBnCommand.lGDEVCodeM)){
                                                lignBnCommand.setMsgLigne(response.get(i).lignes.get(j).getMessage());
                                                lignBnCommand.setCodeLigne(String.valueOf(response.get(i).lignes.get(j).getCode()));
                                            }
                                        }

                                        App.database.ligneBonCommandeDAO().deleteByCmd(lignBnCommand.getlGDEVCodeM());
                                        lignBnCommand.isSync = true;
                                        lignBnCommand.setCodeLigne(String.valueOf(response.get(i).lignes.get(j).getCode()));
                                        lignBnCommand.setMsgLigne(response.get(i).lignes.get(j).getMessage());
                                        lignBnCommand.setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());
                                        lignBnCommand.setLGDEVNumBon(response.get(i).getDEV_Num());
                                        App.database.ligneBonCommandeDAO().insert(lignBnCommand);
                                    }




                                    App.database.bonCommandeDAO().deleteByIdM(response.get(i).getDEV_Code_M());
                                    bonCammand.isSync = true;
                                    bonCammand.setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());
                                    bonCammand.setDEVNum(response.get(i).getDEV_Num());
                                    bonCammand.setCode(String.valueOf(response.get(i).getCode()));
                                    bonCammand.setMsg(response.get(i).getMessage());

                                    bonCammand.setDEVDate(DateUtils.dateToStr(new Date(), Globals.DATE_PATTERN));
                                    bonCammand.setDEVDDm(DateUtils.dateToStr(new Date(), Globals.DATE_PATTERN));
                                    App.database.bonCommandeDAO().insert(bonCammand);

                                    /*
                            const Error_article_pat_affecte = 10704;
                            const Error_article_out = 10705;
                            const Error_article_in = 10706;
                             */


                                }


/*
                                if(response.get(i).getDEV_info3()!=null){
                                    if(response.get(i).getDEV_info3().equals("10704") &&
                                            bonCammand.getDEV_info3().equals(Globals.TYPE_PATRIMOINE.AFFECTATION.getTypePat())){

                                    }
                                    else  if(response.get(i).getDEV_info3().equals("10705") &&
                                            bonCammand.getDEV_info3().equals(Globals.TYPE_PATRIMOINE.SORTIE.getTypePat())){

                                    }
                                    else  if(response.get(i).getDEV_info3().equals("10706") &&
                                            bonCammand.getDEV_info3().equals(Globals.TYPE_PATRIMOINE.ENTREE.getTypePat())){

                                    }
                                    else  if(response.get(i).getDEV_info3().equals("10707") &&
                                            bonCammand.getDEV_info3().equals(Globals.TYPE_PATRIMOINE.INVENTAIRE.getTypePat())){

                                    }
                                }

*/



                            }




                        }
                        //    Toast.makeText(context, R.string.added_succesfully, Toast.LENGTH_SHORT).show();
                    }
                    App.database.appPropertiesDAO().insert(new AppProperties(
                            App.database.appPropertiesDAO().getOne() == null ? new Date().getTime() :
                                    App.database.appPropertiesDAO().getOne().getUpdated_at(), new Date().getTime()));

                     //  syncBonRetour();

                }

                @Override
                public void onUnauthorized() {
                    if (listener != null) {
                        listener.onFailure("onUnauthorized BonCommandes ...", dataSyncStatus);
                    }
           //  syncBonRetour();
                }

                @Override
                public void onFailed(Throwable throwable) {
                    if (listener != null) {
                        listener.onFailure(throwable.getMessage(), dataSyncStatus);
                    }
                    //   syncBonRetour();
                }
            });
        }
      //  else {
       //        syncBonRetour();
      //  }

    }

    public void syncBonRetour() {
        dataSyncStatus = DataSyncStatus.Loading;
        List<BonRetour> nonSyncBonRetours = App.database.bonRetourDAO().getNonSynced();
        List<NestedItem<BonRetour, List<LigneBonRetour>>> nestedItemList = new ArrayList<>();
        if (!nonSyncBonRetours.isEmpty()) {
            if (listener != null) {
                listener.onLoading("syncBonRetour ...");
            }

            for (BonRetour bonCommande : nonSyncBonRetours) {

                Client client=  App.database.clientDAO().getOneByCodeM(bonCommande.getBORCodefrs());
                if(client != null) {
                    String CltCodeM =  client.getcLICodeM();
                    if(CltCodeM.equals(bonCommande.getBORCodefrs()) && client.isSync){
                        App.database.bonRetourDAO().updateCodeClient(client.cLICode, bonCommande.getBORCodefrs());
                    }


                }else {
                    NestedItem<BonRetour, List<LigneBonRetour>> nestedItem = new NestedItem<>();
                    nestedItem.setParent(bonCommande);
                    nestedItem.setChildren(App.database.ligneBonRetourDAO().getByBRNum(bonCommande.getBORNumero()));
                    nestedItemList.add(nestedItem);
                }



            }

            BonRetourDataManager.getInstance().addBatchBonRetours(new GenericObject(prefUtils.getBaseConfig(), nestedItemList), new RemoteCallback<List<BonRetour>>
                    (context, false) {
                @Override
                public void onSuccess(List<BonRetour> response) {
                    if (response != null) {

                        for (int i = 0; i < response.size(); i++) {
                            App.database.ligneBonRetourDAO().updateCodeBonRetour(response.get(i).getBORNumero(), nonSyncBonRetours.get(i).getBORNumero());
                            App.database.bonRetourDAO().deleteById(nonSyncBonRetours.get(i).getBORNumero(), response.get(i).getBORExercice());
                            response.get(i).setSync(true);
                            response.get(i).setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());
                            App.database.bonRetourDAO().insert(response.get(i));
                        }

                        Toast.makeText(context, R.string.added_succesfully, Toast.LENGTH_SHORT).show();
                        App.database.appPropertiesDAO().insert(new AppProperties(
                                App.database.appPropertiesDAO().getOne() == null ? new Date().getTime() :
                                        App.database.appPropertiesDAO().getOne().getUpdated_at(), new Date().getTime()));
                    }

                  //  syncPayments();
                }

                @Override
                public void onUnauthorized() {

                    if (listener != null) {
                        listener.onFailure("onUnauthorized sync BonRetour ...", dataSyncStatus);
                    }
                      // syncPayments();
                }

                @Override
                public void onFailed(Throwable throwable) {
                    if (listener != null) {
                        listener.onFailure(throwable.getMessage(), dataSyncStatus);
                    }
                      // syncPayments();
                }
            });
        }// else {
          //    syncPayments();
       // }

    }

    public void syncTraking() {

        dataSyncStatus = DataSyncStatus.Loading;
        List<Traking> trakingList = App.database.trakingDAO().getTraks();

        if (!trakingList.isEmpty()) {
            if (listener != null) {
                listener.onLoading("sync Traking ...");
            }
            TrakingDataManager.getInstance().addTraking(
                    new GenericObject(new PrefUtils(context).getBaseConfig(), trakingList), new RemoteCallback<Boolean>(context, false) {
                        @Override
                        public void onSuccess(Boolean response) {
                            App.database.trakingDAO().deleteAll();
                            Log.d("Syncccc", "finish syncPayments ...");

                               // syncVCNewProduct();
                        }

                        @Override
                        public void onUnauthorized() {
                            if (listener != null) {
                                listener.onFailure("onUnauthorized sync BonRetour ...", dataSyncStatus);
                            }
                        }

                        @Override
                        public void onFailed(Throwable throwable) {
                            if (listener != null) {
                                listener.onFailure(throwable.getMessage(), dataSyncStatus);
                            }
                        }
                    });
        } //else {
           //  syncVCNewProduct();
        //}
    }

    public void syncPayments() {
        dataSyncStatus = DataSyncStatus.Loading;
        List<TicketWithLinesAndPayments> Payments = new ArrayList<>();
        List<ReglementCaisse> reglements = App.database.reglementCaisseDAO().getAllNotSynced();

        Log.d("uuhygyh","syncPayments");
        for (ReglementCaisse reglement : reglements) {


            Client client=  App.database.clientDAO().getOneByCodeM(reglement.getrEGCCodeClient());
            if(client != null) {
                String CltCodeM =  client.getcLICodeM();
                if(CltCodeM.equals(reglement.getrEGCCodeClient()) && client.isSync){
                    App.database.reglementCaisseDAO().updateCodeClient(client.cLICode, reglement.getrEGCCodeClient());
                }

            }else {
                Payments.add(new TicketWithLinesAndPayments(
                        reglement,
                        App.database.chequeCaisseDAO().getByReglementM(reglement.getrEGCCode_M()),
                        App.database.traiteCaisseDAO().getByReglementM(reglement.getrEGCCode_M())));
            }


        }

        if (!Payments.isEmpty()) {
            if (listener != null) {
                listener.onLoading("sync Payments ...");
            }
            ReglementCaisseDataManager.getInstance().addBatchPayments(new GenericObject(prefUtils.getBaseConfig(),
                    Payments), new RemoteCallback<List<ReglementUpdate>>(context, false) {
                @Override
                public void onSuccess(List<ReglementUpdate> response) {


                    if (response.size() > 0) {

                        /*for (TicketWithLinesAndPayments ticketWithLinesAndPayment : ticketWithLinesAndPayments) {
                            App.database.ticketDAO().delete(ticketWithLinesAndPayment.getTicket());
                        }
                         */
                        for (ReglementUpdate reglementUpdate : response) {

                            //    App.database.reglementCaisseDAO().updateregCode(reglementUpdate.getREGC_Code(),reglementUpdate.getREGC_Code_M());
                            if (reglementUpdate.getCode().equals("10200")) {
                                for (TicketWithLinesAndPayments ticketWithLinesAndPayments : Payments) {
                                    if (reglementUpdate.getREGC_Code_M().equals(ticketWithLinesAndPayments.getReglement().getrEGCCode_M())) {

                                        ReglementCaisse regCaisse = ticketWithLinesAndPayments.getReglement();
                                        App.database.reglementCaisseDAO().deleteByCode(reglementUpdate.getREGC_Code_M(),reglementUpdate.gettIKExerc(),reglementUpdate.getREGC_IdSCaisse());


                                        regCaisse.setSync(true);
                                        regCaisse.setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());
                                        regCaisse.setrEGCCode(reglementUpdate.getREGC_Code());

                                        for (ChequeCaisse chequeCaisse : ticketWithLinesAndPayments.getCheques()) {
                                            chequeCaisse.setReglement(reglementUpdate.getREGC_Code());
                                            chequeCaisse.setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());

                                        }
                                        for (TraiteCaisse traiteCaisse : ticketWithLinesAndPayments.getTraites()) {
                                            traiteCaisse.settRAITReglement(reglementUpdate.getREGC_Code());
                                            traiteCaisse.setStatus(Globals.ITEM_STATUS.SELECTED.getStatus());
                                        }


                                        App.database.reglementCaisseDAO().insert(regCaisse);
                                        App.database.chequeCaisseDAO().insertAll(ticketWithLinesAndPayments.getCheques());
                                        App.database.traiteCaisseDAO().insertAll(ticketWithLinesAndPayments.getTraites());
                                        setClientCreditReglement(reglementUpdate);
                                    }

                                }




                                App.database.appPropertiesDAO().insert(new AppProperties(
                                        App.database.appPropertiesDAO().getOne() == null ? new Date().getTime() :
                                                App.database.appPropertiesDAO().getOne().getUpdated_at(), new Date().getTime()));

                            }

                        }
                    }


                //syncTraking();
                }

                @Override
                public void onUnauthorized() {
                    if (listener != null) {
                        listener.onFailure("onUnauthorized syncPayments ", dataSyncStatus);
                    }
                   // syncTraking();
                }

                @Override
                public void onFailed(Throwable throwable) {
                    if (listener != null) {
                        listener.onFailure(throwable.getMessage(), dataSyncStatus);
                    }
                   //   syncTraking();
                }
            });
        } //else {
            //  syncTraking();
        //}

    }

    public void uploadImages() {

        List<VCImage> vcImages = App.database.vcImageDAO().getNoSynced();
        if (!vcImages.isEmpty()) {
            if (listener != null) {
                listener.onLoading("sync VC Images ...");
            }
            VeuilleConcurrentielleDataManager.getInstance().addVCImages(new GenericObject(prefUtils.getBaseConfig(), vcImages), new RemoteCallback<List<uploadImageResponse>>(context, false) {
                @Override
                public void onSuccess(List<uploadImageResponse> response) {
                    for (uploadImageResponse vcImage : response) {


                        App.database.vcImageDAO().setSynced(vcImage.getCodeIMG());
                    }


                //   getNonSyncDNVisitesTodelete();

                }

                @Override
                public void onUnauthorized() {

                }

                @Override
                public void onFailed(Throwable throwable) {
                    Toasty.info(context, throwable.getMessage()).show();
                }

            });
        }// else {
          // getNonSyncDNVisitesTodelete();

      //  }
    }
    public void syncVCNewProductToDelete() {
        List<VCNewProduct> vcNewProductListToDelete = App.database.vcNewProductDAO().getNoSyncedToDelete();

        if (!vcNewProductListToDelete.isEmpty()) {

            if (listener != null) {
                listener.onLoading("sync VCNewProduct ...");
            }
            List<VCNewProduct>  vcNewProductLisToDelete = new ArrayList();
            for (int i = 0; i < vcNewProductListToDelete.size(); i++) {
                vcNewProductLisToDelete.add( new VCNewProduct(vcNewProductListToDelete.get(i).getCodeVCLanP()));
            }

            VeuilleConcurrentielleDataManager.getInstance().deleteBatchVCNewProduct(new GenericObject(prefUtils.getBaseConfig(), vcNewProductLisToDelete), new RemoteCallback<List<VCAutreDeleteResponse>>(context, false,false) {
                @Override
                public void onSuccess(List<VCAutreDeleteResponse> response) {


                    if (response != null) {

                        if (!response.isEmpty()) {
                            for (int i = 0; i < response.size(); i++) {
                                if (response.get(i).getCode().equals("10200") && response.get(i).getTable().equals("VCLancementNP")) {
                                    App.database.vcNewProductDAO().deleteById(response.get(i).getCodeVCLanP());

                                }
                                else {
                                    if (vcNewProductListToDelete.get(i).getCodeVCLanPM().equals(response.get(i).getCodeVCLanP())) { // item is not sync with backend

                                        App.database.vcNewProductDAO().deleteByIdAndCodeM(response.get(i).getCodeVCLanP(), vcNewProductListToDelete.get(i).getCodeVCLanPM());

                                    }
                                }

                            }

                        }
                        else {
                            for (int i = 0; i < vcNewProductListToDelete.size(); i++) {
                                if (vcNewProductListToDelete.get(i).getCodeVCLanPM().equals(vcNewProductListToDelete.get(i).getCodeVCLanP())) { // item is not sync with backend
                                    App.database.vcNewProductDAO().deleteByIdAndCodeM(vcNewProductListToDelete.get(i).getCodeVCLanP(), vcNewProductListToDelete.get(i).getCodeVCLanPM());
                                }
                            }

                        }
                    } else
                        Toasty.error(context, "Erreur Suppression ! --> response == null").show();

                }

                @Override
                public void onUnauthorized() {
                    System.out.println("errorr onUnauthorized DELETE VCPromo");
                }

                @Override
                public void onFailed(Throwable throwable) {
                    System.out.println("errorr" + throwable.getMessage());
                }
            }, "VCLancementNP");


        }

    }
    public void syncVCNewProduct() {
        List<VCNewProduct> vcNewProductList = App.database.vcNewProductDAO().getNoSyncedToAddOrUpdate();

        if (!vcNewProductList.isEmpty()) {
            VeuilleConcurrentielleDataManager.getInstance().addBatchVC(new GenericObject(prefUtils.getBaseConfig(), vcNewProductList), new RemoteCallback<List<VcResponseBatchData>>(context, false) {
                @Override
                public void onSuccess(List<VcResponseBatchData> response) {
                    for (int i = 0; i < response.size(); i++) {
                        if (response.get(i).getTable().equals("VCLancementNP")) {
                            VCNewProduct vcNewProduct;

                            vcNewProduct = App.database.vcNewProductDAO().getByCodeM(response.get(i).getCodeM());

                            if (vcNewProduct == null)
                                vcNewProduct = App.database.vcNewProductDAO().getByCode(response.get(i).getCode());

                            if (vcNewProduct != null) {
                                vcNewProduct.isSync = true;
                                vcNewProduct.status = Globals.ITEM_STATUS.SELECTED.getStatus();
                                App.database.vcNewProductDAO().insert(vcNewProduct);

                                if (vcNewProduct.getCodeVCLanP().equals(vcNewProduct.getCodeVCLanPM()))
                                    App.database.vcNewProductDAO().updateCloudCode(response.get(i).getCode(), response.get(i).getCodeM());
                            }

                        }
                    }
                  //  syncVCPromos();
                }

                @Override
                public void onUnauthorized() {
                    if (listener != null) {
                        listener.onFailure("onUnauthorized syncPayments ", dataSyncStatus);
                    }
                }

                @Override
                public void onFailed(Throwable throwable) {
                    System.out.println("errorr" + throwable.getMessage());
                    if (listener != null) {
                        listener.onFailure(throwable.getMessage(), dataSyncStatus);
                    }
                  //   syncVCPromos();

                }
            }, "VCLancementNP");

        } //else {
          // syncVCPromos();
       // }
    }

    public void syncVCPromosToDelete() {
        List<VCPromo> vcPromosToDelete = App.database.vcPromosDAO().getNoSyncedToDelete();

        if (!vcPromosToDelete.isEmpty()) {
            if (listener != null) {
                listener.onLoading("sync vcPromosToDelete ...");
            }
            List<VCPromo>  vcPromoSToDelete = new ArrayList();
            for (int i = 0; i < vcPromosToDelete.size(); i++) {
                vcPromoSToDelete.add( new VCPromo(vcPromosToDelete.get(i).getCodeVCPromo()));
            }

            VeuilleConcurrentielleDataManager.getInstance().deleteBatchVCPromotion(new GenericObject(prefUtils.getBaseConfig(), vcPromoSToDelete), new RemoteCallback<List<VCAutreDeleteResponse>>(context, false,false) {
                @Override
                public void onSuccess(List<VCAutreDeleteResponse> response) {


                    if (response != null) {

                        if (!response.isEmpty()) {
                            for (int i = 0; i < response.size(); i++) {
                                if (response.get(i).getCode().equals("10200")  && response.get(i).getTable().equals("VCPromo")) {
                                    App.database.vcPromosDAO().deleteById(response.get(i).getCodeVCPromo());

                                }
                                else {
                                    if (vcPromosToDelete.get(i).getCodeVCPromoM().equals(response.get(i).getCodeVCPromo())) { // item is not sync with backend

                                        App.database.vcPromosDAO().deleteByIdAndCodeM(vcPromosToDelete.get(i).getCodeVCPromo(), vcPromosToDelete.get(i).getCodeVCPromoM());

                                    }
                                }

                            }

                        }
                        else {
                            for (int i = 0; i < vcPromosToDelete.size(); i++) {
                                if (vcPromosToDelete.get(i).getCodeVCPromoM().equals(vcPromosToDelete.get(i).getCodeVCPromo())) { // item is not sync with backend
                                    App.database.vcPromosDAO().deleteByIdAndCodeM(vcPromosToDelete.get(i).getCodeVCPromo(), vcPromosToDelete.get(i).getCodeVCPromoM());
                                }
                            }

                        }
                    } else
                        Toasty.error(context, "Erreur Suppression ! --> response == null").show();
                }

                @Override
                public void onUnauthorized() {
                    System.out.println("errorr onUnauthorized DELETE VCPromo");
                }

                @Override
                public void onFailed(Throwable throwable) {
                    System.out.println("errorr" + throwable.getMessage());
                }
            }, "VCPromo");





        }

    }
    public void syncVCPromos() {

        List<VCPromo> vcPromos = App.database.vcPromosDAO().getNoSyncedToAddOrUpdate();




        if (!vcPromos.isEmpty()) {
            if (listener != null) {
                listener.onLoading("sync vcPromos ...");
            }
            VeuilleConcurrentielleDataManager.getInstance().addBatchVC(new GenericObject(prefUtils.getBaseConfig(), vcPromos), new RemoteCallback<List<VcResponseBatchData>>(context, false) {
                @Override
                public void onSuccess(List<VcResponseBatchData> response) {
                  /*  int i =0;
                    for (VCPromo vcPromo : vcPromos) {
                        vcPromo.isSync = true;
                        vcPromo.status = Globals.ITEM_STATUS.SELECTED.getStatus();
                        App.database.vcPromosDAO().insert(vcPromo);
                        if (response.get(i).getTable().equals("VCPromo")) {
                            App.database.vcPromosDAO().updateCloudCode(response.get(i).getCode(), response.get(i).getCodeM());
                        }
                        i++;

                    }*/
                    //   App.database.vcPromosDAO().insertAll(vcPromos);

                    for (int i = 0; i < response.size(); i++) {
                        if (response.get(i).getTable().equals("VCPromo")) {
                            VCPromo vcPromo;
                            vcPromo = App.database.vcPromosDAO().getByCodeM(response.get(i).getCodeM());

                            if (vcPromo == null)
                                vcPromo = App.database.vcPromosDAO().getByCode(response.get(i).getCode());

                            if (vcPromo != null) {
                                vcPromo.isSync = true;
                                vcPromo.status = Globals.ITEM_STATUS.SELECTED.getStatus();
                                App.database.vcPromosDAO().insert(vcPromo);

                                if (vcPromo.getCodeVCPromoM().equals(vcPromo.getCodeVCPromo()))
                                    App.database.vcPromosDAO().updateCloudCode(response.get(i).getCode(), response.get(i).getCodeM());
                            }

                        }
                    }

                //  syncVCPrix();
                }

                @Override
                public void onUnauthorized() {
                    if (listener != null) {
                        listener.onFailure("onUnauthorized syncPayments ", dataSyncStatus);
                    }
                }

                @Override
                public void onFailed(Throwable throwable) {
                    if (listener != null) {
                        listener.onFailure(throwable.getMessage(), dataSyncStatus);
                    }
                }
            }, "VCPromo");
        } //else {
           //syncVCPrix();
        //}
    }

    public void syncVCPrixToDelete() {
        List<VCPrix> vcPrixListToDelete = App.database.vcPricesDAO().getNoSyncedToDelete();
        if (!vcPrixListToDelete.isEmpty()) {
            if (listener != null) {
                listener.onLoading("sync VCPrix ...");
            }
            List<VCPrix>  vcPrixLisToDelete = new ArrayList();
            for (int i = 0; i < vcPrixListToDelete.size(); i++) {
                vcPrixLisToDelete.add( new VCPrix(vcPrixListToDelete.get(i).getCodeVCPrix()));
            }


            VeuilleConcurrentielleDataManager.getInstance().deleteBatchVCPrix(new GenericObject(prefUtils.getBaseConfig(), vcPrixLisToDelete), new RemoteCallback<List<VCAutreDeleteResponse>>(context, false,false) {
                @Override
                public void onSuccess(List<VCAutreDeleteResponse> response) {

                    if (response != null) {

                        if (!response.isEmpty()) {
                            for (int i = 0; i < response.size(); i++) {
                                if (response.get(i).getCode().equals("10200") && response.get(i).getTable().equals("VCPrix")) {
                                    App.database.vcPricesDAO().deleteById(response.get(i).getCodeVCPrix());

                                }
                                else {
                                    if (vcPrixListToDelete.get(i).getCodeVCPrixM().equals(response.get(i).getCodeVCPrix())) { // item is not sync with backend

                                        App.database.vcPricesDAO().deleteByIdAndCodeM(vcPrixListToDelete.get(i).getCodeVCPrix(), vcPrixListToDelete.get(i).getCodeVCPrixM());

                                    }
                                }

                            }

                        }
                        else {
                            for (int i = 0; i < vcPrixListToDelete.size(); i++) {
                                if (vcPrixListToDelete.get(i).getCodeVCPrixM().equals(vcPrixListToDelete.get(i).getCodeVCPrix())) { // item is not sync with backend
                                    App.database.vcPricesDAO().deleteByIdAndCodeM(vcPrixListToDelete.get(i).getCodeVCPrix(), vcPrixListToDelete.get(i).getCodeVCPrixM());
                                }
                            }

                        }
                    } else
                        Toasty.error(context, "Erreur Suppression ! --> response == null").show();

                }

                @Override
                public void onUnauthorized() {
                    System.out.println("errorr onUnauthorized DELETE vcPrixEdit");
                }

                @Override
                public void onFailed(Throwable throwable) {
                    System.out.println("errorr" + throwable.getMessage());
                }
            }, "VCPrix");


        }

    }
    public void syncVCPrix() {

        List<VCPrix> vcPrixList = App.database.vcPricesDAO().getNoSyncedToAddOrUpdate();

        if (!vcPrixList.isEmpty()) {
            VeuilleConcurrentielleDataManager.getInstance().addBatchVC(new GenericObject(prefUtils.getBaseConfig(), vcPrixList), new RemoteCallback<List<VcResponseBatchData>>(context, false) {
                @Override
                public void onSuccess(List<VcResponseBatchData> response) {
                 /*   int i = 0;
                    for (VCPrix vcPrix : vcPrixList) {
                        vcPrix.isSync = true;
                        vcPrix.status = Globals.ITEM_STATUS.SELECTED.getStatus();
                        App.database.vcPricesDAO().insert(vcPrix);

                        if (response.get(i).getTable().equals("VCPrix")) {
                            App.database.vcPricesDAO().updateCloudCode(response.get(i).getCode(), response.get(i).getCodeM());
                        }
                        i++;
                    }*/

                    //     App.database.vcPricesDAO().insertAll(vcPrixList);

                    for (int i = 0; i < response.size(); i++) {
                        if (response.get(i).getTable().equals("VCPrix")) {
                            VCPrix vcPrix;

                            vcPrix = App.database.vcPricesDAO().getByCodeM(response.get(i).getCodeM());


                            if (vcPrix == null)
                                vcPrix = App.database.vcPricesDAO().getByCode(response.get(i).getCode());

                            if (vcPrix != null) {
                                vcPrix.isSync = true;
                                vcPrix.status = Globals.ITEM_STATUS.SELECTED.getStatus();
                                App.database.vcPricesDAO().insert(vcPrix);

                                if (vcPrix.getCodeVCPrix().equals(vcPrix.getCodeVCPrixM()))
                                    App.database.vcPricesDAO().updateCloudCode(response.get(i).getCode(), response.get(i).getCodeM());
                            }

                        }
                    }
              //      syncVcAutre();
                }

                @Override
                public void onUnauthorized() {
                    if (listener != null) {
                        listener.onFailure("onUnauthorized syncPayments ", dataSyncStatus);
                    }
                }

                @Override
                public void onFailed(Throwable throwable) {
                    if (listener != null) {
                        listener.onFailure(throwable.getMessage(), dataSyncStatus);
                    }
                }
            }, "VCPrix");
        }// else {
         //  syncVcAutre();
        //}

    }

    public void syncVcAutreToDelete() {
        List<VCAutre> vcAutresListToDelete = App.database.vcAutreDAO().getNoSyncedToDelete();
        if (!vcAutresListToDelete.isEmpty()) {
            if (listener != null) {
                listener.onLoading("sync VcAutre ...");
            }

            List<VCAutre>  vcAutresLisToDelete = new ArrayList();
            for (int i = 0; i < vcAutresListToDelete.size(); i++) {
                vcAutresLisToDelete.add( new VCAutre(vcAutresListToDelete.get(i).getCodeAutre()));
            }


            VeuilleConcurrentielleDataManager.getInstance().deleteBatchVCAutre(new GenericObject(prefUtils.getBaseConfig(), vcAutresLisToDelete ), new RemoteCallback<List<VCAutreDeleteResponse>>(context, false,false) {
                @Override
                public void onSuccess(List<VCAutreDeleteResponse> response) {
                    if (response != null) {

                        if (!response.isEmpty()) {
                            for (int i = 0; i < response.size(); i++) {
                                if (response.get(i).getCode().equals("10200") && response.get(i).getTable().equals("VCAutre")) {
                                    App.database.vcAutreDAO().deleteById(response.get(i).getCodeAutre());

                                }
                                else {
                                    if (vcAutresListToDelete.get(i).getCodeMob().equals(response.get(i).getCodeAutre())) { // item is not sync with backend

                                        App.database.vcAutreDAO().deleteByIdAndCodeM(vcAutresListToDelete.get(i).getCodeAutre(), vcAutresListToDelete.get(i).getCodeMob());

                                    }
                                }

                            }

                        }
                        else {
                            for (int i = 0; i < vcAutresListToDelete.size(); i++) {
                                if (vcAutresListToDelete.get(i).getCodeMob().equals(vcAutresListToDelete.get(i).getCodeAutre())) { // item is not sync with backend
                                    App.database.vcAutreDAO().deleteByIdAndCodeM(vcAutresListToDelete.get(i).getCodeAutre(), vcAutresListToDelete.get(i).getCodeMob());
                                }
                            }

                        }
                    } else
                        Toasty.error(context, "Erreur Suppression ! --> response == null").show();


                }

                @Override
                public void onUnauthorized() {
                    System.out.println("errorr onUnauthorized DELETE VC AUTRE");
                }

                @Override
                public void onFailed(Throwable throwable) {
                    System.out.println("errorr" + throwable.getMessage());
                }
            }, "VCAutre");



        }

    }
    public void syncVcAutre() {

        List<VCAutre> vcAutres = App.database.vcAutreDAO().getNoSyncedToAddOrUpdate();
        if (!vcAutres.isEmpty()) {


            VeuilleConcurrentielleDataManager.getInstance().addBatchVC(new GenericObject(prefUtils.getBaseConfig(), vcAutres), new RemoteCallback<List<VcResponseBatchData>>(context, false) {
                @Override
                public void onSuccess(List<VcResponseBatchData> response) {


                    for (int i = 0; i < response.size(); i++) {
                        VCAutre vcAutre;
                        if (response.get(i).getTable().equals("VCAutre")) {
                            vcAutre = App.database.vcAutreDAO().getByCodeM(response.get(i).getCodeM());

                            if (vcAutre == null)
                                vcAutre = App.database.vcAutreDAO().getByCode(response.get(i).getCode());

                            if (vcAutre != null) {
                                vcAutre.isSync = true;
                                vcAutre.status = Globals.ITEM_STATUS.SELECTED.getStatus();
                                App.database.vcAutreDAO().insert(vcAutre);

                                if (vcAutre.getCodeMob().equals(vcAutre.getCodeAutre()))
                                    App.database.vcAutreDAO().updateCloudCode(response.get(i).getCode(), response.get(i).getCodeM());
                            }

                        }
                    }

                 //   uploadImages();
                }

                @Override
                public void onUnauthorized() {
                    if (listener != null) {
                        listener.onFailure("onUnauthorized syncPayments ", dataSyncStatus);
                    }
                }

                @Override
                public void onFailed(Throwable throwable) {
                    Log.d("syncVcAutre Test Ob", throwable.getMessage());

                    if (listener != null) {
                        listener.onFailure(throwable.getMessage(), dataSyncStatus);
                    }
                }
            }, "VCAutre");
        } //else {
           // uploadImages();
       // }
    }

}
