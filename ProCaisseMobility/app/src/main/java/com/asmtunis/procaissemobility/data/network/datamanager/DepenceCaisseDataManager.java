package com.asmtunis.procaissemobility.data.network.datamanager;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.DepenceCaisse;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.DepenceCaisseService;

import java.util.List;

import okhttp3.ResponseBody;

/**
 * Created by Oussama AZIZI on 8/30/22.
 */
public class DepenceCaisseDataManager {
    private static DepenceCaisseDataManager sInstance;
    private final DepenceCaisseService mDepenceCaisseService;

    public DepenceCaisseDataManager() {
        mDepenceCaisseService = new ServiceFactory<>(DepenceCaisseService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Depense")).makeService();
    }

    public static DepenceCaisseDataManager getInstance( ) {
        if (sInstance == null) {
            sInstance = new DepenceCaisseDataManager();
        }
        return sInstance;
    }

    public void getDepenceCaisse(GenericObject connexion, RemoteCallback<List<DepenceCaisse>> listener) {
        mDepenceCaisseService.getDepencesCaisse(connexion)
                .enqueue(listener);
    }

    public void getDepenceCaisseByCaisseId(GenericObject genericObject,
                                     RemoteCallback<List<DepenceCaisse>> listener) {
        mDepenceCaisseService.getDepenceCaisseByCaisseId(genericObject)
                .enqueue(listener);
    }

    public void addBatchDepense(GenericObject genericObject,
                                     RemoteCallback<ResponseBody> listener) {
        mDepenceCaisseService.addBatchDepences(genericObject)
                .enqueue(listener);
    }

    public void deleteDepenseCaisse(GenericObject genericObject,
                                     RemoteCallback<ResponseBody> listener) {
        mDepenceCaisseService.deleteDepenseCaisse(genericObject)
                .enqueue(listener);
    }
}
