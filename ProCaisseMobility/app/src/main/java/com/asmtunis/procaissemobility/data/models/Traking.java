package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

@Entity
public class Traking implements Serializable {
    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "Trak_Id")
    @SerializedName("Trak_Id")
    @Expose
    public String trakId;

    @NonNull
    @ColumnInfo(name = "Trak_Latitude")
    @SerializedName("Trak_Latitude")
    @Expose
    public double trakLat;

    @NonNull
    @ColumnInfo(name = "Trak_Longitude")
    @SerializedName("Trak_Longitude")
    @Expose
    public double trackLong;

    @ColumnInfo(name = "Trak_dateMachine")
    @SerializedName("Trak_dateMachine")
    @Expose
    public String trakDateMachine;

    @ColumnInfo(name = "Trak_dateCamion")
    @SerializedName("Trak_dateCamion")
    @Expose
    public String trakDateCamion;

    @ColumnInfo(name = "Trak_station")
    @SerializedName("Trak_station")
    @Expose
    public String trakStation;

    @ColumnInfo(name = "Trak_user")
    @SerializedName("Trak_user")
    @Expose
    public String trakUser;

    @ColumnInfo(name = "Trak_session")
    @SerializedName("Trak_session")
    @Expose
    public String trakSession;

    @ColumnInfo(name = "Trak_note")
    @SerializedName("Trak_note")
    @Expose
    public String trakNote;

    @ColumnInfo(name = "Trak_CodeOrdre")
    @SerializedName("Trak_CodeOrdre")
    @Expose
    public String trakCodeOrdre;

    public Traking(@NonNull String trakId ,@NonNull double trakLat, @NonNull double trackLong, String trakDateMachine, String trakDateCamion, String trakStation, String trakUser, String trakSession, String trakNote, String trakCodeOrdre) {
        this.trakId = trakId;
        this.trakLat = trakLat;
        this.trackLong = trackLong;
        this.trakDateMachine = trakDateMachine;
        this.trakDateCamion = trakDateCamion;
        this.trakStation = trakStation;
        this.trakUser = trakUser;
        this.trakSession = trakSession;
        this.trakNote = trakNote;
        this.trakCodeOrdre = trakCodeOrdre;
    }
}
