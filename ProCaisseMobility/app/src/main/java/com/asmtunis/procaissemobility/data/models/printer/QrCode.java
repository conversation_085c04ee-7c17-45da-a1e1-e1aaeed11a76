package com.asmtunis.procaissemobility.data.models.printer;


public class QrCode {

    // print the content type
    private int type;
    // Alignment left, center, right
    private int format;
    // The number of empty lines
    private int line;
    // 2D code content
    private String text;


    public QrCode(int type, int format, int line, String text) {
        this.type = type;
        this.format = format;
        this.line = line;
        this.text = text;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getFormat() {
        return format;
    }

    public void setFormat(int format) {
        this.format = format;
    }

    public int getLine() {
        return line;
    }

    public void setLine(int line) {
        this.line = line;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

}
