package com.asmtunis.procaissemobility.helper;

import android.app.Activity;
import android.app.AlarmManager;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.Color;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Build;
import android.provider.Settings;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.inputmethod.InputMethodManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import com.asmtunis.procaissemobility.BuildConfig;
import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Station;
import com.asmtunis.procaissemobility.helper.utils.DateUtils;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;
import com.github.thunder413.datetimeutils.DateTimeUnits;
import com.github.thunder413.datetimeutils.DateTimeUtils;

import java.util.Date;
import java.util.Locale;

import co.chiragm.sbn.StatusBarNotifier;


/**
 * Created by kosh20111 on 18 Oct 2016, 9:29 PM
 */

public class AppHelper {

    public static void hideKeyboard(@NonNull View view) {
        InputMethodManager inputManager = (InputMethodManager) view.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        if (inputManager != null) {
            inputManager.hideSoftInputFromWindow(view.getWindowToken(), 0);
        }
    }

    public static boolean isPOINTMOBILE() {
        return com.blankj.utilcode.util.DeviceUtils.getManufacturer().equalsIgnoreCase("POINTMOBILE");
    }

    @Nullable
    public static Fragment getFragmentByTag(@NonNull FragmentManager fragmentManager, @NonNull String tag) {
        return fragmentManager.findFragmentByTag(tag);
    }


    public static void cancelNotification(@NonNull Context context, int id) {
        NotificationManager notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        if (notificationManager != null) {
            notificationManager.cancel(id);
        }
    }

    public static void cancelAllNotifications(@NonNull Context context) {
        NotificationManager notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        if (notificationManager != null) {
            notificationManager.cancelAll();
        }
    }


    public static String getFastHubIssueTemplate(boolean enterprise) {
        String brand = (!isEmulator()) ? Build.BRAND : "Android Emulator";
        String model = (!isEmulator()) ? DeviceNameGetter.getInstance().getDeviceName() : "Android Emulator";
        StringBuilder builder = new StringBuilder()
                .append("**FastHub Version: ").append(BuildConfig.VERSION_NAME).append(enterprise ? " Enterprise**" : "**").append("  \n")
                .append(!isInstalledFromPlaySore(App.getInstance()) ? "**APK Source: Unknown**  \n" : "")
                .append("**Android Version: ").append(String.valueOf(Build.VERSION.RELEASE)).append(" (SDK: ")
                .append(String.valueOf(Build.VERSION.SDK_INT)).append(")**").append("  \n")
                .append("**Device Information:**").append("  \n")
                .append("- **")
                .append(!model.equalsIgnoreCase(brand) ? "Manufacturer" : "Manufacturer&Brand")
                .append(":** ")
                .append(Build.MANUFACTURER)
                .append("  \n");
        if (!(model.equalsIgnoreCase(brand) || "google".equals(Build.BRAND))) {
            builder.append("- **Brand:** ").append(brand).append("  \n");
        }
        builder.append("- **Model:** ").append(model).append("  \n")
                .append("---").append("\n\n");
        return builder.toString();
    }


    private static void updateResources(Context context, String language) {
        Locale locale = getLocale(language);
        Locale.setDefault(locale);
        Configuration configuration = context.getResources().getConfiguration();
        configuration.setLocale(locale);
        context.createConfigurationContext(configuration);
    }

    @SuppressWarnings("deprecation")
    private static void updateResourcesLegacy(Context context, String language) {
        Locale locale = getLocale(language);
        Locale.setDefault(locale);
        Resources resources = context.getResources();
        Configuration configuration = resources.getConfiguration();
        configuration.locale = locale;
        resources.updateConfiguration(configuration, resources.getDisplayMetrics());
    }

    @NonNull
    private static Locale getLocale(String language) {
        Locale locale = null;
        if (language.equalsIgnoreCase("zh-rCN")) {
            locale = Locale.SIMPLIFIED_CHINESE;
        } else if (language.equalsIgnoreCase("zh-rTW")) {
            locale = Locale.TRADITIONAL_CHINESE;
        }
        if (locale != null) return locale;
        String[] split = language.split("-");
        if (split.length > 1) {
            locale = new Locale(split[0], split[1]);
        } else {
            locale = new Locale(language);
        }
        return locale;
    }

    public static String getDeviceName() {
        if (isEmulator()) {
            return "Android Emulator";
        }
        return DeviceNameGetter.getInstance().getDeviceName();
    }

    public static boolean isEmulator() {
        return Build.FINGERPRINT.startsWith("generic")
                || Build.FINGERPRINT.startsWith("unknown")
                || Build.MODEL.contains("google_sdk")
                || Build.MODEL.contains("Emulator")
                || Build.MODEL.contains("Android SDK built for x86")
                || Build.MANUFACTURER.contains("Genymotion")
                || (Build.BRAND.startsWith("generic") && Build.DEVICE.startsWith("generic"))
                || "google_sdk".equals(Build.PRODUCT);
    }

    public static boolean isConnected() {
        ConnectivityManager
                cm = (ConnectivityManager) App.getInstance().getApplicationContext()
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
        return activeNetwork != null
                && activeNetwork.isConnectedOrConnecting();
    }


    private static boolean isInstalledFromPlaySore(@NonNull Context context) {
        final String ipn = context.getPackageManager().getInstallerPackageName(BuildConfig.APPLICATION_ID);
        return !InputHelper.isEmpty(ipn);
    }


    public static boolean isDeviceAnimationEnabled(@NonNull Context context) {
        float duration = Settings.Global.getFloat(context.getContentResolver(), Settings.Global.ANIMATOR_DURATION_SCALE, 1);
        float transition = Settings.Global.getFloat(context.getContentResolver(), Settings.Global.TRANSITION_ANIMATION_SCALE, 1);
        return (duration != 0 && transition != 0);
    }


    public static void doRestart(Context c) {
        try {
            if (c != null) {

                PackageManager pm = c.getPackageManager();
                //check if we got the PackageManager
                if (pm != null) {
                    //create the intent with the default start activity for your application
                    Intent mStartActivity = pm.getLaunchIntentForPackage(
                            c.getPackageName()
                    );
                    if (mStartActivity != null) {
                        PackageManager packageManager = c.getPackageManager();
                        Intent intent = packageManager.getLaunchIntentForPackage(c.getPackageName());
                        ComponentName componentName = intent.getComponent();
                        Intent mainIntent = Intent.makeRestartActivityTask(componentName);
                        c.startActivity(mainIntent);
                        Runtime.getRuntime().exit(0);
                    } else {
                        Log.e("TAG", "Was not able to restart application, mStartActivity null");
                    }
                } else {
                    Log.e("TAG", "Was not able to restart application, PM null");
                }
            } else {
                Log.e("TAG", "Was not able to restart application, Context null");
            }
        } catch (Exception ex) {
            Log.e("TAG", "Was not able to restart application");
        }
    }

    public static boolean hasPromo(Context context) {
        Station currentStation = App.database.stationDAO().getOneByCode(new PrefUtils(context).getCaisseStationId());


        Log.d("oikolk", String.valueOf(DateTimeUtils.getDateDiff(DateUtils.strToDate(currentStation.getSTATSoldeDD(), "yyyy-MM-dd HH:mm:ss"),
                new Date(), DateTimeUnits.DAYS)));
        return currentStation != null &&
                DateTimeUtils.getDateDiff(DateUtils.strToDate(currentStation.getSTATSoldeDD(), "yyyy-MM-dd HH:mm:ss"),
                new Date(), DateTimeUnits.DAYS) <= 0 &&
                currentStation.getSTATSolde().equals("1");
    }


}