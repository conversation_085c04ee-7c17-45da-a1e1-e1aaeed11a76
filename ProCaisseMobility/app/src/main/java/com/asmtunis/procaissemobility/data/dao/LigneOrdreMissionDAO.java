package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.asmtunis.procaissemobility.data.models.EtatOrdreMission;
import com.asmtunis.procaissemobility.data.models.LigneOrdreMission;
import com.asmtunis.procaissemobility.data.models.OrdreMission;

import java.util.List;

@Dao
public interface LigneOrdreMissionDAO {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<LigneOrdreMission> ligneOrdreMissions);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(LigneOrdreMission ligneOrdreMissions);

    @Update
    void updateLigneOrdreMission(LigneOrdreMission ligneOrdreMission);

    @Query("Delete from LigneOrdreMission")
    void deleteAll();



    @Query("SELECT count(*) FROM LigneOrdreMission where isSync=0")
    LiveData<Integer> getNoSyncCountMutable();

    @Query("SELECT * FROM LigneOrdreMission where isSync=0")
    List<LigneOrdreMission> getNotSync();



    @Query("SELECT * FROM LigneOrdreMission where LIGOR_Code= :LIGORCode")
    List<LigneOrdreMission> getLigneOrdremissionByCode(String LIGORCode);
}
