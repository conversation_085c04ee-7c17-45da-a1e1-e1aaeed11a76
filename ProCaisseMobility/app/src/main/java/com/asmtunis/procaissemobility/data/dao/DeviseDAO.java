package com.asmtunis.procaissemobility.data.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.Devise;

import java.util.List;

/**
 * Created by PC on 11/18/2017.
 */
@Dao
public interface DeviseDAO {

    @Query("SELECT * FROM Devise")
    List<Devise> getAll();

    @Query("SELECT * FROM Devise WHERE Devise = :devise ")
    Devise getOneByDevise(String devise);

    @Query("SELECT * FROM Devise WHERE Activite = '1' ")
    Devise getActiveOne();

    @Query("SELECT * FROM Devise LIMIT 1")
    Devise getOne();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(Devise item);


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<Devise> items);

    @Query("DELETE FROM Devise")
    void deleteAll();


}
