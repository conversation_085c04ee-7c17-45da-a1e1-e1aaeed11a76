package com.asmtunis.procaissemobility.helper.utils;

import static com.asmtunis.procaissemobility.helper.utils.Utils.round;

import android.content.Context;
import android.text.Editable;
import android.util.Log;

import com.asmtunis.procaissemobility.App;
import com.github.thunder413.datetimeutils.DateTimeUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.SecureRandom;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.Locale;
import java.util.Objects;
import java.util.Random;

/**
 * Created by me on 10/7/2017.
 */

public class StringUtils {

    public static final String upper = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    public static final String lower = upper.toLowerCase(Locale.ROOT);
    public static final String digits = "0123456789";
    public static final String alphanum = upper + lower + digits;
    private static final NumberFormat PRICE_FORMATTER = NumberFormat.getNumberInstance();
    static DecimalFormat df = new DecimalFormat("#.##");
    private final Random random;
    private final char[] symbols;
    private final char[] buf;
    public static String fromMap = "fromMap";


    public StringUtils(int length, Random random, String symbols) {
        if (length < 1) throw new IllegalArgumentException();
        if (symbols.length() < 2) throw new IllegalArgumentException();
        this.random = Objects.requireNonNull(random);
        this.symbols = symbols.toCharArray();
        this.buf = new char[length];
    }


    public static String PrefixFormatter(int n, String prefix, String count) {
        Log.d("hiBBVVVV", "PrefixFormatter: " + prefix + " count : " + count);
        return prefix + String.format("%0" + (n - count.length()) + "d", 0) + count;
    }

    public static String FakePrefixFormatter(int n, String prefix, String count) {
        return prefix + String.format("%0" + (n - count.length()) + "d", 0) + count + "fake";
    }



/*

    public static String getDateNow()
    {
        return new Date().toString("dd/MM/yyyy hh:mm:ss");
    }
*/


    /**
     * Create an alphanumeric string generator.
     */
    public StringUtils(int length, Random random) {
        this(length, random, alphanum);
    }

    /**
     * Create an alphanumeric strings from a secure generator.
     */
    public StringUtils(int length) {
        this(length, new SecureRandom());
    }

    /**
     * Create session identifiers.
     */
    public StringUtils() {

        this(21);
    }

    public static double decimalFormat(double value) {
         NumberFormat format = NumberFormat.getInstance(Locale.ENGLISH);

        if (Locale.getDefault().getLanguage().equalsIgnoreCase(Locale.FRENCH.getLanguage())) {
            format = NumberFormat.getInstance(Locale.FRENCH);
        }
        try {
            df = new DecimalFormat("#.####");

         // df.setRoundingMode(RoundingMode.HALF_EVEN); //DONT ROUND VULUE TO INSURE THAT WE GET THE EXTACT PRICE

            return Objects.requireNonNull(format.parse(df.format(value))).doubleValue();
        } catch (ParseException e) {
            e.printStackTrace();
            return value;
        }

    }

    public static String formatDecimal(float number) {
        float epsilon = 0.004f; // 4 tenths of a cent
        if (Math.abs(Math.round(number) - number) < epsilon) {
            return String.format("%10.0f", number); // sdb
        } else {
            return String.format("%10.2f", number); // dj_segfault
        }
    }

    public static String priceFormat(double value) {
         if (PrefUtils.getDecimalCount() > 0) {
            if (Locale.getDefault().getLanguage().equalsIgnoreCase(Locale.FRENCH.getLanguage())) {
                return String.format(Locale.ENGLISH, "%,." + PrefUtils.getDecimalCount() + "f", decimalFormat(value));
            } else {
                return String.format("%,." + PrefUtils.getDecimalCount() + "f", decimalFormat(value));

            }
        } else {
            return Integer.toString((int) decimalFormat(value));
        }



    }

    public static String numberFormat(double value) {
        return  (PrefUtils.getDecimalCount() > 0) ?
                PRICE_FORMATTER.format(value) : Integer.toString((int) value);
    }

    public static double parseDouble(String strNumber, double defaultValue) {
        if (strNumber != null && strNumber.length() > 0) {
            try {
                return Double.parseDouble(strNumber.replaceAll(",","."));
            } catch (Exception e) {
                return defaultValue;   // or some value to mark this field is wrong. or make a function validates field first ...
            }
        } else return defaultValue;
    }

    public static double parseDoublePayment(String strNumber, double defaultValue) {
        if (strNumber != null && strNumber.length() > 0) {
            try {
                return Double.parseDouble(strNumber.replaceAll(",",""));
            } catch (Exception e) {
                return defaultValue;   // or some value to mark this field is wrong. or make a function validates field first ...
            }
        } else return defaultValue;
    }

    public static double parseInteger(String strNumber, int defaultValue) {
        if (strNumber != null && strNumber.length() > 0) {
            try {
                return Integer.parseInt(strNumber.replaceAll(",","."));
            } catch (Exception e) {
                return defaultValue;   // or some value to mark this field is wrong. or make a function validates field first ...
            }
        } else return defaultValue;
    }

    public static boolean isNumeric(String strNum) {
        if (strNum == null) {
            return false;
        }
        try {
            double d = Double.parseDouble(strNum);
        } catch (NumberFormatException nfe) {
            return false;
        }
        return true;
    }

    /**
     * Generate a random string.
     */
    public String nextString() {
        for (int idx = 0; idx < buf.length; ++idx)
            buf[idx] = symbols[random.nextInt(symbols.length)];
        return new String(buf);
    }

    public static boolean isEmptyString(String text) {
        return (text == null || text.trim().equals("null") || text.trim()
                .length() <= 0);
    }


    public static boolean isInputCorrect(Editable s, int size, int dividerPosition, char divider) {
        boolean isCorrect = s.length() <= size;
        for (int i = 0; i < s.length(); i++) {
            if (i > 0 && (i + 1) % dividerPosition == 0) {
                isCorrect &= divider == s.charAt(i);
            } else {
                isCorrect &= Character.isLetterOrDigit(s.charAt(i));
            }
        }
        return isCorrect;
    }

    public static String concatString(char[] digits, int dividerPosition, char divider) {
        final StringBuilder formatted = new StringBuilder();

        for (int i = 0; i < digits.length; i++) {
            if (digits[i] != 0) {
                formatted.append(digits[i]);
                if ((i > 0) && (i < (digits.length - 1)) && (((i + 1) % dividerPosition) == 0)) {
                    formatted.append(divider);
                }
            }
        }

        return formatted.toString();
    }

    public static char[] getDigitArray(final Editable s, final int size) {
        char[] digits = new char[size];
        int index = 0;
        for (int i = 0; i < s.length() && index < size; i++) {
            char current = s.charAt(i);
            if (Character.isLetterOrDigit(current)) {
                digits[index] = current;
                index++;
            }
        }
        return digits;
    }

    public static String createIDTrack(Context context) {
        return new PrefUtils(context).getUserId() + System.currentTimeMillis();
    }

}
