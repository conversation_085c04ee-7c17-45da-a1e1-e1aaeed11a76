package com.asmtunis.procaissemobility.helper.utils;


import com.asmtunis.procaissemobility.data.models.ChequeCaisse;
import com.asmtunis.procaissemobility.data.models.LigneTicket;
import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.data.models.TraiteCaisse;

import java.util.ArrayList;
import java.util.List;

import static com.asmtunis.procaissemobility.helper.utils.StringUtils.decimalFormat;

import android.util.Log;

/**
 * Created by PC on 10/24/2017.
 */

public class Calculator {

    public static double calculateAmountTTC(double price, double quantity) {
        return decimalFormat(decimalFormat(price) * quantity);
    }

    public static double calculateAmountTTCNet(double amount, double discount) {
        return decimalFormat(amount - (discount * amount) / 100.0);
    }

    public static double calculateAmountTTCNet(double price, double quantity, double discount) {
        return decimalFormat((price * quantity));
      //  return decimalFormat((price * quantity)  );
    }

    public static double calculateAmountHT(double amount, double tva) {
        return decimalFormat(amount / (1 + (tva / 100)));
    }

    public static double calculateAmountHT(double price, double quantity, double tva) {
        return decimalFormat((price * quantity) / (1 + (tva / 100)));
    }


    public static double calculateAmountExcludingTax(double price, double vat) {
        return decimalFormat(price / (1 + (vat / 100)));
    }


    public static double calculateAmountExcludingTax(double price, double quantity, double vat) {
        return decimalFormat(price / (1 + (vat / 100))) * quantity;
    }

    public static double calculateAmountHTNet(double amount, double discount, double tva) {
        return decimalFormat((amount - (discount * amount) / 100.0) / (1 + (tva / 100)));
    }

    public static double calculateAmountHTNet(double price, double quantity, double discount, double tva) {
        return decimalFormat(((price * quantity) - (discount * (price * quantity)) / 100.0) / (1 + (tva / 100)));

    }

    public static double calculateDiscountRate(double price, double unitPrice) {
        System.out.println("price" + price);
        System.out.println("unitPrice" + unitPrice);
        return decimalFormat((1 - (price / unitPrice)) * 100.0);
    }


    public static double calculateMade(double amount, double received, boolean isCredit) {
        double made = isCredit ? decimalFormat((received > amount) ? received + amount : 0) : decimalFormat((received > amount) ? received - amount : 0)  ;
        return made > 0 ? made : 0;
    }


    public static double calculateRest(double amount, double received, ArrayList<ChequeCaisse> chequeCaisses, ArrayList<TraiteCaisse> traiteCaisses,boolean isCredit) {
        double rest = isCredit ? amount + calculateReceived(received, chequeCaisses, traiteCaisses) :  amount - calculateReceived(received, chequeCaisses, traiteCaisses);
        return isCredit  ? (rest < 0 ? rest : 0) : (rest > 0 ? rest : 0);
    }

    public static double calculateRest(double amount, double received, double chequeCaisses, double traiteCaisses, boolean isCredit) {
        double rest = isCredit ? amount + calculateReceived(received, chequeCaisses, traiteCaisses) :  amount - calculateReceived(received, chequeCaisses, traiteCaisses);
        return isCredit  ? (rest < 0 ? rest : 0) : (rest > 0 ? rest : 0);
    }


    public static double calculateReceived(double received, ArrayList<ChequeCaisse> chequeCaisses, ArrayList<TraiteCaisse> traiteCaisses) {
        return received + calculateChecks(chequeCaisses) + calculateTickets(traiteCaisses);
    }

    public static double calculateReceived(double received, double chequeCaisses, double traiteCaisses) {
        return received + chequeCaisses + traiteCaisses;
    }

    public static double calculateCheckAndCartResto(double chequeCaisses, double traiteCaisses) {
        return chequeCaisses + traiteCaisses;
    }

    public static double calculateChecks(ArrayList<ChequeCaisse> chequeCaisses) {
        double chequeCaisseAmount = 0;
        if (chequeCaisses != null) {
            for (ChequeCaisse chequeCaisse : chequeCaisses) {
                chequeCaisseAmount += chequeCaisse.getMontant();
            }
        }
        return chequeCaisseAmount;
    }


    public static double calculateTickets(ArrayList<TraiteCaisse> traiteCaisses) {
        double traiteCaisseAmount = 0;
        if (traiteCaisses != null) {
            for (TraiteCaisse traiteCaisse :
                    traiteCaisses) {
                traiteCaisseAmount += traiteCaisse.gettRAITMontant();
            }
        }
        return traiteCaisseAmount;
    }


    public static double calculateTicketAmount(List<LigneTicket> ligneTickets, int function) {
        double result = 0;
        switch (function) {
            case R.id.calculateAmountTTC:
                for (LigneTicket ligneTicket : ligneTickets) {
                   result += calculateAmountTTC(ligneTicket.getArticle().getPvttc(), ligneTicket.getlTQte());
                    //  result += calculateAmountTTC(ligneTicket.getlTMtTTC()/ligneTicket.getlTQte(), ligneTicket.getlTQte());
                }
                break;

            case R.id.calculateAmountTTCNet:
                for (LigneTicket ligneTicket : ligneTickets) {
                    result += ligneTicket.getlTMtTTC();
                }
                break;

            case R.id.calculateAmountHT:
                for (LigneTicket ligneTicket : ligneTickets) {
                    // result += calculateAmountHT(ligneTicket.getlTMtTTC(), ligneTicket.getlTTVA());
                    result += ligneTicket.getlTMtHT();
                }
                break;
            case R.id.calculateAmountHTNet:
                for (LigneTicket ligneTicket : ligneTickets) {
                    result += calculateAmountHTNet(ligneTicket.getlTMtTTC(), ligneTicket.getlTTauxRemise(), ligneTicket.getlTTVA());
                }
                break;

            case R.id.calculateAmountVAT:
                for (LigneTicket ligneTicket : ligneTickets) {
                    result += ligneTicket.getlTMtTTC() - ligneTicket.getlTMtHT();
                }

                break;
        }
        return    Utils.round(decimalFormat(result), 3);
    }
}
