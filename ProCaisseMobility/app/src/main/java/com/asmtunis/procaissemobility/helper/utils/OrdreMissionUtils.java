package com.asmtunis.procaissemobility.helper.utils;

import static com.asmtunis.procaissemobility.helper.utils.DateUtils.getCurrentDate;
import static com.blankj.utilcode.util.ServiceUtils.bindService;

import android.content.Context;
import android.content.Intent;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.LigneOrdreMission;
import com.asmtunis.procaissemobility.data.models.OrdreMission;
import com.asmtunis.procaissemobility.data.models.OrdreWithLines;
import com.asmtunis.procaissemobility.data.models.TrakingAlarm;
import com.asmtunis.procaissemobility.data.models.Vendeur;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.datamanager.OrdreMissionDataManager;
import com.asmtunis.procaissemobility.helper.Globals;
import com.asmtunis.procaissemobility.recivers.AlarmReceiver;
import com.asmtunis.procaissemobility.services.LocationUpdatesService;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.function.Function;

/**
 * Created by Oussama AZIZI on 4/11/22.
 */

public class OrdreMissionUtils {

    static SimpleDateFormat dt = new SimpleDateFormat(Globals.DATE_PATTERN);
    Context context;

    public OrdreMissionUtils(Context context) {
        this.context = context;
    }

    /**
     * get OrdreWithLines data from server
     */
    public void getOrdreWithLines() {
        OrdreMissionDataManager.getInstance().getOrdreMessionWithLines(new GenericObject(new PrefUtils(context).getBaseConfig(),
                        new Vendeur(new PrefUtils(context).getUserId(), getCurrentDate())),
                new RemoteCallback<List<OrdreWithLines>>(context, false) {
                    @Override
                    public void onSuccess(List<OrdreWithLines> response) {
                        if (!response.isEmpty()) {
                            App.database.ordreMissionDAO().deleteAll();
                            App.database.ligneOrdreMissionDAO().deleteAll();
                            for (OrdreWithLines ordreWithLines : response) {
                                OrdreMission ordreMission = App.database.ordreMissionDAO().getBycode(ordreWithLines.ordreMission.oRDCode);
                                App.database.ordreMissionDAO().insertOne(ordreWithLines.ordreMission);
                                for(LigneOrdreMission ligneOrdreMission: ordreWithLines.ligneOrdreMission) {
                                    ligneOrdreMission.isSync = true;
                                }
                                App.database.ligneOrdreMissionDAO().insertAll(ordreWithLines.ligneOrdreMission);
                                TrakingAlarm trakingAlarm = App.database.trakingAlarmDAO().getWithOrdreMission(ordreWithLines.ordreMission.oRDCode);
                                if(ordreMission != null) {
                                    AlarmReceiver alarmReceiver = new AlarmReceiver(context);
                                    switch (ordreMission.compareTo(ordreWithLines.ordreMission)) {
                                        case 0: {
                                            trakingAlarm.started = false;
                                            trakingAlarm.setted = false;
                                            trakingAlarm.startDate = ordreWithLines.ordreMission.oRD_dateDebut;
                                            if(ServiceUtils.mService != null) {
                                                ServiceUtils.mService.removeLocationUpdates();
                                            }
                                            alarmReceiver.stopServiceAlarm(Integer.parseInt(trakingAlarm.startAlarmID));
                                        } break;
                                        case 1: {
                                            trakingAlarm.ended = false;
                                            trakingAlarm.setted = false;
                                            trakingAlarm.endDate = ordreWithLines.ordreMission.oRD_dateFin;
                                            alarmReceiver.stopServiceAlarm(Integer.parseInt(trakingAlarm.endAlarmID));
                                        } break;
                                        case 2: {
                                            trakingAlarm.started = false;
                                            trakingAlarm.ended = false;
                                            trakingAlarm.setted = false;
                                            trakingAlarm.startDate = ordreWithLines.ordreMission.oRD_dateDebut;
                                            trakingAlarm.endDate = ordreWithLines.ordreMission.oRD_dateFin;
                                            alarmReceiver.stopServiceAlarm(Integer.parseInt(trakingAlarm.startAlarmID));
                                            alarmReceiver.stopServiceAlarm(Integer.parseInt(trakingAlarm.endAlarmID));
                                        } break;
                                    }
                                    App.database.trakingAlarmDAO().insertTrAlarm(trakingAlarm);

                                }
                                if (!(ordreWithLines.ordreMission.oRD_dateDebut == null) && !(ordreWithLines.ordreMission.oRD_dateFin == null) && trakingAlarm == null) {
                                    trakingAlarm
                                            = new TrakingAlarm (
                                            ordreWithLines.ordreMission.oRDCode,
                                            ordreWithLines.ordreMission.oRD_dateDebut,
                                            ordreWithLines.ordreMission.oRD_dateFin,
                                            false,
                                            false,
                                            false
                                    );
                                    App.database.trakingAlarmDAO().insertTrAlarm(trakingAlarm);
                                }
                            }

                            if(response.size() > 0) {
                                try {
                                    setupAlarms(context);
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                }

                            }
                        }
                    }

                    @Override
                    public void onUnauthorized() {
                    }

                    @Override
                    public void onFailed(Throwable throwable) {
                    }
                }
        );
    }

    public static void setupAlarms(Context context) throws ParseException {
        List<TrakingAlarm> trakingAlarms = App.database.trakingAlarmDAO().getAll();
        for (TrakingAlarm trakingAlarm: trakingAlarms) {
            Date dateNow = Calendar.getInstance().getTime();
            if(!trakingAlarm.setted) {
                if(dateNow.compareTo(dt.parse(trakingAlarm.endDate)) <= 0) {
                    AlarmReceiver startAlarmReceiver = new AlarmReceiver(context);
                    AlarmReceiver endAlarmReceiver = new AlarmReceiver(context);
                    startAlarmReceiver.setStartServiceAlarm(trakingAlarm.startDate, trakingAlarm.codeOrdreMission);
                    endAlarmReceiver.setEndServiceAlarm(trakingAlarm.endDate, trakingAlarm.codeOrdreMission);
                }
                else {
                    trakingAlarm.started = true;
                    trakingAlarm.ended = true;
                    trakingAlarm.setted = true;
                    App.database.trakingAlarmDAO().insertTrAlarm(trakingAlarm);
                }
            }
            else {
                if(trakingAlarm.started && !(trakingAlarm.ended)) {
                    if(dateNow.compareTo(dt.parse(trakingAlarm.endDate)) <= 0) {
                        if(ServiceUtils.mService != null && !ServiceUtils.mService.serviceIsRunningInForeground(context)) {
                            ServiceUtils.mService.requestLocationUpdates(null);
                        }
                        else bindService(new Intent(context, LocationUpdatesService.class), ServiceUtils.mServiceConnection, Context.BIND_AUTO_CREATE);
                        AlarmReceiver endAlarmReceiver = new AlarmReceiver(context);
                        endAlarmReceiver.setEndServiceAlarm(trakingAlarm.endDate, trakingAlarm.codeOrdreMission);
                    }
                    else {
                        trakingAlarm.started = true;
                        trakingAlarm.ended = true;
                        trakingAlarm.setted = true;
                        App.database.trakingAlarmDAO().insertTrAlarm(trakingAlarm);
                    }
                }
            }
        }
    }

}
