package com.asmtunis.procaissemobility.data.models;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

public class DNVisteWithLines extends BaseModel implements Serializable {


    public DNVisteWithLines() {
    }
    public DNVisteWithLines(List<DN_LigneVisite> dnListligneVisites,DNVIsite dnVisite) {
        this.dnListligneVisites =dnListligneVisites;
        this.dnVisite = dnVisite;
    }

    public List<DN_LigneVisite> getDnListligneVisites() {
        return dnListligneVisites;
    }

    public void setDnListligneVisites(List<DN_LigneVisite> dnListligneVisites) {
        this.dnListligneVisites = dnListligneVisites;
    }

    public DNVIsite getDnVisite() {
        return dnVisite;
    }

    public void setDnVisite(DNVIsite dnVisite) {
        this.dnVisite = dnVisite;
    }

    @SerializedName("DN_LigneVisite")
    @Expose
    List<DN_LigneVisite> dnListligneVisites;


    @SerializedName("DN_Visite")
    @Expose
    DNVIsite dnVisite;

}
