package com.asmtunis.procaissemobility.data.network.datamanager;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.OrdreWithLines;
import com.asmtunis.procaissemobility.data.models.Ticket;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.OrdreMissionService;
import com.asmtunis.procaissemobility.data.network.services.TicketService;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;
import com.asmtunis.procaissemobility.helper.utils.Utils;

import java.util.List;

public class OrdreMissionDataManager {

    private static OrdreMissionDataManager sInstance;

    private final OrdreMissionService ordreMissionService;

    public OrdreMissionDataManager() {
         ordreMissionService = new ServiceFactory<>(OrdreMissionService.class, String.format(Utils.validateBaseUrl(), PrefUtils.getServerIPAddress(),PrefUtils.getServerPort(),"OrdreMission")).makeService();


    }
    public static OrdreMissionDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new OrdreMissionDataManager();
        }
        return sInstance;
    }

    public void getOrdreMessionWithLines(GenericObject genericObject, RemoteCallback<List<OrdreWithLines>> listener) {
        ordreMissionService.getOrdreMissionWithLines(genericObject)
                .enqueue(listener);
    }

    public void updateLigneOrdreMission(GenericObject genericObject, RemoteCallback<Boolean> listener) {
        ordreMissionService.updateLigneOrdreMission(genericObject)
                .enqueue(listener);
    }

    public void batchUpdateLigneOrdreMission(GenericObject genericObject, RemoteCallback<Boolean> listener) {
        ordreMissionService.batchUpdateLigneOrdreMission(genericObject)
                .enqueue(listener);
    }

    public void addOrdreMission(GenericObject genericObject, RemoteCallback<Boolean> listener) {
        ordreMissionService.addOrdreMission(genericObject)
                .enqueue(listener);
    }
}