package com.asmtunis.procaissemobility.data.network.datamanager;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Station;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.StationService;

import java.util.List;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

public class StationDataManager {
    private static StationDataManager sInstance;
    private final StationService mStationService;

    public StationDataManager() {

        mStationService = new ServiceFactory<>(StationService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Station")).makeService();
    }

    public static StationDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new StationDataManager();
        }
        return sInstance;
    }

    public void getAllStations(GenericObject genericObject,
                                  RemoteCallback<List<Station>> listener) {
        mStationService.getAllStations(genericObject)
                .enqueue(listener);
    }

}

