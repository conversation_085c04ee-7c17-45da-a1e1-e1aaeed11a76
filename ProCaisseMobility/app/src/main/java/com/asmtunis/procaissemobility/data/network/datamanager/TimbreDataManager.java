package com.asmtunis.procaissemobility.data.network.datamanager;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Timbre;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.TimbreService;

import java.util.List;

public class TimbreDataManager {
    private static TimbreDataManager sInstance;

    private final TimbreService mTimbreService;

    public TimbreDataManager() {
        mTimbreService = new ServiceFactory<>(TimbreService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Timbre")).makeService();
    }

    public static TimbreDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new TimbreDataManager();
        }
        return sInstance;
    }

    public void getTimbres(GenericObject genericObject,
                                     RemoteCallback<List<Timbre>> listener) {
        mTimbreService.getTimbres(genericObject)
                .enqueue(listener);
    }
}
