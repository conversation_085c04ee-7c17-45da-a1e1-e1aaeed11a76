package com.asmtunis.procaissemobility.adapters.recyclerviews;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import androidx.appcompat.widget.Toolbar;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.listener.ItemCallback;
import com.asmtunis.procaissemobility.listener.MenuItemsAction;
import com.asmtunis.procaissemobility.ui.components.TicketView;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
/**
 * Created by PC on 12/2/2017.
 */

public abstract class BaseListAdapter<T> extends RecyclerView.Adapter<BaseListAdapter<T>
        .ViewHolder> {




    private static final int UNSELECTED = -1;
    MenuItemsAction menuItemsAction;
    int menuId;
    protected Context context;
    protected List<T> mArrayList;
    // TierFilter<T> filter;
    private List<T> mFilteredList;
    protected int selectedItem = UNSELECTED;

    protected ItemCallback itemCallback;


    public BaseListAdapter(Context context, List<T> arrayList, Comparator comparator, int menuId, MenuItemsAction
            menuItemsAction) {
        this.menuId = menuId;
        this.context = context;
        Collections.sort(arrayList, comparator);
        mArrayList = arrayList;
        mFilteredList = arrayList;
        this.menuItemsAction = menuItemsAction;
    }

    public BaseListAdapter(Context context, List<T> arrayList, Comparator comparator, int menuId, MenuItemsAction
            menuItemsAction, ItemCallback itemCallback) {
        this.menuId = menuId;
        this.context = context;
        Collections.sort(arrayList, comparator);
        mArrayList = arrayList;
        mFilteredList = arrayList;
        this.menuItemsAction = menuItemsAction;
        this.itemCallback = itemCallback;
    }




    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemViewType(int position) {
        return position;
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.ticket_item, parent, false);

        return new ViewHolder(itemView);
    }




    @Override
    public void onBindViewHolder(ViewHolder viewHolder, int position, List<Object> payloads) {
        super.onBindViewHolder(viewHolder, position, payloads);
        viewHolder.toolbar.setSelected(false);
        //viewHolder.expandableLayout.collapse(false);
        setViewHolder(viewHolder, position);

    }

    public abstract void setViewHolder(ViewHolder holder, int position);



    @Override
    public int getItemCount() {
        return mArrayList.size();
    }




    public class ViewHolder extends RecyclerView.ViewHolder {
        // @BindView(R.id.expandable_layout)
     //   ExpandableLayout expandableLayout;
        //   @BindView(R.id.toolbar)
        TicketView layoutTicket;
        Toolbar toolbar;
        //  @BindView(R.id.dateCreation)
        TextView dateCreation;
        //   @BindView(R.id.price)
        TextView price;
        //   @BindView(R.id.content_layout)
        FrameLayout content;

        public ViewHolder(View view) {
            super(view);
            //     ButterKnife.bind(this, view);


            // expandableLayout = (ExpandableLayout) itemView.findViewById(R.id.expandable_layout);
        //    expandableLayout.setInterpolator(new OvershootInterpolator());
            layoutTicket =  view.findViewById(R.id.layout_ticket);
            toolbar = (Toolbar) view.findViewById(R.id.toolbar);
            toolbar.inflateMenu(menuId);
            dateCreation = (TextView) view.findViewById(R.id.dateCreation);
            price = (TextView) view.findViewById(R.id.price);
            content = (FrameLayout) view.findViewById(R.id.content_layout);



        }
    }


}