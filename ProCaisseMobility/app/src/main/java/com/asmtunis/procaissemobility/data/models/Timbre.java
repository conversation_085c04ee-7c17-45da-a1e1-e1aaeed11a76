package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

@Entity
public class Timbre implements Serializable {

    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "TIMB_Code")
    @SerializedName("TIMB_Code")
    @Expose
    public String tIMBCode;

    @ColumnInfo(name = "TIMB_Value")
    @SerializedName("TIMB_Value")
    @Expose
    public String tIMBValue;

    @ColumnInfo(name = "TIMB_Loi")
    @SerializedName("TIMB_Loi")
    @Expose
    public String tIMBLoi;

    @ColumnInfo(name = "TIMB_Etat")
    @SerializedName("TIMB_Etat")
    @Expose
    public String tIMBEtat;

    @ColumnInfo(name = "TIMB_User")
    @SerializedName("TIMB_User")
    @Expose
    public String tIMBUser;

    @ColumnInfo(name = "TIMB_Station")
    @SerializedName("TIMB_Station")
    @Expose
    public String tIMBStation;

    @ColumnInfo(name = "TIMB_export")
    @SerializedName("TIMB_export")
    @Expose
    public String tIMBExport;

    @ColumnInfo(name = "TIMB_DDm")
    @SerializedName("TIMB_DDm")
    @Expose
    public String tIMBDDm;

    @ColumnInfo(name = "TIMB_CodCompta")
    @SerializedName("TIMB_CodCompta")
    @Expose
    public String tIMBCodCompta;

    public String getTIMBCode() {
        return tIMBCode;
    }

    public void setTIMBCode(String tIMBCode) {
        this.tIMBCode = tIMBCode;
    }

    public String getTIMBValue() {
        return tIMBValue;
    }

    public void setTIMBValue(String tIMBValue) {
        this.tIMBValue = tIMBValue;
    }

    public Object getTIMBLoi() {
        return tIMBLoi;
    }

    public void setTIMBLoi(String tIMBLoi) {
        this.tIMBLoi = tIMBLoi;
    }

    public String getTIMBEtat() {
        return tIMBEtat;
    }

    public void setTIMBEtat(String tIMBEtat) {
        this.tIMBEtat = tIMBEtat;
    }

    public Object getTIMBUser() {
        return tIMBUser;
    }

    public void setTIMBUser(String tIMBUser) {
        this.tIMBUser = tIMBUser;
    }

    public Object getTIMBStation() {
        return tIMBStation;
    }

    public void setTIMBStation(String tIMBStation) {
        this.tIMBStation = tIMBStation;
    }

    public Object getTIMBExport() {
        return tIMBExport;
    }

    public void setTIMBExport(String tIMBExport) {
        this.tIMBExport = tIMBExport;
    }

    public Object getTIMBDDm() {
        return tIMBDDm;
    }

    public void setTIMBDDm(String tIMBDDm) {
        this.tIMBDDm = tIMBDDm;
    }

    public String getTIMBCodCompta() {
        return tIMBCodCompta;
    }

    public void setTIMBCodCompta(String tIMBCodCompta) {
        this.tIMBCodCompta = tIMBCodCompta;
    }
}
