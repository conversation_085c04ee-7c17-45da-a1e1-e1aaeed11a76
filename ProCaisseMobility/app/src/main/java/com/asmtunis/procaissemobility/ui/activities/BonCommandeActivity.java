package com.asmtunis.procaissemobility.ui.activities;

import static android.view.View.GONE;
import static com.asmtunis.procaissemobility.App.prefUtils;
import static com.asmtunis.procaissemobility.helper.Globals.CLIENT_INTENT_ID_KEY;
import static com.asmtunis.procaissemobility.helper.Globals.DEFAULT_ENCODING;
import static com.asmtunis.procaissemobility.helper.utils.UIUtils.getIconicsDrawable;
import static com.asmtunis.procaissemobility.helper.utils.UIUtils.setDefaultAutoScanColor;
import static com.asmtunis.procaissemobility.helper.utils.UIUtils.switchAutoScanColor;
import static com.asmtunis.procaissemobility.helper.utils.Utils.indexOfClient;
import static com.honeywell.aidc.BarcodeReader.PROPERTY_CODE_128_ENABLED;
import static com.honeywell.aidc.BarcodeReader.PROPERTY_EAN_13_CHECK_DIGIT_TRANSMIT_ENABLED;
import static com.honeywell.aidc.BarcodeReader.PROPERTY_EAN_13_ENABLED;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.ProgressDialog;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.Gravity;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;
import androidx.core.content.res.ResourcesCompat;
import androidx.fragment.app.FragmentActivity;

import com.afollestad.materialdialogs.MaterialDialog;
import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.adapters.spinners.ClientSpinnerAdapter;
import com.asmtunis.procaissemobility.adapters.tables.LigneTicketTableDataAdapter;
import com.asmtunis.procaissemobility.data.models.Article;
import com.asmtunis.procaissemobility.data.models.BonCommande;
import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.data.models.LigneBonCommande;
import com.asmtunis.procaissemobility.data.models.LigneTicket;
import com.asmtunis.procaissemobility.data.models.Ticket;
import com.asmtunis.procaissemobility.data.viewModels.ArticleViewModel;
import com.asmtunis.procaissemobility.data.viewModels.ClientViewModel;
import com.asmtunis.procaissemobility.helper.BluetoothService;
import com.asmtunis.procaissemobility.helper.GPSTracker;
import com.asmtunis.procaissemobility.helper.Globals;
import com.asmtunis.procaissemobility.helper.PrinterHelper;
import com.asmtunis.procaissemobility.helper.utils.DateUtils;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.helper.utils.UIUtils;
import com.asmtunis.procaissemobility.helper.utils.Utils;
import com.asmtunis.procaissemobility.helper.wifi_print.Comman;
import com.asmtunis.procaissemobility.helper.wifi_print.WifiPrint;
import com.asmtunis.procaissemobility.listener.DialogClickInterface;
import com.asmtunis.procaissemobility.ui.components.SortableLigneTicketTableView;
import com.asmtunis.procaissemobility.ui.dialogs.ArticleDialog;
import com.asmtunis.procaissemobility.ui.dialogs.ArticleListDialog;
import com.asmtunis.procaissemobility.ui.dialogs.BarCodeDialogueFragment;
import com.asmtunis.procaissemobility.ui.dialogs.BarcodeScannerListener;
import com.asmtunis.procaissemobility.ui.dialogs.LigneTicketDialog;
import com.blankj.utilcode.util.ObjectUtils;
import com.example.barecodereader.barcode.BarCodeReaderManager;
import com.example.barecodereader.barcode.listener.BarcodeListener;
import com.example.barecodereader.barcode.readers.EdaReader;
import com.example.barecodereader.barcode.readers.PM80Reader;
import com.google.android.material.snackbar.Snackbar;
import com.mikepenz.fontawesome_typeface_library.FontAwesome;
import com.mikepenz.google_material_typeface_library.GoogleMaterial;
import com.mikepenz.iconics.IconicsDrawable;
import com.mobsandgeeks.saripaar.ValidationError;
import com.mobsandgeeks.saripaar.Validator;
import com.rengwuxian.materialedittext.MaterialEditText;
import com.transitionseverywhere.Slide;
import com.transitionseverywhere.TransitionManager;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;
import es.dmoral.toasty.Toasty;
import searchablespinner.interfaces.OnItemSelectedListener;
import timber.log.Timber;

public class BonCommandeActivity extends BaseActivity implements DialogClickInterface {
    private static final int REQUEST_CLIENT_CODE = 101;
    public static final int REQUEST_CONNECT_DEVICE = 845;
    public static final int REQUEST_ENABLE_BT = 875;
    int index_prod_intable = 0;

    public static BluetoothDevice con_dev = null;
    public BluetoothService mService = null;
    int ticketNumber;
    String bonCmdNumber;
    ArticleDialog articleDialog;
    private static final String TAG = "Ticket activity";
    @BindView(R.id.DateInputField)
    TextView dateInputField;
    static Validator.ValidationListener validationListener;

    Ticket ticket;
    BonCommande bonCommande;
    List<LigneBonCommande> ligneBonCommandes;

    static EditText amountInputField;

    static EditText amountwithDiscountInputField;

    static EditText discountInputField;

    @BindView(R.id.addItemButton)
    com.mikepenz.iconics.view.IconicsButton addItemButton;

    SortableLigneTicketTableView tableView;

    @BindView(R.id.SearchableClientSpinner)
    searchablespinner.SearchableSpinner searchableSpinner;

    @BindView(R.id.buttons_layout)
    LinearLayoutCompat linearLayoutCompat;

    @BindView(R.id.autoscan)
    com.mikepenz.iconics.view.IconicsButton autoScanButton;

    @BindView(R.id.scan)
    com.mikepenz.iconics.view.IconicsButton scanArt;

    View footerTicketView;


    View dataTableView;
    TextView tableTitle;

    @BindView(R.id.headerTicketView)
    View headerTicketView;

    @BindView(R.id.dividerView1)
    View dividerView1;
    @BindView(R.id.dividerView2)
    View dividerView2;

    @BindView(R.id.discountLayout)
    RelativeLayout discountLayout;

    @BindView(R.id.AmountwithDiscountLayout)
    RelativeLayout AmountwithDiscountLayout;

    @BindView(R.id.addClientButton)
    com.mikepenz.iconics.view.IconicsButton addClientButton;

    MenuItem save, scan;

    static ViewGroup content;

    ClientSpinnerAdapter simpleListAdapter;

    Client client;

    static boolean dialogShown = false;
    static LigneTicketDialog ligneTicketDialog;

    Bundle savedInstanceState;
    static ArticleListDialog articleListDialog;
    static LigneTicketTableDataAdapter tableDataAdapter = null;

    ArrayList<LigneTicket> selectedArticles;
    static double amount;
    static double amountWithDiscount;
    double made;
    static double discount;
    double received;
    Date date;

    @BindView(R.id.toolbar)
    Toolbar toolbar;
    static FragmentActivity context;
    Intent intent;
    Unbinder unbinder;
    GPSTracker gPSTracker;
    double entreLongitude, entreAltitude;

    BarCodeReaderManager barCodeReaderManager;
    ProgressDialog mprogress;

    private Boolean isUpdate = false;
    String ddm = "";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        unbinder = ButterKnife.bind(this);
        initComponents();

        autoScanButton.setDrawableBottom(getIconicsDrawable(GoogleMaterial.Icon.gmd_wb_auto, R.color.material_drawer_accent, this));
        setDefaultAutoScanColor(autoScanButton, this);
        scanArt.setDrawableBottom(getIconicsDrawable(GoogleMaterial.Icon.gmd_flip, R.color.material_drawer_accent, this));
        mprogress = new ProgressDialog(this);
        isUpdate = getIntent().getBooleanExtra(Globals.UPDATE_TICKET, false);
        bonCommande = (BonCommande) getIntent().getSerializableExtra(Globals.TICKET_TO_UPDATE);
        if (isUpdate) ddm = bonCommande.getDEVDDm();
        this.savedInstanceState = savedInstanceState;
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setDisplayShowCustomEnabled(true);
        getSupportActionBar().setDisplayUseLogoEnabled(false);
        context = this;
        tableDataAdapter = null;
        gPSTracker = new GPSTracker(context);
        entreLongitude = gPSTracker.getLongitude();
        entreAltitude = gPSTracker.getLatitude();
        discountLayout.setVisibility(GONE);
        dividerView1.setVisibility(GONE);
        dividerView2.setVisibility(GONE);
        AmountwithDiscountLayout.setVisibility(GONE);
        if ((gPSTracker.getLatitude() > 0) && (gPSTracker.getLongitude() > 0)) {
            getSupportActionBar().setSubtitle(String.format(context.getResources().getString(R.string.lat_log), gPSTracker.getLatitude() + "", gPSTracker.getLongitude() + ""));
        }
        startBarcode();
     /*   barCodeReaderManager = new BarCodeReaderManager();
        barCodeReaderManager.addReader(new EdaReader(this, new BarcodeListener() {
            @Override
            public void onSuccess(String event) {
                showArticleFromScan(event);
            }

            @Override
            public void onFail(String event) {
                Toasty.info(context, R.string.error_read_codebare).show();
            }
        }, null)).addReader(new PM80Reader(new BarcodeListener() {
            @Override
            public void onSuccess(String event) {
                showArticleFromScan(event);
            }

            @Override
            public void onFail(String event) {
                Toasty.info(context, R.string.error_read_codebare).show();
            }
        }));
        barCodeReaderManager.startListener();
*/
        getData();

        validationListener = new Validator.ValidationListener() {
            @Override
            public void onValidationSucceeded() {
            }

            @Override
            public void onValidationFailed(List<ValidationError> errors) {
                for (ValidationError error : errors) {
                    View view = error.getView();
                    String message = error.getCollatedErrorMessage(context);

                    if (view instanceof EditText) {
                        ((MaterialEditText) view).setError(message);
                    } else {
                        Toast.makeText(context, message, Toast.LENGTH_LONG).show();
                    }
                }
            }
        };
        addClientButton.setVisibility(GONE);

        if (isUpdate) {
            bonCmdNumber = bonCommande.getDEVNum();
            client = App.database.clientDAO().getOneByCode(bonCommande.getDEVCodeClient());
        } else {
            bonCmdNumber = String.valueOf(App.database.bonCommandeDAO().getCount() + 1);
        }
        getSupportActionBar().setTitle(String.format(getString(R.string.bc_number_field), bonCmdNumber + ""));
    }

    /**
     * load view from xml
     */
    private void initComponents() {
        amountInputField = findViewById(R.id.AmountInputField);
        amountwithDiscountInputField = findViewById(R.id.AmountwithDiscountInputField);
        discountInputField = findViewById(R.id.DiscountInputField);
        tableView = findViewById(R.id.tableView);
        dataTableView = findViewById(R.id.dataTableView);
        tableTitle = findViewById(R.id.produitTxtVw);
        footerTicketView = findViewById(R.id.footerTicketView);
        linearLayoutCompat.setVisibility(GONE);
        content = findViewById(android.R.id.content);
    }

    void setupView(List<Article> articles, List<Article> articlesStockable) {
        setArticlesListView(articles, articlesStockable);
        setTicketHeader();
        setTicketFooter();
    }

    void startBarcode() {
        barCodeReaderManager = new BarCodeReaderManager();
        Map<String, Object> properties = new HashMap();
        properties.put(PROPERTY_CODE_128_ENABLED, true);
        properties.put(PROPERTY_EAN_13_ENABLED, true);
        properties.put(PROPERTY_EAN_13_CHECK_DIGIT_TRANSMIT_ENABLED, prefUtils.getEan13CheckDigitEnabled());
        EdaReader edaReader = new EdaReader(this, new BarcodeListener() {
            @Override
            public void onSuccess(String event) {
                Toasty.info(context, "" + event).show();
                showArticleFromScan(event);
            }

            @Override
            public void onFail(String event) {
                Toasty.info(context, R.string.error_read_codebare).show();
            }
        }, properties);
        barCodeReaderManager.addReader(edaReader).addReader(new PM80Reader(new BarcodeListener() {
            @Override
            public void onSuccess(String event) {
                Toasty.info(context, "" + event).show();
                showArticleFromScan(event);
            }

            @Override
            public void onFail(String event) {
                Log.d(TAG, "event : " + event);
                Toasty.info(context, R.string.error_read_codebare).show();
            }
        }));
        barCodeReaderManager.startListener();

    }

    /**
     * get articles list form local database
     */
    private void getData() {
        ArticleViewModel.getInstance(this).getAllNotPat().observe(this, articles -> {
            List<Article> allArticles = articles;
            ArticleViewModel.getInstance(this).getAllNotPatStockable().observe(this, articles1 -> {
                List<Article> articlesStockables = articles1;
                if (allArticles.size() > 0) {
                    date = new Date();
                    setupView(allArticles, articlesStockables);
                } else {

                    Snackbar snackbar = Snackbar.make(linearLayoutCompat, "La liste des Articles est vide ! ", Snackbar.LENGTH_INDEFINITE)
                            .setAction("quitter", v -> {

                                finish();

                            });
                    snackbar.show();
                }
            });
        });
    }

    /**
     * send articles list to dialog
     */
    void setArticlesListView(List<Article> articles, List<Article> articlesStockable) {
        articleListDialog = new ArticleListDialog(articles, articlesStockable, false, 1, true, true, false);

    }


    /**
     * load the spinner with clients list
     */
    void setSearchableSpinner(List<Client> clients) {
        simpleListAdapter = new ClientSpinnerAdapter(this, (ArrayList<Client>) clients);
        searchableSpinner.setAdapter(simpleListAdapter);
        if (isUpdate) {
            searchableSpinner.setSelectedItem(indexOfClient((ArrayList<Client>) clients, client) + 1);
            TransitionManager.beginDelayedTransition(content, new Slide(Gravity.BOTTOM));
            dataTableView.setVisibility(View.VISIBLE);
            List<LigneBonCommande> ligneBonCommandes = App.database.ligneBonCommandeDAO().getByBCCode(bonCommande.getDevCodeM());
            for (LigneBonCommande ld : ligneBonCommandes) {
                Article article = App.database.articleDAO().getOneByCode(ld.getLGDEVCodeArt());
                article.setPvttc(Double.parseDouble(ld.getLGDEVPUTTC()));
                //articleHashMap.put(article,ld);
                Log.d("kkkd", ld.getLGDEVPUTTC());
                articleListDialog.getList().put(article, Double.parseDouble(ld.getLGDEVQte()), Double.parseDouble(ld.getLGDEVRemise()), false);
            }
            //articleListDialog.getList().putAll(articleHashMap);
            setTicketDataTable(true);
            setFooter();
        }
        searchableSpinner.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(View view, int position, long id) {
                TransitionManager.beginDelayedTransition(content, new Slide(Gravity.BOTTOM));
                dataTableView.setVisibility(position > 0 ? View.VISIBLE : GONE);
                client = simpleListAdapter.getItem(position);
            }

            @Override
            public void onNothingSelected() {

            }
        });

    }


    void showArticlesListDialog() {

        articleListDialog.showConfirmDialog(this, 0, savedInstanceState);
        if (tableDataAdapter != null && !tableDataAdapter.getData().isEmpty()) {
            for (LigneTicket ligneTicket : tableDataAdapter.getData()) {
                updateListArticleQuantity(ligneTicket.getArticle(), ligneTicket.lTQte);
            }
        }
        articleListDialog.generateView();

    }


    private void updateListArticleQuantity(Article article, double qty) {
        for (Article article1 : articleListDialog.articles) {
            if (article.aRTCode.equals(article1.aRTCode)) {
                article1.setCount(qty);
            }
        }
        if (!articleListDialog.getList().isEmpty()) {
            for (Article article1 : articleListDialog.getList().keySet()) {
                if (article.aRTCode.equals(article1.aRTCode)) {
                    article1.setCount(qty);
                }
            }
        }
        if (!articleListDialog.getCurrenSelection().isEmpty()) {
            for (Article article1 : articleListDialog.getCurrenSelection().keySet()) {
                if (article.aRTCode.equals(article1.aRTCode)) {
                    article1.setCount(qty);
                }
            }
        }
    }


    void setTicketHeader() {
        dateInputField.setText(DateUtils.dateToStr(date, "EEEE, dd MMMM yyyy HH:mm"));
        ClientViewModel.getInstance(this).getByStation(prefUtils.getFiltreCltAuthorization(), prefUtils.getUserStationId()).observe(this, clients -> {
            if (clients != null)
                setSearchableSpinner(clients);
        });
    }

    void setTicketFooter() {
        discountInputField.addTextChangedListener(new TextWatcher() {
            @Override
            public void onTextChanged(CharSequence cs, int arg1, int arg2, int arg3) {
            }

            @Override
            public void beforeTextChanged(CharSequence s, int arg1, int arg2, int arg3) {
            }

            @Override
            public void afterTextChanged(Editable arg0) {
                amountWithDiscount = articleListDialog.setList(articleListDialog.getList().setDiscount(discountInputField.getText().toString())).getAmountWithDiscount();
                setTicketDataTable(true);
                setFooter();
            }
        });


    }

    /**
     * load table adapter with tickets
     */
    void setTicketDataTable(boolean changeFromGlobal) {

        tableView.setSwipeToRefreshEnabled(false);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            tableView.setElevation(10);
        }
        tableView.setHeaderBackground(R.color.material_teal500);
        if (changeFromGlobal) {
            amount = articleListDialog.setList(articleListDialog.getList().setDiscount(discountInputField.getText().toString())).getAmount();
            amountWithDiscount = articleListDialog.setList(articleListDialog.getList().setDiscount(discountInputField.getText().toString())).getAmountWithDiscount();
        } else {
            amount = articleListDialog.getList().getAmount();
            amountWithDiscount = articleListDialog.getList().getAmountWithDiscount();
        }
        tableDataAdapter = new LigneTicketTableDataAdapter(context, articleListDialog.getList().getLigneTickets(), tableView);
        tableDataAdapter.setNotifyOnChange(true);
        tableView.setDataAdapter(tableDataAdapter);

        if (tableTitle != null)
            tableTitle.setText(context.getString(R.string.product_title, String.valueOf(tableDataAdapter.getData().size())));

        tableView.addDataLongClickListener((rowIndex, clickedData) -> {
            if (!dialogShown) {
                dialogShown = true;
                new MaterialDialog.Builder(context)
                        .title(R.string.confirmation)
                        .content(R.string.delete_confirmation_msg)
                        .positiveText(R.string.yes)
                        .negativeText(R.string.no)
                        .onPositive((dialog, which) -> {
                                    try {
                                        if (tableDataAdapter.getData() != null) {
                                            tableDataAdapter.getData().get(rowIndex).getArticle().setCount(0);
                                            articleListDialog.getList().remove(tableDataAdapter.getData().get(rowIndex).getArticle());
                                            tableDataAdapter.getData().remove(rowIndex);
                                            tableDataAdapter.notifyDataSetChanged();
                                            amount = articleListDialog.setList(articleListDialog.getList().setDiscount(discountInputField.getText().toString())).getAmount();
                                            amountWithDiscount = articleListDialog.setList(articleListDialog.getList().setDiscount(discountInputField.getText().toString())).getAmountWithDiscount();
                                            setFooter();
                                            TransitionManager.beginDelayedTransition(content, new Slide(Gravity.BOTTOM));
                                            footerTicketView.setVisibility(!tableDataAdapter.getData().isEmpty() ? View.VISIBLE : GONE);
                                        }
                                    } catch (NullPointerException ignored) {
                                    }
                                    dialogShown = false;
                                }

                        ).onNegative((dialog, which) -> dialogShown = false)
                        .show();
            }
            return false;
        });

        TransitionManager.beginDelayedTransition(content, new Slide(Gravity.BOTTOM));
        footerTicketView.setVisibility(!tableDataAdapter.getData().isEmpty() ? View.VISIBLE : GONE);
        tableView.addDataClickListener((rowIndex, clickedData) -> {
            if (!dialogShown) {
                dialogShown = true;
                openModifyArticleDialog((LigneTicket) clickedData, false);
            }
        });
    }

    /**
     * modify ticket from table option
     */
 /*   private void openModifyArticleDialog(LigneTicket clickedData, Boolean scanned) {
        ligneTicketDialog = new LigneTicketDialog(context, scanned, clickedData, 1, false,
                (dialog, which) -> ligneTicketDialog.validator.validate(), (dialog, which) -> {
            dialog.dismiss();
            dialogShown = false;
        }, new Validator.ValidationListener() {
            @Override
            public void onValidationSucceeded() {
                if (ligneTicketDialog.validate()) {
                    double quantity = StringUtils.parseDouble(ligneTicketDialog.getQuantityInputField().getText().toString(), 0);
                    double discount1 = StringUtils.parseDouble(ligneTicketDialog.getDiscountInputField().getText().toString(), 0);
                    double price = StringUtils.parseDouble(ligneTicketDialog.getUnitPriceInputField().getText().toString(), 0);
                    clickedData.getArticle().setPvttc(price);
                    clickedData.getArticle().setCount(quantity);
                    articleListDialog.getList().put(clickedData.getArticle(), quantity, discount1, false);
                    setTicketDataTable(false);
                    setFooter();
                    ligneTicketDialog.dismiss();
                    dialogShown = false;
                }
            }

            @Override
            public void onValidationFailed(List<ValidationError> errors) {
                for (ValidationError error : errors) {
                    View view = error.getView();
                    String message = error.getCollatedErrorMessage(context);

                    if (view instanceof EditText) {
                        ((MaterialEditText) view).setError(message);
                    } else {
                        Toast.makeText(context, message, Toast.LENGTH_LONG).show();
                    }
                }
            }
        });
        ligneTicketDialog.show(context.getFragmentManager(), StringUtils.upper);
    }
*/


    /**
     * modify ticket from table option
     */
    private void openModifyArticleDialog(LigneTicket clickedData, Boolean scanned) {
        if (prefUtils.getIsAutoScan() && scanned) {
            addArticleToTable("scan", clickedData, -11.1);
        } else {
            ligneTicketDialog = new LigneTicketDialog(context, scanned, clickedData, 1, false,
                    (dialog, which) -> ligneTicketDialog.validator.validate(), (dialog, which) -> {
                dialog.dismiss();
                dialogShown = false;
            }, new Validator.ValidationListener() {
                @Override
                public void onValidationSucceeded() {
                    if (ligneTicketDialog.validate()) {
                        if (!ArticleListDialog.passagerBlocked) {
                            double quantity = StringUtils.parseDouble(ligneTicketDialog.getQuantityInputField().getText().toString(), 0);
                            double discount1 = StringUtils.parseDouble(ligneTicketDialog.getDiscountInputField().getText().toString(), 0);
                            double price = StringUtils.parseDouble(ligneTicketDialog.getUnitPriceInputField().getText().toString(), 0);
                            if (tableDataAdapter != null) {
                                List<LigneTicket> ligneTickets = tableDataAdapter.getData();


                                if (ligneTickets != null) {
                                    for (int rowIndex = 0; rowIndex < ligneTickets.size(); rowIndex++) {
                                        if (ligneTickets.get(rowIndex).getArticle().getaRTCodeBar().equals(clickedData.article.getaRTCodeBar())) {

                                            if (scanned)
                                                quantity += ligneTickets.get(rowIndex).article.getCount(); // if from scan increment quantity else if from modify then replace with user input quantity

                                            articleListDialog.getList().remove(tableDataAdapter.getData().get(rowIndex).getArticle());
                                            articleListDialog.getCurrenSelection().remove(tableDataAdapter.getData().get(rowIndex).getArticle());
                                            tableDataAdapter.getData().remove(rowIndex);
                                            tableDataAdapter.notifyDataSetChanged();
                                        }
                                    }
                                }
                            }

                            clickedData.getArticle().setPvttc(price);
                            clickedData.getArticle().setCount(quantity);
                            articleListDialog.getList().put(clickedData.getArticle(), quantity, discount1, false);
                            setTicketDataTable(false);
                            setFooter();
                            ligneTicketDialog.dismiss();
                            dialogShown = false;
                        }
                    }

                }

                @Override
                public void onValidationFailed(List<ValidationError> errors) {
                    for (ValidationError error : errors) {
                        View view = error.getView();
                        String message = error.getCollatedErrorMessage(context);

                        if (view instanceof EditText) {
                            ((MaterialEditText) view).setError(message);
                        } else {
                            Toast.makeText(context, message, Toast.LENGTH_LONG).show();
                        }
                    }
                }
            }, client);

            ligneTicketDialog.show(context.getFragmentManager(), StringUtils.upper);


        }
    }


    private void addArticleToTable(String from, LigneTicket clickedData, double quant) {
        double quantity = 0.0;
        if (tableDataAdapter != null) {
            List<LigneTicket> ligneTickets = tableDataAdapter.getData();
            if (ligneTickets != null) {
                for (int rowIndex = 0; rowIndex < ligneTickets.size(); rowIndex++) {
                    if (ligneTickets.get(rowIndex).getArticle().getaRTCodeBar().equals(clickedData.article.getaRTCodeBar())) {
                        if (from.equals("scan")) {
                            quantity = clickedData.getlTQte() + ligneTickets.get(rowIndex).article.getCount(); // if from scan increment quantity else if from modify then replace with user input quantity
                        } else if (from.equals("prodexist")) {
                            quantity = clickedData.getlTQte(); // quant+ clickedData.getlTQte(); TO INCREMENT QUANTITY
                        }

                        articleListDialog.getList().remove(tableDataAdapter.getData().get(rowIndex).getArticle());
                        articleListDialog.getCurrenSelection().remove(tableDataAdapter.getData().get(rowIndex).getArticle());
                        tableDataAdapter.getData().remove(rowIndex);
                        tableDataAdapter.notifyDataSetChanged();
                    }
                }
            }
        }


        double discount1;
        if (StringUtils.parseDouble(discountInputField.getText().toString(), 0) > 0) {
            discount1 = StringUtils.parseDouble(discountInputField.getText().toString(), 0);
        } else {
            if (Double.parseDouble(clickedData.getArticle().getTauxSolde()) != clickedData.getlTTauxRemise())
                discount1 = clickedData.getlTTauxRemise();
            else discount1 = Double.parseDouble(clickedData.getArticle().getTauxSolde());

        }


        clickedData.getArticle().setPvttc(clickedData.getArticle().getPvttc());
        if (quantity < 1) quantity = 1.0;
        clickedData.getArticle().setCount(quantity);
        articleListDialog.getList().put(clickedData.getArticle(), quantity, discount1, false);
        setTicketDataTable(false);
        setFooter();

    }

    /**
     * add new client button
     */
    @OnClick(R.id.addClientButton)
    void addClient() {
        addClientButton.setEnabled(false);
        addClientButton.setClickable(false);
        intent = new Intent(context, AddClientActivity.class);
        startActivityForResult(intent, REQUEST_CLIENT_CODE);
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        // Check which request we're responding to
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case REQUEST_ENABLE_BT:
                if (resultCode == Activity.RESULT_OK) {
                    Toast.makeText(context, "Bluetooth open successful", Toast.LENGTH_LONG).show();
                } else {
                    Toast.makeText(context, "Bluetooth failed to connect", Toast.LENGTH_LONG).show();
                }
                break;
            case REQUEST_CONNECT_DEVICE:
                if (resultCode == Activity.RESULT_OK) {
                    String address = data.getExtras().getString(DeviceListActivity.EXTRA_DEVICE_ADDRESS);
                    try {
                        con_dev = mService.getDevByMac(address);
                        mService.connect(con_dev);
                    } catch (Exception e) {
                        Timber.tag("erro").d(e);
                    }
                }
            case Activity.RESULT_CANCELED:
                finish();
                break;
        }
        if (requestCode == REQUEST_CLIENT_CODE) {
            if (resultCode == RESULT_OK) {
                final Client client1 = (Client) data.getSerializableExtra(CLIENT_INTENT_ID_KEY);
                if (client1 != null) {
                    simpleListAdapter.addItem(client1);
                    searchableSpinner.setSelectedItem(simpleListAdapter.getCount() - 1);
                }
            }
            addClientButton.setEnabled(true);
            addClientButton.setClickable(true);
        }

    }


    @OnClick(R.id.addItemButton)
    void addItems() {
        showArticlesListDialog();
    }

    @OnClick(R.id.autoscan)
    void autoScan() {
        switchAutoScanColor(autoScanButton, context);
    }

    @OnClick(R.id.scan)
    void scanArt() {
        scanArt.setEnabled(false);
        showScanner();
    }

    @Override
    protected int setContentView() {
        return R.layout.activity_ticket;
    }

   /* @Override
    public void onClickPositiveButton(DialogInterface pDialog) {
        for (Article article : articleListDialog.getCurrenSelection().keySet()) {
            articleListDialog.getList().remove(article);
        }
        articleListDialog.getList().putAll(articleListDialog.getCurrentSelection());
        articleListDialog.getCurrentSelection().clear();
        articleListDialog.generateView();
        selectedArticles = articleListDialog.getList().getLigneTickets();
        setTicketDataTable(false);
        setFooter();
        pDialog.dismiss();
    }
*/
    /*
   public void onClickPositiveButton(DialogInterface pDialog) {
       index_prod_intable = 0;
       boolean prodexist = false;
       Article articl = null;

       if (tableDataAdapter == null) {
           tableDataAdapter = new LigneTicketTableDataAdapter(context, articleListDialog.getList().getLigneTickets(), tableView);
       } else {
           tableDataAdapter.clear();
           tableDataAdapter.addAll(articleListDialog.getList().getLigneTickets());
       }

       List<LigneTicket> ligneTickets = tableDataAdapter.getData();


       for (Article article : articleListDialog.getCurrenSelection().keySet()) {
           if (isExistArticle(article, ligneTickets)) {
               articl = article;
               prodexist = true;
               break;
           }
           //  articleListDialog.getList().remove(article);
       }

       if (!prodexist) {
           articleListDialog.getList().putAll(articleListDialog.getCurrentSelection());
           articleListDialog.getCurrentSelection().clear();
           articleListDialog.generateView();
           selectedArticles = articleListDialog.getList().getLigneTickets();
           setTicketDataTable(false);
           setFooter();

       } else
           addArticleToTable("prodexist", Objects.requireNonNull(articleListDialog.getCurrenSelection().get(articl)),
                   ligneTickets.get(index_prod_intable).lTQte);


       pDialog.dismiss();
   }

     */


    public void onClickPositiveButton(DialogInterface pDialog) {
        index_prod_intable = 0;
        boolean prodexist = false;
        Article articl = null;

        if (tableDataAdapter == null) {
            tableDataAdapter = new LigneTicketTableDataAdapter(context, articleListDialog.getList().getLigneTickets(), tableView);
        } else {
            tableDataAdapter.clear();
            tableDataAdapter.addAll(articleListDialog.getList().getLigneTickets());
        }

        List<LigneTicket> ligneTickets = tableDataAdapter.getData();


        for (Article article : articleListDialog.getCurrenSelection().keySet()) {
            if (isExistArticle(article, ligneTickets)) {
                articl = article;
                prodexist = true;
                break;
            }
            //  articleListDialog.getList().remove(article);
        }

        if (!prodexist) {
            articleListDialog.getList().putAll(articleListDialog.getCurrentSelection());
            articleListDialog.getCurrentSelection().clear();
            articleListDialog.generateView();
            selectedArticles = articleListDialog.getList().getLigneTickets();

            setTicketDataTable(false);
            setFooter();

        } else
            addArticleToTable("prodexist", Objects.requireNonNull(articleListDialog.getCurrenSelection().get(articl)),
                    ligneTickets.get(index_prod_intable).lTQte);


        pDialog.dismiss();
    }


    void setFooter() {
        setText(amountInputField, StringUtils.priceFormat(amount));
        setText(amountwithDiscountInputField, StringUtils.priceFormat(amountWithDiscount));
        //AmountwithDiscountLayout.setVisibility(View.VISIBLE);
    }

    @Override
    public void onClickNegativeButton(DialogInterface pDialog) {
        articleListDialog.getCurrentSelection().clear();
        if (tableDataAdapter != null && !tableDataAdapter.getData().isEmpty()) {
            for (LigneTicket ligneTicket : tableDataAdapter.getData()) {
                updateListArticleQuantity(ligneTicket.getArticle(), ligneTicket.lTQte);
            }
        }
        articleListDialog.generateView();
        pDialog.dismiss();
    }

    @Override
    public void onBackPressed() {
        new MaterialDialog.Builder(this).onPositive((dialog, which) -> finish()).onNegative((dialog, which) -> {
                }).title(R.string.confirmation)
                .content("Êtes-vous sûr de vouloir quitter?")
                .positiveText(R.string.yes)
                .negativeText(R.string.no)
                .show();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem menuItem) {
        switch (menuItem.getItemId()) {
            case android.R.id.home:
                onBackPressed();
                break;
        }

        return super.onOptionsItemSelected(menuItem);
    }

    private boolean isExistArticle(Article selectedArticle, List<LigneTicket> ligneTickets) {
        boolean exist = false;
        for (LigneTicket ligneTicket : ligneTickets) {
            if (ligneTicket.getArticle().getaRTCodeBar().equals(selectedArticle.getaRTCodeBar())) {
                exist = true;
                break;
            }
            index_prod_intable++;
        }
        return exist;
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.add_ticket_menu, menu);
        save = menu.findItem(R.id.save);
        scan = menu.findItem(R.id.scan);
        scan.setVisible(false);

        scan.setIcon(new IconicsDrawable(context).icon(FontAwesome.Icon.faw_barcode)
                .color(Color.WHITE).sizeDp(20));
        scan.setOnMenuItemClickListener(item -> {

            scan.setEnabled(false);
            showScanner();
            return false;
        });

        save.setIcon(new IconicsDrawable(context).icon(GoogleMaterial.Icon.gmd_send)
                .color(ResourcesCompat.getColor(getResources(), R.color.material_white, null))
                .sizeDp(24));
        //  save.setEnabled(false);
        save.setOnMenuItemClickListener(item -> {
            if (amount > 0 && (tableView.getVisibility() == View.VISIBLE) && (searchableSpinner.getSelectedPosition() > 0)) {
                new MaterialDialog.Builder(this)
                        .title(R.string.confirmation)
                        .content(getString(R.string.sauv_label))
                        .negativeText(R.string.cancel)
                        .positiveText(R.string.dialog_ok)
                        .cancelable(false)
                        .onPositive((dialog, which) -> sendData())
                        .onNegative((dialog, which) -> save.setEnabled(true))
                        .show();
            } else {
                UIUtils.showDialog(context, R.string.fields_error);
                save.setEnabled(true);

            }
            return false;
        });
        return super.onCreateOptionsMenu(menu);
    }

    private void showScanner() {
//        new BarcodeScannerDialog(context, new BarcodeScannerListener() {
//            @Override
//            public void onDismiss(DialogInterface pDialog) {
//                if (scanArt != null) {
//                    scanArt.setEnabled(true);
//                }
//            }
//
//            @Override
//            public void onDecoded(DialogInterface pDialog, Result result) {
//                showArticleFromScan(result.getText());
//                pDialog.dismiss();
//            }
//        }).show();

        BarCodeDialogueFragment dialog = new BarCodeDialogueFragment(new BarcodeScannerListener() {


            @Override
            public void onDecoded(String result) {

                showArticleFromScan(result);
            }
        });

        dialog.show(getSupportFragmentManager(), "MyDialogFragment");
    }

    public void showArticleFromScan(String code) {

        Log.d("dsgsfgvcxxcd", "code "+ code);
        code = code.replaceAll("\\s+", "");
        Article selectedArticle = App.database.articleDAO().getByCodeBarAndSation("%" + code + "%", App.prefUtils.getUserStationId());
        //  Article selectedArticle = App.database.articleDAO().getByCodeBarAndSation( code , App.prefUtils.getUserStationId());
        if (selectedArticle != null) {
            if (!prefUtils.getArtZeroStockAuthorization() && selectedArticle.getsARTQte() <= 0) {
                Toasty.error(BonCommandeActivity.this, "Pas d'autorisation pour ajouter Article avec un Stock Zero").show();
            }
            else {
                dataTableView.setVisibility(View.VISIBLE);
                if (tableDataAdapter != null) {
                    List<LigneTicket> ligneTickets = tableDataAdapter.getData();
                    if (ligneTickets != null) {
                        boolean exist = false;
                        for (LigneTicket ligneTicket : ligneTickets) {
                            if (ligneTicket.getArticle().getaRTCodeBar().equals(selectedArticle.getaRTCodeBar())) {
                                openModifyArticleDialog(ligneTicket, true);
                                exist = true;
                            }
                        }
                        if (!exist) {
                            showArticleDialog(selectedArticle);
                        }
                    } else {
                        showArticleDialog(selectedArticle);
                    }
                } else {
                    showArticleDialog(selectedArticle);
                }
            }

        } else Toasty.error(BonCommandeActivity.this, "Article introuvable").show();


    }

    private void goBack(){
        setResult(Activity.RESULT_OK, new Intent());
        finish();
    }
    void sendData() {

        if (articleListDialog.getList().getLigneTickets().isEmpty()) {
            bonCommande = null;
            ticket = null;
            save.setEnabled(true);
            Toasty.info(context, R.string.no_article_selected).show();
            return;
        }
        try {
            new MaterialDialog.Builder(context)
                    .title("Imprimer ?")
                    .content("Voulez-vous imprimer la B.C.?")
                    .positiveText("Oui")
                    .negativeText("Non")
                    .onNegative((dialog, which12) -> {
                                createBc();
                                //createTicketWithLines(which, ticket);
                                setResult(Activity.RESULT_OK, new Intent());
                                finish();
                            }
                    )
                    .onPositive((dialog, which1) -> {
                        //createTicketWithLines(which, ticket);
                        BluetoothAdapter mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
                        if (prefUtils.getPrintA4Enabled()) {
                            createBc();
                            bonCommande.setDEVClient(client.cLINomPren);
                            if (ligneBonCommandes != null) {
                                if (prefUtils.getPrintA4Enabled()) {
                                    new File(Objects.requireNonNull(Comman.Companion.getAppPath(context))).mkdirs();
                                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                                        if (ligneBonCommandes != null && ligneBonCommandes.size() > 0) {
                                            putArticleInLignes(ligneBonCommandes);
                                            calculateTotalAndRemisePrices(bonCommande, ligneBonCommandes);

                                            WifiPrint.Companion.createBCPDFFile(context, bonCommande, ligneBonCommandes, () -> {
                                                setResult(Activity.RESULT_OK, new Intent());
                                                finish();
                                            });

                                        }
                                    }

                                }
                            }

                        } else {
                            if (mBluetoothAdapter == null) {
                                // Device does not support Bluetooth

                                Toasty.error(getApplication(), "No Bluetooth available in this Device").show();

                            } else if (!mBluetoothAdapter.isEnabled()) {
                                // Bluetooth is not enabled :)

                                Toasty.error(getApplication(), "Activer le Bluetooth").show();


                                Intent enableIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
                                if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                                    // TODO: Consider calling
                                    //    ActivityCompat#requestPermissions
                                    // here to request the missing permissions, and then overriding
                                    //   public void onRequestPermissionsResult(int requestCode, String[] permissions,
                                    //                                          int[] grantResults)
                                    // to handle the case where the user grants the permission. See the documentation
                                    // for ActivityCompat#requestPermissions for more details.
                                    return;
                                }

                                startActivityForResult(enableIntent, REQUEST_ENABLE_BT);

                            } else {
                                createBc();
                                bonCommande.setDEVClient(client.cLINomPren);
                                // Bluetooth is enabled
                                if (ligneBonCommandes != null) {
                                    printTicket(bonCommande, ligneBonCommandes);

                                } else {
                                    Toasty.info(context, "Aucune ligne").show();
                                }

                            }


                        }


                    })
                    .show();
        } catch (Exception e) {
            Toasty.info(context, e.getMessage()).show();
        }
    }

    private void createBc() {
        String numBonCommande = "";
        String numBonCommandeM = "";
        if (ObjectUtils.isEmpty(bonCommande)) {
            numBonCommande = App.database.prefixeDAO().getOneById("Devis").getpREPrefixe() +
                    App.database.bonCommandeDAO().getNewCode(App.database.prefixeDAO().getOneById("Devis").getpREPrefixe());
            numBonCommandeM = Utils.generateMobileDevisCode(prefUtils.getUserStationId(), numBonCommande);
        } else {
            numBonCommande = bonCmdNumber;
            numBonCommandeM = bonCommande.devCodeM;
        }
        ligneBonCommandes = new ArrayList<>();

        bonCommande = new BonCommande();
        bonCommande.setDEVNum(numBonCommande);
        bonCommande.devCodeM = numBonCommandeM;
        bonCommande.setBONLIVExerc(App.prefUtils.getExercice());
        bonCommande.setDEVExerc(App.prefUtils.getExercice());
        bonCommande.setDEVCodeClient(client.getcLICode());
        bonCommande.setDEVStationOrigine(App.prefUtils.getUserStationId());
        bonCommande.setDEVStation(App.prefUtils.getUserStationId());
        bonCommande.setDEVEtat("BCC_client");
        bonCommande.setDEVMntFodec("0.0");
        bonCommande.setDEVMntDC("0.0");
        bonCommande.setDEVEtatBon("1");
        bonCommande.setDEVExonoration("0");
        bonCommande.setDEVTimbre("0.0");
        bonCommande.setDEV_info3("");

        double mntTTc = articleListDialog.getList().getAmountWithDiscount();

   //   if(client.cltMntRevImp != null)  if(client.cltMntRevImp> 0.0) mntTTc = articleListDialog.getList().getAmountWithDiscount() * (1+ (client.cltMntRevImp/100));

        bonCommande.setDEVUser(App.prefUtils.getUserId());
        bonCommande.setDEVMntTTC(String.valueOf(mntTTc));
        bonCommande.setDEVMntTva(String.valueOf(articleListDialog.getList().getVATAmount()));
        bonCommande.setDEVMntht(String.valueOf(articleListDialog.getList().getAmountHT()));
        bonCommande.setDEVMntNetHt(String.valueOf(articleListDialog.getList().getAmountHT()));
        bonCommande.setDEVDate(DateUtils.dateToStr(DateUtils.strToDate(new Date().toString(), "EEEE, dd MMMM yyyy HH:mm"), Globals.DATE_PATTERN));
        //bonCommande.setDEVDDm(DateUtils.dateToStr(DateUtils.strToDate(new Date().toString(), "EEEE, dd MMMM yyyy HH:mm"), Globals.DATE_PATTERN));

        if (App.prefUtils.getBlAuthorization())
            bonCommande.setStatus(Globals.ITEM_STATUS.WAITING.getStatus());
        else bonCommande.setStatus(Globals.ITEM_STATUS.INSERTED.getStatus());
        bonCommande.isSync = false;
        bonCommande.setDEVDDm(isUpdate ? ddm : "empty");
        App.database.bonCommandeDAO().insert(bonCommande);
        ArrayList<LigneTicket> ligneTickets = articleListDialog.getList().getLigneTickets();


        //if (isUpdate) {// delete all lg bc  with current code m
        //    List<LigneBonCommande> lgBcList = App.database.ligneBonCommandeDAO().getByBCCode(bonCommande.getDevCodeM());
        //     App.database.ligneBonCommandeDAO().deleteByCmdM(bonCommande.getDevCodeM());

           /* for (int i = 0; i < lgBcList.size(); i++) {
                    App.database.ligneBonCommandeDAO().deleteByCmdM(lgBcList.get(i).lGDEVCodeM);

            }*/
        // }

        //  App.database.ligneBonCommandeDAO().deleteByCmdM(numBonCommandeM);
        App.database.ligneBonCommandeDAO().deleteByCmd(numBonCommandeM);


        for (int i = 0, ligneTicketsSize = ligneTickets.size(); i < ligneTicketsSize; i++) {
            LigneTicket ligneTicket = ligneTickets.get(i);
            LigneBonCommande ligneBonCommande = new LigneBonCommande();
            ligneBonCommande.setLGDEVNumBon(numBonCommandeM);
            ligneBonCommande.setLGDEVDDm(DateUtils.dateToStr(DateUtils.strToDate(new Date().toString(), "EEEE, dd MMMM yyyy HH:mm"), Globals.DATE_PATTERN));
            ligneBonCommande.setLGDEVExerc(prefUtils.getExercice());
            ligneBonCommande.setLGDEVUnite("Pièce");
            ligneBonCommande.setLGDEVStation(ligneTicket.getArticle().sARTCodeSatation);
            ligneBonCommande.setLGDEVCodeArt(ligneTicket.getlTCodArt());
            ligneBonCommande.setLGDEVMntHT(String.valueOf(ligneTicket.getlTMtHT()));
            ligneBonCommande.setLGDEVMntBrutHT(String.valueOf(ligneTicket.getlTMtHT()));
            ligneBonCommande.setLGDEVMntTTC(String.valueOf(ligneTicket.getlTMtTTC()));
            ligneBonCommande.setLGDEVPUTTC(String.valueOf(ligneTicket.lTPrixVente));
            ligneBonCommande.setLGDEVPUHT(String.valueOf(ligneTicket.lTPACHAT));
            ligneBonCommande.setLGDEVMntTva(String.valueOf(bonCommande.getDEVMntTva()));
            ligneBonCommande.setLGDEVQte(String.valueOf(ligneTicket.getlTQte()));
            ligneBonCommande.setLGDEVRemise(String.valueOf(ligneTicket.getlTTauxRemise()));
            ligneBonCommande.isSync = false;
            ligneBonCommande.lGDEVNumOrdre = ligneTickets.get(i).lTNumOrdre;

            if (App.prefUtils.getBlAuthorization())
                ligneBonCommande.setStatus(Globals.ITEM_STATUS.WAITING.getStatus());
            else ligneBonCommande.setStatus(Globals.ITEM_STATUS.INSERTED.getStatus());

            //  ligneBonCommande.lGDEVCodeM = Utils.generateMobileLigneDevisCode(prefUtils.getUserStationId(), numBonCommande);
            //  ligneBonCommande.lGDEVCodeM =numBonCommandeM;//+"_"+ligneTickets.get(i).lTNumOrdre;
            ligneBonCommande.lGDEVCodeM = numBonCommandeM + "_" + ligneTickets.get(i).lTNumOrdre;
            ligneBonCommandes.add(ligneBonCommande);


        }


        App.database.bonCommandeDAO().insert(bonCommande);
        App.database.ligneBonCommandeDAO().insertAll(ligneBonCommandes);


        App.database.bonCommandeDAO().insert(bonCommande);
    }

    private void calculateTotalAndRemisePrices(BonCommande item, List<LigneBonCommande> ligneBonRetours) {
        double mntRemise = 0.0;
        double total = 0.0;
        for (LigneBonCommande ligneTicket : ligneBonRetours) {
            try {
                mntRemise += Double.parseDouble(ligneTicket.getLGDEVQte()) *
                        ligneTicket.getArticle().pvttc * Double.parseDouble(ligneTicket.getLGDEVRemise()) / 100;
                total += Double.parseDouble(ligneTicket.getLGDEVQte()) * ligneTicket.getArticle().pvttc;

            } catch (Exception e) {
                Timber.d(e);
            }
        }


        item.setDEVRemise(mntRemise + "");


        double mntTTc = total;

      //  if(client.cltMntRevImp != null)  if(client.cltMntRevImp> 0.0) mntTTc = total * (1+ (client.cltMntRevImp/100));



        item.setDEVMntTTC(mntTTc + "");
    }

    private void putArticleInLignes(List<LigneBonCommande> ligneTickets) {
        for (int i = 0; i < ligneTickets.size(); i++) {
            ligneTickets.get(i).setArticle(App.database.articleDAO().getOneByCodeAndStation(ligneTickets.get(i).getLGDEVCodeArt(), ligneTickets.get(i).getLGDEVStation()));
        }
    }

    @SuppressLint("HandlerLeak")
    private void printTicket(BonCommande item, List<LigneBonCommande> ligneBonCommandes) {
        if (ligneBonCommandes != null && ligneBonCommandes.size() > 0) {
            putArticleInLignes(ligneBonCommandes);
            calculateTotalAndRemisePrices(item, ligneBonCommandes);
            mService = new BluetoothService(getApplication(), new Handler() {
                @Override
                public void handleMessage(Message msg2) {
                    switch (msg2.what) {
                        case BluetoothService.MESSAGE_STATE_CHANGE:
                            switch (msg2.arg1) {
                                case BluetoothService.STATE_CONNECTED:
                                    Toast.makeText(getApplication(), "Connect successful", Toast.LENGTH_SHORT).show();

                                    try {
                                        new PrinterHelper(mService, DEFAULT_ENCODING).printBonCommande(context, item, ligneBonCommandes, false);

                                        finish();

                                    } catch (IOException e) {
                                        Toasty.error(getApplication(), e.getMessage()).show();
                                    }

                                    break;
                                case BluetoothService.STATE_CONNECTING:
                                    break;
                                case BluetoothService.STATE_LISTEN:
                                case BluetoothService.STATE_NONE:
                                    Toasty.info(getApplication(), "Unable to connect device").show();

                                    break;
                            }
                            break;
                        case BluetoothService.MESSAGE_CONNECTION_LOST:
                            Toast.makeText(getApplication(), "Device connection was lost",
                                    Toast.LENGTH_SHORT).show();

                            break;
                        case BluetoothService.MESSAGE_UNABLE_CONNECT:
                           /* new MaterialDialog.Builder(context)
                                    .title("Unable to connect device ")
                                   // .content("Voulez-vous imprimer la B.C.?")
                                    .positiveText("Ok")
                                   // .negativeText("Non")
                                   // .onNegative((dialog, which12) -> {})
                                    .onPositive((dialog, which1) -> {dialog.dismiss();})
                                    .show();*/
                            //   Toast.makeText(getApplication(), "Unable to connect device", Toast.LENGTH_SHORT).show();
                            break;

                        case Activity.RESULT_CANCELED:
                            Toasty.info(getApplication(), "RESULT_CANCELED Bluetooth").show();

                            finish();
                            break;
                    }
                }

            });
            if (!mService.isBTopen()) {
                Intent enableIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
                if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                    // TODO: Consider calling
                    //    ActivityCompat#requestPermissions
                    // here to request the missing permissions, and then overriding
                    //   public void onRequestPermissionsResult(int requestCode, String[] permissions,
                    //                                          int[] grantResults)
                    // to handle the case where the user grants the permission. See the documentation
                    // for ActivityCompat#requestPermissions for more details.
                    return;
                }
                Toast.makeText(getApplication(), "Bluetooth Not enabled",
                        Toast.LENGTH_SHORT).show();
                startActivityForResult(enableIntent, REQUEST_ENABLE_BT);
            }
            if (!mService.isAvailable()) {
                Toast.makeText(context, "Bluetooth is not available", Toast.LENGTH_LONG).show();
            } else {
                Intent serverIntent = new Intent(context, DeviceListActivity.class);
                startActivityForResult(serverIntent, REQUEST_CONNECT_DEVICE);
            }
        } else {
            Toasty.info(context, "Aucune ligne à été trouvée pour cette B.C!!").show();
        }
    }


    static void setText(EditText text, String value) {
        text.setText(value);
    }


    @Override
    protected void onResume() {
        //  barCodeReaderManager.resume();
        startBarcode();
        super.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
    }

    @Override
    protected void onDestroy() {

        barCodeReaderManager.destroy();
        super.onDestroy();
    }


    @Override
    protected void onStop() {
        super.onStop();
        barCodeReaderManager.destroy();
    }

    @Override
    protected void onPostResume() {
        super.onPostResume();
        barCodeReaderManager.resume();
    }



    /**
     * show the dialog which contains all the articles to chose from
     */
    private void showArticleDialog(Article selectedArticle) {
        if (prefUtils.getIsAutoScan()) addPurchaseLineFromAutoScan(selectedArticle);
        else {

            boolean isScanSource = true;

            articleDialog = new ArticleDialog(context, false, selectedArticle, 1, true, isScanSource, false, (dialog, which) -> {
                articleDialog.validator.validate();
                selectedArticle.setSync(false);
                selectedArticle.setStatus(Globals.ITEM_STATUS.INSERTED.getStatus());
                selectedArticle.setCount(StringUtils.parseDouble(articleDialog.getQuantityInputField().getText().toString(), 0));
                articleListDialog.getList().put(selectedArticle, Double.parseDouble(articleDialog.getQuantityInputField().getText().toString()), 0, false);
                setTicketDataTable(false);
                setFooter();
                articleDialog.dismiss();
            }, (dialog, which) -> {
                if (ArticleDialog.article != null)
                    ArticleDialog.article.setPvttc(App.database.articleDAO().getOneByCodeAndStation(ArticleDialog.article.getaRTCode(), ArticleDialog.article.getsARTCodeSatation()).getPvttc());
                dialog.dismiss();

            }, validationListener);
            articleDialog.show(context.getSupportFragmentManager(), StringUtils.digits);
        }
    }

    private void addPurchaseLineFromAutoScan(Article selectedArticle) {
        selectedArticle.setSync(false);
        selectedArticle.setStatus(Globals.ITEM_STATUS.INSERTED.getStatus());
        selectedArticle.setCount(StringUtils.parseDouble("1", 1));
        articleListDialog.getList().put(selectedArticle, Double.parseDouble("1"), 0, false);
        setTicketDataTable(false);
        setFooter();
        App.database.articleDAO().insert(ArticleDialog.article);
    }
}
