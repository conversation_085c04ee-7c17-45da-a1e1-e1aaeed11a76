package com.asmtunis.procaissemobility.adapters.tables;

import android.content.Context;
import android.graphics.Typeface;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.data.models.LigneBonCommande;
import com.asmtunis.procaissemobility.data.models.LigneTicket;
import com.asmtunis.procaissemobility.helper.Globals;
import com.asmtunis.procaissemobility.helper.utils.Calculator;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.ui.components.SortableLigneTicketTableView;

import java.text.NumberFormat;
import java.util.List;

import de.codecrafters.tableview.toolkit.LongPressAwareTableDataAdapter;





public class LigneInvTicketTableDataAdapter extends LongPressAwareTableDataAdapter<LigneTicket> {

    private static final int TEXT_SIZE = 14;
    private static final NumberFormat PRICE_FORMATTER = NumberFormat.getNumberInstance();
    private PrefUtils prefUtils;

    public LigneInvTicketTableDataAdapter(final Context context, final List<LigneTicket> data, final SortableLigneTicketTableView tableView) {
        super(context, data, tableView);
        prefUtils = new PrefUtils(context);
    }

    @Override
    public View getDefaultCellView(int rowIndex, int columnIndex, ViewGroup parentView) {
        final LigneTicket ligneTicket = getRowData(rowIndex);
        switch (columnIndex) {
            case 0:
                return renderNumSerie(ligneTicket);//renderQuantity(ligneTicket);
            case 1:
                return renderLigneTicketName(ligneTicket);//renderNumSerie(ligneTicket);
        /*    case 2:
                return renderLigneTicketName(ligneTicket);*/
        }
        return new View(getContext());
    }

    @Override
    public View getLongPressCellView(int rowIndex, int columnIndex, ViewGroup parentView) {
        return getDefaultCellView(rowIndex, columnIndex, parentView);
    }

    private View renderLigneTicketName(final LigneTicket ligneTicket) {
        final TextView textView = new TextView(getContext());
        final String ligneTicketNameString = StringUtils.isEmptyString(ligneTicket.getArticle().getaRTDesignation()) ? ligneTicket.getArticle().getmARDesignation() : ligneTicket.getArticle().getaRTDesignation();
        textView.setText(ligneTicketNameString);
        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        textView.setHeight(160);
        textView.setGravity(Gravity.CENTER_VERTICAL);
        return textView;
    }

    private View renderQuantity(final LigneTicket ligneTicket) {
        final TextView textView = new TextView(getContext());
        final String quantityString = String.valueOf(StringUtils.decimalFormat(ligneTicket.getlTQte()));
        textView.setText(quantityString);
        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        textView.setHeight(140);
        textView.setGravity(Gravity.CENTER_VERTICAL);
        return textView;
    }







    private View renderNumSerie(final LigneTicket ligneTicket) {
        final TextView textView = new TextView(getContext());
        final String ligneTicketNameString = StringUtils.isEmptyString(ligneTicket.getArticle().getaRTDesignation()) ? ligneTicket.getArticle().getmARDesignation() : ligneTicket.getArticle().getaRTDesignation();
        textView.setText(ligneTicket.article.getPhotoPath());
        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        textView.setHeight(160);
        textView.setGravity(Gravity.CENTER_VERTICAL);
        return textView;
    }




}
