package com.asmtunis.procaissemobility.data.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.Prefixe;

import java.util.List;

/**
 * Created by PC on 11/18/2017.
 */
@Dao
public interface PrefixeDAO {

    @Query("SELECT * FROM Prefixe")
    List<Prefixe> getAll();

    @Query("SELECT * FROM Prefixe WHERE lower(PRE_Id_table) = lower(:code) ")
    Prefixe getOneById(String code);

    @Query("SELECT COUNT(*) FROM Prefixe")
    int count();







    @Query("SELECT * FROM Prefixe LIMIT 1")
    Prefixe getOne();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(Prefixe item);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<Prefixe> items);

    @Query("DELETE FROM Prefixe")
    void deleteAll();

}
