package com.asmtunis.procaissemobility.data.network.services;

import com.asmtunis.procaissemobility.data.models.DepenceType;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.custom.AddBatchDepenseModel;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * Created by WAEL on 8/30/2022.
 */
public interface DepenceService {

    @POST("getTypeDepense")
    Call<List<DepenceType>> getDepences(@Body GenericObject connexion);

    @POST("addBatchTypeDepense")
    Call<List<AddBatchDepenseModel>> addBatchDepenceType(@Body GenericObject connexion);
}
