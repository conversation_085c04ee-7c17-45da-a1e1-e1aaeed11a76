package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * Created by Achraf on 14/09/2017.
 */

@Entity( primaryKeys =
        {"FAM_Code"})
public class Famille {
    @NonNull
    @ColumnInfo(name = "FAM_Code")
    @SerializedName("FAM_Code")
    @Expose
    private String FAM_Code;
    @ColumnInfo(name = "FAM_Lib")
    @SerializedName("FAM_Lib")
    @Expose
    private String FAM_Lib;
    @ColumnInfo(name = "FAM_User")
    @SerializedName("FAM_User")
    @Expose
    private String FAM_User;
    @ColumnInfo(name = "FAM_Station")
    @SerializedName("FAM_Station")
    @Expose
    private String FAM_Station;
    @ColumnInfo(name = "FAM_NumOrdre")
    @SerializedName("FAM_NumOrdre")
    @Expose
    private Integer FAM_NumOrdre;
    @ColumnInfo(name = "is_FAMParent")
    @SerializedName("is_FAMParent")
    @Expose
    public boolean is_FAMParent;
    @ColumnInfo(name = "FAM_Image")
    @SerializedName("FAM_Image")
    @Expose
    private String FAM_Image;

    public Famille() {
    }


    public Integer getFAM_NumOrdre() {
        return FAM_NumOrdre;
    }

    public String getFAM_Image() {
        return FAM_Image;
    }

    public String getFAM_Code() {
        return FAM_Code;
    }

    public void setFAM_Code(String FAM_Code) {
        this.FAM_Code = FAM_Code;
    }

    public String getFAM_Lib() {
        return FAM_Lib;
    }

    public void setFAM_Lib(String FAM_Lib) {
        this.FAM_Lib = FAM_Lib;
    }

    public String getFAM_User() {
        return FAM_User;
    }

    public void setFAM_User(String FAM_User) {
        this.FAM_User = FAM_User;
    }

    public String getFAM_Station() {
        return FAM_Station;
    }

    public void setFAM_Station(String FAM_Station) {
        this.FAM_Station = FAM_Station;
    }

    public void setFAM_Image(String FAM_Image) {
        this.FAM_Image = FAM_Image;
    }

    public void setFAM_NumOrdre(Integer FAM_NumOrdre) {
        this.FAM_NumOrdre = FAM_NumOrdre;
    }

    public void setIs_FAMParent(boolean is_FAMParent) {
        this.is_FAMParent = is_FAMParent;
    }

    @Override
    public String toString() {
        return "famille{" +
                "FAM_Code=" + FAM_Code +
                ", FAM_Lib='" + FAM_Lib + '\'' +
                ", FAM_User='" + FAM_User + '\'' +
                ", FAM_Station='" + FAM_Station + '\'' +

                '}';
    }

}
