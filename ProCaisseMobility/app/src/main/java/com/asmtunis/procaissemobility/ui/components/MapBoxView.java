package com.asmtunis.procaissemobility.ui.components;

import static com.asmtunis.procaissemobility.helper.utils.MapUtils.getCurrentCoordinate;
import static com.asmtunis.procaissemobility.helper.utils.MapUtils.getMarkerBitmapFromView;
import static com.blankj.utilcode.util.StringUtils.getString;

import android.app.Activity;
import android.content.Context;
import android.content.IntentSender;
import android.location.LocationManager;
import android.util.Log;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Client;
import com.google.android.gms.common.api.ApiException;
import com.google.android.gms.common.api.ResolvableApiException;
import com.google.android.gms.location.LocationRequest;
import com.google.android.gms.location.LocationServices;
import com.google.android.gms.location.LocationSettingsRequest;
import com.google.android.gms.location.LocationSettingsResponse;
import com.google.android.gms.location.LocationSettingsStatusCodes;
import com.google.android.gms.tasks.Task;
import com.mapbox.mapboxsdk.camera.CameraPosition;
import com.mapbox.mapboxsdk.geometry.LatLng;
import com.mapbox.mapboxsdk.maps.MapView;
import com.mapbox.mapboxsdk.plugins.annotation.Symbol;
import com.mapbox.mapboxsdk.plugins.annotation.SymbolManager;
import com.mapbox.mapboxsdk.plugins.annotation.SymbolOptions;

/**
 * Created by Oussama AZIZI on 6/13/22.
 */

public class MapBoxView {
    private static SymbolManager symbolManager;
    Symbol symbol = null;
    Context context;
    MapboxListener mapboxListener;
    Activity activity;
    MapView mapView;

    public MapBoxView(Context context, Activity activity, MapView mapView, MapboxListener mapboxListener) {
        this.context = context;
        this.mapboxListener = mapboxListener;
        this.activity = activity;
        this.mapView = mapView;
    }

    private String makeStyleUrl() {
        try {
            return context.getResources().getString(R.string.jawg_styles_url) + "jawg-streets.json?access-token=" + getString(R.string.jawg_access_token);
        } catch (Exception e) {
            return "";
        }
    }

    public void setupMap(Client client) {
        if(symbolManager != null) {
            symbolManager.deleteAll();
            symbolManager = null;
        }
        mapView.getMapAsync(map -> map.setStyle(makeStyleUrl(), style -> {

            symbolManager = new SymbolManager(mapView, map, style);

            style.addImage("MARKER_ICON", getMarkerBitmapFromView(R.layout.marker_view, context), true);


            if(client != null && client.getLatitude() != null && client.getLongitude() != null) {
                symbol = symbolManager.create(
                        new SymbolOptions()
                                .withLatLng(new LatLng(client.getLatitude(), client.getLongitude()))
                                .withIconImage("MARKER_ICON")
                                .withIconSize(0.5f)
                );
                symbolManager.update(symbol);
            }

            map.getUiSettings().setAttributionMargins(15, 0, 0, 15);

            if(App.prefUtils.getCustomLocationAuth()) {
                map.addOnMapLongClickListener(point -> {
                    symbolManager.deleteAll();
                    symbol = symbolManager.create(
                            new SymbolOptions()
                                    .withLatLng(new LatLng(point.getLatitude(), point.getLongitude()))
                                    .withIconImage("MARKER_ICON")
                                    .withIconSize(0.5f)
                    );
                    mapboxListener.onGettingLongClickData(point);
                    symbolManager.update(symbol);
                    return true;
                });
            }

            // Set the map view center

            if(client != null && client.getLatitude() != null && client.getLongitude() != null) {
                map.setCameraPosition(new CameraPosition.Builder()
                        .target(new LatLng(client.getLatitude(), client.getLongitude()))
                        .zoom(10.0)
                        .build());
            }

        }));
    }

    public void requestLocation(Client client) {
        LocationManager lm = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);
        boolean gps_enabled = false;

        try {
            gps_enabled = lm.isProviderEnabled(LocationManager.GPS_PROVIDER);
        } catch (Exception ex) {
        }
        if (!gps_enabled) {
            LocationRequest locationRequest = LocationRequest.create();
            locationRequest.setPriority(LocationRequest.PRIORITY_HIGH_ACCURACY);
            LocationSettingsRequest.Builder builder = new LocationSettingsRequest.Builder()
                    .addLocationRequest(locationRequest);

            Task<LocationSettingsResponse> result =
                    LocationServices.getSettingsClient(activity).checkLocationSettings(builder.build());

            result.addOnCompleteListener(task -> {
                try {
                    LocationSettingsResponse response = task.getResult(ApiException.class);
                    // All location settings are satisfied. The client can initialize location
                    // requests here.
                } catch (ApiException exception) {
                    switch (exception.getStatusCode()) {
                        case LocationSettingsStatusCodes.RESOLUTION_REQUIRED:
                            // Location settings are not satisfied. But could be fixed by showing the
                            // user a dialog.
                            try {

                                // Cast to a resolvable exception.
                                ResolvableApiException resolvable = (ResolvableApiException) exception;
                                // Show the dialog by calling startResolutionForResult(),
                                // and check the result in onActivityResult().
                                try {
                                    resolvable.startResolutionForResult(
                                            activity,
                                            LocationRequest.PRIORITY_HIGH_ACCURACY);
                                } catch (IntentSender.SendIntentException e) {
                                    e.printStackTrace();
                                }
                            } catch (ClassCastException e) {
                                // Ignore, should be an impossible error.
                            }
                            break;
                        case LocationSettingsStatusCodes.SETTINGS_CHANGE_UNAVAILABLE:
                            // Location settings are not satisfied. However, we have no way to fix the
                            // settings so we won't show the dialog.
                            break;
                    }
                }
            });
        } else {
            String coor = getCurrentCoordinate(context);
            mapboxListener.onGettingLongClickData(new LatLng(Double.parseDouble(coor.split(",")[0]), Double.parseDouble(coor.split(",")[1])));
            setupMap(client);
        }
    }


    public interface MapboxListener {
        void onGettingLongClickData(LatLng point);
    }

}
