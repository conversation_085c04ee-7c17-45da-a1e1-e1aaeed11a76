package com.asmtunis.procaissemobility.data.models;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;
import androidx.annotation.NonNull;

/**
 * Created by PC on 12/15/2017.
 */
@Entity
public class AppProperties {
    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "Id")
    public int id=0;

    @NonNull
    @ColumnInfo(name = "Updated_at")
    public Long updated_at=0L;

    @NonNull
    @ColumnInfo(name = "Synced_at")
    public Long synced_at=0L;

    public AppProperties() {
    }
@Ignore
    public AppProperties(@NonNull Long updated_at, @NonNull Long synced_at) {

        if (updated_at>0L)
        { this.updated_at = updated_at;}
        if (synced_at>0L)
        {  this.synced_at = synced_at;}
    }

    @NonNull
    public int getId() {
        return id;
    }

    public void setId(@NonNull int id) {
        this.id = id;
    }

    @NonNull
    public Long getUpdated_at() {
        return updated_at;
    }

    public void setUpdated_at(@NonNull Long updated_at) {
        this.updated_at = updated_at;
    }


    @NonNull
    public Long getSynced_at() {
        return synced_at;
    }

    public void setSynced_at(@NonNull Long synced_at) {
        this.synced_at = synced_at;
    }

    @Override
    public String toString() {
        return "AppProperties{" +
                "id=" + id +
                ", updated_at=" + updated_at +
                '}';
    }
}
