package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import org.jetbrains.annotations.NonNls;

/**
 * Created by Oussama AZIZI on 5/27/22.
 */

@Entity(primaryKeys = {"AutoCodeUt", "AutoCodeAu"})
public class Authorization {
    @NonNull
    @ColumnInfo(name = "AutoCodeUt")
    @SerializedName("AutoCodeUt")
    @Expose
    private int autoCodeUt;
    @NonNull
    @ColumnInfo(name = "AutoCodeAu")
    @SerializedName("AutoCodeAu")
    @Expose
    private int autoCodeAu;
    @ColumnInfo(name = "AutEtat")
    @SerializedName("AutEtat")
    @Expose
    private boolean autEtat;

    @ColumnInfo(name = "AutValues")
    @SerializedName("AutValues")
    @Expose
    private String authValues;

    public void setAuthValues(String authValues) {
        this.authValues = authValues;
    }

    public String getAuthValues() {
        return authValues;
    }

    public Integer getAutoCodeUt() {
        return autoCodeUt;
    }

    public void setAutoCodeUt(Integer autoCodeUt) {
        this.autoCodeUt = autoCodeUt;
    }

    public Integer getAutoCodeAu() {
        return autoCodeAu;
    }

    public void setAutoCodeAu(Integer autoCodeAu) {
        this.autoCodeAu = autoCodeAu;
    }

    public boolean getAutEtat() {
        return autEtat;
    }

    public void setAutEtat(Boolean autEtat) {
        this.autEtat = autEtat;
    }
}
