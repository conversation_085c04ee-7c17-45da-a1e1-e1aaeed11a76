package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.VCListeConcurrent;

import java.util.List;

/**
 * Created by Oussama AZIZI on 6/24/22.
 */

@Dao
public interface VCListeConcurrentDAO {
    @Query("SELECT * FROM VCListeConcurrent")
    LiveData<List<VCListeConcurrent>> getAll();
    @Query("SELECT concurrent FROM VCListeConcurrent WHERE Codeconcurrent=:code")
    String getConcurrent(String code);


    @Query("SELECT Codeconcurrent FROM VCListeConcurrent WHERE concurrent=:code")
    String getConcurrentbyname(String code);


    @Query("SELECT * FROM VCListeConcurrent WHERE Codeconcurrent=:code")
    VCListeConcurrent getListConcurrent(String code);

    @Query("delete from VCListeConcurrent")
    void deleteAll();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<VCListeConcurrent> vcListeConcurrents);
}
