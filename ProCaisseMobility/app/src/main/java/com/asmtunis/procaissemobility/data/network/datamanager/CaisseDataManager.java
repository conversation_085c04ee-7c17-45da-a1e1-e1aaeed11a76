package com.asmtunis.procaissemobility.data.network.datamanager;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Caisse;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.CaisseService;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;

import java.util.List;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

/**
 * Created by Achraf on 29/09/2017.
 */

public class CaisseDataManager  {

    private static CaisseDataManager sInstance;
    PrefUtils prefUtils;

    private final CaisseService mCaisseService;

    public CaisseDataManager() {
        mCaisseService = new ServiceFactory<>(CaisseService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Caisse")).makeService();
    }

    public static CaisseDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new CaisseDataManager();
        }
        return sInstance;
    }

    public void getCaisses(GenericObject genericObject,
            RemoteCallback<List<Caisse>> listener) {
        mCaisseService.getCaisses(genericObject)
                .enqueue(listener);
    }


}


