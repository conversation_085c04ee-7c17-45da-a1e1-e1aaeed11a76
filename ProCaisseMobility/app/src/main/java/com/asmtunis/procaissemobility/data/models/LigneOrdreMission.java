package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import java.io.Serializable;

@Entity(primaryKeys = {"LIGOR_Code","LIGOR_Exer", "LIGOR_Clt"})
public class LigneOrdreMission implements Serializable {
    @SerializedName("LIGOR_Code")
    @ColumnInfo(name = "LIGOR_Code")
    @NonNull
    @Expose
    public String lIGORCode;
    @SerializedName("LIGOR_Exer")
    @NonNull
    @ColumnInfo(name = "LIGOR_Exer")
    @Expose
    public String lIGORExer;
    @SerializedName("LIGOR_Clt")
    @NonNull
    @ColumnInfo(name = "LIGOR_Clt")
    @Expose
    public String lIGORClt;
    @SerializedName("LIGOR_Etat")
    @ColumnInfo(name = "LIGOR_Etat")
    @Expose
    public String lIGOREtat;
    @SerializedName("LIGOR_Note")
    @ColumnInfo(name = "LIGOR_Note")
    @Expose
    public String lIGORNote;
    @SerializedName("export")
    @ColumnInfo(name = "export")
    @Expose
    public Integer export;
    @SerializedName("DDm")
    @ColumnInfo(name = "DDm")
    @Expose
    public String dDm;
    @SerializedName("exportM")
    @ColumnInfo(name = "exportM")
    @Expose
    public Integer exportM;
    @SerializedName("DDmM")
    @ColumnInfo(name = "DDmM")
    @Expose
    public String dDmM;
    @SerializedName("LIGOR_Ordre")
    @ColumnInfo(name = "LIGOR_Ordre")
    @Expose
    public Integer lIGOROrdre;

    @SerializedName("LIGOR_Latitude")
    @ColumnInfo(name = "LIGOR_Latitude")
    @Expose
    public double lIGOR_Latitude;

    @SerializedName("LIGOR_Longitude")
    @ColumnInfo(name = "LIGOR_Longitude")
    @Expose
    public double lIGOR_Longitude;

    @SerializedName("LIGOR_Date")
    @ColumnInfo(name = "LIGOR_Date")
    @Expose
    public String lIGOR_Date;

    @SerializedName("infoM")
    @ColumnInfo(name = "infoM")
    @Expose
    public String infoM;

    public String getInfoM() {
        return infoM;
    }

    public void setInfoM(String infoM) {
        this.infoM = infoM;
    }

    @ColumnInfo(name = "isSync")
    @Expose
    public boolean isSync = true;

    private double latitude;
    private double longitude;
    private double distance;
    private String address;
    private String distanceKm;
    private String distanceTime;

    public String getDistanceTime() {
        return distanceTime;
    }

    public void setDistanceTime(String distanceTime) {
        this.distanceTime = distanceTime;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getDistanceKm() {
        return distanceKm;
    }

    public void setDistanceKm(String distanceKm) {
        this.distanceKm = distanceKm;
    }

    public double getDistance() {
        return distance;
    }

    public void setDistance(double distance) {
        this.distance = distance;
    }

    public double getLatitude() {
        return latitude;
    }

    public void setLatitude(double latitude) {
        this.latitude = latitude;
    }

    public double getLongitude() {
        return longitude;
    }

    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }

    @Ignore
    public LigneOrdreMission(@NonNull String lIGORCode, @NonNull String lIGORClt, @NonNull String lIGORExer, double latitude, double longitude, String lIGOREtat) {
        this.lIGORCode = lIGORCode;
        this.lIGORClt = lIGORClt;
        this.lIGORExer = lIGORExer;
        this.latitude = latitude;
        this.longitude = longitude;
        this.lIGOREtat = lIGOREtat;
    }

    public LigneOrdreMission(String lIGORCode, String lIGORExer, String lIGORClt, String lIGOREtat, String lIGORNote, Integer export, String dDm, Integer exportM, String dDmM, Integer lIGOROrdre) {
        this.lIGORCode = lIGORCode;
        this.lIGORExer = lIGORExer;
        this.lIGORClt = lIGORClt;
        this.lIGOREtat = lIGOREtat;
        this.lIGORNote = lIGORNote;
        this.export = export;
        this.dDm = dDm;
        this.exportM = exportM;
        this.dDmM = dDmM;
        this.lIGOROrdre = lIGOROrdre;
    }


}
