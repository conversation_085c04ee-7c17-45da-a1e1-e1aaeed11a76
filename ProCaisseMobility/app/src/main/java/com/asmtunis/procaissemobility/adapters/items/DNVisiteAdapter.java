package com.asmtunis.procaissemobility.adapters.items;

        import android.content.Context;
        import android.view.View;
        import android.widget.FrameLayout;
        import android.widget.ImageView;
        import android.widget.LinearLayout;
        import android.widget.TextView;

        import androidx.annotation.NonNull;
        import androidx.appcompat.widget.Toolbar;
        import androidx.lifecycle.ViewModelProvider;
        import androidx.lifecycle.ViewModelStoreOwner;
        import androidx.recyclerview.widget.RecyclerView;

        import com.asmtunis.procaissemobility.R;
        import com.asmtunis.procaissemobility.App;
        import com.asmtunis.procaissemobility.data.models.Client;
        import com.asmtunis.procaissemobility.data.models.DNVIsite;
        import com.asmtunis.procaissemobility.data.viewModels.DNVisiteViewModel;
        import com.asmtunis.procaissemobility.helper.utils.DateUtils;
        import com.asmtunis.procaissemobility.listener.ItemCallback;
        import com.asmtunis.procaissemobility.listener.ItemDNCallback;
        import com.asmtunis.procaissemobility.listener.MenuItemsAction;
        import com.mikepenz.fastadapter.items.AbstractItem;

        import java.util.List;
        import java.util.Locale;


public class DNVisiteAdapter extends AbstractItem<DNVisiteAdapter, DNVisiteAdapter.VCAutreViewHolder> {
    private Context context;
    public DNVIsite dnVisite;
    protected ItemDNCallback itemCallback;
    MenuItemsAction menuItemsAction;
    DNVisiteViewModel dnVisiteViewModel;


    public DNVisiteAdapter(Context context, DNVIsite dnVisite, ViewModelStoreOwner viewModelStoreOwner , ItemDNCallback itemCallback, MenuItemsAction menuItemsAction) {
        this.context = context;
        this.dnVisite = dnVisite;
        this.itemCallback = itemCallback;
        this.menuItemsAction = menuItemsAction;
        dnVisiteViewModel = new ViewModelProvider(viewModelStoreOwner).get(DNVisiteViewModel.class);
    }

    public class VCAutreViewHolder extends RecyclerView.ViewHolder{
        TextView code;
        TextView nommagasin;
        TextView nomGearant;
        TextView nomClient;
        TextView adresse;
        TextView date;
        LinearLayout totalContainer;

        //ImageView delete_dn_viste;
      //  ImageView edit_dn_visite;
        ImageView tiketuserImV;
        TextView surface;
        TextView typpTvENTE;
        TextView typeService;



        LinearLayout footerLayout;
        public Toolbar toolbar;
        FrameLayout content;
        com.asmtunis.procaissemobility.ui.components.TicketView  ticketView;
        jp.shts.android.library.TriangleLabelView itemStatusLabel;
        public VCAutreViewHolder(@NonNull View itemView) {
            super(itemView);
          /*  code = itemView.findViewById(R.id.code_dnvisite);
            nommagasin =itemView.findViewById(R.id.nommagazin_tv);
            nomGearant =itemView.findViewById(R.id.dn_nomgerant);
            adresse =itemView.findViewById(R.id.dn_adresse);
            date=itemView.findViewById(R.id.date_dn);

            surface=itemView.findViewById(R.id.dn_surface);
            typpTvENTE=itemView.findViewById(R.id.dn_typeptvente);
            typeService=itemView.findViewById(R.id.dn_type_service);



            delete_dn_viste=itemView.findViewById(R.id.delete_dn_viste);
            edit_dn_visite=itemView.findViewById(R.id.edit_dn_visite);
*/



            ticketView = itemView.findViewById(R.id.layout_ticket);
            footerLayout = itemView.findViewById(R.id.footer_layout);
            toolbar = itemView.findViewById(R.id.toolbar);

            code = itemView.findViewById(R.id.ticketFirstLine);
            nomClient = itemView.findViewById(R.id.ticketSecondLine);
            nomGearant = itemView.findViewById(R.id.ticketThirdLine);
            totalContainer = itemView.findViewById(R.id.totalContainer);
            nommagasin = itemView.findViewById(R.id.ticketFourthLine);
          //  tiketuserImV = itemView.findViewById(R.id.tiketuserImV);

            date = itemView.findViewById(R.id.dateCreation);
            content = itemView.findViewById(R.id.content_layout);
            itemStatusLabel = itemView.findViewById(R.id.item_status_label);


        }
    }

    @NonNull
    @Override
    public VCAutreViewHolder getViewHolder(View v) {
        return new VCAutreViewHolder(v);
    }

    @Override
    public int getType() {
        return R.id.fastadapter_ticket_item_id;
    }

    @Override
    public int getLayoutRes() {
        return R.layout.ticket_item_ii;
    }
   // public int getLayoutRes() {return R.layout.dn_visite;}

    void onDeleteClick(DNVisiteAdapter.VCAutreViewHolder viewHolder) {
        if (dnVisite != null) {
            if (itemCallback == null) {
                return;
            } else {
                itemCallback.onItemClicked(viewHolder, dnVisite);
            }

        }
    }

    void onEditClick(DNVisiteAdapter.VCAutreViewHolder viewHolder) {
        if (dnVisite != null) {
            if (itemCallback == null) {
                return;
            } else {
                itemCallback.onItemModifClicked(dnVisite);
            }

        }
    }

    void onViewClick(DNVisiteAdapter.VCAutreViewHolder viewHolder) {
        if (dnVisite != null) {
            if (itemCallback == null) {
                return;
            } else {
                itemCallback.onItemClicked(dnVisite);
            }

        }
    }

    @Override
    public void bindView(VCAutreViewHolder holder, List<Object> payloads) {
        super.bindView(holder, payloads);
        if(!dnVisite.getVIS_Num().equals(""))holder.code.setText(dnVisite.getVIS_Num());
            else holder.code.setText(dnVisite.getVIS_Code_M());
        holder.nommagasin.setText("Magasin : " +dnVisite.getVIS_NomMagazin());
        holder.nomGearant.setText("Gérant : " +dnVisite.getVIS_NomGerant());

        Client client = App.database.clientDAO().getOneByCode(dnVisite.getVIS_CodeClient());

        String preffix = "Client : ";


        if(client!=null){
            if(client.getcLIType().toLowerCase(Locale.ROOT).equals("prospect")) preffix = "Prospect : ";
            holder.nomClient.setText(preffix +client.cLINomPren);
        }
        else {
            holder.nomClient.setText("Code client : "+dnVisite.getVIS_CodeClient());
        }






      //  holder.tiketuserImV.setVisibility(View.INVISIBLE);
        //    holder.adresse.setText(dnVisite.getVIS_Adresse());
        holder.date.setText(dnVisite.getVIS_Date().replace(".000",""));



     //   holder.surface.setText(App.database.dnSuperficieDAO().getbyCode(dnVisite.getVIS_Superf()).getTypeSuperf());
       // holder.typeService.setText(App.database.dnTypeServicesDAO().getbyCode(dnVisite.getVIS_TypeServ()).getTypeSv());
     //   holder.typpTvENTE.setText(App.database.dnTypePVenteSDAO().getbyCode(dnVisite.getVIS_TypePV()).getTypePV());
        if (itemCallback != null) {
            //  holder.itemView.setOnClickListener(v -> onViewClick(holder));

            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onViewClick(holder);
                }
            });
            holder.toolbar.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onViewClick(holder);
                }
            });
        }


        if(!dnVisite.isSync){
            setTriangleView(holder.itemStatusLabel, 0);
            holder.date.setTextColor(context.getResources().getColor(R.color.warningColor));
        }

        else  {
            setTriangleView(holder.itemStatusLabel, -1);
            holder.date.setTextColor(context.getResources().getColor(R.color.successColor));
        }

    /*    if (itemCallback != null) {
            holder.itemView.setOnClickListener(v -> onViewClick(holder));
        }

        holder.delete_dn_viste.setOnClickListener(v -> onDeleteClick(holder));

        holder.edit_dn_visite.setOnClickListener(v -> onEditClick(holder));*/
    }



    void setTriangleView(jp.shts.android.library.TriangleLabelView labelView, int status) {
        labelView.setVisibility(View.VISIBLE);
        switch (status) {
            case 0:
                labelView.setTriangleBackgroundColorResource(R.color.warningColor);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.notSync);
                // labelView.setPrimaryTextColorResource(R.color.md_red_100);
                labelView.setPrimaryTextColorResource(R.color.black);
                break;

            case 1:
                labelView.setTriangleBackgroundColorResource(R.color.md_green_800);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.new_label);
                labelView.setPrimaryTextColorResource(R.color.md_green_100);
                break;
            default:
                labelView.setVisibility(View.GONE);

                break;
        }

    }

}


