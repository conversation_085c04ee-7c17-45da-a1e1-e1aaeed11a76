package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.Article;
import com.asmtunis.procaissemobility.data.models.Fournisseur;

import java.util.List;

/**
 * Created by PC on 11/18/2017.
 */
@Dao
public interface FournisseurDAO {

    @Query("SELECT * FROM Fournisseur")
    LiveData<List<Fournisseur>>  getAll();


    @Query("SELECT COUNT(*) FROM Fournisseur")
    int count();

    @Query("SELECT * FROM Fournisseur where FRS_codef=:code")
    Fournisseur getByCode(String code);


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(Fournisseur item);


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<Fournisseur> items);

    @Query("DELETE FROM Fournisseur")
    void deleteAll();

}
