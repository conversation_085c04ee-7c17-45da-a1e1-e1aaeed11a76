package com.asmtunis.procaissemobility.helper.utils;

import android.app.DatePickerDialog;
import android.app.Dialog;
import android.os.Bundle;
import android.util.Log;
import android.widget.DatePicker;

import androidx.fragment.app.DialogFragment;

import com.asmtunis.procaissemobility.ui.dialogs.DepenceDialog;
import com.asmtunis.procaissemobility.ui.fragments.ExpensesFragment;

import java.util.Calendar;

/**
 * Created by WAEL on 9/2/22.
 */
public class DatePickerFragment extends DialogFragment implements
        DatePickerDialog.OnDateSetListener {

    @Override
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        // Use the current date as the default date in the picker
        final Calendar c = Calendar.getInstance();
        int year = c.get(Calendar.YEAR);
        int month = c.get(Calendar.MONTH);
        int day = c.get(Calendar.DAY_OF_MONTH);

        // Create a new instance of DatePickerDialog and return it
        return new DatePickerDialog(getActivity(), this, year, month, day);
    }

    public void onDateSet(DatePicker view, int year, int month, int day) {
        Log.w("DatePicker","Date = " + year +"-"+ (month + 1) +"-"+ day);
       try {
            DepenceDialog.etDateExpense.setText(year + "-" + (month + 1) + "-" + day);
        }catch (NullPointerException ignored) {

       }
       try {
            ExpensesFragment.dateDepenceToUpdate.setText(year + "-" + (month + 1) + "-" + day);
        }catch (NullPointerException ignored) {

       }
    }
}
