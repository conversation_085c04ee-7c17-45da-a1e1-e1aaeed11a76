package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.VCNewProduct;
import com.asmtunis.procaissemobility.data.models.VCPrix;
import com.asmtunis.procaissemobility.data.models.VCPromo;

import java.util.List;

/**
 * Created by Oussama AZIZI on 6/24/22.
 */

@Dao
public interface VCPricesDAO {
    @Query("SELECT * FROM VCPrix where Status !='DELETED' order by strftime('%Y-%m-%d %H-%M-%S',DateOp) desc")
    LiveData<List<VCPrix>> getAll();

    @Query("SELECT * FROM VCPrix WHERE CodeVCPrixM = :code")
    VCPrix getByCodeM(String code);


    @Query("SELECT * FROM VCPrix WHERE CodeVCPrix = :code")
    VCPrix getByCode(String code);


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<VCPrix> vcPrixList);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(VCPrix vcPrixe);


    @Query("SELECT * FROM VCPrix where isSync=0 and  (Status='INSERTED'  or Status='UPDATED') ")
    List<VCPrix> getNoSyncedToAddOrUpdate();

    @Query("SELECT * FROM VCPrix where isSync=0 and Status='DELETED' ")
    List<VCPrix> getNoSyncedToDelete();



    @Query("SELECT count(*) FROM VCPrix where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    int getCountNonSync();

    @Query("SELECT count(*) FROM VCPrix where isSync=0 and Status='DELETED' ")
    int getCountNoSyncedToDelete();


    @Query("SELECT count(*) FROM VCPrix where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    LiveData<Integer> getNoSyncCountMubtale();

    @Query("SELECT count(*) FROM VCPrix where isSync=0 and Status='DELETED' ")
    LiveData<Integer> getCountNoSyncedToDeleteMubtale();

    @Query("delete from VCPrix")
    void deleteAll();

    @Query("DELETE FROM VCPrix where CodeVCPrix=:CodeAutre")
    void deleteById(String CodeAutre);


    @Query("DELETE FROM VCPrix where CodeVCPrix=:CodeAutre or CodeVCPrixM = :CodeMobile")
    void deleteByIdAndCodeM(String CodeAutre, String CodeMobile);




    @Query("UPDATE VCPrix SET CodeVCPrix = :code_procaiss where CodeVCPrixM = :CodeMobile")
    void updateCloudCode(String code_procaiss, String CodeMobile);
}
