package com.asmtunis.procaissemobility.data.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.Facture;

import java.util.List;



@Dao
public interface FactureDAO {

    @Query("SELECT * FROM Facture")
    List<Facture> getAll();



    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(Facture item);


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<Facture> items);

    @Query("DELETE FROM Facture")
    void deleteAll();

    @Query("SELECT * FROM Facture WHERE FACT_NumBC = :code and FACT_user = :user and FACT_station = :station ")
    Facture getByTicket(String code, String user, String station);


}

