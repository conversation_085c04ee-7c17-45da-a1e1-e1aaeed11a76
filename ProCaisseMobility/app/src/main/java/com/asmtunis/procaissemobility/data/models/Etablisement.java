package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
@Entity
public class Etablisement {
    @NonNull
    @PrimaryKey
    @SerializedName("Code_Et")
    @Expose
    private String codeEt;
    @SerializedName("Desg_Et")
    @Expose
    private String desgEt;
    @SerializedName("Et_Adresse")
    @Expose
    private String etAdresse;
    @SerializedName("Et_Activite")
    @Expose
    private String etActivite;
    @SerializedName("Et_MAtriculeF")
    @Expose
    private String etMAtriculeF;
    @SerializedName("Et_TVA")
    @Expose
    private String etTVA;
    @SerializedName("Et_Tel1")
    @Expose
    private String etTel1;
    @SerializedName("Et_Tel2")
    @Expose
    private String etTel2;
    @SerializedName("Et_Fax")
    @Expose
    private String etFax;

    public String getCodeEt() {
        return codeEt;
    }

    public void setCodeEt(String codeEt) {
        this.codeEt = codeEt;
    }

    public String getDesgEt() {
        return desgEt;
    }

    public void setDesgEt(String desgEt) {
        this.desgEt = desgEt;
    }

    public String getEtAdresse() {
        return etAdresse;
    }

    public void setEtAdresse(String etAdresse) {
        this.etAdresse = etAdresse;
    }

    public String getEtActivite() {
        return etActivite;
    }

    public void setEtActivite(String etActivite) {
        this.etActivite = etActivite;
    }

    public String getEtMAtriculeF() {
        return etMAtriculeF;
    }

    public void setEtMAtriculeF(String etMAtriculeF) {
        this.etMAtriculeF = etMAtriculeF;
    }

    public String getEtTVA() {
        return etTVA;
    }

    public void setEtTVA(String etTVA) {
        this.etTVA = etTVA;
    }

    public String getEtTel1() {
        return etTel1;
    }

    public void setEtTel1(String etTel1) {
        this.etTel1 = etTel1;
    }

    public String getEtTel2() {
        return etTel2;
    }

    public void setEtTel2(String etTel2) {
        this.etTel2 = etTel2;
    }

    public String getEtFax() {
        return etFax;
    }

    public void setEtFax(String etFax) {
        this.etFax = etFax;
    }

}
