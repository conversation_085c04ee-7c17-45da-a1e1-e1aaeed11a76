package com.asmtunis.procaissemobility.data.network.services;


import com.asmtunis.procaissemobility.data.models.Fournisseur;
import com.asmtunis.procaissemobility.data.models.GenericObject;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * Created by PC on 10/12/2017.
 */

public interface FournisseurService {


    @POST("getFournisseurs")
    Call<List<Fournisseur>> getFournisseurs(@Body GenericObject genericObject);

}
