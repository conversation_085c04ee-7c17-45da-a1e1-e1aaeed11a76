package com.asmtunis.procaissemobility.adapters.items;

import static com.asmtunis.procaissemobility.helper.utils.StringUtils.isEmptyString;

import android.content.Context;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.afollestad.materialdialogs.DialogAction;
import com.afollestad.materialdialogs.MaterialDialog;
import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Article;
import com.asmtunis.procaissemobility.data.models.PricePerStation;
import com.asmtunis.procaissemobility.helper.AppHelper;
import com.asmtunis.procaissemobility.helper.Globals;
import com.asmtunis.procaissemobility.helper.PrefHelper;
import com.asmtunis.procaissemobility.helper.utils.ArticleHashMap;
import com.asmtunis.procaissemobility.helper.utils.Calculator;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.ui.dialogs.UnitPriceDialog;
import com.asmtunis.procaissemobility.ui.components.ExtendedMaterialEditText;
import com.mikepenz.fastadapter.IAdapter;
import com.mikepenz.fastadapter.items.AbstractItem;
import com.mikepenz.fastadapter.listeners.OnClickListener;
import com.mobsandgeeks.saripaar.Validator;
import com.mobsandgeeks.saripaar.annotation.NotEmpty;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import cn.nekocode.badge.BadgeDrawable;
import es.dmoral.toasty.Toasty;


/**
 * Created by PC on 10/3/2017.
 */

public class ArticleItem extends AbstractItem<ArticleItem, ArticleItem.ViewHolder> implements OnClickListener<ArticleItem> {

    public Article article;
    public  ArticleHashMap list;
    public int from;
    PrefUtils prefUtils;
    MaterialDialog dialog;
    Validator validator;
    @NotEmpty
    ExtendedMaterialEditText quantityInputField;
    @NotEmpty
    ExtendedMaterialEditText discountInputField;
    @NotEmpty
    ExtendedMaterialEditText priceInputField;
    LinearLayout discountView;
    int count = 0;
    Context context;

    public ArticleItem(Context context, Article article,int from) {
        this.article = article;
        this.context = context;
        this.from = from;
    }

    public ArticleItem(Context context, Article article,int from, ArticleHashMap list) {
        this.article = article;
        this.context = context;
        this.from = from;
        this.list = list;
    }

    public Article getArticle() {
        return article;
    }

    public void setArticle(Article article) {
        this.article = article;
    }

    @Override
    public int getType() {
        return R.id.fastadapter_article_item_id;
    }

    @Override
    public int getLayoutRes() {
          return R.layout.grid_item;
    }

    @Override
    public ArticleItem withOnItemClickListener(OnClickListener<ArticleItem> onItemClickListener) {
        return super.withOnItemClickListener(onItemClickListener);
    }

    @Override
    public void bindView(final ViewHolder viewHolder, List<Object> payloads) {
        super.bindView(viewHolder, payloads);
        prefUtils = new PrefUtils(context);
       viewHolder.title.setText(article.getaRTDesignation());
        // viewHolder.title.setText(article.aRTCode);
        double stockq = article.getaRTQteStock();
        try {
            stockq = (App.database.stationStockDAO().getOneByCode(article.aRTCode, article.sARTCodeSatation).getSARTQte());
        } catch (Exception e) {
           //  Log.d("lod", e.getMessage());
        }
      PricePerStation pricePerStation = App.database.pricePerStationDAO().getOneByArticle(article.aRTCode, article.sARTCodeSatation);
        viewHolder.thumbnail.setImageResource((pricePerStation != null && pricePerStation.getTauxPromo() > 0 &&
                AppHelper.hasPromo(context)
                ? R.drawable.ic_promo
                : R.drawable.ic_box));

        final BadgeDrawable badgeDrawable =
                new BadgeDrawable.Builder()
                        .type(BadgeDrawable.TYPE_WITH_TWO_TEXT_COMPLEMENTARY)
                        .cornerRadius(5)
                        .badgeColor(
                                StringUtils.parseDouble(String.valueOf(stockq), 0) <= 0 ?
                                        0xffCC0000 : 0xff009999)
                        .text1(String.valueOf(StringUtils.parseDouble(String.valueOf(stockq), 0)))
                        .text2(String.valueOf(article.getuNITEARTICLECodeUnite()))
                        .build();


        if(from==1)  {
            viewHolder.subtitle.setVisibility(View.GONE);
            viewHolder.count.setVisibility(View.GONE);
        }

       // else {
            viewHolder.selectecItemCount.setVisibility(article.getCount() > 0 ? View.VISIBLE : View.GONE);
            viewHolder.selectecItemCount.setSecondaryText(String.valueOf(StringUtils.parseDouble(String.valueOf(article.getCount()), 0)));

      //  }

        viewHolder.title.setSingleLine(false);
        viewHolder.title.setLines(3);


        viewHolder.count.setImageDrawable(badgeDrawable);
     //   viewHolder.subtitle.setText(String.valueOf(StringUtils.decimalFormat(article.getaRTPrixUnitaireHT())));

        viewHolder.subtitle.setText(StringUtils.priceFormat(article.getPvttc()));
        viewHolder.subtitle.setBackgroundColor(article.getColorCode() !=0?article.getColorCode():
                context.getResources().getColor(R.color.material_drawer_primary_dark));
    }

    @Override
    public void unbindView(ViewHolder holder) {
        super.unbindView(holder);
        //  holder.thumbnail.setImage(null);
        holder.title.setText(null);
        holder.subtitle.setText(null);
    }

    //Init the viewHolder for this Item
    @Override
    public ViewHolder getViewHolder(View v) {
        return new ViewHolder(v);
    }

    @Override
    public boolean onClick(View v, IAdapter<ArticleItem> adapter, ArticleItem item, int position) {
        boolean dialogShown = false;

        if (!dialogShown) {
            boolean wrapInScrollView = true;
            dialog = new MaterialDialog.Builder(context)
                    .title(item.article.getaRTDesignation())
                    .customView(R.layout.add_article_view, wrapInScrollView).autoDismiss(false).cancelable(false)
                    .positiveText("R.string.positive")
                    .onPositive(new MaterialDialog.SingleButtonCallback() {
                        @Override
                        public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {

                            validator.validate();

                            if (validator.isValidating()) {
                                count = Integer.parseInt(quantityInputField.getText().toString());

                            }
                        }
                    }).build();

            View view = dialog.getCustomView();
            quantityInputField = view.findViewById(R.id.quantityInputField);
            discountInputField = view.findViewById(R.id.discountInputField);
            priceInputField = view.findViewById(R.id.unitPriceInputField);

            priceInputField.setOnClickListener(view1 -> {
                if (!prefUtils.getchoosePriceCategorieAuthorization())
                    Toasty.error(context,"Vous n'avez pas l'autorisation pour changer la catégorie des prix.").show();
                else{
                    UnitPriceDialog unitPriceDialog = new UnitPriceDialog(context) {
                        @Override
                        public void onChangeChecked(int checkedId) {

                        }

                        @Override
                        public void onConfirm(int checkedId) {
                            App.prefUtils.settPriceCategorie(checkedId);
                            setupArticlePrice(checkedId, article);
                            App.database.articleDAO().insert(article);
                            //   generateArticles();

                            priceInputField.setText(article.getPvttc() + "");
                            PrefHelper.set(Globals.UNIT_PRICE_TYPE_KEY, Globals.UNIT_PRICE_TYPE.MULTIPLE);
                            // calculateCashedPrice();
                            // calculateTotalPriceTTC();
                        }
                    };
                   checkCurrenPrice(unitPriceDialog, article);
                    unitPriceDialog.show();
                }

            });
            discountView = view.findViewById(R.id.discountView);
            discountView.setVisibility(isEmptyString(prefUtils.getDiscountAuthorization()) || (prefUtils.getDiscountAuthorization().equalsIgnoreCase("-")) ? View.GONE : View.VISIBLE);
            dialog.show();
        }


        return false;
    }


    private void setupArticlePrice(int checkedId,Article article) {
        switch (checkedId) {
           /* case R.id.publicPrice: {

                article.setPvttc((Double.parseDouble(article.getArtPrixPublique()) > 0) ? Double.parseDouble(article.getArtPrixPublique()) : article.getPvttc());
                break;
            }*/
            case R.id.priceGros1: {
                Log.d("PriceLog", "onConfirm: " + article);
                article.setPvttc(Double.parseDouble(article.getPrixGros1()) > 0 ? Double.parseDouble(article.getPrixGros1()) : article.getPvttc());

                break;
            }
            case R.id.priceGros2: {
                article.setPvttc(Double.parseDouble(article.getPrixGros2()) > 0 ? Double.parseDouble(article.getPrixGros2()) : article.getPvttc());

                break;
            }
            case R.id.priceGros3: {
                article.setPvttc(Double.parseDouble(article.getPrixGros3()) > 0 ? Double.parseDouble(article.getPrixGros3()) : article.getPvttc());
                break;
            }

            default:
                article.setPvttc((Double.parseDouble(article.getArtPrixPublique()) > 0) ? Double.parseDouble(article.getArtPrixPublique()) : article.getPvttc());

                break;
        }


    }

    private void checkCurrenPrice(UnitPriceDialog unitPriceDialog, Article article) {

        if (Double.parseDouble(article.getArtPrixPublique()) == article.getPvttc()) {
               unitPriceDialog.check(R.id.publicPrice);
            App.prefUtils.settPriceCategorie(R.id.publicPrice);
            return;
        }
        if (Double.parseDouble(article.getPrixGros1()) == article.getPvttc()) {
             unitPriceDialog.check(R.id.priceGros1);
            App.prefUtils.settPriceCategorie(R.id.priceGros1);
            return;

        }
        if (Double.parseDouble(article.getPrixGros2()) == article.getPvttc()) {
             unitPriceDialog.check(R.id.priceGros2);
            App.prefUtils.settPriceCategorie(R.id.priceGros2);
            return;

        }
        if (Double.parseDouble(article.getPrixGros3()) == article.getPvttc()) {
             unitPriceDialog.check(R.id.priceGros3);
            App.prefUtils.settPriceCategorie(R.id.priceGros3);

        }
    }

    //The viewHolder used for this item. This viewHolder is always reused by the RecyclerView so scrolling is blazing fast
    protected static class ViewHolder extends RecyclerView.ViewHolder {
        protected View view;
        @BindView(R.id.title)
        TextView title;
        @BindView(R.id.subtitle)
        TextView subtitle;
        @BindView(R.id.station)
        TextView station;
        @BindView(R.id.count)
        ImageView count;
        @BindView(R.id.thumbnail)
        ImageView thumbnail;


        @BindView(R.id.countLabel)
        jp.shts.android.library.TriangleLabelView selectecItemCount;


        public ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
            this.view = view;
        }
    }
}
