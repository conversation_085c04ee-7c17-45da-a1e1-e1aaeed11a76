package com.asmtunis.procaissemobility.ui.activities;

import static com.asmtunis.procaissemobility.App.prefUtils;
import static com.asmtunis.procaissemobility.helper.Globals.CLIENT_INTENT_ID_KEY;
import static com.asmtunis.procaissemobility.helper.Globals.CLIENT_REG_PARTIEL_KEY;
import static com.asmtunis.procaissemobility.helper.Globals.DEFAULT_ENCODING;
import static com.asmtunis.procaissemobility.helper.Globals.REGLEMENT_PARTIEL_KEY;
import static com.asmtunis.procaissemobility.helper.Globals.TICKET_REG_PARTIEL_KEY;
import static com.asmtunis.procaissemobility.helper.Globals.TICKET_WITH_LINES_AND_PAYEMENT_KEY;
import static com.asmtunis.procaissemobility.helper.Globals.TICKET_WITH_LINES_KEY;
import static com.asmtunis.procaissemobility.helper.utils.UIUtils.getIconicsDrawable;
import static com.asmtunis.procaissemobility.helper.utils.UIUtils.setDefaultAutoScanColor;
import static com.asmtunis.procaissemobility.helper.utils.UIUtils.switchAutoScanColor;
import static com.asmtunis.procaissemobility.helper.utils.Utils.indexOfClient;
import static com.blankj.utilcode.util.SnackbarUtils.dismiss;
import static com.honeywell.aidc.BarcodeReader.PROPERTY_CODE_128_ENABLED;
import static com.honeywell.aidc.BarcodeReader.PROPERTY_EAN_13_CHECK_DIGIT_TRANSMIT_ENABLED;
import static com.honeywell.aidc.BarcodeReader.PROPERTY_EAN_13_ENABLED;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.AlertDialog;
import android.app.Dialog;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.Editable;
import android.text.InputType;
import android.text.TextWatcher;
import android.util.Log;
import android.view.Gravity;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;
import androidx.fragment.app.FragmentActivity;

import com.afollestad.materialdialogs.MaterialDialog;
import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.adapters.spinners.ClientSpinnerAdapter;
import com.asmtunis.procaissemobility.adapters.tables.LigneTicketTableDataAdapter;
import com.asmtunis.procaissemobility.data.models.Article;
import com.asmtunis.procaissemobility.data.models.ArticleCodeBar;
import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.data.models.LigneBonCommande;
import com.asmtunis.procaissemobility.data.models.LigneTicket;
import com.asmtunis.procaissemobility.data.models.Ticket;
import com.asmtunis.procaissemobility.data.models.TicketWithLines;
import com.asmtunis.procaissemobility.data.models.TicketWithLinesAndPayments;
import com.asmtunis.procaissemobility.data.models.Timbre;
import com.asmtunis.procaissemobility.data.viewModels.ArticleViewModel;
import com.asmtunis.procaissemobility.data.viewModels.ClientViewModel;
import com.asmtunis.procaissemobility.data.viewModels.OrdreWithLinesViewMiodel;
import com.asmtunis.procaissemobility.helper.BluetoothService;
import com.asmtunis.procaissemobility.helper.Globals;
import com.asmtunis.procaissemobility.helper.PrinterHelper;
import com.asmtunis.procaissemobility.helper.utils.Calculator;
import com.asmtunis.procaissemobility.helper.utils.DateUtils;
import com.asmtunis.procaissemobility.helper.utils.MapUtils;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.helper.utils.UIUtils;
import com.asmtunis.procaissemobility.helper.utils.Utils;
import com.asmtunis.procaissemobility.listener.DialogClickInterface;
import com.asmtunis.procaissemobility.ui.components.ExtendedMaterialEditText;
import com.asmtunis.procaissemobility.ui.components.SortableLigneTicketTableView;
import com.asmtunis.procaissemobility.ui.dialogs.ArticleDialog;
import com.asmtunis.procaissemobility.ui.dialogs.ArticleListDialog;
import com.asmtunis.procaissemobility.ui.dialogs.BarCodeDialogueFragment;
import com.asmtunis.procaissemobility.ui.dialogs.BarcodeScannerListener;
import com.asmtunis.procaissemobility.ui.dialogs.LigneTicketDialog;
import com.blankj.utilcode.util.ObjectUtils;
import com.example.barecodereader.barcode.BarCodeReaderManager;
import com.example.barecodereader.barcode.listener.BarcodeListener;
import com.example.barecodereader.barcode.readers.EdaReader;
import com.example.barecodereader.barcode.readers.PM80Reader;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.snackbar.Snackbar;
import com.mikepenz.fontawesome_typeface_library.FontAwesome;
import com.mikepenz.google_material_typeface_library.GoogleMaterial;
import com.mikepenz.iconics.IconicsDrawable;
import com.mobsandgeeks.saripaar.ValidationError;
import com.mobsandgeeks.saripaar.Validator;
import com.rengwuxian.materialedittext.MaterialEditText;
import com.transitionseverywhere.Slide;
import com.transitionseverywhere.TransitionManager;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;
import device.common.ScanConst;
import es.dmoral.toasty.Toasty;
import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;
import io.reactivex.internal.operators.observable.ObservableCreate;
import searchablespinner.interfaces.OnItemSelectedListener;
import timber.log.Timber;

public class TicketActivity extends BaseActivity implements DialogClickInterface {
    private static final int REQUEST_CLIENT_CODE = 101;
    public static int REQUEST_PAYEMENT_CODE = 111;
    private static final int REQUEST_ENABLE_BT = 2;
    private static final int REQUEST_CONNECT_DEVICE = 1;
    private boolean isUpdate;
    double amountToSet = 0.0;
    int index_prod_intable = 0;

    static BluetoothService mService = null;
    BluetoothDevice con_dev = null;
    private static final String TAG = "Ticket activity";
    @BindView(R.id.DateInputField)
    TextView dateInputField;
    static Validator.ValidationListener validationListener;
    BarCodeReaderManager barCodeReader;
    static TicketActivity ticketActivity;

    public static TicketActivity getInstance() {
        return ticketActivity;
    }

    static ExtendedMaterialEditText amountInputField;

    static ExtendedMaterialEditText amountwithDiscountInputField;

    static ExtendedMaterialEditText discountInputField;

    @BindView(R.id.addItemButton)
    com.mikepenz.iconics.view.IconicsButton addItemButton;

    @BindView(R.id.autoscan)
    com.mikepenz.iconics.view.IconicsButton autoScanButton;

    @BindView(R.id.scan)
    com.mikepenz.iconics.view.IconicsButton scanArt;

    static SortableLigneTicketTableView tableView;

    @BindView(R.id.SearchableClientSpinner)
    searchablespinner.SearchableSpinner searchableSpinner;



    @BindView(R.id.societe)
    TextView text;


    View footerTicketView;


    View dataTableView;


    TextView tableTitle;

    @BindView(R.id.headerTicketView)
    View headerTicketView;

    @BindView(R.id.buttons_layout)
    LinearLayoutCompat linearLayoutCompat;

    @BindView(R.id.regBut)
    MaterialButton regBut;

    @BindView(R.id.creBut)
    MaterialButton creBut;

    @BindView(R.id.dividerView1)
    View dividerView1;

    @BindView(R.id.dividerView2)
    View dividerView2;

    @BindView(R.id.discountLayout)
    RelativeLayout discountLayout;

    @BindView(R.id.footerLayout)
    LinearLayoutCompat footerLayout;

    @BindView(R.id.AmountwithDiscountLayout)
    RelativeLayout amountwithDiscountLayout;

    @BindView(R.id.addClientButton)
    com.mikepenz.iconics.view.IconicsButton addClientButton;

    MenuItem save, scan;

    static ViewGroup content;

    ClientSpinnerAdapter simpleListAdapter;


    Client client;

    static boolean dialogShown = false;
    static LigneTicketDialog ligneTicketDialog;
    Bundle savedInstanceState;
    static ArticleListDialog articleListDialog;
    static LigneTicketTableDataAdapter tableDataAdapter = null;
    ArrayList<LigneTicket> selectedArticles;
    static double amount = 0.0;
    static double amountWithDiscount = 0.0;
    double made;
    static double discount;
    double received;
    Date date;
    int ticketNumber;
    Ticket ticket;
    List<LigneTicket> ligneTicket;
    @BindView(R.id.toolbar)
    Toolbar toolbar;
    static FragmentActivity context;
    Intent intent;
    Unbinder unbinder;
    double entreLongitude, entreLatitude;
    private final int mBackupResultType = ScanConst.ResultType.DCD_RESULT_COPYPASTE;
    static boolean isShowing = false;
    static boolean isInventory = false;
    String ddm = "";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        unbinder = ButterKnife.bind(this);

        initComponents();
      //  startBarcode();
        isUpdate = getIntent().getBooleanExtra(Globals.UPDATE_TICKET, false);
        ticket = (Ticket) getIntent().getSerializableExtra(Globals.TICKET_TO_UPDATE);

        if(client==null || isUpdate){
            scanArt.setVisibility(View.GONE);
            autoScanButton.setVisibility(View.GONE);
            addItemButton.setVisibility(View.GONE);
        }
        if(isUpdate){
            ddm = ticket.tIKDDm;
            addItemButton.setEnabled(false);
            autoScanButton.setEnabled(false);
            scanArt.setEnabled(false);



         /*   searchableSpinner.setEnabled(false);
            searchableSpinner.hideEdit();
            searchableSpinner.setSelected(false);
            searchableSpinner.setClickable(false);
*/

            if(client==null){
                client = App.database.clientDAO().getOneByCode(ticket.tIKCodClt);
                if(client!=null){
                    searchableSpinner.setVisibility(View.GONE);
                    text.setText("Client : "+client.getcLINomPren());
                    text.setTextSize(20);
                    text.setTextColor(Color.GRAY);
                    //  text.setGravity(Gravity.CENTER_VERTICAL | Gravity.CENTER_HORIZONTAL);
                    text.setGravity(Gravity.CENTER);
                    //   text.setPadding(12,12,0,0);
                    text.setTypeface(Typeface.DEFAULT_BOLD);

                }

            }

            //  searchableSpinner.setVisibility(View.GONE);

            ticketNumber = ticket.tIKNumTicket;
            client = App.database.clientDAO().getOneByCode(ticket.tIKCodClt);
            if (client.getCliisCredit()==0.0) creBut.setVisibility(View.GONE);
            else creBut.setVisibility(View.VISIBLE);

        }
        else {
            ticketNumber = App.database.ticketDAO().getNewNumTicket(prefUtils.getCarnetId()) == 0 ? prefUtils.getMaxNumTicket() + 1 : App.database.ticketDAO().getNewNumTicket(prefUtils.getCarnetId());
        }


        autoScanButton.setDrawableBottom(getIconicsDrawable(GoogleMaterial.Icon.gmd_wb_auto, R.color.material_drawer_accent, this));
        setDefaultAutoScanColor(autoScanButton, this);
        scanArt.setDrawableBottom(getIconicsDrawable(GoogleMaterial.Icon.gmd_flip, R.color.material_drawer_accent, this));
        this.savedInstanceState = savedInstanceState;
        setSupportActionBar(toolbar);//set the back arrow in the toolbar
       // isUpdate = getIntent().getBooleanExtra(Globals.UPDATE_TICKET, false);
       // ticket = (Ticket) getIntent().getSerializableExtra(Globals.TICKET_TO_UPDATE);
      /*  if(ticket!=null){
            if (StringUtils.parseDouble(discountInputField.getText().toString(), 0) > 0) {
                articleListDialog.getCurrentSelection().setDiscount(String.valueOf(StringUtils.parseDouble(discountInputField.getText().toString(), 0)));
            }


            articleListDialog.getList().putAll(articleListDialog.getCurrentSelection());
            articleListDialog.getCurrentSelection().clear();
            articleListDialog.generateView();
            selectedArticles = articleListDialog.getList().getLigneTickets();
            setTicketDataTable(false,"4");
            setFooter();
        }*/


        Objects.requireNonNull(getSupportActionBar()).setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setDisplayShowCustomEnabled(true);
        getSupportActionBar().setDisplayUseLogoEnabled(false);
        context = this;
        tableDataAdapter = null;
        String coor = MapUtils.getCurrentCoordinate(context);
        if (!coor.isEmpty()) { // for certain device coor can be empty!! like huawei y5
            entreLongitude = Double.parseDouble(coor.split(",")[1]);
            entreLatitude = Double.parseDouble(coor.split(",")[0]);
            getSupportActionBar().setSubtitle(String.format(context.getResources().getString(R.string.lat_log), entreLatitude + "", entreLongitude + ""));
        } else {
            getSupportActionBar().setSubtitle("");

        }

        getData();

        ticketActivity = this;
        validationListener = new Validator.ValidationListener() {
            @Override
            public void onValidationSucceeded() {
            }

            @Override
            public void onValidationFailed(List<ValidationError> errors) {
                for (ValidationError error : errors) {
                    View view = error.getView();
                    String message = error.getCollatedErrorMessage(context);
                    // Display error messages ;)
                    if (view instanceof EditText) {
                        ((MaterialEditText) view).setError(message);
                    } else {
                        Toast.makeText(context, message, Toast.LENGTH_LONG).show();
                    }
                }
            }
        };
        addClientButton.setVisibility(View.GONE);
        regBut.setOnClickListener(v ->{
            if(client!=null)
            goToReglements(0, false);
            else Toasty.info(context, "Choisir Un Client").show();

        } );
        creBut.setOnClickListener(v -> {
            if(client!=null) {

                if(prefUtils.getReglementPartielAuth()){
                    new MaterialDialog.Builder(this)
                            .title("Voulez vous passer un :")
                            .backgroundColor(Color.WHITE)
                            //  .content(String.format(StringUtils.priceFormat(amount) + "") + new PrefUtils(context).getCurrency())
                            .positiveColor(Color.BLACK)
                            .negativeColor(Color.BLACK)
                            .positiveText(R.string.credit_label)
                            .negativeText("Réglement partiel")

                            .onPositive((dialog, which) -> {
                                goToReglements(1, false);
                            })
                            .onNegative((dialog, which) ->{
                                goToReglements(1, true);
                            } )

                            .show();
                }
                else  goToReglements(1, false);




            }
            else Toasty.info(context, "Choisir Un Client").show();
        });



        getSupportActionBar().setTitle(String.format(getString(R.string.ticket_number_field), ticketNumber + ""));
    }


    /**
     * load view from xml
     */
    private void initComponents() {
        creBut.setIconTint(ColorStateList.valueOf(Color.BLACK));
        creBut.setTextColor(Color.BLACK);
        creBut.setBackgroundColor(getResources().getColor(R.color.error));
        if (!prefUtils.getCrCtAuthorization()) creBut.setVisibility(View.GONE);

        amountInputField = findViewById(R.id.AmountInputField);
        amountwithDiscountInputField = findViewById(R.id.AmountwithDiscountInputField);
        discountInputField = findViewById(R.id.DiscountInputField);
        tableView = findViewById(R.id.tableView);
        dataTableView = findViewById(R.id.dataTableView);
        tableTitle = findViewById(R.id.produitTxtVw);
        footerTicketView = findViewById(R.id.footerTicketView);
        content = findViewById(android.R.id.content);
        linearLayoutCompat = findViewById(R.id.buttons_layout);
        linearLayoutCompat.setVisibility(View.GONE);
        footerLayout = findViewById(R.id.footerLayout);
    }

    void setupView(List<Article> articlesStockable, List<Article> allArticles) {
        setArticlesListView(articlesStockable, allArticles);
        setTicketHeader();
        dataTableView.setVisibility(View.VISIBLE);
        if(isUpdate)   setTableFromUpdate();
        //articleListDialog.getList().putAll(articleHashMap);
        setTicketDataTable(true, "1");
        setFooter();
        setTicketFooter();
    }

    /**
     * get articles list form local database
     */
    void getData() {

        ArticleViewModel.getInstance(this).getAllByStationMutableNotPat(prefUtils.getUserStationId()).observe(this, articles -> {
            ArticleViewModel.getInstance(this).getByStationMutableNotPat(prefUtils.getUserStationId()).observe(this, articleEnStock -> {
                if (!articleEnStock.isEmpty()) {
                    date = new Date();
                    setupView(articleEnStock, articles);
                } else {
                    date = new Date();
                    setupView(articleEnStock, articles);

                    if(!isUpdate){
                        Snackbar snackbar = Snackbar.make(linearLayoutCompat, "La liste des articles en Stock est vide ! ", Snackbar.LENGTH_INDEFINITE)
                                .setAction("quitter", v -> {
                                    dismiss();
                                    //   finish();

                                });
                        snackbar.show();
                    }

                }
            });
        });

    }

    /**
     * send articles list to dialog
     */
    void setArticlesListView(List<Article> articlesStockable, List<Article> allArticles) {
        articleListDialog = new ArticleListDialog(allArticles, articlesStockable, false, 0, true, false, false, (item, quantity) -> {
            amountToSet += item.getPvttc() * quantity;
            if (ObjectUtils.isNotEmpty(client.getcLIType()) && client.getcLIType().equalsIgnoreCase("Passager")) {
                ArticleListDialog.passagerBlocked = (amountToSet + amount > prefUtils.getMaxPassager()) && prefUtils.getMaxPassager() != 0;
            } else {
                ArticleListDialog.passagerBlocked = false;
            }
        });
    }

    /**
     * load the spinner with clients list
     */
  /*befor add bc to bl
    void setSearchableSpinner(List<Client> clients) {
        simpleListAdapter = new ClientSpinnerAdapter(this, (ArrayList<Client>) clients);
        searchableSpinner.setAdapter(simpleListAdapter);
        if (isUpdate) {
            searchableSpinner.setSelectedItem(indexOfClient((ArrayList<Client>) clients, client) + 1);
            TransitionManager.beginDelayedTransition(content, new Slide(Gravity.BOTTOM));
            dataTableView.setVisibility(View.VISIBLE);
            ArticleHashMap articleHashMap = new ArticleHashMap();
            List<LigneTicket> ligneTickets = App.database.ligneTicketDAO().getByTicket(ticket.tIKNumTicket, ticket.gettIKIdCarnet(), ticket.gettIKExerc());
            for (LigneTicket lt : ligneTickets) {
                Article article = App.database.articleDAO().getOneByCode(lt.lTCodArt);
                articleHashMap.put(article, lt);
            }
           articleListDialog.getList().putAll(articleHashMap);
            setTicketDataTable(true,"1");
            setFooter();
        }
        searchableSpinner.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(View view, int position, long id) {
                Client client1 = simpleListAdapter.getItem(position);
                if (ObjectUtils.isNotEmpty(client1)) {
                    if (ObjectUtils.isNotEmpty(client1.getcLIType()) && client1.getcLIType().equalsIgnoreCase("Passager")) {
                        if ((amount > prefUtils.getMaxPassager()) && prefUtils.getMaxPassager() != 0) {
                            if (client != null) searchableSpinner.setSelectedItem(client);
                            else searchableSpinner.setSelectedItem(0);
                            Toast.makeText(context, "Impossible d'effectuer l'opération", Toast.LENGTH_LONG).show();
                        } else {
                            TransitionManager.beginDelayedTransition(content, new Slide(Gravity.BOTTOM));
                            dataTableView.setVisibility(position > 0 ? View.VISIBLE : View.GONE);
                            client = simpleListAdapter.getItem(position);
                        }
                    } else {
                        TransitionManager.beginDelayedTransition(content, new Slide(Gravity.BOTTOM));
                        dataTableView.setVisibility(position > 0 ? View.VISIBLE : View.GONE);
                        client = simpleListAdapter.getItem(position);
                    }
                }


                if(client!=null){
                    if(client.getCliisCredit()==0.0) creBut.setVisibility(View.GONE);
                    else creBut.setVisibility(View.VISIBLE);
                }
            }

            @Override
            public void onNothingSelected() {
            }
        });
    }
*/
    void setSearchableSpinner(List<Client> clients) {
        simpleListAdapter = new ClientSpinnerAdapter(this, (ArrayList<Client>) clients);
        searchableSpinner.setAdapter(simpleListAdapter);
        if (isUpdate) {
            searchableSpinner.setSelectedItem(indexOfClient((ArrayList<Client>) clients, client) + 1);
            TransitionManager.beginDelayedTransition(content, new Slide(Gravity.BOTTOM));
        //    ArticleHashMap articleHashMap = new ArticleHashMap();
        }


        searchableSpinner.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(View view, int position, long id) {
             if(position!=0){
                 Client client1 = simpleListAdapter.getItem(position);
                 if (ObjectUtils.isNotEmpty(client1)) {
                     if (ObjectUtils.isNotEmpty(client1.getcLIType()) && client1.getcLIType().equalsIgnoreCase("Passager")) {
                         if ((amount > prefUtils.getMaxPassager()) && prefUtils.getMaxPassager() != 0) {
                             if (client != null) searchableSpinner.setSelectedItem(client);
                             else searchableSpinner.setSelectedItem(0);
                             Toast.makeText(context, "Impossible d'effectuer l'opération", Toast.LENGTH_LONG).show();
                         } else {
                             TransitionManager.beginDelayedTransition(content, new Slide(Gravity.BOTTOM));
                             dataTableView.setVisibility(position > 0 ? View.VISIBLE : View.GONE);
                             client = simpleListAdapter.getItem(position);
                         }
                     } else {
                         TransitionManager.beginDelayedTransition(content, new Slide(Gravity.BOTTOM));
                         dataTableView.setVisibility(position > 0 ? View.VISIBLE : View.GONE);
                         client = simpleListAdapter.getItem(position);
                     }
                 }
                 if(client!=null){
                     if(client.getCliisCredit()==0.0) creBut.setVisibility(View.GONE);
                     else creBut.setVisibility(View.VISIBLE);
                 }

                 scanArt.setVisibility(View.VISIBLE);
                 autoScanButton.setVisibility(View.VISIBLE);
                 addItemButton.setVisibility(View.VISIBLE);
             }
            else {
                client = null;
                 scanArt.setVisibility(View.GONE);
                 autoScanButton.setVisibility(View.GONE);
                 addItemButton.setVisibility(View.GONE);
             }
            }

            @Override
            public void onNothingSelected() {
                client = null;
                scanArt.setVisibility(View.GONE);
                autoScanButton.setVisibility(View.GONE);
                addItemButton.setVisibility(View.GONE);
            }
        });
    }
    void showArticlesListDialog() {
        amountToSet = 0.0;
        articleListDialog.showConfirmDialog(this, 0, savedInstanceState, client);
        if (ObjectUtils.isNotEmpty(tableDataAdapter) && ObjectUtils.isNotEmpty(tableDataAdapter.getData())) {
            for (LigneTicket ligneTicket : tableDataAdapter.getData()) {
                updateListArticleQuantity(ligneTicket.getArticle(), ligneTicket.lTQte);
            }
        }
        articleListDialog.generateView();
    }

    private void updateListArticleQuantity(Article article, double qty) {
        for (Article article1 : articleListDialog.articles) {
            if (article.aRTCode.equals(article1.aRTCode)) {
                article1.setCount(qty);
            }
        }
        if (!articleListDialog.getList().isEmpty()) {
            for (Article article1 : articleListDialog.getList().keySet()) {
                if (article.aRTCode.equals(article1.aRTCode)) {
                    article1.setCount(qty);
                }
            }
        }
        if (!articleListDialog.getCurrenSelection().isEmpty()) {
            for (Article article1 : articleListDialog.getCurrenSelection().keySet()) {
                if (article.aRTCode.equals(article1.aRTCode)) {
                    article1.setCount(qty);
                }
            }
        }
    }

    void setTicketHeader() {
        final boolean[] setted = {false};
        dateInputField.setText(DateUtils.dateToStr(date, "EEEE, dd MMMM yyyy HH:mm"));


        // if there is an exist "tournee", display only "tournee" client
        ObservableCreate<List<Client>> observable = new ObservableCreate<>(emitter -> {
            List<Client> clients = (List<Client>) OrdreWithLinesViewMiodel.getInstance(this).getClientListOfCurrentTournee().get(0);
            emitter.onNext(clients);
            emitter.onComplete();
        });

        Observer<List<Client>> observer = new Observer<List<Client>>() {
            @Override
            public void onSubscribe(Disposable d) {
            }

            @Override
            public void onNext(List<Client> clients) {

                if (clients.size() > 0) {
                    setSearchableSpinner(clients);
                    setted[0] = true;
                }
            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onComplete() {
            }
        };

        observable.subscribe(observer);

        if (setted[0]) {
            if (!(boolean) OrdreWithLinesViewMiodel.getInstance(this).getClientListOfCurrentTournee().get(1)) {
                UIUtils.showDialogWithoutChoice(context, getString(R.string.error), getString(R.string.client_tournee_confirm_error), (dialog, which) -> {
                    if(!isUpdate) finish();
                    else goToBlFragment();
                });
            }
        }


        // if there is no "tournee", display all clients
        if (!setted[0])
            ClientViewModel.getInstance(context).getByStation(prefUtils.getFiltreCltAuthorization(), prefUtils.getUserStationId()).observe(context, clientList -> {
                if (clientList != null)
                    setSearchableSpinner(clientList);
            });


    }

    void setTicketFooter() {
        discountInputField.setOnFocusChangeListener((v, hasFocus) -> {
            if (hasFocus) {
                discountInputField.addTextChangedListener(new TextWatcher() {
                    @Override
                    public void onTextChanged(CharSequence cs, int arg1, int arg2, int arg3) {
                        // not event here
                    }

                    @Override
                    public void beforeTextChanged(CharSequence s, int arg1, int arg2, int arg3) {
                        // not event here
                    }

                    @Override
                    public void afterTextChanged(Editable arg0) {
                        String discount = discountInputField.getText().toString();
                        if (discount.isEmpty()) discount = "0";
                        amountWithDiscount = articleListDialog.setList(articleListDialog.getList().setDiscount(discount)).getAmountWithDiscount();
                        if (amountWithDiscount < 0) amountWithDiscount = 0;
                        setTicketDataTable(true,"2");
                        setFooter();
                    }
                });
            } else {
                discountInputField.clearTextChangedListeners();
            }
        });

        amountwithDiscountInputField.setOnFocusChangeListener((v, hasFocus) -> {
            if (hasFocus) {
                amountwithDiscountInputField.addTextChangedListener(new TextWatcher() {
                    @Override
                    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

                    }

                    @Override
                    public void onTextChanged(CharSequence s, int start, int before, int count) {

                    }

                    @Override
                    public void afterTextChanged(Editable s) {
                        calculateDiscountRateFromTotalWithDiscountTTC();
                        setTicketDataTable(true,"3");
                        //setFooter();
                    }
                });
            } else {
                amountwithDiscountInputField.clearTextChangedListeners();
            }
        });
    }

    void calculateDiscountRateFromTotalWithDiscountTTC() {
        double discountRate = 0;
        if (Objects.requireNonNull(amountwithDiscountInputField.getText()).toString().length() != 0) {
            discountRate = Calculator.calculateDiscountRate(
                    StringUtils.parseDouble(amountwithDiscountInputField.getText().toString(), 0),
                    StringUtils.parseDouble(Objects.requireNonNull(amountInputField.getText()).toString(), 1));
            if (Double.isNaN(discountRate)) discountRate = 0;
            if (discountRate < 0) {
                discountRate = 0;
            }
        } else {
            discountRate = 0;
        }
        discountInputField.setText(String.valueOf(StringUtils.decimalFormat(discountRate)));
    }

    void setTableFromUpdate(){
        List<LigneBonCommande> ligneBonCommandes = App.database.ligneBonCommandeDAO().getByBCCode(ticket.tikNumTicketM);

        List<LigneTicket> ligneTickets = App.database.ligneTicketDAO().getByTicket(ticket.tIKNumTicket, ticket.gettIKIdCarnet(), ticket.gettIKExerc());

        Log.d("ddsdd", String.valueOf(ligneTickets.size()));
        tableTitle = findViewById(R.id.produitTxtVw);
        if(tableTitle!=null)
        tableTitle.setText(context.getString(R.string.product_title ,String.valueOf(ligneTickets.size()+1)));

        if(!ligneBonCommandes.isEmpty()){
            for (LigneBonCommande lb : ligneBonCommandes) {
                Article article = App.database.articleDAO().getOneByCode(lb.getLGDEVCodeArt());
                article.setPvttc(Double.parseDouble(lb.getLGDEVPUTTC()));
                articleListDialog.getList().put(article, Double.parseDouble(lb.getLGDEVQte()), Double.parseDouble(lb.getLGDEVRemise()), false);

                //  articleListDialog.getList().put(ticket,article,lb, Double.parseDouble(lb.getLGDEVQte()), Double.parseDouble(lb.getLGDEVRemise()), false);


                //  articleHashMap.put(article, lb);
            }
        }
        else if(!ligneTickets.isEmpty()){
            for (LigneTicket lt : ligneTickets) {
                Article article = App.database.articleDAO().getOneByCode(lt.getlTCodArt());
                article.setPvttc(Double.parseDouble(String.valueOf(lt.getlTPrixVente())));
                articleListDialog.getList().put(article, Double.parseDouble(String.valueOf(lt.getlTQte())), Double.parseDouble(String.valueOf(lt.getlTRemise())), false);

                //  articleListDialog.getList().put(ticket,article,lb, Double.parseDouble(lb.getLGDEVQte()), Double.parseDouble(lb.getLGDEVRemise()), false);


                //  articleHashMap.put(article, lb);
            }
        }
    }
    /**
     * load table adapter with tickets
     */
    void setTicketDataTable(boolean changeFromGlobal, String from) {

        Log.d("nhjdf",from);
        tableView.setSwipeToRefreshEnabled(false);
        changeTableElevationInNewApis();
        tableView.setHeaderBackground(R.color.material_teal500);
        setAmountsValues(changeFromGlobal);
        if (tableDataAdapter == null){
            if(isUpdate){
                articleListDialog.getList().clear();
                setTableFromUpdate();
             /*   List<LigneBonCommande> lgBc =App.database.ligneBonCommandeDAO().getByBCCode(ticket.getTikNumTicketM());


                List<LigneTicket> ligneTicketList = new ArrayList<>();
                for (int i = 0, ligneTicketsSize = lgBc.size(); i < ligneTicketsSize; i++) {
                    Article article = App.database.articleDAO().getOneByCode(lgBc.get(i).getLGDEVCodeArt());

                    LigneTicket ligneTicket = new LigneTicket();
                    ligneTicket.setlTCommande(false);
                    ligneTicket.setlTAnnuler(Globals.Boolean.FALSE.getValue());
                    ligneTicket.setlTQte(Double.parseDouble(lgBc.get(i).getLGDEVQte()));
                    ligneTicket.setlTQtePiece(article.getuNITEARTICLEQtePiece());
                    ligneTicket.setlTTauxRemise(discount);
                    ligneTicket.setlTTVA(article.getaRTTVA());
                    ligneTicket.setlTUnite(article.getuNITEARTICLECodeUnite());
                    //value.setlTPrixEncaisse(Calculator.calculateAmountTTCNet(key.getPvttc(), discount));
                    ligneTicket.setlTPrixEncaisse(Calculator.calculateAmountTTCNet(Double.parseDouble(lgBc.get(i).getLGDEVPUTTC()), discount));
                    // value.setlTPrixVente(key.getPvttc());
                    ligneTicket.setlTPrixVente(Double.parseDouble(lgBc.get(i).getLGDEVPUTTC()));
                    ligneTicket.setlTPACHAT(article.getaRTPrixUnitaireHT());
                    ligneTicket.setlTMtTTC(Calculator.calculateAmountTTC(
                            Double.parseDouble(lgBc.get(i).getLGDEVMntTTC()),
                            Double.parseDouble(lgBc.get(i).getLGDEVQte())));
                    ligneTicket.setlTMtHT(Calculator.calculateAmountHT(Double.parseDouble(lgBc.get(i).getLGDEVPUTTC()), article.getaRTTVA()));
                    ligneTicket.setlTRemise(Calculator.calculateAmountExcludingTax(Double.parseDouble(lgBc.get(i).getLGDEVMntTTC()), Double.parseDouble(lgBc.get(i).getLGDEVQte()), Double.parseDouble(lgBc.get(i).getLGDEVMntTva())) -
                            Calculator.calculateAmountExcludingTax(Double.parseDouble(lgBc.get(i).getLGDEVMntTTC()), Double.parseDouble(lgBc.get(i).getLGDEVQte()),Double.parseDouble(lgBc.get(i).getLGDEVMntTva())));


                    ligneTicket.setlTCodArt(lgBc.get(i).getLGDEVCodeArt());
                    ligneTicket.setArticle(article);
                    ligneTicket.setlTIdCarnet(ticket.tIKIdCarnet);
                    ligneTicket.setlTExerc(ticket.tIKExerc);
                    ligneTicketList.add(ligneTicket);
                }
*/
               // tableDataAdapter = new LigneTicketTableDataAdapter(context, ligneTicketList, tableView);

            }
            tableDataAdapter = new LigneTicketTableDataAdapter(context, articleListDialog.getList().getLigneTickets(), tableView);

        }
        else {
            tableDataAdapter.clear();
            tableDataAdapter.addAll(articleListDialog.getList().getLigneTickets());
        }
        tableDataAdapter.setNotifyOnChange(true);
        tableView.setDataAdapter(tableDataAdapter);
        tableTitle = findViewById(R.id.produitTxtVw);

        if(!isUpdate && tableTitle!= null)
            tableTitle.setText(context.getString(R.string.product_title ,String.valueOf(tableDataAdapter.getData().size())));

        tableView.addDataLongClickListener((rowIndex, clickedData) -> {
            // dialogShown = false;
            if (!dialogShown) {
                dialogShown = true;
                Dialog materialDialog = new MaterialDialog.Builder(context)
                        .title(R.string.confirmation)
                        .content(R.string.delete_confirmation_msg)
                        .positiveText(R.string.yes)
                        .negativeText(R.string.no)
                        .onPositive((dialog, which) -> {
                                    try {
                                        if (tableDataAdapter.getData() != null) {
                                            tableDataAdapter.getData().get(rowIndex).getArticle().setCount(0);
                                            if (articleListDialog.getList() != null)
                                                articleListDialog.getList().remove(tableDataAdapter.getData().get(rowIndex).getArticle());
                                            if (articleListDialog.getCurrenSelection() != null)
                                                articleListDialog.getCurrenSelection().remove(tableDataAdapter.getData().get(rowIndex).getArticle());
                                            tableDataAdapter.getData().remove(rowIndex);
                                            tableDataAdapter.notifyDataSetChanged();
                                            amount = articleListDialog.setList(articleListDialog.getList().setDiscount(discountInputField.getText().toString())).getAmount();
                                            amountWithDiscount = articleListDialog.setList(articleListDialog.getList().setDiscount(discountInputField.getText().toString())).getAmountWithDiscount();
                                            setFooter();
                                            TransitionManager.beginDelayedTransition(content, new Slide(Gravity.BOTTOM));
                                        }
                                    } catch (NullPointerException e) {
                                    }
                                    dialogShown = false;
                                }

                        ).onNegative((dialog, which) -> dialogShown = false)
                        .show();
                materialDialog.setCanceledOnTouchOutside(false);
            }
            return false;
        });

        TransitionManager.beginDelayedTransition(content, new Slide(Gravity.BOTTOM));
        tableView.addDataClickListener((rowIndex, clickedData) -> {
            if(!isUpdate){
                if (!dialogShown) {
                    dialogShown = true;
                    openModifyArticleDialog(clickedData, false);
                }
            }

        });




    }

    private void setAmountsValues(boolean changeFromGlobal) {
        if (changeFromGlobal) {
            amount = articleListDialog.setList(articleListDialog.getList().setDiscount(discountInputField.getText().toString())).getAmount();
            amountWithDiscount = articleListDialog.setList(articleListDialog.getList().setDiscount(discountInputField.getText().toString())).getAmountWithDiscount();
        } else {
            amount = articleListDialog.getList().getAmount();
            amountWithDiscount = articleListDialog.getList().getAmountWithDiscount();
        }
    }

    private void changeTableElevationInNewApis() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            tableView.setElevation(10);
        }
    }

    /**
     * add new client button
     */
    @OnClick(R.id.addClientButton)
    void addClient() {
        addClientButton.setEnabled(false);
        addClientButton.setClickable(false);
        intent = new Intent(context, AddClientActivity.class);
        startActivityForResult(intent, REQUEST_CLIENT_CODE);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        // Check which request we're responding to
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case REQUEST_ENABLE_BT:
                showResultMessage(resultCode);
                break;
            case REQUEST_CONNECT_DEVICE:
                connectToBluetoothDevice(resultCode, data);
                break;
            case Activity.RESULT_CANCELED:
                if(!isUpdate) finish();
                else goToBlFragment();
                break;
        }
        if (requestCode == REQUEST_CLIENT_CODE) {
            if (resultCode == RESULT_OK) {
                final Client client1 = (Client) data.getSerializableExtra(CLIENT_INTENT_ID_KEY);
                Toasty.info(context, client1.cLICode).show();
                if (client1 != null) {
                    simpleListAdapter.addItem(client1);
                    searchableSpinner.setSelectedItem(simpleListAdapter.getCount() - 1);
                }
            }
            addClientButton.setEnabled(true);
            addClientButton.setClickable(true);
        }

        if (requestCode == REQUEST_PAYEMENT_CODE) {
            if (resultCode == RESULT_OK) {
                TicketWithLinesAndPayments ticketWithLinesAndPayments = data.getParcelableExtra(TICKET_WITH_LINES_AND_PAYEMENT_KEY);
                Toast.makeText(context, "Enregistrement a été effectué avec succès", Toast.LENGTH_LONG).show();
                if (ticketWithLinesAndPayments != null) {
                    saveData(ticketWithLinesAndPayments);
                }
                setResult(Activity.RESULT_OK, new Intent());
                if(!isUpdate) finish();
                else goToBlFragment();
            }
        }
    }

    private void showResultMessage(int resultCode) {
        if (resultCode == Activity.RESULT_OK) {
            Toast.makeText(context, "Bluetooth open successful", Toast.LENGTH_LONG).show();
        } else {
            Toast.makeText(context, "Bluetooth failed to connect", Toast.LENGTH_LONG).show();

        }
    }

    private void connectToBluetoothDevice(int resultCode, Intent data) {
        if (resultCode == Activity.RESULT_OK) {
            String address = data.getExtras()
                    .getString(DeviceListActivity.EXTRA_DEVICE_ADDRESS);
            try {
                con_dev = mService.getDevByMac(address);

                mService.connect(con_dev);
            } catch (Exception e) {
                Log.d("erro", e.getMessage());
            }
        }
    }

    void saveData(TicketWithLinesAndPayments ticketWithLinesAndPayments) {
        App.database.ticketDAO().deleteByCode(ticketWithLinesAndPayments.getTicket().tIKNumTicket,
                ticketWithLinesAndPayments.getTicket().tIKExerc
        );
        App.database.ticketDAO().insert(ticketWithLinesAndPayments.getTicket());
        App.database.ligneTicketDAO().insertAll(ticketWithLinesAndPayments.getLigneTicket());
        App.database.reglementCaisseDAO().insert(ticketWithLinesAndPayments.getReglement());
        App.database.traiteCaisseDAO().insertAll(ticketWithLinesAndPayments.getTraites());


        App.database.chequeCaisseDAO().insertAll(ticketWithLinesAndPayments.getCheques());
    }


    @OnClick(R.id.addItemButton)
    void addItems() {

        if(client!=null)
        showArticlesListDialog();
        else Toasty.info(context, "Choisir Un Client").show();
    }

    @OnClick(R.id.autoscan)
    void autoScan() {
        switchAutoScanColor(autoScanButton, context);
    }

    @OnClick(R.id.scan)
    void scanArt() {
        scanArt.setEnabled(false);
        showScanner();
    }

    @Override
    protected int setContentView() {
        return R.layout.activity_ticket;
    }

/*
    public void onClickPositiveButton(DialogInterface pDialog) {
       // articleListDialog.showConfirmDialog(this, 0, savedInstanceState, client);

        index_prod_intable = 0;
        boolean prodexist = false;
        Article articl = null;

        if (tableDataAdapter == null) {
            tableDataAdapter = new LigneTicketTableDataAdapter(context, articleListDialog.getList().getLigneTickets(), tableView);
        }
        else {
            tableDataAdapter.clear();
            tableDataAdapter.addAll(articleListDialog.getList().getLigneTickets());
        }

        List<LigneTicket> ligneTickets = tableDataAdapter.getData();


        for (Article article : articleListDialog.getCurrenSelection().keySet()) {
            if (isExistArticle(article, ligneTickets)) {
                articl = article;
                prodexist = true;
                break;
            }
            //  articleListDialog.getList().remove(article);
        }

        if (!prodexist) {
            double discount1;

            if (StringUtils.parseDouble(discountInputField.getText().toString(), 0) > 0) {
                articleListDialog.getCurrentSelection().setDiscount(String.valueOf(StringUtils.parseDouble(discountInputField.getText().toString(), 0)));
            }


            articleListDialog.getList().putAll(articleListDialog.getCurrentSelection());
            articleListDialog.getCurrentSelection().clear();
            articleListDialog.generateView();
            selectedArticles = articleListDialog.getList().getLigneTickets();
            setTicketDataTable(false,"4");
            setFooter();

        }
        else addArticleToTable("prodexist", Objects.requireNonNull(articleListDialog.getCurrenSelection().get(articl)), ligneTickets.get(index_prod_intable).lTQte);
        pDialog.dismiss();
    }
*/
public void onClickPositiveButton(DialogInterface pDialog) {
    index_prod_intable = 0;
    boolean prodexist = false;
    Article articl = null;

    if (tableDataAdapter == null) {
        tableDataAdapter = new LigneTicketTableDataAdapter(context, articleListDialog.getList().getLigneTickets(), tableView);
    } else {
        tableDataAdapter.clear();
        tableDataAdapter.addAll(articleListDialog.getList().getLigneTickets());
    }

    List<LigneTicket> ligneTickets = tableDataAdapter.getData();

    for (Article article : articleListDialog.getCurrenSelection().keySet()) {
        if (isExistArticle(article, ligneTickets)) {
            articl = article;
            prodexist = true;
            break;
        }
    }

    if (!prodexist) {
        double discount1;

        if (StringUtils.parseDouble(discountInputField.getText().toString(), 0) > 0) {
            articleListDialog.getCurrentSelection().setDiscount(String.valueOf(
                    StringUtils.parseDouble(discountInputField.getText().toString(), 0)));
        }

        articleListDialog.getList().putAll(articleListDialog.getCurrentSelection());
        articleListDialog.getCurrentSelection().clear();
        articleListDialog.generateView();
        selectedArticles = articleListDialog.getList().getLigneTickets();
        setTicketDataTable(false, "4");
        setFooter();

    } else {
        try {
            addArticleToTable("prodexist",
                    Objects.requireNonNull(articleListDialog.getCurrenSelection().get(articl)),
                    ligneTickets.get(index_prod_intable).lTQte);
        } catch (IndexOutOfBoundsException e) {
            Log.e("onClickPositiveButton", "Index out of bounds: " + e.getMessage());
            showErrorDialog("Unexpected error occurred. Please try again.");
        }
    }

    pDialog.dismiss();
}

    private void showErrorDialog(String message) {
        new AlertDialog.Builder(context)
                .setTitle("Error")
                .setMessage(message)
                .setPositiveButton("OK", null)
                .show();
    }

    void setFooter() {

        if (amount > 0) {
            linearLayoutCompat.setVisibility(View.VISIBLE);
            footerTicketView.setVisibility(View.VISIBLE);
        } else {
            linearLayoutCompat.setVisibility(View.GONE);
            footerTicketView.setVisibility(View.GONE);
        }
        if (!discountInputField.getText().toString().isEmpty()) {
            if (Double.parseDouble(discountInputField.getText().toString()) > 100) {
                discountInputField.setText("100");
            }
        }
        calculateTotalAndRemisePrices(ticket, articleListDialog.getList().getLigneTickets());
//        setText(amountInputField, StringUtils.priceFormat(ticket.gettIKMtTTC()));
//        setText(amountwithDiscountInputField, StringUtils.priceFormat(ticket.gettIKMtRemise()));
        discountLayout.setVisibility(UIUtils.setDiscountVisibility());
        dividerView1.setVisibility(UIUtils.setDiscountVisibility());
        dividerView2.setVisibility(UIUtils.setDiscountVisibility());
        amountwithDiscountLayout.setVisibility(UIUtils.setDiscountVisibility());

        //if(ticket != null) ticket.tIKMtTTC = amount;
    }

    @Override
    public void onClickNegativeButton(DialogInterface pDialog) {
        articleListDialog.getCurrentSelection().clear();
        pDialog.dismiss();
        pDialog.dismiss();
    }

    @SuppressLint("ResourceType")
    @Override
    public void onBackPressed() {
        new MaterialDialog.Builder(this).onPositive((dialog, which) -> {
                   // if(!isUpdate)
                        finish();
                 //   else goToBlFragment();
                }).onNegative((dialog, which) -> {
                }).title(getString(R.string.confirmation))
                .content("Êtes-vous sûr de vouloir quitter")
                .positiveText(R.string.yes)
                .negativeText(R.string.no)
                .show();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem menuItem) {
        if (menuItem.getItemId() == android.R.id.home) {
            onBackPressed();
        }
        return super.onOptionsItemSelected(menuItem);
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.add_ticket_menu, menu);
        save = menu.findItem(R.id.save);
        save.setVisible(false);
        scan = menu.findItem(R.id.scan);
        scan.setVisible(Utils.hasCamera(context));
        scan.setVisible(false);
        scan.setIcon(new IconicsDrawable(context).icon(FontAwesome.Icon.faw_barcode)
                .color(Color.WHITE).sizeDp(20));
        scan.setOnMenuItemClickListener(item -> {

            scan.setEnabled(false);
            showScanner();
            return false;
        });
        //save.setIcon(new IconicsDrawable(context).icon(GoogleMaterial.Icon.gmd_send).color(ResourcesCompat.getColor(getResources(), R.color.material_white, null)).sizeDp(24));
        save.setOnMenuItemClickListener(item -> {
            if (articleListDialog.getList().getLigneTickets().isEmpty()) {
                ticket = null;
                save.setEnabled(true);
                Toasty.info(context, R.string.no_article_selected).show();
                return false;
            }
            if (amount > 0 && (tableView.getVisibility() == View.VISIBLE) && (searchableSpinner.getSelectedPosition() > 0)) {
                new MaterialDialog.Builder(this)
                        .title(R.string.go_to_title)
                        .items(R.array.ticket_newt_step_label).negativeText(R.string.cancel)
                        .itemsCallback((dialog, view, which, text) -> sendData(which, false)).cancelable(false)
                        .onNegative((dialog, which) -> save.setEnabled(true))
                        .show();
                // save.setEnabled(false);
            } else {
                UIUtils.showDialog(context, R.string.fields_error);
                save.setEnabled(true);
            }
            return false;
        });
        return super.onCreateOptionsMenu(menu);
    }

    private void goToReglements(int reglement, boolean regPartielle) {
        boolean authorized = true;
        for (LigneTicket ligneTicket : articleListDialog.getList().getLigneTickets()) {
            if (!ligneTicket.authorizedDiscount) {
                authorized = false;
                break;
            }
        }
        if (authorized) {
            if (articleListDialog.getList().getLigneTickets().isEmpty()) {
                ticket = null;
                Toasty.info(context, R.string.no_article_selected).show();
            } else if (amount > 0 && (tableView.getVisibility() == View.VISIBLE)
                    && (searchableSpinner.getSelectedPosition() > 0)) {
                sendData(reglement, regPartielle);
            } else {
                UIUtils.showDialog(context, R.string.fields_error);
            }
        } else {
            UIUtils.showDialog(context, R.string.discount_auth);
        }
    }

    private void showScanner() {
//        new BarcodeScannerDialog(context, new BarcodeScannerListener() {
//            @Override
//            public void onDismiss(DialogInterface pDialog) {
//                if (scanArt != null) {
//                    scanArt.setEnabled(true);
//                }
//            }
//
//            @Override
//            public void onDecoded(DialogInterface pDialog, Result result) {
//                showArticleFromScan(result.getText());
//                pDialog.dismiss();
//            }
//        }).show();

        BarCodeDialogueFragment dialog = new BarCodeDialogueFragment(new BarcodeScannerListener() {


            @Override
            public void onDecoded(String result) {

                showArticleFromScan(result);
            }
        });

        dialog.show(getSupportFragmentManager(), "MyDialogFragment");

    }

    public void showArticleFromScan(String code) {
        code = code.replaceAll("\\s+", "");
        ArticleCodeBar selectedArticleCodeBar = App.database.articleCodeBarDAO().getFils("%" + code + "%");

        if (selectedArticleCodeBar != null && !isDestroyed()) {
            Article selectedArticle = App.database.articleDAO().getByCodeBarAndSation(selectedArticleCodeBar.getParentCodeBar(), prefUtils.getUserStationId());
            if (selectedArticle != null){
                if(prefUtils.getIsChahiaAuthorization()){
                    if(!prefUtils.getArtZeroStockAuthorization() && selectedArticleCodeBar.cod_Qte<=0){
                        Toasty.error(TicketActivity.this, "Pas d'autorisation pour ajouter Article avec un Stock Zero").show();
                    } else{
                        if (!isDestroyed()) showArticDialog(selectedArticle, selectedArticleCodeBar);
                     //   else Toasty.error(TicketActivity.this, "Article introuvable").show();
                    }
                }else {
                    if(!prefUtils.getArtZeroStockAuthorization() && selectedArticle.getsARTQte()<=0){
                        Toasty.error(TicketActivity.this, "Pas d'autorisation pour ajouter Article avec un Stock Zero").show();
                    } else{
                        if (!isDestroyed()) showArticDialog(selectedArticle, selectedArticleCodeBar);
                      //  else Toasty.error(TicketActivity.this, "Article introuvable").show();
                    }
                }

            }else
                Toasty.error(TicketActivity.this, "Article introuvable").show();

            /**
             * prefUtils.getIsChahiaAuthorization() to know if we use selectedArticleCodeBar.cod_Qte or not
             * prefUtils.getIsChahiaAuthorization() is true for chahia client
             */

        } else {
            Toasty.error(TicketActivity.this, "Article introuvable").show();
        }
    }


    private boolean isExistArticle(Article selectedArticle, List<LigneTicket> ligneTickets) {
        boolean exist = false;
        for (LigneTicket ligneTicket : ligneTickets) {
            if (ligneTicket.getArticle().getaRTCodeBar().equals(selectedArticle.getaRTCodeBar())) {
                exist = true;
                break;
            }
            index_prod_intable++;
        }
        return exist;
    }

    private void showArticDialog(Article selectedArticle,ArticleCodeBar selectedArticleCodeBar){
        //ArticleCodeBar articleCodeBar = App.database.articleCodeBarDAO().getParent(selectedArticle.getaRTCodeBar());
        LigneTicket ligneTicket = new LigneTicket();
        ligneTicket.setSync(false);
        ligneTicket.setStatus(Globals.ITEM_STATUS.INSERTED.getStatus());
        ligneTicket.setArticle(selectedArticle);
        ligneTicket.codeBarFils = selectedArticleCodeBar.filsCodeBar;
        ligneTicket.lTTauxRemise = Double.parseDouble(selectedArticle.getTauxSolde());

        if (ObjectUtils.isNotEmpty(selectedArticleCodeBar)) {
            ligneTicket.setlTQte(selectedArticleCodeBar.getCod_Qte());
        }
        dataTableView.setVisibility(View.VISIBLE);
            /*if (tableDataAdapter != null) {
                List<LigneTicket> ligneTickets = tableDataAdapter.getData();
                if (ligneTickets != null) {
                    if (!isExistArticle(selectedArticle, ligneTickets))
                        openModifyArticleDialog(ligneTicket, true);
                } else
                    openModifyArticleDialog(ligneTicket, true);
            } else

             */


        openModifyArticleDialog(ligneTicket, true);
    }
    private void validateData(int which, boolean regPartielle, String note) {
        /**
         * why update client location !!!
         */
       // updateClient();
        TicketWithLines ticketWithLines;
        Intent intent = new Intent(getBaseContext(), Ticket2PaymentActivity.class);
        switch (which) {
            case 0:
                createTicket(note, regPartielle);

                ticketWithLines = new TicketWithLines(ticket, articleListDialog.getList().getLigneTickets(ticket));


                ticket.settIKEtat(Globals.TICKET_STATE.PAYED.getValue());

               /* if (ticket != null) {
                    setTimbre();
                }*/

                intent.putExtra(TICKET_WITH_LINES_KEY, ticketWithLines);
                intent.putExtra(REGLEMENT_PARTIEL_KEY, regPartielle);

                startActivityForResult(intent, REQUEST_PAYEMENT_CODE);
                break;


            case 1:



                    if(isUpdate){
                        createTicket(note, regPartielle);
                        ticketWithLines = new TicketWithLines(ticket, articleListDialog.getList().getLigneTickets(ticket));

                    } else {

                        if (ticket == null) {
                            createTicket(note, regPartielle);
                            // setTimbreCredit(ticket);
                            ticketWithLines = new TicketWithLines(ticket, articleListDialog.getList().getLigneTickets(ticket));
                            //   setTimbre();

                        } else {
                            ticketWithLines = new TicketWithLines(ticket, articleListDialog.getList().getLigneTickets(ticket));


                            setTimbre();


                        }
                    }

                    //  Log.d("vbnvb",client.getcLICode());

                    //  ticket.settIKEtat(Globals.TICKET_STATE.CREDIT.getValue());**
                if(!regPartielle){
                    Utils.insertTicket(
                            false,
                            ticket ,
                            ticketWithLines,
                            client,
                            0.0,
                    0.0,
                    0.0
                    );
                    if(!isUpdate) finish();
                    else goToBlFragment();
                }
                else {
                    intent.putExtra(TICKET_WITH_LINES_KEY, ticketWithLines);
                    intent.putExtra(REGLEMENT_PARTIEL_KEY, true);
                    intent.putExtra(TICKET_REG_PARTIEL_KEY, ticket);
                    intent.putExtra(CLIENT_REG_PARTIEL_KEY, client);
                    startActivityForResult(intent, REQUEST_PAYEMENT_CODE);
                }

          /*      try {

                    new MaterialDialog.Builder(context)
                            .title("Imprimer ?")
                            .content("Voulez-vous imprimer le B.L.?")
                            .positiveText("Oui")
                            .negativeText("Non")
                            .onNegative((dialog, which1) -> {


                                setResult(Activity.RESULT_OK, new Intent().putExtra(TICKET_WITH_LINES_KEY, ticketWithLines));
                                dialog.dismiss();

                                if(!isUpdate) finish();
                                else goToBlFragment();
                            })
                            .onPositive((dialog, which2) -> {

                                BluetoothAdapter mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter();

                                if (mBluetoothAdapter == null) {
                                    // Device does not support Bluetooth
                                    Toasty.error(getApplication(), "No Bluetooth available in this Device").show();
                                    //   insertTicket(ticket, ticketWithLines);
                                }
                                else if (!mBluetoothAdapter.isEnabled()) {
                                    // Bluetooth is not enabled :)
                                    Toasty.error(getApplication(), "Activer le Bluetooth").show();

                                    Intent enableIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
                                    if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                                        // TODO: Consider calling
                                        //    ActivityCompat#requestPermissions
                                        // here to request the missing permissions, and then overriding
                                        //   public void onRequestPermissionsResult(int requestCode, String[] permissions,
                                        //                                          int[] grantResults)
                                        // to handle the case where the user grants the permission. See the documentation
                                        // for ActivityCompat#requestPermissions for more details.
                                        return;
                                    }
                                    startActivityForResult(enableIntent, REQUEST_ENABLE_BT);

                                }
                                else {
                                    // Bluetooth is enabled

                                    Log.d("tghg", String.valueOf(ticket.gettIKMtTTC()));
                                        printTicket(ticketWithLines, true);

                                    // dialog.dismiss();

                                    //   if(!isUpdate) finish();
                                    //   else goToBlFragment();

                                }
                            })
                            .show();
                } catch (Exception e) {
                    e.printStackTrace();
                }*/



                break;
        }
    }




    void sendData(int which, boolean regPartielle) {
        if (ObjectUtils.isNotEmpty(client.getcLIType()) && client.getcLIType().equalsIgnoreCase("Passager")) {
            try {
                new MaterialDialog.Builder(context)
                        .title("Client passager")
                        .content("Vous devez entrer l'adresse:")
                        .positiveText("Oui")
                        .negativeText("Annuler")
                        .inputType(InputType.TYPE_TEXT_FLAG_MULTI_LINE)
                        .input("", "", (dialog, input) -> System.out.println(input.toString()))
                        .onNegative((dialog, which1) -> {

                        })
                        .onPositive((dialog, which2) -> {
                            validateData(which,regPartielle, dialog.getInputEditText().getText().toString());
                        })
                        .show();
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else validateData(which,regPartielle, "");
    }

    private void updateClient() {
        String datePA = client.getDatePA();
        if (client.getLongitude() == 0 || client.getLatitude() == 0) {
            client.setLongitude(entreLongitude);
            client.setLatitude(entreLatitude);
            client.cLIExoValable = "";
            client.isSync = false;
            client.status = Globals.ITEM_STATUS.UPDATED.getStatus();
            client.setDatePA(datePA);
         //   App.database.clientDAO().insert(client);
        }
    }

    private void createTicket(String note, boolean regPartielle) {
        String session = "";
        String tikNumTicketM = "";

        if (isUpdate) {
            session = ticket.gettIKIdSCaisse();
            tikNumTicketM = ticket.tikNumTicketM;
        } else session = prefUtils.getSessionCaisseId();
      //  Log.d("ncbdss","  "+);


        ticket = new Ticket(
                ticketNumber,
                App.prefUtils.getExercice(), ///att
                App.prefUtils.getCarnetId(),
                DateUtils.dateToStr(DateUtils.strToDate(dateInputField.getText().toString(), "EEEE, dd MMMM yyyy HH:mm:ss"), Globals.DATE_PATTERN),
                DateUtils.dateToStr(DateUtils.strToDate(dateInputField.getText().toString(), "EEEE, dd MMMM yyyy HH:mm:ss"), Globals.DATE_PATTERN),
                session,
                client.getcLICode(),
                Utils.round(StringUtils.decimalFormat(articleListDialog.getList().getAmountWithDiscount()),3),//montant ttc

              //  Utils.round(StringUtils.decimalFormat(articleListDialog.getList().getAmount() - articleListDialog.getList().getAmountWithDiscount()),3),     //montant remise//////
               Utils.round(StringUtils.decimalFormat(sumOfList(articleListDialog.getList().getLigneTickets())),3),     //montant remise//////

               // articleListDialog.getList().getLigneTickets().get(9).lTTauxRemise,


                Utils.round(StringUtils.decimalFormat(StringUtils.decimalFormat(articleListDialog.getList().getAmountHT())),3),// -- ht
                Utils.round(StringUtils.decimalFormat(articleListDialog.getList().getVATAmount()),3),// -- tva
                Utils.round(StringUtils.decimalFormat(received),3),// mnt recue
                StringUtils.decimalFormat(made),// mnt rendu
                client.getcLINomPren(),
                App.prefUtils.getUserId(),
                Globals.Boolean.FALSE.getValue(),
                Globals.Boolean.FALSE.getValue(),
                null,
                discount,  //ligne ticket remise
                Globals.Boolean.FALSE.getValue(),
                false,
                Globals.ITEM_STATUS.INSERTED.getStatus(),
                prefUtils.getUserStationId(),
                Globals.SOURCE,
                String.valueOf(articleListDialog.getList().size())
        );


        //   Log.d("plmkfvcd",String.valueOf(articleListDialog.getList().size()));

        if (ObjectUtils.isNotEmpty(client.getcLIType()) && client.getcLIType().equalsIgnoreCase("Passager")) {
            ticket.settIKEmplacementMariage(note);
        }
        if (!isUpdate) {
            ticket.tIKDDm = "empty";
            ticket.tikNumTicketM = Utils.generateMobileTicketCode(date, ticket.gettIKStation(), String.valueOf(ticketNumber));
        } else {
            ticket.tikNumTicketM = tikNumTicketM;
            ticket.tIKDDm = ddm;
        }
        //ticket.settIKDDm("empty");
        ticket.setTIKLatitudeEv(entreLatitude);
        ticket.setTIKLongitudeEv(entreLongitude);
        ticket.setTIKLatitudeSv(entreLatitude);
        ticket.setTIKLongitudeSv(entreLongitude);

        setMntRevImp(ticket, client);
            setTimbre();
           }



    void setMntRevImp(Ticket ticket, Client client) {
        if(ticket.timbre != 0.0)  ticket.settIKMtTTC(ticket.tIKMtTTC - ticket.timbre);
        if(client != null && client.cltMntRevImp!= null) {
            if(client.cltMntRevImp>0) {



                ticket.setMntRevImp(ticket.tIKMtTTC * (client.cltMntRevImp/ 100));
                ticket.settIKMtTTC(ticket.tIKMtTTC * (1 +(client.cltMntRevImp / 100)));
            }
            else  ticket.setMntRevImp(0.0);
        }


        if(ticket.timbre != 0.0)  ticket.settIKMtTTC(ticket.tIKMtTTC + ticket.timbre);
    }

   private void setTimbre() {
      // MontantTTC =ttc* (1 +(RevenuImposable / 100))



       if (PrefUtils.getInstance(context).getAutoBLTimbreEnabled() && client.getcLITimbre() == 1) {

                Timbre timbre = App.database.timbreDAO().getActif();
                if (timbre != null) {
                    ticket.setTimbre(Double.parseDouble(timbre.tIMBCode));
                    //ticket.setTimbre(Double.parseDouble(timbre.tIMBValue));
                  ticket.settIKMtTTC(Utils.round(articleListDialog.getList().getAmountWithDiscount() + Double.parseDouble(timbre.tIMBValue), 3));
                }
        }
        else  ticket.setTimbre(0.0);

    }



    void setTimbreCredit(Ticket ticket) {
        Timbre timbre = App.database.timbreDAO().getActif();
        if (ticket != null && timbre != null) {
            Client client = App.database.clientDAO().getOneByCode(ticket.tIKCodClt);
            if (client != null) {
                if (client.getcLITimbre() == 0) ticket.setTimbre(0.0);
                else if (!PrefUtils.getInstance(context).getAutoBLTimbreEnabled() && ticket.timbre == 0.0){
                    ticket.setTimbre(Double.parseDouble(timbre.tIMBCode));

                    //  addTimbretoTicketPayment(ticket,timbre);
                    ticket.settIKMtTTC(Utils.round(ticket.gettIKMtTTC() + Double.parseDouble(timbre.tIMBValue), 3));
                    App.database.ticketDAO().insert(ticket);
                }
            }
        }

    }


    private void printTicket(TicketWithLines ticketWithLines, boolean credit) {
        TicketWithLinesAndPayments ticketWithLinesAndPayments = new TicketWithLinesAndPayments();
       // ticketWithLinesAndPayments.setTicket(ticketWithLines.getTicket());
        ticketWithLinesAndPayments.setTicket(ticket);
        ticketWithLinesAndPayments.setLigneTicket(ticketWithLines.getLigneTicket());

        putArticleInLignes(ticketWithLinesAndPayments.getTicket(), ticketWithLinesAndPayments.getLigneTicket());
          calculateTotalAndRemisePrices(ticketWithLinesAndPayments.getTicket(), ticketWithLinesAndPayments.getLigneTicket());

        Log.d("tghg","ee " + String.valueOf(ticket.gettIKMtTTC()));
        mService = new BluetoothService(context, new Handler() {
            @Override
            public void handleMessage(Message msg2) {
                switch (msg2.what) {
                    case BluetoothService.MESSAGE_STATE_CHANGE:
                        switch (msg2.arg1) {
                            case BluetoothService.STATE_CONNECTED:
                                Toast.makeText(context, "Connect successful", Toast.LENGTH_SHORT).show();
                                if (ticketWithLinesAndPayments != null)
                                    try {

                                        new PrinterHelper(mService, DEFAULT_ENCODING).printTicket(context, ticketWithLinesAndPayments);
                                        if(!isUpdate) finish();
                                        else goToBlFragment();
                                    } catch (IOException e) {
                                        e.printStackTrace();
                                    }
                                break;
                            case BluetoothService.STATE_CONNECTING:
                                break;
                            case BluetoothService.STATE_LISTEN:

                            case BluetoothService.STATE_NONE:
                                Toasty.info(getApplication(), "Unable to connect device").show();

                                break;
                        }
                        break;
                    case BluetoothService.MESSAGE_CONNECTION_LOST:
                        Toasty.error(getApplication(), "Device connection was lost").show();
                        break;
                    case BluetoothService.MESSAGE_UNABLE_CONNECT:
                    //    Toast.makeText(context, "Unable to connect device", Toast.LENGTH_SHORT).show();
                        break;
                    case Activity.RESULT_CANCELED:
                        Toasty.info(getApplication(), "RESULT_CANCELED Bluetooth").show();

                        if(!isUpdate) finish();
                        else goToBlFragment();
                        break;
                }
            }

        });
        if (!mService.isBTopen()) {
            Intent enableIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                // TODO: Consider calling
                //    ActivityCompat#requestPermissions
                // here to request the missing permissions, and then overriding
                //   public void onRequestPermissionsResult(int requestCode, String[] permissions,
                //                                          int[] grantResults)
                // to handle the case where the user grants the permission. See the documentation
                // for ActivityCompat#requestPermissions for more details.
                return;
            }
            startActivityForResult(enableIntent, REQUEST_ENABLE_BT);
        }
        if (!mService.isAvailable()) {
            Toast.makeText(context, "Bluetooth is not available", Toast.LENGTH_LONG).show();
        } else {
            Intent serverIntent = new Intent(context, DeviceListActivity.class);
            startActivityForResult(serverIntent, REQUEST_CONNECT_DEVICE);
        }
    }

    private void calculateTotalAndRemisePrices(Ticket ticket, List<LigneTicket> ligneTickets) {
        double mntRemise = 0.0;
        double total = 0.0;


       /* if(isUpdate){
            List<LigneBonCommande> lgBc =App.database.ligneBonCommandeDAO().getByBCCode(ticket.getTikNumTicketM());

            for (int i = 0, ligneTicketsSize = lgBc.size(); i < ligneTicketsSize; i++) {

                try {
                    mntRemise += Utils.round(Double.parseDouble(lgBc.get(i).getLGDEVQte())*Double.parseDouble( lgBc.get(i).getLGDEVPUTTC()) * Double.parseDouble(lgBc.get(i).getLGDEVRemise()) / 100,3);
                    total += Utils.round(Double.parseDouble(lgBc.get(i).getLGDEVQte()) * Double.parseDouble( lgBc.get(i).getLGDEVPUTTC()),3);

                } catch (Exception e) {
                    Log.d("error", e.getMessage());
                }

            }


        }
        else {*/
            for (LigneTicket ligneTicket : ligneTickets) {
                try {
                    //  mntRemise += Utils.round(ligneTicket.getlTQte() * ligneTicket.getlTMtTTC() * ligneTicket.getlTTauxRemise() / 100,3);
                     mntRemise += Utils.round(ligneTicket.getlTQte() * ligneTicket.getArticle().pvttc * ligneTicket.getlTTauxRemise() / 100,3);
                     total += Utils.round(ligneTicket.getlTQte() * ligneTicket.getArticle().pvttc,3);
                    //    total += Utils.round(ligneTicket.getlTQte() * ligneTicket.getlTMtTTC(),3);
                } catch (Exception e) {
                    Log.d("error", e.getMessage());
                }


                //   setText(amountInputField, String.valueOf(StringUtils.decimalFormat(total)));
                //  setText(amountwithDiscountInputField,  String.valueOf(StringUtils.decimalFormat(total-mntRemise)));
            }

      //  }

        setText(amountInputField, StringUtils.priceFormat(total));
        setText(amountwithDiscountInputField, StringUtils.priceFormat(total-mntRemise));
        if (ticket != null) {

            ticket.settIKMtRemise(mntRemise);
            ticket.settIKMtTTC(total);

          //  setTimbred(total);
        }
    }


    private void putArticleInLignes(Ticket ticket, List<LigneTicket> ligneTickets) {
        for (int i = 0; i < ligneTickets.size(); i++) {
            ligneTickets.get(i).setArticle(App.database.articleDAO().getOneByCodeAndStation(ligneTickets.get(i).getlTCodArt(), ticket.tIKStation));
        }
    }

    static void setText(EditText text, String value) {
        text.setText(value);
    }

    @Override
    protected void onResume() {
        startBarcode();



        super.onResume();
    }

    @Override
    protected void onPause() {
        barCodeReader.destroy();
        super.onPause();
        isShowing = false;
        isInventory = false;
    }

    @Override
    protected void onDestroy() {
    //    barCodeReader.destroy();
        unbinder.unbind();
        super.onDestroy();
    }




    /**
     * modify ticket from table option
     */
    private void openModifyArticleDialog(LigneTicket clickedData, Boolean scanned) {
        if (prefUtils.getIsAutoScan() && scanned) {
            addArticleToTable("scan", clickedData, -11.1);
        } else {
            ligneTicketDialog = new LigneTicketDialog(context, scanned, clickedData, 0, false,
                    (dialog, which) -> ligneTicketDialog.validator.validate(), (dialog, which) -> {
                dialog.dismiss();
                dialogShown = false;
            }, new Validator.ValidationListener() {
                @Override
                public void onValidationSucceeded() {
                    if (ligneTicketDialog.validate()) {
                        if (!ArticleListDialog.passagerBlocked) {
                            double quantity = StringUtils.parseDouble(Objects.requireNonNull(ligneTicketDialog.getQuantityInputField().getText()).toString(), 0);
                            double discount1 = StringUtils.parseDouble(Objects.requireNonNull(ligneTicketDialog.getDiscountInputField().getText()).toString(), 0);

                            double price = Double.parseDouble(ligneTicketDialog.getUnitPriceInputField().getText().toString());//StringUtils.parseDouble(ligneTicketDialog.getUnitPriceInputField().getText().toString(), 0);



                            if (tableDataAdapter != null) {
                                List<LigneTicket> ligneTickets = tableDataAdapter.getData();


                                if (ligneTickets != null) {
                                    for (int rowIndex = 0; rowIndex < ligneTickets.size(); rowIndex++) {
                                        if (ligneTickets.get(rowIndex).getArticle().getaRTCodeBar().equals(clickedData.article.getaRTCodeBar())) {

                                            if (scanned)
                                                quantity += ligneTickets.get(rowIndex).article.getCount(); // if from scan increment quantity else if from modify then replace with user input quantity

                                            articleListDialog.getList().remove(tableDataAdapter.getData().get(rowIndex).getArticle());
                                            articleListDialog.getCurrenSelection().remove(tableDataAdapter.getData().get(rowIndex).getArticle());
                                            tableDataAdapter.getData().remove(rowIndex);
                                            tableDataAdapter.notifyDataSetChanged();
                                        }
                                    }
                                }
                            }


                            clickedData.getArticle().setPvttc(price);
                            clickedData.getArticle().setCount(quantity);
                            articleListDialog.getList().put(clickedData.getArticle(), quantity, discount1, false);
                            setTicketDataTable(false,"5");
                            setFooter();
                            ligneTicketDialog.dismiss();
                            dialogShown = false;
                        }
                    }

                }

                @Override
                public void onValidationFailed(List<ValidationError> errors) {
                    for (ValidationError error : errors) {
                        View view = error.getView();
                        String message = error.getCollatedErrorMessage(context);

                        if (view instanceof EditText) {
                            ((MaterialEditText) view).setError(message);
                        } else {
                            Toast.makeText(context, message, Toast.LENGTH_LONG).show();
                        }
                    }
                }
            }, client);

            ligneTicketDialog.show(context.getFragmentManager(), StringUtils.upper);


        }
    }


    private void addArticleToTable(String from, LigneTicket clickedData, double quant) {
        double quantity = 0.0;

        if (tableDataAdapter != null) {
            List<LigneTicket> ligneTickets = tableDataAdapter.getData();
            if (ligneTickets != null) {
                for (int rowIndex = 0; rowIndex < ligneTickets.size(); rowIndex++) {
                    if (ligneTickets.get(rowIndex).getArticle().getaRTCodeBar().equals(clickedData.article.getaRTCodeBar())) {
                        if (from.equals("scan")) {
                            quantity = clickedData.getlTQte() + ligneTickets.get(rowIndex).article.getCount(); // if from scan increment quantity else if from modify then replace with user input quantity
                        } else if (from.equals("prodexist")) {
                            quantity = clickedData.getlTQte(); // quant+ clickedData.getlTQte(); TO INCREMENT QUANTITY
                        }

                        articleListDialog.getList().remove(tableDataAdapter.getData().get(rowIndex).getArticle());
                        articleListDialog.getCurrenSelection().remove(tableDataAdapter.getData().get(rowIndex).getArticle());
                        tableDataAdapter.getData().remove(rowIndex);
                        tableDataAdapter.notifyDataSetChanged();
                    }
                }
            }
        }


        double discount1;
        if (StringUtils.parseDouble(discountInputField.getText().toString(), 0) > 0) {
            discount1 = StringUtils.parseDouble(discountInputField.getText().toString(), 0);
        } else {
            if (Double.parseDouble(clickedData.getArticle().getTauxSolde()) != clickedData.getlTTauxRemise())
                discount1 = clickedData.getlTTauxRemise();
            else discount1 = Double.parseDouble(clickedData.getArticle().getTauxSolde());

        }


        clickedData.getArticle().setPvttc(clickedData.getArticle().getPvttc());
        if (quantity < 1) quantity = 1.0;
        clickedData.getArticle().setCount(quantity);
        articleListDialog.getList().put(clickedData.getArticle(), quantity, discount1, false);
        setTicketDataTable(false,"6");
        setFooter();

    }

    private void addPurchaseLineFromAutoScan(Article selectedArticle) {
        selectedArticle.setSync(false);
        selectedArticle.setStatus(Globals.ITEM_STATUS.INSERTED.getStatus());
        selectedArticle.setCount(StringUtils.parseDouble("1", 1));
        articleListDialog.getList().put(selectedArticle, Double.parseDouble("1"), 0, false);
        setTicketDataTable(false,"7");
        setFooter();
        App.database.articleDAO().insert(ArticleDialog.article);
    }

    void startBarcode() {
        barCodeReader = new BarCodeReaderManager();
        Map<String, Object> properties = new HashMap();
        properties.put(PROPERTY_CODE_128_ENABLED, true);
        properties.put(PROPERTY_EAN_13_ENABLED, true);
        properties.put(PROPERTY_EAN_13_CHECK_DIGIT_TRANSMIT_ENABLED, prefUtils.getEan13CheckDigitEnabled());
        EdaReader edaReader = new EdaReader(this, new BarcodeListener() {
            @Override
            public void onSuccess(String event) {
                Toasty.info(context, "" + event).show();
                showArticleFromScan(event);
            }

            @Override
            public void onFail(String event) {
                Toasty.info(context, R.string.error_read_codebare).show();
            }
        }, properties);
        barCodeReader.addReader(edaReader).addReader(new PM80Reader(new BarcodeListener() {
            @Override
            public void onSuccess(String event) {
                Toasty.info(context, "" + event).show();
                showArticleFromScan(event);
            }

            @Override
            public void onFail(String event) {
                Log.d(TAG, "event : " + event);
                Toasty.info(context, R.string.error_read_codebare).show();
            }
        }));
        barCodeReader.startListener();

    }



    void goToBlFragment(){
    /*    Intent i = new Intent(this, MainActivity.class);
        i.putExtra("frgmntToLoad", "BL");
        // Now start your activity
        startActivity(i);*/
        finish();
    }




    public static double sumOfList(List<LigneTicket> numbers) {

        if (numbers == null || numbers.isEmpty()) {
            return 0; // Handle empty list gracefully
        }

        double sum = 0;
        for (LigneTicket number : numbers) {
            sum += number.lTRemise;
        }
        return sum;
    }

}
