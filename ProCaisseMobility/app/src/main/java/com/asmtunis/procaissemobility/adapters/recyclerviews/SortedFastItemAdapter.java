package com.asmtunis.procaissemobility.adapters.recyclerviews;


import com.mikepenz.fastadapter.commons.adapters.FastItemAdapter;

import java.util.Comparator;
import java.util.List;

/**
 * Created by PC on 10/31/2017.
 */
public class SortedFastItemAdapter<T> extends FastItemAdapter {

    Comparator<T> comparator;

    public SortedFastItemAdapter(Comparator<T> comparator) {
        this.comparator = comparator;

    }

    @Override
    public FastItemAdapter add(List list) {
        clear();
        return super.add(list);
    }

    @Override
    public int getItemViewType(int position) {
        return super.getItemViewType(position);
    }

    @Override
    public void notifyAdapterDataSetChanged() {
        super.notifyAdapterDataSetChanged();
    }
}
