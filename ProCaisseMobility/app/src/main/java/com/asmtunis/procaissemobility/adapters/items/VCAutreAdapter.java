package com.asmtunis.procaissemobility.adapters.items;

import android.content.Context;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.Toolbar;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelStoreOwner;
import androidx.recyclerview.widget.RecyclerView;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.data.models.VCAutre;

import com.asmtunis.procaissemobility.data.viewModels.VCTypeCommunicationViewModel;
import com.asmtunis.procaissemobility.helper.utils.DateUtils;
import com.asmtunis.procaissemobility.listener.ItemCallback;
import com.asmtunis.procaissemobility.listener.MenuItemsAction;
import com.mikepenz.fastadapter.items.AbstractItem;

import java.util.List;

public class VCAutreAdapter extends AbstractItem<VCAutreAdapter, VCAutreAdapter.VCAutreViewHolder> {
    private Context context;
    public VCAutre vcAutre;
    protected ItemCallback itemCallback;
    MenuItemsAction menuItemsAction;
    VCTypeCommunicationViewModel vcTypeCommunicationViewModel;


    public VCAutreAdapter(Context context, VCAutre vcAutre, ViewModelStoreOwner viewModelStoreOwner , ItemCallback itemCallback, MenuItemsAction menuItemsAction) {
        this.context = context;
        this.vcAutre = vcAutre;
        this.itemCallback = itemCallback;
        this.menuItemsAction = menuItemsAction;
        vcTypeCommunicationViewModel = new ViewModelProvider(viewModelStoreOwner).get(VCTypeCommunicationViewModel.class);
    }

    public class VCAutreViewHolder extends RecyclerView.ViewHolder{
        TextView code;
        TextView autre;
        TextView note;
        TextView typeCommunication;
        TextView date;
        ImageView tiketuserImV;

        LinearLayout footerLayout;
        public Toolbar toolbar;
        FrameLayout content;
        com.asmtunis.procaissemobility.ui.components.TicketView  ticketView;
        jp.shts.android.library.TriangleLabelView itemStatusLabel;

        public VCAutreViewHolder(@NonNull View itemView) {
            super(itemView);
         /*   code = itemView.findViewById(R.id.code_autre);
            autre=itemView.findViewById(R.id.autre_tv);
            note =itemView.findViewById(R.id.note_autre);
            typeCommunication=itemView.findViewById(R.id.type_communication_autre);
            date=itemView.findViewById(R.id.date_autre);
*/


            ticketView = itemView.findViewById(R.id.layout_ticket);
            footerLayout = itemView.findViewById(R.id.footer_layout);
            toolbar = itemView.findViewById(R.id.toolbar);
            typeCommunication = itemView.findViewById(R.id.price);
            code = itemView.findViewById(R.id.ticketNumber);
            autre = itemView.findViewById(R.id.ticketUser);
            date = itemView.findViewById(R.id.dateCreation);
            content = itemView.findViewById(R.id.content_layout);
            itemStatusLabel = itemView.findViewById(R.id.item_status_label);
            tiketuserImV = itemView.findViewById(R.id.tiketuserImV);
        }
    }

    @NonNull
    @Override
    public VCAutreViewHolder getViewHolder(View v) {
        return new VCAutreViewHolder(v);
    }

    @Override
    public int getType() {
        return R.id.fastadapter_ticket_item_id;
    }

    @Override
    //public int getLayoutRes() {return R.layout.vc_autre;}
    public int getLayoutRes() {return R.layout.ticket_item;}

    void onViewClick(VCAutreAdapter.VCAutreViewHolder viewHolder) {
        if (vcAutre != null) {
            if (itemCallback == null) {
                return;
            } else {
                itemCallback.onItemClicked(viewHolder, vcAutre);
            }

        }
    }


    @Override
    public void bindView(VCAutreViewHolder holder, List<Object> payloads) {
        super.bindView(holder, payloads);
        holder.code.setText(vcAutre.getCodeAutre());
        holder.autre.setText("Autre : " +vcAutre.getAutre());
        holder.tiketuserImV.setVisibility(View.INVISIBLE);
       // holder.note.setText(vcAutre.getNoteOp());
        holder.typeCommunication.setText(vcTypeCommunicationViewModel.getTypeCommunicationByCode(vcAutre.getCodeTypeCom()));

        holder.date.setText(vcAutre.getDateOp().replace(".000",""));

        if (itemCallback != null) {
          //  holder.itemView.setOnClickListener(v -> onViewClick(holder));

            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onViewClick(holder);
                }
            });
            holder.toolbar.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onViewClick(holder);
                }
            });
        }


        if(!vcAutre.isSync) {
            holder.date.setTextColor(context.getResources().getColor(R.color.warningColor));
            setTriangleView(holder.itemStatusLabel, 0);
        }
        else{
            holder.date.setTextColor(context.getResources().getColor(R.color.successColor));
            setTriangleView(holder.itemStatusLabel, -1);
        }
    }


    void setTriangleView(jp.shts.android.library.TriangleLabelView labelView, int status) {
        labelView.setVisibility(View.VISIBLE);
        switch (status) {
            case 0:
                labelView.setTriangleBackgroundColorResource(R.color.warningColor);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.notSync);
                labelView.setPrimaryTextColorResource(R.color.md_red_100);

                break;

            case 1:
                labelView.setTriangleBackgroundColorResource(R.color.md_green_800);
                labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
                labelView.setPrimaryText(R.string.new_label);
                labelView.setPrimaryTextColorResource(R.color.md_green_100);
                break;


            default:
                labelView.setVisibility(View.GONE);

                break;
        }

    }

}
