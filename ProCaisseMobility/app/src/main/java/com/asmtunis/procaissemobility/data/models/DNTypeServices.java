package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

@Entity
public class DNTypeServices extends BaseModel implements Serializable {

    public DNTypeServices() {
    }
    @NonNull
    public String getCodeTypeSv() {
        return codeTypeSv;
    }

    public void setCodeTypeSv(@NonNull String codeTypeSv) {
        this.codeTypeSv = codeTypeSv;
    }

    public String getTypeSv() {
        return typeSv;
    }

    public void setTypeSv(String typeSv) {
        this.typeSv = typeSv;
    }

    public String getNoteSv() {
        return noteSv;
    }

    public void setNoteSv(String noteSv) {
        this.noteSv = noteSv;
    }

    public int getEtatSV() {
        return etatSV;
    }

    public void setEtatSV(int etatSV) {
        this.etatSV = etatSV;
    }

    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "CodeTypeSv")//
    @SerializedName("CodeTypeSv")
    @Expose
    private String codeTypeSv;

    @ColumnInfo(name = "TypeSv")//
    @SerializedName("TypeSv")
    @Expose
    private String typeSv;



    @ColumnInfo(name = "NoteSv")//
    @SerializedName("NoteSv")
    @Expose
    private String noteSv;



    @ColumnInfo(name = "EtatPv")//EtatSv
    @SerializedName("EtatPv")
    @Expose
    private int etatSV;

}
