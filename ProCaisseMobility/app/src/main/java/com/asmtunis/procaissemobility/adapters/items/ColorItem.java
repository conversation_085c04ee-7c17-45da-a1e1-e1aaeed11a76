package com.asmtunis.procaissemobility.adapters.items;

import android.graphics.Color;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.recyclerview.widget.RecyclerView;

import com.asmtunis.procaissemobility.R;
import com.mikepenz.fastadapter.items.AbstractItem;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

public class ColorItem extends AbstractItem<ColorItem, ColorItem.ViewHolder> {

    String clientName;
    int clientColor;

    public ColorItem(String clientName, int clientColor) {
        this.clientName = clientName;
        this.clientColor = clientColor;
    }

    //The unique ID for this type of item
    @Override
    public int getType() {
        return R.id.fastadapter_article_item_id;
    }

    //The layout to be used for this type of item
    @Override
    public int getLayoutRes() {
        return R.layout.color_item;
    }

    //The logic to bind your data to the view

    @Override
    public void bindView(@NonNull final ColorItem.ViewHolder viewHolder, @NonNull List<Object> payloads) {
        //call super so the selection is already handled for you
        super.bindView(viewHolder, payloads);
        viewHolder.clientColor.setBackgroundColor(Color.parseColor(String.format("#%06X", (0xFFFFFF & clientColor))));
        viewHolder.clientName.setText(clientName);
    }

    //reset the view here (this is an optional method, but recommended)
    @Override
    public void unbindView(@NonNull ColorItem.ViewHolder holder) {
        super.unbindView(holder);
        //  holder.thumbnail.setImage(null);

    }

    //Init the viewHolder for this Item
    @NonNull
    @Override
    public ColorItem.ViewHolder getViewHolder(@NonNull View v) {
        return new ColorItem.ViewHolder(v);
    }


    //The viewHolder used for this item. This viewHolder is always reused by the RecyclerView so scrolling is blazing fast
    protected static class ViewHolder extends RecyclerView.ViewHolder {
        protected View view;
        @BindView(R.id.client_color)
        View clientColor;
        @BindView(R.id.client_name)
        AppCompatTextView clientName;

        public ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
            this.view = view;
        }
    }
}
