package com.asmtunis.procaissemobility.data.network.datamanager;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.RecetteByReglementResponseModel;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.RecapService;

/**
 * Created by WAEL on 9/7/22.
 */

public class RecapDataManager {
    private static RecapDataManager sInstance;
    private final RecapService mRecapService;

    public RecapDataManager() {
        mRecapService = new ServiceFactory<>(RecapService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"RecapVente")).makeService();
    }

    public static RecapDataManager getInstance( ) {
        if (sInstance == null) {
            sInstance = new RecapDataManager();
        }
        return sInstance;
    }

    public void getRecapeData(GenericObject connexion, RemoteCallback<RecetteByReglementResponseModel> listener) {
        mRecapService.getRecap(connexion)
                .enqueue(listener);
    }
}

