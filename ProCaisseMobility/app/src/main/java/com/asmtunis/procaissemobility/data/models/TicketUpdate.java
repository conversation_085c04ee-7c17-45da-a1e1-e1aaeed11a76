package com.asmtunis.procaissemobility.data.models;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class TicketUpdate {

    @SerializedName("TIK_NumTicket")
    @Expose
    private int tIKNumTicket;
    @SerializedName("TIK_IdCarnet")
    @Expose
    private String tIKIdCarnet;
    @SerializedName("TIK_Exerc")
    @Expose
    private String tIKExerc;
    @SerializedName("TIK_NumTicket_M")
    @Expose
    private String tIKNumTicketM;
    @SerializedName("TIK_NumeroBL")
    @Expose
    private String tIKNumeroBL;

    @SerializedName("message")
    @Expose
    private String message;

    @SerializedName("code")
    @Expose
    private String code;

    @SerializedName("CodeClient")
    @Expose
    private String codeClient;

    @SerializedName("SoldeClient")
    @Expose
    private String soldeClient;

    @SerializedName("Debit")
    @Expose
    private String debit;

    @SerializedName("Credit")
    @Expose
    private String credit;

    public Facture getFacture() {
        return facture;
    }

    public void setFacture(Facture facture) {
        this.facture = facture;
    }

    @SerializedName("facture")
    @Expose
    private Facture facture;


    public String getObservation() {
        return observation;
    }

    public void setObservation(String observation) {
        this.observation = observation;
    }

    @SerializedName("DEV_Observation")
    @Expose
    private String observation;

    public String getMessage() {
        return message;
    }

    public String getDebit() {
        return debit;
    }

    public void setDebit(String debit) {
        this.debit = debit;
    }

    public String getCredit() {
        return credit;
    }

    public void setCredit(String credit) {
        this.credit = credit;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }



    public String getCodeClient() {
        return codeClient;
    }

    public void setCodeClient(String codeClient) {
        this.codeClient = codeClient;
    }

    public String getSoldeClient() {
        return soldeClient;
    }

    public void setSoldeClient(String soldeClient) {
        this.soldeClient = soldeClient;
    }




    public int getTIKNumTicket() {
        return tIKNumTicket;
    }

    public void setTIKNumTicket(int tIKNumTicket) {
        this.tIKNumTicket = tIKNumTicket;
    }

    public String getTIKIdCarnet() {
        return tIKIdCarnet;
    }

    public void setTIKIdCarnet(String tIKIdCarnet) {
        this.tIKIdCarnet = tIKIdCarnet;
    }

    public String getTIKExerc() {
        return tIKExerc;
    }

    public void setTIKExerc(String tIKExerc) {
        this.tIKExerc = tIKExerc;
    }

    public String getTIKNumTicketM() {
        return tIKNumTicketM;
    }

    public void setTIKNumTicketM(String tIKNumTicketM) {
        this.tIKNumTicketM = tIKNumTicketM;
    }

    public String getTIKNumeroBL() {
        return tIKNumeroBL;
    }

    public void setTIKNumeroBL(String tIKNumeroBL) {
        this.tIKNumeroBL = tIKNumeroBL;
    }

}
