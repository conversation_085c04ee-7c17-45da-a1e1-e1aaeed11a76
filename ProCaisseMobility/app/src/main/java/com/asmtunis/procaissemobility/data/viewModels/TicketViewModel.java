package com.asmtunis.procaissemobility.data.viewModels;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelProviders;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.dao.TicketDAO;
import com.asmtunis.procaissemobility.data.models.Ticket;

import java.util.List;

public class TicketViewModel extends ViewModel {

    public TicketDAO dao;

    private static TicketViewModel instance;

    public static TicketViewModel getInstance(Fragment activity) {
        if (instance == null)
            instance = new ViewModelProvider(activity).get(TicketViewModel.class);
        instance.dao = App.database.ticketDAO();

        return instance;
    }

    public static TicketViewModel getInstance(FragmentActivity activity) {
        if (instance == null)
            instance = new ViewModelProvider(activity).get(TicketViewModel.class);
        instance.dao = App.database.ticketDAO();
        return instance;
    }

    public LiveData<Integer> getNoSyncCount() {
        return dao.getNoSyncCountMutable();
    }

    public Integer getNoSyncCountNonMutable() {
        return dao.getNoSyncCountNonMutable();
    }

    public LiveData<List<Ticket>> getAllTicketBySession(String session, String station) {
        return dao.getAllTicketBySessionMutable(session, station);
    }

    public LiveData<Integer> getAllCountBySessionMutable(String session, String station) {
        return dao.getAllCountBySessionMutable(session, station);
    }

    public LiveData<Ticket> getOneByCode(String session) {
        return dao.getOneByCodeMutable(session);
    }

}
