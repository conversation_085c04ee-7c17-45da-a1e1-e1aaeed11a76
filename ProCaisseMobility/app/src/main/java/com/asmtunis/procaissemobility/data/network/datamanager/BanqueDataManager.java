package com.asmtunis.procaissemobility.data.network.datamanager;


import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Banque;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.BanqueService;

import java.util.ArrayList;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

/**
 * Created by Achraf on 29/09/2017.
 */

public class BanqueDataManager  {

    private static BanqueDataManager sInstance;
    private final BanqueService mBanqueService;

    public BanqueDataManager() {
        mBanqueService = new ServiceFactory<>(BanqueService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Banque")).makeService();
    }

    public static BanqueDataManager getInstance( ) {
        if (sInstance == null) {
            sInstance = new BanqueDataManager();
        }
        return sInstance;
    }

    public void getBanques(GenericObject genericObject,
            RemoteCallback<ArrayList<Banque>> listener) {
        mBanqueService.getBanques(genericObject)
                .enqueue(listener);
    }


}


