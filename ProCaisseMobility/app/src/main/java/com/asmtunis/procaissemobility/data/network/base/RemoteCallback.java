package com.asmtunis.procaissemobility.data.network.base;

import android.app.Activity;
import android.content.Context;
import android.util.Log;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.helper.utils.UIUtils;
import com.asmtunis.procaissemobility.ui.dialogs.LoadingDialog;

import java.net.HttpURLConnection;

import javax.net.ssl.HttpsURLConnection;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by Achraf on 25/09/2017.
 */


//Conn http (les reponses http (404 etc))
public abstract class RemoteCallback<T> implements Callback<T> {
    static LoadingDialog alertDialog;
    Context context;
    String erreurmsg = "";
    Activity Acontext;
     boolean showErrorDialog = true;
    public RemoteCallback(Context context, boolean isShown) {
        this.context = context;
        if (isShown) {
            alertDialog = LoadingDialog.getInstance(context);
            alertDialog.show();
        }
    }

    public RemoteCallback(Context context, boolean isShown, String erreurmsg) {
        this.context = context;
        this.erreurmsg = erreurmsg;



        if (isShown) {
            alertDialog = LoadingDialog.getInstance(context);
            alertDialog.show();
        }
    }


    public RemoteCallback(Context context, boolean isShown, boolean showErrorDialog) {
        this.context = context;
        this.showErrorDialog = showErrorDialog;
        if (isShown) {
            alertDialog = LoadingDialog.getInstance(context);
            alertDialog.show();
        }
    }






    @Override
    public final synchronized void onResponse(Call<T> call, Response<T> response) {

        switch (response.code()) {
            case HttpsURLConnection.HTTP_OK:
            case HttpsURLConnection.HTTP_CREATED:
            case HttpsURLConnection.HTTP_ACCEPTED:
            case HttpsURLConnection.HTTP_NOT_AUTHORITATIVE:
                //   if (response.body() != null) {

                dismissAlertDialog();
                try {
                    onSuccess(response.body());
                } catch (NoSuchFieldException e) {
                    e.printStackTrace();
                } catch (InstantiationException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
                //}

                if (response == null) {
               if(showErrorDialog)     UIUtils.showDialog(context, R.string.connection_failed_title,  erreurmsg.equals("") ? context.getString(R.string.connection_failed_content) :erreurmsg);
                }
                break;

            case HttpURLConnection.HTTP_UNAUTHORIZED:
                dismissAlertDialog();
                onUnauthorized();
               // UIUtils.showDialog(context, R.string.connection_failed_title, R.string.connection_failed_content); // Bug --> dialog is shown before an Activity is created or after it's hidden
                break;


            default:
                dismissAlertDialog();
                onFailed(new Throwable("Default " + response.code() + " " + response.message()));
                String message = "Default " + response.code() + " " + response.message();

                try {
                    if(showErrorDialog)     UIUtils.showDialog(context, R.string.connection_failed_title, erreurmsg.equals("") ? context.getString(R.string.connection_failed_content) :erreurmsg);
                } catch (Exception e) {
                    Log.d("ddd", e.getMessage());

                }


        }
    }

    @Override
    public final synchronized void onFailure(Call<T> call, Throwable t) {
        dismissAlertDialog();
        onFailed(t);

        if (t != null)
            try {
               if(showErrorDialog)      UIUtils.showDialog(context, R.string.connection_failed_title,erreurmsg.equals("") ? context.getString(R.string.connection_failed_content) :erreurmsg);
                // if(showErrorDialog)      UIUtils.showDialog(context, R.string.connection_failed_title,t.getMessage().toString());
                System.out.println("error " + t.toString());
            } catch (NullPointerException e) {
            }
    }


    public abstract void onSuccess(T response) throws NoSuchFieldException, InstantiationException, IllegalAccessException;

    public abstract void onUnauthorized();

    public abstract void onFailed(Throwable throwable);


    void dismissAlertDialog() {
        if (alertDialog != null)
            alertDialog.dismiss();
    }
}