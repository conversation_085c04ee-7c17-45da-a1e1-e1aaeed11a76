package com.asmtunis.procaissemobility.helper;

import static com.asmtunis.procaissemobility.App.prefUtils;
import static com.asmtunis.procaissemobility.helper.Globals.DEFAULT_ENCODING;
import static com.asmtunis.procaissemobility.helper.Globals.DEFAULT_VALUE;
import static com.asmtunis.procaissemobility.helper.Globals.LEFT_LENGTH;
import static com.asmtunis.procaissemobility.helper.Globals.LEFT_TEXT_MAX_LENGTH;
import static com.asmtunis.procaissemobility.helper.Globals.REPLACE_PATTERN;
import static com.asmtunis.procaissemobility.helper.Globals.RIGHT_LENGTH;
import static com.asmtunis.procaissemobility.helper.Globals.nbCharAllowed;
import static com.asmtunis.procaissemobility.helper.utils.Calculator.calculateAmountExcludingTax;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Build;
import android.os.Handler;
import android.util.Base64;
import android.util.Log;

import androidx.annotation.RequiresApi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.BuildConfig;
import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.data.models.Article;
import com.asmtunis.procaissemobility.data.models.BonCommande;
import com.asmtunis.procaissemobility.data.models.BonRetour;
import com.asmtunis.procaissemobility.data.models.ChequeCaisse;
import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.data.models.Etablisement;
import com.asmtunis.procaissemobility.data.models.Facture;
import com.asmtunis.procaissemobility.data.models.LigneBonCommande;
import com.asmtunis.procaissemobility.data.models.LigneBonRetour;
import com.asmtunis.procaissemobility.data.models.LigneTicket;
import com.asmtunis.procaissemobility.data.models.RecetteByReglementResponseModel;
import com.asmtunis.procaissemobility.data.models.ReglementCaisse;
import com.asmtunis.procaissemobility.data.models.Ticket;
import com.asmtunis.procaissemobility.data.models.TicketWithLinesAndPayments;
import com.asmtunis.procaissemobility.data.models.Timbre;
import com.asmtunis.procaissemobility.data.models.TraiteCaisse;
import com.asmtunis.procaissemobility.data.models.printer.BarCode;
import com.asmtunis.procaissemobility.data.models.printer.Goods;
import com.asmtunis.procaissemobility.data.models.printer.Image;
import com.asmtunis.procaissemobility.data.models.printer.PosParam;
import com.asmtunis.procaissemobility.data.models.printer.PosTpl;
import com.asmtunis.procaissemobility.data.models.printer.QrCode;
import com.asmtunis.procaissemobility.data.models.printer.Text;
import com.asmtunis.procaissemobility.enums.PrinterType;
import com.asmtunis.procaissemobility.enums.PrinterWidth;
import com.asmtunis.procaissemobility.helper.utils.DateUtils;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.helper.utils.Utils;
import com.example.tscdll.TSCActivity;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by PC on 10/31/2017.
 */

public class PrinterHelper {
    static TSCActivity TscDll = new TSCActivity();

    public static final int BIG = PrinterWidth.FOUR_EIGHT.printerWidth();

    public static final int SMALL = PrinterWidth.THREE_ZERO.printerWidth();





    private static String encoding = null;
    // use ip as the key, PrinterHelper instance as the value of the Map
    private static final Map<String, PrinterHelper> posMap = new HashMap<String, PrinterHelper>();
    private static PrinterHelper escPos = null;
    // read and write through the socket stream
    private static BluetoothService bluetoothService = null;


    private static final String versionCode = BuildConfig.VERSION_NAME;

    public PrinterHelper(BluetoothService bluetoothService, String encoding) throws IOException {
        this.encoding = encoding;
        this.bluetoothService = bluetoothService;

    }

    public synchronized static PrinterHelper getInstance(BluetoothService bluetoothService, String encoding) throws IOException {
        if (escPos == null) {
            escPos = new PrinterHelper(bluetoothService, encoding);
        }
        return escPos;
    }

    public synchronized static PrinterHelper getInstance(BluetoothService bluetoothService) throws IOException {
        return getInstance(bluetoothService, DEFAULT_ENCODING);
    }


    /**
     * Print a small ticket based on the contents and parameters of a class
     *
     * @param template ????
     * @param param    ??
     */
    public static void print(String template, String param) throws IOException {
        PosParam posParam = JSON.parseObject(param, PosParam.class);

        Map<String, Object> keyMap = posParam.getKeys();
        List<Map<String, Object>> goodsParam = posParam.getGoods();

        // replace placeholder in template
        Pattern pattern = Pattern.compile(REPLACE_PATTERN);

        Matcher matcher = pattern.matcher(template);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String key = matcher.group(1);
            matcher.appendReplacement(sb, keyMap.get(key).toString());
        }

        matcher.appendTail(sb);

        template = sb.toString();

        PosTpl posTpl = JSON.parseObject(template, PosTpl.class);

        // print header
        for (JSONObject jsonObject : posTpl.getHeader()) {
            print(jsonObject);
        }

        // print goods
        // print title
        for (Goods goods : posTpl.getGoods()) {
            printTitle(goods);
        }
        escPos.line(1);

        // print detail
        for (Map<String, Object> goods : goodsParam) {
            printGoods(goods, posTpl.getGoods());
        }

        // print bill
        for (JSONObject jsonObject : posTpl.getBill()) {
            print(jsonObject);
        }

        // print footer
        for (JSONObject jsonObject : posTpl.getFooter()) {
            print(jsonObject);
        }

        escPos.line(2);

        escPos.feedAndCut();
    }

    /**
     * ??????
     *
     * @param jsonObject ??????
     * @throws IOException
     */
    private static void print(JSONObject jsonObject) throws IOException {
        int type = jsonObject.getInteger("type");

        switch (type) {
            case 0:
                Text text = JSON.toJavaObject(jsonObject, Text.class);
                // printText(text);
                break;
            case 1:
                BarCode barCode = JSON.toJavaObject(jsonObject, BarCode.class);
                printBarCode(barCode);
                break;
            case 2:
                QrCode qrCode = JSON.toJavaObject(jsonObject, QrCode.class);
                printQrCode(qrCode);
                break;
            case 3:
                Image image = JSON.toJavaObject(jsonObject, Image.class);
                printImage(image);
                break;
        }
    }

    private static void printTextForImage(byte[] msg) {
        // Print normal text
        bluetoothService.write(msg);
    }


    public static void printText(String text, int size, int align, boolean isBold, boolean isUnderLine, int lineSpace) throws IOException {
        if(Objects.equals(prefUtils.getPrinterType(), PrinterType.ESC_POS.PrinterType())) {
            int fontSize;
            init();
            switch (size) {
                case 10:
                    fontSize = 8;
                    break;
                case 2:
                    fontSize = 17;
                    break;
                case 3:
                    fontSize = 34;
                    break;
                case 4:
                    fontSize = 51;
                    break;
                case 5:
                    fontSize = 68;
                    break;
                case 6:
                    fontSize = 85;
                    break;
                case 7:
                    fontSize = 102;
                    break;
                case 8:
                    fontSize = 119;
                    break;
                case 1:
                default:
                    fontSize = 0;
            }


            bluetoothService.write(new byte[]{0x1B, 97, (byte) align});
            if (isBold) {
                bluetoothService.write(new byte[]{0x1B, 69, 0xF});
            }
            if (isUnderLine) {

                bluetoothService.write(new byte[]{0x1B, 45, 2});
            }


            bluetoothService.write(new byte[]{0x1b, 0x20, (byte) fontSize});



            bluetoothService.sendMessage(text, DEFAULT_ENCODING);
            for (int i = 0; i < lineSpace; i++) {
                bluetoothService.sendMessage("\n", DEFAULT_ENCODING);
            }
            reset();
        }
        else {
            if (TscDll.IsConnected) {

                String newline = System.getProperty("line.separator");
                if (isBold) {

                    TscDll.sendcommand(new String(new byte[]{0x1B, '!', 'G'}));
                } else TscDll.sendcommand((new String(new byte[]{0x1B, '!', 'P'})));


                if (isUnderLine) TscDll.sendcommand(new String(new byte[]{0x1B, '-', '1'}));
                else TscDll.sendcommand(new String(new byte[]{0x1B, '-', '0'}));
                TscDll.sendcommand(text + newline);
            }
        }

    }


    static void reset() throws IOException {
        sizeReset();
        boldOff(true);
        underlineOff(true);
    }


    /**
     * ?????
     *
     * @param barCode ?????
     * @throws IOException
     */
    public static void printBarCode(BarCode barCode) throws IOException {
        escPos.align(barCode.getFormat())
                .barCode(barCode.getText())
                .line(barCode.getLine());
    }

    /**
     * ?????
     *
     * @param qrCode ?????
     * @throws IOException
     */
    public static void printQrCode(QrCode qrCode) throws IOException {
        escPos.qrCode(qrCode.getFormat(), qrCode.getText())
                .line(qrCode.getLine());
    }

    /**
     * ????
     *
     * @param image ????
     * @throws IOException
     */
    private static void printImage(Image image) throws IOException {
        escPos.align(image.getFormat())
                .image(image.getPath())
                .line(image.getLine());
        escPos.init();
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    private static void printImage(Context context) {
        // Bitmap bmp = com.blankj.utilcode.util.ConvertUtils.drawable2Bitmap(context.getDrawable(R.drawable.blue_sobeco));
        byte[] decodedString = Base64.decode(prefUtils.getEntrepriseIcon(), Base64.DEFAULT);
        Bitmap bmp = BitmapFactory.decodeByteArray(decodedString, 0, decodedString.length);



        try {
            if (bmp != null) {
                //  bmp = Utils.toGrayscale(bmp);

                double width = bmp.getWidth();
                double height = bmp.getHeight();
                double ratio = height / 100;
                byte[] command = Utils.decodeBitmap(Bitmap.createScaledBitmap(bmp, (int) (width / ratio), 100, false));
                //   byte[] command = Utils.decodeBitmap(Bitmap.createScaledBitmap(bmp, (int) width, (int) height, false));
                init();
                bluetoothService.write(new byte[]{0x1B, 97, (byte) 1});
                printTextForImage(command);
                bluetoothService.sendMessage("\n", DEFAULT_ENCODING);
                reset();
            } else {
                Log.e("Print Photo error", "the file isn't exists");
            }
        } catch (Exception e) {
            e.printStackTrace();
            Log.e("PrintTools", "the file isn't exists");
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    private static void printBitmap(Context context) {
        // Bitmap bmp = com.blankj.utilcode.util.ConvertUtils.drawable2Bitmap(context.getDrawable(R.drawable.blue_sobeco));
        byte[] decodedString = Base64.decode( prefUtils.getEntrepriseIcon(), Base64.DEFAULT);
        Bitmap bmp = BitmapFactory.decodeByteArray(decodedString, 0, decodedString.length);



        try {
            if (bmp != null) {
                //  bmp = Utils.toGrayscale(bmp);

                double width = bmp.getWidth();
                double height = bmp.getHeight();
                double ratio = height / 100;
                byte[] command = Utils.decodeBitmap(Bitmap.createScaledBitmap(bmp, (int) (width / ratio), 100, false));
                //   byte[] command = Utils.decodeBitmap(Bitmap.createScaledBitmap(bmp, (int) width, (int) height, false));
                init();
                bluetoothService.write(new byte[]{0x1B, 97, (byte) 1});
                printTextForImage(command);
                bluetoothService.sendMessage("\n", DEFAULT_ENCODING);
                reset();
            } else {
                Log.e("Print Photo error", "the file isn't exists");
            }
        } catch (Exception e) {
            e.printStackTrace();
            Log.e("PrintTools", "the file isn't exists");
        }
    }

    /**
     * ?????????
     *
     * @param goods
     * @throws IOException
     */
    private static void printTitle(Goods goods) throws IOException {
      /*  escPos.align(goods.getFormat())
                .bold(false)
                .underline(false)
                .size(1)
                .printStr(fillLength(goods.getName(), goods))
                .boldOff(false)
                .underlineOff(false)
                .sizeReset()
                .line(0);*/
    }

    /**
     * ????????
     *
     * @param goods
     * @param goodsList
     * @throws IOException
     */
    private static void printGoods(Map<String, Object> goods, List<Goods> goodsList) throws IOException {
        for (Goods ele : goodsList) {

        }
        escPos.line(1);
    }

    /**
     * ????????
     *
     * @param str
     * @param goods
     * @return
     */
    private static String fillLength(String str, Goods goods) {
        try {
            int width = goods.getWidth();
            int length = str.getBytes(encoding).length;
            switch (goods.getFormat()) {
                case 0: {
                    StringBuilder strBuilder = new StringBuilder(str);
                    while (length < width) {
                        strBuilder.append(" ");
                        length++;
                    }
                    str = strBuilder.toString();
                    break;
                }
                case 1: {
                    if (length < width) {
                        StringBuilder text = new StringBuilder();
                        int pre = (width - length) / 2;
                        int end = width - length - pre;
                        while (pre > 0) {
                            text.append(" ");
                            pre--;
                        }
                        StringBuilder strBuilder = new StringBuilder(str);
                        while (end > 0) {
                            strBuilder.append(" ");
                            end--;
                        }
                        str = strBuilder.toString();
                        str = text + str;
                    }
                    break;
                }
                case 2: {
                    StringBuilder text = new StringBuilder();
                    while (length < width) {
                        text.append(" ");
                        length++;
                    }
                    str = text + str;
                    break;
                }
                default:
                    break;
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return str;
    }

    /**
     * ??
     *
     * @param lineNum line feed, 0 is not wrapped
     * @return
     * @throws IOException
     */
    private static PrinterHelper line(int lineNum) throws IOException {
        for (int i = 0; i < lineNum; i++) {
            bluetoothService.sendMessage("\n", DEFAULT_ENCODING);

        }
        return null;
    }

    /**
     * ???
     *
     * @param flag false is not underlined
     * @return
     * @throws IOException
     */
  /*  private  PrinterHelper underline(boolean flag) throws IOException {
        if (flag) {
            byte[] underline = {0x1B, 45,2};
            bluetoothService.write(underline);
        }
        return this;
    }
*/
    private static void underline(boolean flag) throws IOException {
        if (flag) {
            byte[] underline = {0x1B, 45, 2};
            bluetoothService.write(underline);
        }
    }


    /**
     * Cancel underline
     *
     * @param flag true??????
     * @return
     * @throws IOException
     */
    private static PrinterHelper underlineOff(boolean flag) throws IOException {
        if (flag) {
            byte[] underlineOff = {0x1B, 45, 0};
            bluetoothService.write(underlineOff);

        }
        return null;
    }

    /**
     * Bold
     *
     * @param flag false????
     * @return
     * @throws IOException
     */
    private static PrinterHelper bold(boolean flag) throws IOException {
        if (flag) {
            byte[] bold = {0x1B, 69, 0xF};
            bluetoothService.write(bold);
        }
        return null;
    }

    /**
     * Cancel bold*
     *
     * @param flag true???????
     * @return
     * @throws IOException
     */
    private static PrinterHelper boldOff(boolean flag) throws IOException {
        if (flag) {
            byte[] boldOff = {0x1B, 69, 0};
            bluetoothService.write(boldOff);
        }
        return null;
    }

    /**
     * ??
     *
     * @param position 0:??(??) 1:?? 2:??
     * @return
     * @throws IOException
     */
    public static PrinterHelper align(int position) throws IOException {
        byte[] align = {0x1B, 97, (byte) position};
        bluetoothService.write(align);
        return null;
    }

    /**
     * ??????
     *
     * @return
     * @throws IOException
     */
    public static PrinterHelper init() throws IOException {
        byte[] init = {0x1B, 0x40};
        bluetoothService.write(init);
        return null;
    }

    /**
     * ?????????
     *
     * @param position   0:??(??) 1:?? 2:??
     * @param moduleSize ???version??
     * @return
     * @throws IOException
     */
    private PrinterHelper alignQr(int position, int moduleSize) throws IOException {

        bluetoothService.write(new byte[]{0x1B, 97});

        if (position == 1) {
            bluetoothService.write(new byte[]{1});

            centerQr(moduleSize);
        } else if (position == 2) {
            bluetoothService.write(new byte[]{2});

            rightQr(moduleSize);
        } else {
            bluetoothService.write(new byte[]{0});

        }

        byte[] init = {0x1B, 0x40};
        bluetoothService.write(init);
        return this;
    }

    /**
     * ?????
     *
     * @param moduleSize ???version??
     * @throws IOException
     */
    private void centerQr(int moduleSize) throws IOException {
        switch (moduleSize) {
            case 1: {
                printSpace(16);
                break;
            }
            case 2: {
                printSpace(18);
                break;
            }
            case 3: {
                printSpace(20);
                break;
            }
            case 4: {
                printSpace(22);
                break;
            }
            case 5: {
                printSpace(24);
                break;
            }
            case 6: {
                printSpace(26);
                break;
            }
            default:
                break;
        }
    }

    /**
     * ???????
     *
     * @param moduleSize ???version??
     * @throws IOException
     */
    private void rightQr(int moduleSize) throws IOException {
        switch (moduleSize) {
            case 1:
                printSpace(14);
                break;
            case 2:
                printSpace(17);
                break;
            case 3:
                printSpace(20);
                break;
            case 4:
                printSpace(23);
                break;
            case 5:
                printSpace(26);
                break;
            case 6:
                printSpace(28);
                break;
            default:
                break;
        }
    }

    /**
     * ????
     *
     * @param length ?????????
     * @throws IOException
     */
    private static void printSpace(int length) throws IOException {
        for (int i = 0; i < length; i++) {
            bluetoothService.sendMessage(" ", DEFAULT_ENCODING);
        }
    }

    /**
     * ????
     *
     * @param size 1-8 ????
     * @return
     * @throws IOException
     */
    private static PrinterHelper size(int size) throws IOException {
        int fontSize;
        switch (size) {
            case 2:
                fontSize = 17;
                break;
            case 3:
                fontSize = 34;
                break;
            case 4:
                fontSize = 51;
                break;
            case 5:
                fontSize = 68;
                break;
            case 6:
                fontSize = 85;
                break;
            case 7:
                fontSize = 102;
                break;
            case 8:
                fontSize = 119;
                break;
            default:
                fontSize = 0;
        }

        byte[] font = {0x1D, 33, (byte) fontSize};

        bluetoothService.write(font);


        return null;
    }

    /**
     * ??????
     *
     * @return
     * @throws IOException
     */
    private static PrinterHelper sizeReset() throws IOException {


        byte[] sizeReset = {0x1B, 39, 0};

        bluetoothService.write(sizeReset);
        return null;
    }

    /**
     * ???????
     *
     * @return
     * @throws IOException
     */
    private PrinterHelper feedAndCut() throws IOException {


        byte[] feedAndCut = {0x1D, 86, 65, 0};

        bluetoothService.write(feedAndCut);

        return this;
    }

    /**
     * ?????
     *
     * @param value
     * @return
     * @throws IOException
     */
    private PrinterHelper barCode(String value) throws IOException {


        byte[] feedAndCut = {0x1D, 107, 67, (byte) value.length()};

        bluetoothService.write(feedAndCut);
        bluetoothService.sendMessage(value, DEFAULT_ENCODING);


        return this;
    }

    /**
     * ?????
     *
     * @param qrData
     * @return
     * @throws IOException
     */
    private PrinterHelper qrCode(int position, String qrData) throws IOException {
        int moduleSize = 0;
        int length = qrData.getBytes(encoding).length;
        int l = (int) (Math.ceil(1.5 * length) * 8);
        if (l < 200) {
            moduleSize = 1;
        } else if (l < 429) {
            moduleSize = 2;
        } else if (l < 641) {
            moduleSize = 3;
        } else if (l < 885) {
            moduleSize = 4;
        } else if (l < 1161) {
            moduleSize = 5;
        } else if (l < 1469) {
            moduleSize = 6;
        }

        alignQr(position, moduleSize);
        byte[] cmd = {0x1D};

        bluetoothService.write(new byte[]{0x1D});// init

        bluetoothService.sendMessage("(k", DEFAULT_ENCODING);
        bluetoothService.write(new byte[]{(byte) (length + 3), 0, 49, 80, 48});

        bluetoothService.sendMessage(qrData, DEFAULT_ENCODING);

        bluetoothService.write(new byte[]{0x1D});// init
        bluetoothService.sendMessage("(k", DEFAULT_ENCODING);
        bluetoothService.write(new byte[]{3, 0, 49, 69, 48, 0x1D});

        bluetoothService.sendMessage("(k", DEFAULT_ENCODING);
        bluetoothService.write(new byte[]{3, 0, 49, 67, (byte) moduleSize, 0x1D});

        bluetoothService.sendMessage("(k", DEFAULT_ENCODING);


        bluetoothService.write(new byte[]{3, 0, 49, 81, 48});


        return this;
    }

    /**
     * ????
     *
     * @param path ????
     * @return
     */
    private PrinterHelper image(String path) throws IOException {
        // trans to byte array
        Bitmap bmp = BitmapFactory.decodeFile(path);

        byte[] data = new byte[]{0x1B, 0x33, 0x00};
        write(data);
        data[0] = (byte) 0x00;
        data[1] = (byte) 0x00;
        data[2] = (byte) 0x00;    //????

        int pixelColor;

        // ESC * m nL nH ???
        byte[] escBmp = new byte[]{0x1B, 0x2A, 0x00, 0x00, 0x00};

        escBmp[2] = (byte) 0x21;

        //nL, nH
        escBmp[3] = (byte) (bmp.getWidth() % 256);
        escBmp[4] = (byte) (bmp.getWidth() / 256);

        // ??????
        for (int i = 0; i < bmp.getHeight() / 24 + 1; i++) {
            write(escBmp);

            for (int j = 0; j < bmp.getWidth(); j++) {
                for (int k = 0; k < 24; k++) {
                    if (((i * 24) + k) < bmp.getHeight()) {
                        pixelColor = bmp.getPixel(j, (i * 24) + k);
                        if (pixelColor != -1) {
                            data[k / 8] += (byte) (128 >> (k % 8));
                        }
                    }
                }

                write(data);
                // ????
                data[0] = (byte) 0x00;
                data[1] = (byte) 0x00;
                data[2] = (byte) 0x00;
            }
            //??
            byte[] byte_send1 = new byte[2];
            byte_send1[0] = 0x0d;
            byte_send1[1] = 0x0a;
            write(byte_send1);
        }
        return this;
    }

    private void write(byte... data) throws IOException {
        bluetoothService.write(data);
    }

    /**
     * ?????
     *
     * @param str ???????
     * @return
     * @throws IOException
     */
    public static PrinterHelper printStr(String str) {

        byte[] cmd = new byte[3];
        cmd[0] = 0x1b;
        cmd[1] = 0x21;
        cmd[2] |= 0x10;
        bluetoothService.write(cmd);
        bluetoothService.sendMessage(str, DEFAULT_ENCODING);

        return null;
    }

    /**
     * ????
     *
     * @param leftText  ????
     * @param rightText ????
     * @return
     */
    @SuppressLint("NewApi")
    public static String printTwoData(String leftText, String rightText) {
        StringBuilder sb = new StringBuilder();
        int leftTextLength = getBytesLength(leftText);
        int rightTextLength = getBytesLength(rightText);
        sb.append(leftText);

        Log.d("plllgnbbbe", " eeeddd "+ nbCharAllowed);
        int marginBetweenMiddleAndRight = nbCharAllowed - leftTextLength - rightTextLength;

        for (int i = 0; i < marginBetweenMiddleAndRight; i++) {
            sb.append(" ");
        }
        sb.append(rightText);

        return sb.toString();
    }


    @SuppressLint("NewApi")
    public static void printTicket(Activity context, TicketWithLinesAndPayments ticket) throws IOException {

        if(Objects.equals(prefUtils.getPrinterType(), PrinterType.TSC.PrinterType()))
            TscDll.openport(BluetoothService.mdevice.getAddress());
       // BluetoothService.mdevice
       // if(Objects.equals(prefUtils.getPrinterType(), PrinterType.ESC_POS.PrinterType())) {
            printTicketESCPOS (context, ticket);
      /*}
        else {
            TscDll.openport(BluetoothService.mdevice.getAddress());

            if (TscDll.IsConnected){

                String newline = System.getProperty("line.separator");

                //String status = TscDll.printerstatus(300);
                //	TscDll.clearbuffer();
                //	TscDll.sendcommand("GAPDETECT [0,0]");
                //	TscDll.setup(50, 20, 4, 10, 1, 3, 0);
                //	TscDll.sendcommand("SET TEAR ON\n");
                //	TscDll.sendcommand("SET COUNTER @1 1\n");
//			TscDll.sendcommand("@1 = \"\"\n");
//			TscDll.sendcommand("TEXT 100,300,\"3\",0,1,1,@1\n");
                //	TscDll.barcode(70, -30, "128", 80, 1, 0, 1, 12, value);
//		      TscDll.printerfont(100, 250, "3", 0, 1, 1, value);
                //	TscDll.printlabel(1, 1);
                TscDll.downloadpcx("UL.PCX");
                TscDll.downloadbmp("Triangle.bmp");
                TscDll.downloadttf("ARIAL.TTF");
                TscDll.setup(prefUtils.getPrinterWidth(), 110, 4, 4, 0, 0, 0);
                TscDll.clearbuffer();

                TscDll.sendcommand("SET TEAR ON\n");
                TscDll.sendcommand("SET COUNTER @1 1\n");
                TscDll.sendcommand("@1 = \"0001\"\n");
                TscDll.sendcommand("TEXT 100,300,\"3\",0,1,1,@1\n");
                TscDll.sendcommand("PUTPCX 100,300,\"UL.PCX\"\n");
                TscDll.sendcommand("PUTBMP 100,520,\"Triangle.bmp\"\n");
                TscDll.sendcommand("TEXT 100,760,\"ARIAL.TTF\",0,15,15,\"THIS IS ARIAL FONT\"\n");
                TscDll.barcode(100, 100, "128", 100, 1, 0, 3, 3, "123456789");
                TscDll.printerfont(100, 250, "3", 0, 1, 1, "987654321");


                TscDll.printlabel(2, 1);
//TscDll.sendfile("zpl.txt");
                TscDll.closeport(5000);
            }
        }

*/

    }

    public static void printTicketESCPOS(Activity context, TicketWithLinesAndPayments ticket) throws IOException  {
        Facture facture = App.database.factureDAO().getByTicket(String.valueOf(ticket.getTicket().tIKNumTicket), ticket.getTicket().tIKUser, ticket.getTicket().tIKStation);

        byte[] cmd = new byte[3];
        boolean invoiced = false;
        String numFact = "";
        cmd[0] = 0x1b;
        cmd[1] = 0x21;
        cmd[2] |= 0x10;
        bluetoothService.write(new byte[]{0x1B, 97, 1});

        Client client = App.database.clientDAO().getOneByCode(ticket.getTicket().gettIKCodClt());


        for (LigneTicket ligneTicket: ticket.getLigneTicket()) {
            if(ligneTicket.lTnumFacture != null) {
                invoiced = true;
                numFact = ligneTicket.lTnumFacture;
            }
        }

        if ((ticket.getTicket().tIKNumeroBL != null || ticket.getTicket().tIKNumFact != 0.0) && !ticket.getTicket().tIKNumeroBL.contains("BL_M")) {
            invoiced = true;
            numFact = ticket.getTicket().tIKNumeroBL;
        }


        String num = "Num : " + (!invoiced ? ticket.getTicket().tikNumTicketM : numFact);


        if(num.contains("_") && !invoiced){
            if(ticket.getTicket().isSync) {
                num = "BL num "+ ticket.getTicket().gettIKNumTicket();
            } else {
                String[] numTicket = num.split("_");
                num ="BL_M_"+ numTicket[2]+"_"+numTicket[3]+"_"+numTicket[4];
            }

        }

        String Title = invoiced ? "Facture" : "Bon de Livraison";




        Date today = DateUtils.strToDate(ticket.getTicket().gettIKDateHeureTicket(), "yyyy-MM-dd HH:mm");
        DateFormat date = new SimpleDateFormat("dd/MM/yyyy");
        DateFormat time = new SimpleDateFormat("HH:mm");
        printText(Title , 1, 1, true, false, 0);

        boolean credit = ticket.getTicket().gettIKEtat().equals(Globals.TICKET_STATE.CREDIT.getValue());
        if (credit)   printText("(Credit)" , 1, 1, true, false, 0);

        ReglementCaisse reglementCaissePartiel = App.database.reglementCaisseDAO().getbyNumTicketPart(String.valueOf(ticket.getTicket().tIKNumTicket));
        if(credit && reglementCaissePartiel != null) printText("(Paiement Partiel)" , 1, 1, true, false, 0);

        printText(num, 1, 1, false, false, 0);
        printText("Date: " + date.format(today) + " " + time.format(today) + " ", 0, 1, false, false, 1);


        List<Etablisement> etablisements = App.database.etablisementDAO().getAll();
        Etablisement etablisement = null;
        if (etablisements != null && !etablisements.isEmpty() && etablisements.get(0) != null) {
            etablisement = etablisements.get(0);
        }

        if (etablisement != null) {

            if (etablisement.getDesgEt() != null) {
                if (etablisement.getDesgEt().length() > 16) {
                    String[] stationDesignations = etablisement.getDesgEt().split(" ");
                    StringBuilder stationDesignation = new StringBuilder();
                    List<String> finalList = new ArrayList<>();
                    for (int i = 0; i < stationDesignations.length; i++) {
                        if ((stationDesignation.length() + stationDesignations[i].length()) <= 16 && i != stationDesignations.length - 1) {
                            stationDesignation.append(stationDesignations[i]).append(" ");
                        } else if ((stationDesignation.length() + stationDesignations[i].length()) <= 16 && i == stationDesignations.length - 1) {
                            finalList.add(stationDesignation.toString());
                            stationDesignation = new StringBuilder();
                            stationDesignation.append(stationDesignations[i]).append(" ");
                            finalList.add(stationDesignation.toString());
                        } else if ((stationDesignation.length() + stationDesignations[i].length()) > 16 && i == stationDesignations.length - 1) {
                            finalList.add(stationDesignation.toString());
                            stationDesignation = new StringBuilder();
                            stationDesignation.append(stationDesignations[i]).append(" ");
                            finalList.add(stationDesignation.toString());
                        } else {
                            finalList.add(stationDesignation.toString());
                            stationDesignation = new StringBuilder();
                            stationDesignation.append(stationDesignations[i]).append(" ");
                        }
                    }

                    for (String designation : finalList) {
                        printText(String.format(" " + designation + " "), 1, 1, false, false, 0);
                    }
                } else {
                    printText(String.format(" " + etablisement.getDesgEt() + " "), 1, 1, true, false, 0);
                }
            }
            else printText(String.format(" " + "N/A" + " "), 2, 1, true, false, 0);


            if (!prefUtils.getEntrepriseIcon().equals(DEFAULT_VALUE) && !prefUtils.getEntrepriseIcon().equals(""))
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    printImage(context);
                }


            //   printText(printTwoData("Adresse: " + (etablisement.getEtAdresse() != null ? etablisement.getEtAdresse() : "N/A"),  "Tel: "+  (etablisement.getEtTel1() != null ? etablisement.getEtTel1() : "N/A")), 0, 0, false, false, 0);
            printText(printTwoData("Adresse: " ,  etablisement.getEtAdresse() != null ? etablisement.getEtAdresse() : "N/A"), 0, 0, false, false, 0);
            printText(printTwoData("Tel: ",    etablisement.getEtTel1() != null ? etablisement.getEtTel1() : "N/A"), 0, 0, false, false, 0);
            printText(printTwoData("MF: ",    etablisement.getEtMAtriculeF() != null ? etablisement.getEtMAtriculeF() : "N/A"), 0, 0, false, false, 0);



            // printText(String.format(etablisement.getEtMAtriculeF() != null ? etablisement.getEtMAtriculeF() : "N/A" + " "), 2, 1, false, false, 0);

        }

        printLineSeparator();




        try {
            printText(printTwoData("Client: ", ticket.getTicket().gettIKNomClient()), 0, 0, false, false, 0);

            printText(printTwoData("Code Client: ", ticket.getTicket().gettIKCodClt()), 0, 0, false, false, 0);

            if (client != null) {
                printText(printTwoData("MF Client: ",
                                client.getCLI_MatFisc() != null && !"null".equals(client.getCLI_MatFisc())
                                        ? client.getCLI_MatFisc()
                                        : "N/A"),
                        0, 0, false, false, 0);

                printText(printTwoData("Tel Client: ",
                                client.getcLITel1() != null ? client.getcLITel1() : "N/A"),
                        0, 0, false, false, 0);

                printText(printTwoData("Adresse Client: ",
                                client.cLIAdresse != null ? client.cLIAdresse : ticket.getTicket().tIKEmplacementMariage!= null ?ticket.getTicket().tIKEmplacementMariage : "N/A"),
                        0, 0, false, false, 0);
            } else {
                printText("Client information is not available.", 0, 0, false, false, 0);
            }

        } catch (IOException e) {
            e.printStackTrace();
        }



        double tIKMtRemise = ticket.getTicket().gettIKMtRemise();
        printLineSeparator();
        printText(printTwoData("CAISSE: ", ticket.getTicket().gettIKIdSCaisse() + " "), 0, 0, false, false, 0);
        printText(printTwoData("Operateur: ", new PrefUtils(context).getUserName() + " "), 0, 0, false, false, 0);
        printLineSeparator();


        printTicketTable(tIKMtRemise, ticket.getLigneTicket());


        printLineSeparator();

        printText(printTwoData("Total Articles:", String.valueOf(ticket.getLigneTicket().size())), 0, 0, true, false, 0);

        printLineSeparator();


        Timbre timbre = null;
        if(ticket.getTicket().timbre != 0.0) {
            timbre = App.database.timbreDAO().getByID(String.valueOf((int)ticket.getTicket().timbre));
        }

       // String timbValue = timbre == null ? StringUtils.priceFormat(ticket.getTicket().timbre) : timbre.tIMBValue;
        String timbValue = timbre == null ? String.valueOf(ticket.getTicket().timbre) : timbre.tIMBValue;

        if(facture != null) {
            if(Double.parseDouble(Objects.requireNonNull(facture.getFACT_Timbre())) != 0.0) {
                timbValue = facture.getFACT_Timbre();
            }
        }
        double mntRedevance = 0.0;
        if(client != null && client.cltMntRevImp != null && client.cltMntRevImp > 0.0 && ticket.getTicket() != null) {
            mntRedevance = ticket.getTicket().gettIKMtTTC() - (ticket.getTicket().gettIKMtTTC() / (1 + client.cltMntRevImp / 100));
        }

        double total = ticket.getTicket().gettIKMtTTC() + ticket.getTicket().tIKMtRemise - Double.parseDouble(timbValue) - mntRedevance;



        printText(printTwoData("Montant Total : ", String.format("%s ", StringUtils.priceFormat(total))), 0, 0, true, false, 0);
        if(tIKMtRemise > 0.0)   printText(printTwoData("Total Remise : ", String.format("%s ", "-" + StringUtils.priceFormat(tIKMtRemise))), 0, 0, true, false, 0);


        printText(printTwoData(context.getString(R.string.montantapresremise), String.format("%s ", StringUtils.priceFormat(ticket.getTicket().gettIKMtTTC() - mntRedevance -Double.parseDouble(timbValue)))), 0, 0, true, false, 0);


        printText(printTwoData("Timbre Fiscal : ", String.format("%s ", StringUtils.priceFormat(Double.parseDouble(timbValue)))), 0, 0, true, false, 0);

        if(ticket.getTicket().gettIKMtTVA()>0)
            printText(printTwoData("Montant TVA : ", String.format("%s ", StringUtils.priceFormat(ticket.getTicket().gettIKMtTVA()))), 0, 0, true, false, 0);




        double tikMttc = 0.0;

        if(facture != null) {
          if(facture.getFACT_MntRevImp() != null) {
              if(Double.parseDouble(Objects.requireNonNull(facture.getFACT_MntRevImp()))>0) {
                  printText(printTwoData("Avance Imp : ", String.format("%s ", StringUtils.priceFormat(Double.parseDouble(Objects.requireNonNull(facture.getFACT_MntRevImp()))))), 0, 0, true, false, 0);
              }
          }


            tikMttc = Math.max(Double.parseDouble(Objects.requireNonNull(facture.getFACT_MntTTC())), ticket.getTicket().gettIKMtTTC());
        }
        else tikMttc = ticket.getTicket().gettIKMtTTC();

        if(facture == null && client != null && client.cltMntRevImp != null && client.cltMntRevImp > 0.0 && tikMttc > 0.0) {
                  mntRedevance = tikMttc - (tikMttc / (1+ client.cltMntRevImp/ 100));
                printText(printTwoData("Redevance : ", String.format("%s ", StringUtils.priceFormat(mntRedevance))), 0, 0, true, false, 0);
        }


        printText(printTwoData("Net A Payer : ", StringUtils.priceFormat(tikMttc) + " "), 0, 0, true, false, 0);

        if (ticket.getReglement() != null) {
            if(ticket.getReglement().rEGCMntEspeceRecue!=null){
                if (ticket.getReglement().rEGCMntEspeceRecue > 0) {
                    printText(printTwoData("Espece : ", String.format("%s ", StringUtils.priceFormat(ticket.getReglement().rEGCMntEspeceRecue))), 0, 0, true, false, 0);
                }
            }

            if (ticket.getReglement().getrEGCMntChQue() > 0) {
                printText(printTwoData("Cheque: ", String.format("%s ", StringUtils.priceFormat(ticket.getReglement().getrEGCMntChQue()))), 0, 0, true, false, 0);
            }

            if(ticket.getReglement().rEGCMntTraite!=null){

                if (ticket.getReglement().rEGCMntTraite > 0) {
                    printText(printTwoData("Tikets Restaurant : ", String.format("%s ", StringUtils.priceFormat(ticket.getReglement().rEGCMntTraite))), 0, 0, true, false, 0);
                }
            }

            if(ticket.getReglement().made!=null){

                if (ticket.getReglement().made > 0) {
                    printText(printTwoData("A Rendre : ", String.format("%s ", StringUtils.priceFormat(ticket.getReglement().made))), 0, 0, true, false, 0);
                }
            }
        }
        else if(credit && reglementCaissePartiel != null){

            if(reglementCaissePartiel.rEGCMntEspeceRecue!=null){
                if (reglementCaissePartiel.rEGCMntEspeceRecue > 0) {
                    printText(printTwoData("Espece : ", String.format("%s ", StringUtils.priceFormat(reglementCaissePartiel.rEGCMntEspeceRecue))), 0, 0, true, false, 0);
                }
            }





            if (reglementCaissePartiel.getrEGCMntChQue() > 0) {
                printText(printTwoData("Cheque: ", String.format("%s ", StringUtils.priceFormat(reglementCaissePartiel.getrEGCMntChQue()))), 0, 0, true, false, 0);
            }



            if(reglementCaissePartiel.rEGCMntTraite!=null){

                if (reglementCaissePartiel.rEGCMntTraite > 0) {
                    printText(printTwoData("Tikets Restaurant : ", String.format("%s ", StringUtils.priceFormat(reglementCaissePartiel.rEGCMntTraite))), 0, 0, true, false, 0);
                }
            }

            if(reglementCaissePartiel.made!=null){

                if (reglementCaissePartiel.made > 0) {
                    printText(printTwoData("A Rendre : ", String.format("%s ", StringUtils.priceFormat(reglementCaissePartiel.made))), 0, 0, true, false, 0);
                }
            }


        }
        printLineSeparator();

        reset();


        printCachet();


        printText("         ", 0, 0, false, false, 0);

        printText(printTwoData("   ", versionCode), 0, 0, false, false, 0);
        printText("         ", 0, 0, false, false, 0);
        printText("         ", 0, 0, false, false, 0);
        printText("         ", 0, 0, false, false, 0);

        printLineSeparator();
        printText("         ", 0, 0, false, false, 0);
        printText( String.format("%s: %s", context.getString(R.string.sold_CLIENT),  Utils.round(client.getSolde(),3)), 0, 0, true, false, 0);
        printText("         ", 0, 0, false, false, 0);
        printLineSeparator();
        printText("         ", 0, 0, false, false, 0);
        printText("         ", 0, 0, false, false, 0);
        printText("         ", 0, 0, false, false, 0);




        if(credit && reglementCaissePartiel != null) printReglement(context, reglementCaissePartiel,true);
        else {
            if(Objects.equals(prefUtils.getPrinterType(), PrinterType.TSC.PrinterType()))    TscDll.closeport(5000);
            else  {
                Handler handler = new Handler();
                handler.postDelayed(() -> {
                    if (bluetoothService != null)
                        bluetoothService.stop();
                    bluetoothService = null;
                }, 10000);
            }

        }
    }

    public  static void printTicketTable(double tIKMtRemise, List<LigneTicket> ligneTicket)  throws IOException{
       boolean printTax = PrefUtils.getPrintArticleTax();
        if(BIG == nbCharAllowed) {
            if (tIKMtRemise > 0.0)
                printText("QTE            P.U          R%       TOTAL    ", 0, 1, true, false, 0);
            else printText("QTE              P.U                 TOTAL    ", 0, 1, true, false, 0);

            printLineSeparator();

            for (LigneTicket lgTicket : ligneTicket) {
              double mntHTTotal = calculateAmountExcludingTax(lgTicket.getlTMtTTC(), lgTicket.getlTTVA());
              double mntHT = lgTicket.lTMtHT;

              String prixUnitaire = StringUtils.priceFormat(lgTicket.getlTPrixVente());



                String prixHT = "";
                if(printTax) prixHT = " (HT: "+ StringUtils.priceFormat(mntHT) + ")";
                String qte = String.valueOf(lgTicket.getlTQte());
                String label = lgTicket.getArticle().getaRTDesignation() + prixHT;

                printText(printTwoData(label, " "), 0, 0, true, false, 0);

                if (tIKMtRemise > 0.0)
                    printText(printFourData(qte, prixUnitaire, String.valueOf(lgTicket.getlTTauxRemise()), StringUtils.priceFormat(lgTicket.getlTMtTTC())), 0, 0, false, false, 0);

                else
                    printText(printThreeData(qte, prixUnitaire, StringUtils.priceFormat(lgTicket.getlTMtTTC())), 0, 0, false, false, 0);


                printLineSeparator();
            }
        }
        else if(SMALL == nbCharAllowed){
            printText("QTE          P.U           TOTAL", 0, 0, true, false, 0);

            printLineSeparator();

            for (LigneTicket lgTicket : ligneTicket) {
                double mntHT = lgTicket.lTMtHT;

                String prixUnitaire = StringUtils.priceFormat(lgTicket.getlTPrixVente());

                String prixHT = "";
                if(printTax) prixHT = " (HT: "+ StringUtils.priceFormat(mntHT) + ")";

                String qte = String.valueOf(lgTicket.getlTQte());
                String label = lgTicket.getArticle().getaRTDesignation() + prixHT;
                String remise = "";

                if (tIKMtRemise > 0.0) remise = "-" + (double) lgTicket.getlTTauxRemise() + "%";

                printText(printTwoData(label, remise), 0, 0, true, false, 0);


                printText(printThreeData(qte, prixUnitaire, StringUtils.priceFormat(lgTicket.getlTMtTTC())), 0, 0, false, false, 0);


                printLineSeparator();
            }


        }
    }
    public static void printLineSeparator() throws IOException {
        if(BIG == nbCharAllowed) {
            printText(String.format("---------------------------------------------"), 0, 1, false, false, 0);

        }
        else if(SMALL == nbCharAllowed) {
            printText(String.format("-------------------------------"), 0, 0, false, false, 0);

        }
    }

    public static void printCachet() throws IOException {
        if(BIG == nbCharAllowed) {
            printText("+--------------------+  +-------------------+ ", 0, 0, false, false, 0);
            printText("|                    |  |                   | ", 0, 0, false, false, 0);
            printText("|                    |  |                   | ", 0, 0, false, false, 0);
            printText("|                    |  |                   | ", 0, 0, false, false, 0);
            printText("+--------------------+  +-------------------+ ", 0, 0, false, false, 0);

            printText("  Cachet et signature     Cachet et signature ", 0, 0, false, false, 0);
            printText("      fournisseur               client ", 0, 0, false, false, 0);
        }
        else if(SMALL == nbCharAllowed) {

            printText("+-------------+  +------------+", 0, 0, false, false, 0);
            printText("|             |  |            |", 0, 0, false, false, 0);
            printText("|             |  |            |", 0, 0, false, false, 0);
            printText("+-------------+  +------------+", 0, 0, false, false, 0);
            printText("  Fournisseur        Client", 0, 0, false, false, 0);
        }



    }

    @SuppressLint("NewApi")
    public static void printReglement(Activity context, ReglementCaisse reglementCaisse,boolean isRegPartiel) throws IOException {
        byte[] cmd = new byte[3];
        boolean invoiced = false;
        cmd[0] = 0x1b;
        cmd[1] = 0x21;
        cmd[2] |= 0x10;
        bluetoothService.write(new byte[]{0x1B, 97, 1});
        Client client = App.database.clientDAO().getOneByCode(reglementCaisse.getrEGCCodeClient());
        List<Etablisement> etablisements = App.database.etablisementDAO().getAll();
        Etablisement etablisement = null;
        if (etablisements != null && etablisements.size() > 0 && etablisements.get(0) != null) {
            etablisement = etablisements.get(0);
        }


        String tikNum ="";
        Ticket ticket = App.database.ticketDAO().getOneByCode((int) reglementCaisse.getrEGCNumTicket(), App.prefUtils.getExercice());



        if (ticket != null) {
            if (String.valueOf(ticket.gettIK_NumeroBL()).equals("null")) {

                tikNum = ticket.tikNumTicketM;
                if (tikNum.contains("_")) {
                    String[] numTicket = tikNum.split("_");
                    tikNum = context.getString(R.string.ticket_number_field, numTicket[2] + "_" + numTicket[3] + "_" + numTicket[4]);
                }

            } else
                tikNum = context.getString(R.string.fact, String.valueOf(ticket.gettIK_NumeroBL()));

            if (ticket.tIKAnnuler == 1) {

                tikNum = tikNum + " (Annuler)";
            }


            if (reglementCaisse.getrEGNumTicketPart() != null) {
                if (reglementCaisse.getrEGNumTicketPart() > 0)
                    tikNum = context.getString(R.string.paiement_Partiel);
                else  tikNum = reglementCaisse.getrEGCNumTicket() > 0 ? tikNum : context.getString(R.string.credit_label);

            } else{
                if(reglementCaisse.getrEGCRemarque().equals("Regler Acpt"))
                    tikNum = "Reglement Libre";
                else  tikNum = reglementCaisse.getrEGCNumTicket() > 0 ? tikNum : context.getString(R.string.credit_label);
            }


        }
        else {
            if (reglementCaisse.getrEGNumTicketPart() != null) {
                if (reglementCaisse.getrEGNumTicketPart() > 0)
                    tikNum = context.getString(R.string.paiement_Partiel);
                else{
                    if(reglementCaisse.getrEGCRemarque().equals("Regler Acpt"))
                        tikNum = "Reglement Libre";
                    else tikNum = reglementCaisse.getrEGCNumTicket() > 0 ? String.valueOf(reglementCaisse.rEGCNumTicket) : context.getString(R.string.credit_label);
                }

            } else{
                if(reglementCaisse.getrEGCRemarque().equals("Regler Acpt"))
                    tikNum = "Reglement Libre";
                else tikNum = reglementCaisse.getrEGCNumTicket() > 0 ? String.valueOf(reglementCaisse.rEGCNumTicket) : context.getString(R.string.credit_label);
            }


        }


        Date today = DateUtils.strToDate(reglementCaisse.getrEGCDateReg(), "yyyy-MM-dd HH:mm");
        DateFormat date = new SimpleDateFormat("dd/MM/yyyy");
        DateFormat time = new SimpleDateFormat("HH:mm");
        printText("Reglement" , 1, 1, true, false, 0);
        printText(tikNum, 1, 1, false, false, 0);
        printText("Date: " + date.format(today) + " " + time.format(today) + " ", 0, 1, false, false, 1);




        if (etablisement != null) {

            if (etablisement.getDesgEt() != null) {
                if (etablisement.getDesgEt().length() > 16) {
                    String[] stationDesignations = etablisement.getDesgEt().split(" ");
                    StringBuilder stationDesignation = new StringBuilder();
                    List<String> finalList = new ArrayList<>();
                    for (int i = 0; i < stationDesignations.length; i++) {
                        if ((stationDesignation.length() + stationDesignations[i].length()) <= 16 && i != stationDesignations.length - 1) {
                            stationDesignation.append(stationDesignations[i]).append(" ");
                        } else if ((stationDesignation.length() + stationDesignations[i].length()) <= 16 && i == stationDesignations.length - 1) {
                            finalList.add(stationDesignation.toString());
                            stationDesignation = new StringBuilder();
                            stationDesignation.append(stationDesignations[i]).append(" ");
                            finalList.add(stationDesignation.toString());
                        } else if ((stationDesignation.length() + stationDesignations[i].length()) > 16 && i == stationDesignations.length - 1) {
                            finalList.add(stationDesignation.toString());
                            stationDesignation = new StringBuilder();
                            stationDesignation.append(stationDesignations[i]).append(" ");
                            finalList.add(stationDesignation.toString());
                        } else {
                            finalList.add(stationDesignation.toString());
                            stationDesignation = new StringBuilder();
                            stationDesignation.append(stationDesignations[i]).append(" ");
                        }
                    }

                    for (String designation : finalList) {
                        printText(String.format(" " + designation + " "), 1, 1, false, false, 0);
                    }
                } else {
                    printText(String.format(" " + etablisement.getDesgEt() + " "), 1, 1, true, false, 0);
                }
            }
            else printText(String.format(" " + "N/A" + " "), 2, 1, true, false, 0);


            if (!prefUtils.getEntrepriseIcon().equals(DEFAULT_VALUE) && !prefUtils.getEntrepriseIcon().equals(""))  printImage(context);




            //   printText(printTwoData("Adresse: " + (etablisement.getEtAdresse() != null ? etablisement.getEtAdresse() : "N/A"),  "Tel: "+  (etablisement.getEtTel1() != null ? etablisement.getEtTel1() : "N/A")), 0, 0, false, false, 0);
            printText(printTwoData("Adresse: " ,  etablisement.getEtAdresse() != null ? etablisement.getEtAdresse() : "N/A"), 0, 0, false, false, 0);
            printText(printTwoData("Tel: ",    etablisement.getEtTel1() != null ? etablisement.getEtTel1() : "N/A"), 0, 0, false, false, 0);
            printText(printTwoData("MF: ",    etablisement.getEtMAtriculeF() != null ? etablisement.getEtMAtriculeF() : "N/A"), 0, 0, false, false, 0);



            // printText(String.format(etablisement.getEtMAtriculeF() != null ? etablisement.getEtMAtriculeF() : "N/A" + " "), 2, 1, false, false, 0);

        }

        printLineSeparator();


        //   printLineSeparator();

        try {
            // printText("Client: " + ticket.getTicket().gettIKNomClient(), 1, 1, true, false, 0);

            printText(printTwoData("Client: ", reglementCaisse.getrEGCNomPrenom()), 0, 0, false, false, 0);

            printText(printTwoData("Code Client: ", reglementCaisse.getrEGCCodeClient()), 0, 0, false, false, 0);
            //  printText(printTwoData("MF Client: ", (client.getCLI_MatFisc() != null ? client.getCLI_MatFisc() : "N/A")), 0, 0, false, false, 0);

            printText(printTwoData("MF Client: ", (client.getCLI_MatFisc() != null? !client.getCLI_MatFisc().equals("null")?client.getCLI_MatFisc() :"N/A" : "N/A")), 0, 0, false, false, 0);

            printText(printTwoData("Tel Client: ", (client.getcLITel1() != null ? client.getcLITel1() : "N/A")), 0, 0, false, false, 0);
            printText(printTwoData("Adresse Client: ", String.format(client.cLIAdresse != null ? client.cLIAdresse : "N/A")), 0, 0, false, false, 0);

            //  printText(String.format(client.cLIAdresse != null ? client.cLIAdresse : " "), 0, 0, false, false, 0);
        } catch (IOException e) {
            e.printStackTrace();
        }
        printLineSeparator();
        printText(printTwoData("CAISSE: ", reglementCaisse.getrEGCIdCaisse() + " "), 0, 0, false, false, 0);
        printText(printTwoData("Operateur: ", new PrefUtils(context).getUserName() + " "), 0, 0, false, false, 0);
        printLineSeparator();


        if (Math.abs(reglementCaisse.getrEGCMntEspece()) > 0)
            printText(printTwoData("Espece : ", String.format("%s ", StringUtils.priceFormat(reglementCaisse.getrEGCMntEspece()))), 0, 0, true, false, 0);
        if (Math.abs(reglementCaisse.getrEGCMntChQue()) > 0)
            printText(printTwoData("Cheque: ", String.format("%s ", StringUtils.priceFormat(reglementCaisse.getrEGCMntChQue()))), 0, 0, true, false, 0);
        if (Math.abs(reglementCaisse.getrEGCMntTraite()) > 0)
            printText(printTwoData("Tikets Restaurant : ", String.format("%s ", StringUtils.priceFormat(reglementCaisse.getrEGCMntTraite()))), 0, 0, true, false, 0);

        double total = reglementCaisse.getrEGCMntEspece()+ reglementCaisse.getrEGCMntChQue() + reglementCaisse.getrEGCMntTraite();
        printText(printTwoData("Total : ", String.format("%s ", StringUtils.priceFormat(total))), 0, 0, true, false, 0);

        printLineSeparator();

        reset();

        printCachet();

        printText("         ", 0, 0, false, false, 0);

        printText(printTwoData("   ", String.valueOf(versionCode)), 0, 0, false, false, 0);
        printText("         ", 0, 0, false, false, 0);
        printText("         ", 0, 0, false, false, 0);
        printText("         ", 0, 0, false, false, 0);

        printLineSeparator();
        printText("         ", 0, 0, false, false, 0);
        printText( String.format("%s: %s", context.getString(R.string.sold_CLIENT),  Utils.round(client.getSolde(),3)), 0, 1, true, false, 0);
        printText("         ", 0, 0, false, false, 0);
        printLineSeparator();
        printText("         ", 0, 0, false, false, 0);
        printText("         ", 0, 0, false, false, 0);
        printText("         ", 0, 0, false, false, 0);

        Handler handler = new Handler();
        handler.postDelayed(() -> {
            if (bluetoothService != null)
                bluetoothService.stop();
            bluetoothService = null;
        }, 10000);

    }


    @SuppressLint("NewApi")
    public static void printRecapTicket(Activity context, Ticket ticket, List<LigneTicket> lignesTicket) throws IOException {
        byte[] cmd = new byte[3];
        cmd[0] = 0x1b;
        cmd[1] = 0x21;
        cmd[2] |= 0x10;
        bluetoothService.write(new byte[]{0x1B, 97, 1});
        Client client = App.database.clientDAO().getOneByCode(ticket.gettIKCodClt());
        List<Etablisement> etablisements = App.database.etablisementDAO().getAll();
        Etablisement etablisement = null;
        if (etablisements != null && etablisements.size() > 0 && etablisements.get(0) != null) {
            etablisement = etablisements.get(0);
        }






        String Title = "Recap Tickets";



        String NumSpace = new String(new char[Math.round((nbCharAllowed / 2 - Title.length() / 2))]).replace("\0", " ");
        //    printText(String.format(NumSpace + " "), 2, 2, false, false, 0);
        //printText(String.format(NumSpace + Title + " "), 2, 2, false, false, 1);

        Date today = DateUtils.strToDate(ticket.gettIKDateHeureTicket(), "yyyy-MM-dd HH:mm");
        DateFormat date = new SimpleDateFormat("dd/MM/yyyy");
        DateFormat time = new SimpleDateFormat("HH:mm");
        printText(Title , 1, 1, true, false, 0);
        printText(ticket.gettIK_NumeroBL(), 0, 1, true, false, 0);




        if (etablisement != null) {

            if (etablisement.getDesgEt() != null) {
                if (etablisement.getDesgEt().length() > 16) {
                    String[] stationDesignations = etablisement.getDesgEt().split(" ");
                    StringBuilder stationDesignation = new StringBuilder();
                    List<String> finalList = new ArrayList<>();
                    for (int i = 0; i < stationDesignations.length; i++) {
                        if ((stationDesignation.length() + stationDesignations[i].length()) <= 16 && i != stationDesignations.length - 1) {
                            stationDesignation.append(stationDesignations[i]).append(" ");
                        } else if ((stationDesignation.length() + stationDesignations[i].length()) <= 16 && i == stationDesignations.length - 1) {
                            finalList.add(stationDesignation.toString());
                            stationDesignation = new StringBuilder();
                            stationDesignation.append(stationDesignations[i]).append(" ");
                            finalList.add(stationDesignation.toString());
                        } else if ((stationDesignation.length() + stationDesignations[i].length()) > 16 && i == stationDesignations.length - 1) {
                            finalList.add(stationDesignation.toString());
                            stationDesignation = new StringBuilder();
                            stationDesignation.append(stationDesignations[i]).append(" ");
                            finalList.add(stationDesignation.toString());
                        } else {
                            finalList.add(stationDesignation.toString());
                            stationDesignation = new StringBuilder();
                            stationDesignation.append(stationDesignations[i]).append(" ");
                        }
                    }

                    for (String designation : finalList) {
                        printText(String.format(" " + designation + " "), 1, 1, false, false, 0);
                    }
                } else {
                    printText(String.format(" " + etablisement.getDesgEt() + " "), 1, 1, true, false, 0);
                }
            }
            else printText(String.format(" " + "N/A" + " "), 2, 1, true, false, 0);


            if (!prefUtils.getEntrepriseIcon().equals(DEFAULT_VALUE) && !prefUtils.getEntrepriseIcon().equals(""))  printImage(context);




            //   printText(printTwoData("Adresse: " + (etablisement.getEtAdresse() != null ? etablisement.getEtAdresse() : "N/A"),  "Tel: "+  (etablisement.getEtTel1() != null ? etablisement.getEtTel1() : "N/A")), 0, 0, false, false, 0);
            printText(printTwoData("Adresse: " ,  etablisement.getEtAdresse() != null ? etablisement.getEtAdresse() : "N/A"), 0, 0, false, false, 0);
            printText(printTwoData("Tel: ",    etablisement.getEtTel1() != null ? etablisement.getEtTel1() : "N/A"), 0, 0, false, false, 0);
            printText(printTwoData("MF: ",    etablisement.getEtMAtriculeF() != null ? etablisement.getEtMAtriculeF() : "N/A"), 0, 0, false, false, 0);



            // printText(String.format(etablisement.getEtMAtriculeF() != null ? etablisement.getEtMAtriculeF() : "N/A" + " "), 2, 1, false, false, 0);

        }


        printLineSeparator();
        printText(printTwoData("CAISSE: ", ticket.gettIKIdSCaisse() + " "), 0, 0, false, false, 0);
        printText(printTwoData("Operateur: ", new PrefUtils(context).getUserName() + " "), 0, 0, false, false, 0);
        printLineSeparator();




        printTicketTable(0, lignesTicket);



        double total = ticket.gettIKMtTTC() + ticket.tIKMtRemise;
        System.out.println("total " + total);

        printText(printTwoData("Montant Total : ", String.format("%s ", StringUtils.priceFormat(total))), 0, 0, true, false, 0);
        if(ticket.gettIKMtRemise()>0.0)     printText(printTwoData("Total Remise : ", String.format("%s ", "-" + StringUtils.priceFormat(ticket.gettIKMtRemise()))), 0, 0, true, false, 0);




        printText(printTwoData(context.getString(R.string.montantapresremise), String.format("%s ", StringUtils.priceFormat(ticket.gettIKMtTTC()))), 0, 0, true, false, 0);


        printText(printTwoData("Net Payer : ", StringUtils.priceFormat(ticket.gettIKMtTTC() ) + " "), 0, 0, true, false, 0);
        if (ticket.gettIKMtCredit() > 0) {
            printText(printTwoData("Credit : ", String.format("%s ", StringUtils.priceFormat(ticket.gettIKMtCredit()))), 0, 0, true, false, 0);
        }
        if (ticket.gettIKMtEspece() > 0) {
            // printText(printTwoData("Espece : ", String.format("%s ", StringUtils.priceFormat(ticket.getReglement().rEGCMntEspeceRecue))), 0, 0, true, false, 0);
            printText(printTwoData("Espece : ", String.format("%s ", StringUtils.priceFormat(ticket.gettIKMtEspece()))), 0, 0, true, false, 0);
        }


        if (Double.parseDouble(ticket.gettIKMtCheque()) > 0) {
            printText(printTwoData("Cheque: ", String.format("%s ", StringUtils.priceFormat(Double.parseDouble(ticket.gettIKMtCheque())))), 0, 0, true, false, 0);
        }

        if (ticket.gettIKMtrecue() > 0) {
            printText(printTwoData("Tikets Restaurant : ", String.format("%s ", StringUtils.priceFormat(ticket.gettIKMtrecue()))), 0, 0, true, false, 0);
        }




        printLineSeparator();

        reset();


        printText("         ", 0, 0, false, false, 0);

        printText(printTwoData("   ", String.valueOf(versionCode)), 0, 0, false, false, 0);
        printText("         ", 0, 0, false, false, 0);
        printText("         ", 0, 0, false, false, 0);
        printText("         ", 0, 0, false, false, 0);
        Handler handler = new Handler();
        handler.postDelayed(() -> {
            if (bluetoothService != null)
                bluetoothService.stop();
            bluetoothService = null;
        }, 10000);

    }


    @SuppressLint("NewApi")
    public static void printRecapRecipe(Activity context, RecetteByReglementResponseModel recetteByReglementResponseModel, boolean credit) throws IOException {
        byte[] cmd = new byte[3];
        boolean invoiced = false;
        String numFact = "";
        cmd[0] = 0x1b;
        cmd[1] = 0x21;
        cmd[2] |= 0x10;
        bluetoothService.write(new byte[]{0x1B, 97, 1});

        printText("         ", 0, 0, false, false, 0);
        printText( context.getResources().getString(R.string.recap_recette_title), 1, 1, true, false, 0);
        printText("         ", 0, 0, false, false, 0);

        printText(printTwoData(context.getResources().getString(R.string.station) + " ", prefUtils.getUserStationId()), 0, 0, false, false, 0);
        printText(printTwoData(context.getResources().getString(R.string.user) + " ", prefUtils.getUserName()), 0, 0, false, false, 0);


        printText("         ", 0, 0, false, false, 0);

        printText(printTwoData(context.getResources().getString(R.string.session_title) + " ", App.prefUtils.getCaisseCode()), 0, 0, false, false, 0);

        printText(printTwoData(context.getResources().getString(R.string.date_created_session) + " ", App.prefUtils.getSessionDateOuv()), 0, 0, false, false, 0);
        printText(printTwoData(context.getResources().getString(R.string.date_open_session) + " ", App.prefUtils.getSessionDateOuv()), 0, 0, false, false, 0);
        printText(printTwoData(context.getResources().getString(R.string.date_cloture_session) + " ", (App.prefUtils.getClotSess().toString().length() < 15 ? "-" : String.valueOf(App.prefUtils.getClotSess()))), 0, 0, false, false, 0);


        printLineSeparator();

        printText("         ", 0, 0, false, false, 0);

        printLineSeparator();

        String fondCaisse = recetteByReglementResponseModel.getFondCaisse();
        String depenses = recetteByReglementResponseModel.getDepCaisse();

        String espece = recetteByReglementResponseModel.getMntEspece();
        String cheque = recetteByReglementResponseModel.getMntCheque();
        String traite = recetteByReglementResponseModel.getMntTraite();
        String bonAchat = recetteByReglementResponseModel.getMntBonAchat();

        String totalRecette = recetteByReglementResponseModel.getTotalRecette();
        String totalCaisse = recetteByReglementResponseModel.getTotalCaisse();

        try {
            fondCaisse = StringUtils.priceFormat(Double.parseDouble(recetteByReglementResponseModel.getFondCaisse()));
            depenses = StringUtils.priceFormat(Double.parseDouble(recetteByReglementResponseModel.getDepCaisse()));

            espece = StringUtils.priceFormat(Double.parseDouble(recetteByReglementResponseModel.getMntEspece()));
            cheque = StringUtils.priceFormat(Double.parseDouble(recetteByReglementResponseModel.getMntCheque()));
            traite = StringUtils.priceFormat(Double.parseDouble(recetteByReglementResponseModel.getMntTraite()));
            bonAchat = StringUtils.priceFormat(Double.parseDouble(recetteByReglementResponseModel.getMntBonAchat()));

            totalRecette = StringUtils.priceFormat(Double.parseDouble(recetteByReglementResponseModel.getTotalRecette()));
            totalCaisse = StringUtils.priceFormat(Double.parseDouble(recetteByReglementResponseModel.getTotalCaisse()));

        } catch (Exception e) {

        }
        if(BIG == nbCharAllowed) {
            printText("    |"+context.getResources().getString(R.string.fonds_de_caisse)+" |      "+ fondCaisse /*+"     | "*/, 0, 0, false, false, 0);
            printText("    ------------------------------------", 0, 0, false, false, 0);
            printText("    |   "+context.getResources().getString(R.string.depenses)+"    |      "+ depenses /*+"     | "*/, 0, 0, false, false, 0);
            printText("    ------------------------------------", 0, 0, false, false, 0);

            printText("         ", 0, 0, false, false, 0);

            printText("    ------------------------------------", 0, 0, false, false, 0);
            printText("    |    "+context.getResources().getString(R.string.espece)+"     |      "+ espece /*+"     | "*/, 0, 0, false, false, 0);
            printText("    ------------------------------------", 0, 0, false, false, 0);
            printText("    |    "+context.getResources().getString(R.string.cheque)+"     |      "+ cheque /*+"     | "*/, 0, 0, false, false, 0);
            printText("    ------------------------------------", 0, 0, false, false, 0);
            printText("    |  "+context.getResources().getString(R.string.carte_resto)+"   |      "+ traite /*+"     | "*/, 0, 0, false, false, 0);
            printText("    ------------------------------------", 0, 0, false, false, 0);
            printText("    |   "+context.getResources().getString(R.string.bon_achat)+"    |      "+ bonAchat /*+"     | "*/, 0, 0, false, false, 0);
            printText("    ------------------------------------", 0, 0, false, false, 0);


            printText("         ", 0, 0, false, false, 0);
            printText("         ", 0, 0, false, false, 0);
            printText("--------------------------------------------- ", 0, 0, false, false, 0);

        }
        else if(SMALL == nbCharAllowed) {

            printText("    |"+context.getResources().getString(R.string.fonds_de_caisse)+" |      "+ fondCaisse /*+"     | "*/, 0, 0, false, false, 0);
            printText("    --------------------------", 0, 0, false, false, 0);
            printText("    |   "+context.getResources().getString(R.string.depenses)+"    |      "+ depenses /*+"     | "*/, 0, 0, false, false, 0);
            printText("    --------------------------", 0, 0, false, false, 0);

            printText("         ", 0, 0, false, false, 0);

            printText("    --------------------------", 0, 0, false, false, 0);
            printText("    |    "+context.getResources().getString(R.string.espece)+"     |      "+ espece /*+"     | "*/, 0, 0, false, false, 0);
            printText("    --------------------------", 0, 0, false, false, 0);
            printText("    |    "+context.getResources().getString(R.string.cheque)+"     |      "+ cheque /*+"     | "*/, 0, 0, false, false, 0);
            printText("    --------------------------", 0, 0, false, false, 0);
            printText("    |  "+context.getResources().getString(R.string.carte_resto)+"   |      "+ traite /*+"     | "*/, 0, 0, false, false, 0);
            printText("    --------------------------", 0, 0, false, false, 0);
            printText("    |   "+context.getResources().getString(R.string.bon_achat)+"    |      "+ bonAchat /*+"     | "*/, 0, 0, false, false, 0);
            printText("    --------------------------", 0, 0, false, false, 0);


            printText("         ", 0, 0, false, false, 0);
            printText("         ", 0, 0, false, false, 0);
            printText("---------------------------- ", 0, 0, false, false, 0);

        }



        printText(printTwoData(context.getResources().getString(R.string.total_recette) + " ", totalRecette), 0, 0, true, false, 0);
        printText(printTwoData(context.getResources().getString(R.string.total_caisse) + " ", totalCaisse), 0, 0, true, false, 0);

        printText("         ", 0, 0, false, false, 0);
        printText("         ", 0, 0, false, false, 0);
        printText("         ", 0, 0, false, false, 0);

        Handler handler = new Handler();
        handler.postDelayed(() -> {
            if (bluetoothService != null)
                bluetoothService.stop();
            bluetoothService = null;
        }, 10000);

    }


    @SuppressLint("NewApi")
    public static void printRecapDetailed(Activity context, TicketWithLinesAndPayments ticket, boolean credit, List<Ticket> ticketList, List<List<LigneTicket>> ligneTicketList) throws IOException {
        byte[] cmd = new byte[3];
        boolean invoiced = false;
        String numFact = "";
        cmd[0] = 0x1b;
        cmd[1] = 0x21;
        cmd[2] |= 0x10;
        bluetoothService.write(new byte[]{0x1B, 97, 1});

        printText("         ", 0, 0, false, false, 0);
        printText(context.getResources().getString(R.string.recap_detailed_title), 1, 1, true, false, 0);
        printText("         ", 0, 0, false, false, 0);

        printText(context.getResources().getString(R.string.station) + " " + prefUtils.getUserStationId(), 0, 1, true, false, 0);
        printText(context.getResources().getString(R.string.user) + " " + prefUtils.getUserName(), 0, 1, true, false, 0);


        printText(context.getResources().getString(R.string.date_creation_recap) + Utils.returnCurrentDate() + " ", 0, 1, false, false, 0);

        printText("         ", 0, 0, false, false, 0);

        for (int position = 0; position < ticketList.size(); position++) {

            if (ticketList.get(position).tIKNumeroBL!=null){
                invoiced = true;
                numFact = ticketList.get(position).tIKNumeroBL;
            }


            String num = context.getResources().getString(R.string.number_field_recap) + " " + (!invoiced ? ticketList.get(position).tikNumTicketM : numFact);

            List<Etablisement> etablisements = App.database.etablisementDAO().getAll();
            Etablisement etablisement = null;
            if (etablisements != null && etablisements.size() > 0 && etablisements.get(0) != null) {
                etablisement = etablisements.get(0);
            }

            printLineSeparator();


            String Title = !invoiced ? context.getResources().getString(R.string.bon_livraison_recap) : context.getResources().getString(R.string.facture_recap);

            printText(Title, 1, 1, false, false, 0);
            printText(num, 0, 1, true, false, 1);

            if (etablisement != null) {
                printText(context.getResources().getString(R.string.date_recap) + " " + ticketList.get(position).gettIKDateHeureTicket() + " ", 0, 1, false, false, 0);

            }

            printText(printTwoData(context.getResources().getString(R.string.client_recap) + " ", ticketList.get(position).gettIKNomClient()), 0, 0, false, false, 0);
            try {
                printText(printTwoData(context.getResources().getString(R.string.code_client_recap) + " ", ticketList.get(position).gettIKCodClt() + " "), 0, 0, false, false, 0);

                printText(printTwoData(context.getResources().getString(R.string.amount_ttc_recap) + " ", StringUtils.priceFormat(ticketList.get(position).gettIKMtTTC()) + " "), 0, 0, true, false, 0);

            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        printText("         ", 0, 0, false, false, 0);
        printText("         ", 0, 0, false, false, 0);
        printText("         ", 0, 0, false, false, 0);

        Handler handler = new Handler();
        handler.postDelayed(() -> {
            if (bluetoothService != null)
                bluetoothService.stop();
            bluetoothService = null;
        }, 10000);

    }


    public static void printBonCommandeTable(double dEVRemise, List<LigneBonCommande> ligneBonCommandes) throws IOException {
        boolean printTax = PrefUtils.getPrintArticleTax();


        if(BIG == nbCharAllowed) {
            if(dEVRemise>0.0)
                printText("QTE            P.U          R%       TOTAL    ", 0, 1, true, false, 0);
            else
                printText("QTE              P.U                 TOTAL    ", 0, 1, true, false, 0);

            printLineSeparator();

            for (LigneBonCommande ligneTicket : ligneBonCommandes ) {
                String prixHT = "";
                if(printTax) prixHT = " (HT: "+ StringUtils.priceFormat(Double.parseDouble(ligneTicket.getLGDEVNetht())) + ")";

                String qte = (StringUtils.decimalFormat(Double.parseDouble(ligneTicket.getLGDEVQte())) + "").replace(".0", "");
                //String label = ((ligneTicket.getArticle().getaRTDesignation().length() > 16) ? ligneTicket.getArticle().getaRTDesignation().substring(0, 16) : ligneTicket.getArticle().getaRTDesignation());
                String label = ligneTicket.getArticle().getaRTDesignation() + prixHT;

                printText(printTwoData( label,  " "), 0, 0, true, false, 0);

                if(dEVRemise>0.0)
                    printText(printFourData(qte,
                            StringUtils.priceFormat(Double.parseDouble(ligneTicket.getLGDEVPUTTC())),
                            String.valueOf(((double) Double.parseDouble(ligneTicket.getLGDEVRemise()))),
                            StringUtils.priceFormat(Double.parseDouble(ligneTicket.getLGDEVMntTTC()))
                    ), 0, 0, false, false, 0);
                else
                    printText(printThreeData(qte,
                            StringUtils.priceFormat(Double.parseDouble(ligneTicket.getLGDEVPUTTC())),
                            StringUtils.priceFormat(Double.parseDouble(ligneTicket.getLGDEVMntTTC()))
                    ), 0, 0, false, false, 0);

                printLineSeparator();
            }

        }
        else if(SMALL == nbCharAllowed) {

            printText("QTE          P.U           TOTAL", 0, 0, true, false, 0);

            printLineSeparator();

            for (LigneBonCommande ligneTicket : ligneBonCommandes ) {
                String prixHT = "";
                if(printTax) prixHT = " (HT: "+ StringUtils.priceFormat(Double.parseDouble(ligneTicket.getLGDEVNetht())) + ")";

                String qte = (StringUtils.decimalFormat(Double.parseDouble(ligneTicket.getLGDEVQte())) + "").replace(".0", "");
                //String label = ((ligneTicket.getArticle().getaRTDesignation().length() > 16) ? ligneTicket.getArticle().getaRTDesignation().substring(0, 16) : ligneTicket.getArticle().getaRTDesignation());
                String label = ligneTicket.getArticle().getaRTDesignation() + prixHT;

                String remise ="";

                if(dEVRemise>0) remise = "-"+ Double.parseDouble(ligneTicket.getLGDEVRemise()) + "%";

                printText(printTwoData( label + remise,  " "), 0, 0, true, false, 0);


                printText(printThreeData(qte,
                        StringUtils.priceFormat(Double.parseDouble(ligneTicket.getLGDEVPUTTC())),
                        StringUtils.priceFormat(Double.parseDouble(ligneTicket.getLGDEVMntTTC()))
                ), 0, 0, false, false, 0);

                printLineSeparator();
            }

        }

    }

    @SuppressLint("NewApi")
    public static void printBonCommande(Activity context, BonCommande bonCommande, List<LigneBonCommande> ligneBonCommandes, boolean original) throws IOException {

        byte[] cmd = new byte[3];
        cmd[0] = 0x1b;
        cmd[1] = 0x21;
        cmd[2] |= 0x10;
        bluetoothService.write(new byte[]{0x1B, 97, 1});
        Client client = App.database.clientDAO().getOneByCode(bonCommande.getDEVCodeClient());
        printBcHeader(bonCommande,context, client);


        double dEVRemise = Double.parseDouble(bonCommande.getDEVRemise());

         printBonCommandeTable(dEVRemise, ligneBonCommandes);


        printLineSeparator();

        printText(printTwoData("Total Articles:", String.valueOf(ligneBonCommandes.size())), 0, 0, true, false, 0);

        printLineSeparator();

        if(Double.parseDouble(bonCommande.getDEVMntTva())>0)
            printText(printTwoData("Montant TVA : ", String.format("%s ", StringUtils.priceFormat(Double.parseDouble(bonCommande.getDEVMntTva())))), 0, 0, true, false, 0);

//        if(client.cltMntRevImp> 0.0)   {
//         double mntRedevance = Double.parseDouble(bonCommande.getDEVMntTTC()) - (Double.parseDouble(bonCommande.getDEVMntTTC()) / (1+ client.cltMntRevImp/ 100));
//            printText(printTwoData("Redevance : ", String.format("%s ", StringUtils.priceFormat(mntRedevance))), 0, 0, true, false, 0);
//        }

        printText(printTwoData("Montant Total : ", String.format("%s ", StringUtils.priceFormat(Double.parseDouble(bonCommande.getDEVMntTTC())))), 0, 0, true, false, 0);
        //   printText(printTwoData("Montant Total : ", String.format("%s ", StringUtils.priceFormat((Double.parseDouble(bonCommande.getDEVMntTTC()))))), 0, 0, true, false, 0);
        if(dEVRemise>0.0)    printText(printTwoData("Total Remise : ", String.format("%s ", StringUtils.priceFormat(dEVRemise))), 0, 0, true, false, 0);
        printText(printTwoData("Net A Payer : ", StringUtils.priceFormat(Double.parseDouble(bonCommande.getDEVMntTTC()) - Double.parseDouble(bonCommande.getDEVRemise())) + " "), 0, 0, true, false, 0);


        reset();
      printCachet();
        printText("         ", 0, 0, false, false, 0);

        printText(printTwoData("   ", String.valueOf(versionCode)), 0, 0, false, false, 0);
        printText("         ", 0, 0, false, false, 0);
        printText("         ", 0, 0, false, false, 0);
        printText("         ", 0, 0, false, false, 0);

        Handler handler = new Handler();
        handler.postDelayed(() -> {
            if (bluetoothService != null)
                bluetoothService.stop();
            bluetoothService = null;
        }, 10000);

    }

    private static void printBcHeader(BonCommande bonCommande, Context context, Client client) throws IOException {

        String num = "Num : ";

        if(bonCommande.isSync) num= num+bonCommande.getDEVNum()+ "";
        else{
            num= num+bonCommande.getDevCodeM()+ "";

            if(num.contains("_")){
                String[] numTicket = num.split("_");
                num ="DEV_M_"+ numTicket[2]+"_"+numTicket[3]+"_"+numTicket[5];
            }
        }





        String Title = "Bon Commande";

        Date today = DateUtils.strToDate(bonCommande.getDEVDate(), " yyyy-MM-dd HH:mm");
        DateFormat date = new SimpleDateFormat("dd/MM/yyyy");
        DateFormat time = new SimpleDateFormat("HH:mm");

        printText(Title, 1, 1, true, false, 0);
        printText(num, 1, 1, false, false, 0);
        printText("Date: " + date.format(today) + " " + time.format(today) + " ", 0, 1, false, false, 1);


        List<Etablisement> etablisements = App.database.etablisementDAO().getAll();
        Etablisement etablisement = null;
        if (etablisements.get(0) != null) {
            etablisement = etablisements.get(0);



            if (etablisement.getDesgEt() != null) {
                if (etablisement.getDesgEt().length() > 16) {
                    String[] stationDesignations = etablisement.getDesgEt().split(" ");
                    StringBuilder stationDesignation = new StringBuilder();
                    List<String> finalList = new ArrayList<>();
                    for (int i = 0; i < stationDesignations.length; i++) {
                        if ((stationDesignation.length() + stationDesignations[i].length()) <= 16 && i != stationDesignations.length - 1) {
                            stationDesignation.append(stationDesignations[i]).append(" ");
                        } else if ((stationDesignation.length() + stationDesignations[i].length()) <= 16 && i == stationDesignations.length - 1) {
                            finalList.add(stationDesignation.toString());
                            stationDesignation = new StringBuilder();
                            stationDesignation.append(stationDesignations[i]).append(" ");
                            finalList.add(stationDesignation.toString());
                        } else if ((stationDesignation.length() + stationDesignations[i].length()) > 16 && i == stationDesignations.length - 1) {
                            finalList.add(stationDesignation.toString());
                            stationDesignation = new StringBuilder();
                            stationDesignation.append(stationDesignations[i]).append(" ");
                            finalList.add(stationDesignation.toString());
                        } else {
                            finalList.add(stationDesignation.toString());
                            stationDesignation = new StringBuilder();
                            stationDesignation.append(stationDesignations[i]).append(" ");
                        }
                    }

                    for (String designation : finalList) {
                        printText(String.format(" " + designation + " "), 1, 1, true, false, 0);
                    }

                } else {
                    printText(String.format(" " + etablisement.getDesgEt() + " "), 1, 1, true, false, 0);
                }
            } else printText(String.format(" N/A "  + " "), 2, 1, true, false, 0);

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                if (!prefUtils.getEntrepriseIcon().equals(DEFAULT_VALUE)&& !prefUtils.getEntrepriseIcon().equals(""))  printImage(context);
            }

            printText(printTwoData("Adresse: " ,  etablisement.getEtAdresse() != null ? etablisement.getEtAdresse() : "N/A"), 0, 0, false, false, 0);
            printText(printTwoData("Tel: ",  etablisement.getEtTel1() != null ? etablisement.getEtTel1() : "N/A"), 0, 0, false, false, 0);
            printText(printTwoData("MF: ",  etablisement.getEtMAtriculeF() != null ? etablisement.getEtMAtriculeF() : "N/A" ), 0, 0, false, false, 0);
        }


        printLineSeparator();


        try {
            printText(printTwoData("Client: ", bonCommande.getDEVClient() + " "), 0, 0, false, false, 0);

            printText(printTwoData("Code Client: ", client.cLICode + " "), 0, 0, false, false, 0);
            printText(printTwoData("MF Client: ", (client.getCLI_MatFisc() != null? !client.getCLI_MatFisc().equals("null")?client.getCLI_MatFisc() :"N/A" : "N/A") + " "), 0, 0, false, false, 0);
            printText(printTwoData("Tel Client: ", (client.getcLITel1() != null ? client.getcLITel1() : "N/A") + " "), 0, 0, false, false, 0);
            printText(printTwoData("Adresse Client: ", String.format(client.cLIAdresse != null ? client.cLIAdresse : "N/A")+ " "), 0, 0, false, false, 0);

        } catch (IOException e) {
            e.printStackTrace();
        }
        printLineSeparator();
        printText(printTwoData("Operateur: ", prefUtils.getUserName() + " "), 0, 0, false, false, 0);
        printLineSeparator();

    }

    public static void printBonRetour(Activity context, BonRetour bonCommande, List<LigneBonRetour> ligneBonCommandes, boolean original) throws IOException {

        byte[] cmd = new byte[3];
        cmd[0] = 0x1b;
        cmd[1] = 0x21;
        cmd[2] |= 0x10;
        bluetoothService.write(new byte[]{0x1B, 97, 1});
        Client client = App.database.clientDAO().getOneByCode(bonCommande.getBORCodefrs());
        List<Etablisement> etablisements = App.database.etablisementDAO().getAll();
        Etablisement etablisement;

        String num = "Num : " + bonCommande.getBORNumero() + "";

        String Title = "Bon Retour";



        printText(Title, 1, 1, false, false, 0);



        Date today = DateUtils.strToDate(bonCommande.getBORDate(), "yyyy-MM-dd HH:mm");
        DateFormat date = new SimpleDateFormat("dd/MM/yyyy");
        DateFormat time = new SimpleDateFormat("HH:mm");

        printText(num, 1, 1, false, false, 1);

        printText("Date: " + date.format(today) + " " + time.format(today) + " ", 0, 1, false, false, 0);


        if (etablisements.get(0) != null) {
            etablisement = etablisements.get(0);
            if (etablisement.getDesgEt() != null) {
                if (etablisement.getDesgEt().length() > 16) {
                    String[] stationDesignations = etablisement.getDesgEt().split(" ");
                    StringBuilder stationDesignation = new StringBuilder();
                    List<String> finalList = new ArrayList<>();
                    for (int i = 0; i < stationDesignations.length; i++) {
                        if ((stationDesignation.length() + stationDesignations[i].length()) <= 16 && i != stationDesignations.length - 1) {
                            stationDesignation.append(stationDesignations[i]).append(" ");
                        } else if ((stationDesignation.length() + stationDesignations[i].length()) <= 16 && i == stationDesignations.length - 1) {
                            finalList.add(stationDesignation.toString());
                            stationDesignation = new StringBuilder();
                            stationDesignation.append(stationDesignations[i]).append(" ");
                            finalList.add(stationDesignation.toString());
                        } else if ((stationDesignation.length() + stationDesignations[i].length()) > 16 && i == stationDesignations.length - 1) {
                            finalList.add(stationDesignation.toString());
                            stationDesignation = new StringBuilder();
                            stationDesignation.append(stationDesignations[i]).append(" ");
                            finalList.add(stationDesignation.toString());
                        } else {
                            finalList.add(stationDesignation.toString());
                            stationDesignation = new StringBuilder();
                            stationDesignation.append(stationDesignations[i]).append(" ");
                        }
                    }

                    for (String designation : finalList) {
                        printText(String.format(" " + designation + " "), 1, 1, true, false, 0);
                    }

                } else {
                    printText(String.format(" " + etablisement.getDesgEt() + " "), 1, 1, true, false, 0);
                }
            }
            else printText(String.format(" " + "N/A" + " "), 2, 1, true, false, 0);




            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                if (!prefUtils.getEntrepriseIcon().equals(DEFAULT_VALUE)&& !prefUtils.getEntrepriseIcon().equals(""))  printImage(context);
            }

            printText(printTwoData("Adresse: " ,  etablisement.getEtAdresse() != null ? etablisement.getEtAdresse() : "N/A"), 0, 0, false, false, 0);
            printText(printTwoData("Tel: ",  etablisement.getEtTel1() != null ? etablisement.getEtTel1() : "N/A"), 0, 0, false, false, 0);
            printText(printTwoData("MF: ",  etablisement.getEtMAtriculeF() != null ? etablisement.getEtMAtriculeF() : "N/A" ), 0, 0, false, false, 0);





        }

        printLineSeparator();


        try {
            printText(printTwoData("Client: ", client != null ? client.getcLITel1() + " ": "N/A" + " "), 0, 0, false, false, 0);
            printText(printTwoData("Code Client: ",  client != null ? client.getcLICode()+ " ": "N/A" + " "), 0, 0, false, false, 0);
            printText(printTwoData("Tel Client: ", client != null ?(client.getcLITel1() != null ? client.getcLITel1() + " ": "N/A"+ " ")+ " ": "N/A" + " "), 0, 0, false, false, 0);


        } catch (IOException e) {
            e.printStackTrace();
        }


        printLineSeparator();
        printText(printTwoData("Operateur: ", new PrefUtils(context).getUserName() + " "), 0, 0, false, false, 0);
        printLineSeparator();

        if(BIG == nbCharAllowed) {

            printText("QTE            P.U                 TOTAL    ", 0, 1, true, false, 0);
            printLineSeparator();

            for (LigneBonRetour ligneTicket : ligneBonCommandes) {
                Article article = ligneTicket.article;
                String qte = (StringUtils.decimalFormat(
                        Double.parseDouble(ligneTicket.getLIGBonEntreeQte())) +
                        "").replace(".0", "");
                //String label = ((ligneTicket.getArticle().getaRTDesignation().length() > 16) ? ligneTicket.getArticle().getaRTDesignation().substring(0, 16) : ligneTicket.getArticle().getaRTDesignation());
                String label = ligneTicket.getArticle().getaRTDesignation();

                printText(printTwoData( label,  " "), 0, 0, true, false, 0);

                printText(printThreeData(qte,
                        StringUtils.priceFormat(article.getPvttc()) ,
                        StringUtils.priceFormat(Double.parseDouble(ligneTicket.getLIGBonEntreeMntTTC()))), 0, 0, false, false, 0);

                printLineSeparator();
            }
        }
        else if(SMALL == nbCharAllowed) {

            printText("QTE          P.U           TOTAL", 0, 0, true, false, 0);
            printLineSeparator();

            for (LigneBonRetour ligneTicket : ligneBonCommandes) {
                Article article = ligneTicket.article;
                String qte = (StringUtils.decimalFormat(
                        Double.parseDouble(ligneTicket.getLIGBonEntreeQte())) +
                        "").replace(".0", "");
                //String label = ((ligneTicket.getArticle().getaRTDesignation().length() > 16) ? ligneTicket.getArticle().getaRTDesignation().substring(0, 16) : ligneTicket.getArticle().getaRTDesignation());
                String label = ligneTicket.getArticle().getaRTDesignation();

                printText(printTwoData( label,  " "), 0, 0, true, false, 0);

                printText(printThreeData(qte,
                        StringUtils.priceFormat(article.getPvttc()) ,
                        StringUtils.priceFormat(Double.parseDouble(ligneTicket.getLIGBonEntreeMntTTC()))), 0, 0, false, false, 0);

                printLineSeparator();
            }

        }






        printLineSeparator();
        printText(printTwoData("Montant Total : ", String.format("%s ", StringUtils.priceFormat(
                Double.parseDouble(bonCommande.getBORMntTTC())))), 0, 0, true, false, 0);
        reset();
        printCachet();
        printText("         ", 0, 0, false, false, 0);

        printText(printTwoData("   ", String.valueOf(versionCode)), 0, 0, false, false, 0);
        printText("         ", 0, 0, false, false, 0);
        printText("         ", 0, 0, false, false, 0);
        printText("         ", 0, 0, false, false, 0);
        Handler handler = new Handler();
        handler.postDelayed(() -> {
            if (bluetoothService != null)
                bluetoothService.stop();
            bluetoothService = null;
        }, 10000);

    }


    private static String getTotalTraite(List<TraiteCaisse> traites) {
        double totalTraite = 0.0;
        for (TraiteCaisse traiteCaisse : traites) {
            totalTraite += traiteCaisse.tRAITMontant;
        }
        return totalTraite + "";
    }

    private static String getTotalcheque(List<ChequeCaisse> chequeCaisseList) {
        double totalCheques = 0.0;
        for (ChequeCaisse chequeCaisse : chequeCaisseList) {
            totalCheques += chequeCaisse.getMontant();
        }
        return totalCheques + "";
    }

    @SuppressLint("NewApi")
    public static String printFourData(String quantity, String designation, String price, String amount) {
        StringBuilder sb = new StringBuilder();


        int firstSpacesNb = 13- quantity.length();
        int secondSpaces;
        if(firstSpacesNb<0)
            secondSpaces = 15-designation.length()+firstSpacesNb;
        else secondSpaces = 15-designation.length();


        int thirdSpaces = 13-amount.length();


        String des = designation;
        if (designation.length()>17) des =designation.substring(0, 17);

        //  double totalamount= Double.parseDouble(quantity)*Double.parseDouble(price) ;

        DecimalFormat df = new DecimalFormat("#.000");
        //   df.setMinimumFractionDigits(8);


        sb.append(quantity).append(new String(new char[Math.max(firstSpacesNb, 1)]).replace("\0", " ")).append(
                //  " "+
                des).append(new String(new char[Math.max(secondSpaces, 1)]).replace("\0", " ")).append(
                //  "   "+
                price).append(new String(new char[Math.max(thirdSpaces, 1)]).replace("\0", " ")).append(
                //"   "+
                //     df.format(Utils.round(StringUtils.decimalFormat(Double.parseDouble(amount)),3))   );
                //  Utils.round(StringUtils.decimalFormat(Double.parseDouble(amount)),3)   );
                amount);
        // totalamount   );

        return sb.toString();
    }

    /**
     * ????
     *
     * @param leftText   ????
     * @param middleText ????
     * @param rightText  ????
     * @return
     */
    @SuppressLint("NewApi")
    public static String printThreeData(String leftText, String middleText, String rightText) {


        StringBuilder sb = new StringBuilder();
        // ?????? LEFT_TEXT_MAX_LENGTH ??? + ???
        if (leftText.length() > LEFT_TEXT_MAX_LENGTH) {
            leftText = leftText.substring(0, LEFT_TEXT_MAX_LENGTH) + "..";
        }
        int leftTextLength = getBytesLength(leftText);
        int middleTextLength = getBytesLength(middleText);
        int rightTextLength = getBytesLength(rightText);

        sb.append(leftText);
        // ????????????????
        int marginBetweenLeftAndMiddle = LEFT_LENGTH - leftTextLength - middleTextLength / 2;

        for (int i = 0; i < marginBetweenLeftAndMiddle; i++) {
            sb.append(" ");
        }
        sb.append(middleText);

        // ????????????????
        int marginBetweenMiddleAndRight = RIGHT_LENGTH - middleTextLength / 2 - rightTextLength;

        for (int i = 0; i < marginBetweenMiddleAndRight; i++) {
            sb.append(" ");
        }

        // ???????,??????????????,??????????
        sb.delete(sb.length() - 1, sb.length()).append(rightText);
        return sb.toString();
    }


    /**
     * ??????
     *
     * @param msg
     * @return
     */
    @SuppressLint("NewApi")
    private static int getBytesLength(String msg) {
        return msg.getBytes(Charset.forName(DEFAULT_ENCODING)).length;
    }

}
