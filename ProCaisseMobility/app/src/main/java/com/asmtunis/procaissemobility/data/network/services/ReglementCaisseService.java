package com.asmtunis.procaissemobility.data.network.services;


import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.ReglementCaisse;
import com.asmtunis.procaissemobility.data.models.ReglementUpdate;
import com.asmtunis.procaissemobility.data.models.TicketWithLinesAndPayments;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Headers;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * Created by Achraf on 29/09/2017.
 */

public interface ReglementCaisseService {

    @Headers("User-Agent: android-api-client")
    @POST("getReglementCaisseByTicket")
    Call<List<ReglementCaisse>> getReglementCaisseByTicket(@Body GenericObject genericObject);

    @Headers("User-Agent: android-api-client")
    @POST("getReglementCaisseByTickets")
    Call<List<List<ReglementCaisse>>> getReglementCaisseByTickets(@Body GenericObject genericObject);

    @Headers("User-Agent: android-api-client")
    @POST("getReglementCaisseBySession")
    Call<List<ReglementCaisse>> getReglementCaisseBySession(@Body GenericObject genericObject, @Query("exercice") String exercice, @Query("archive") Boolean archive);

    @Headers("User-Agent: android-api-client")
    @POST("addBatchPayments")
    Call<List<ReglementUpdate>> addBatchPayments(@Body GenericObject genericObject);
}
