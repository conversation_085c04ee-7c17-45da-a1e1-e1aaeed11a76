package com.asmtunis.procaissemobility.data.network.services;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.DELETE;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.PUT;

/**
 * Created by PC on 10/9/2017.
 */

interface BaseService<T> {

    @GET
    Call<List<T>> getList();

    @POST("getByPrimaryKey")
    Call<T> get(@Body T t);

    @POST("getByX")
    @FormUrlEncoded
    Call<List<T>> getByX(@Field("field") String field, @Field("value") String value);

    @POST
    Call<String> add(@Body T t);

    @PUT
    Call<String> update(@Body T t);

    @DELETE
    @FormUrlEncoded
    Call<String> delete(@Field("tagged") String tags);


}
