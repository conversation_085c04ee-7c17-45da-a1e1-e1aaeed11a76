package com.asmtunis.procaissemobility.data.models;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.annotation.NonNull;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

@Entity
public class Reclamation extends BaseModel implements Serializable {
    @PrimaryKey
    @ColumnInfo(name = "Rec_Code")
    @NonNull
    @SerializedName("Rec_Code")
    @Expose
    private String recCode;
    @ColumnInfo(name = "Rec_Date")
    @SerializedName("Rec_Date")
    @Expose
    private String recDate;
    @ColumnInfo(name = "Rec_Note")
    @SerializedName("Rec_Note")
    @Expose
    private String recNote;
    @ColumnInfo(name = "Rec_IdClient")
    @SerializedName("Rec_IdClient")
    @Expose
    private String recIdClient;
    @ColumnInfo(name = "Rec_Image")
    @SerializedName("Rec_Image")
    @Expose
    private String recImage;
    @ColumnInfo(name = "Rec_User")
    @SerializedName("Rec_User")
    @Expose
    private String recUser;

    public Reclamation() {
    }

    public String getRecCode() {
        return recCode;
    }

    public void setRecCode(String recCode) {
        this.recCode = recCode;
    }

    public String getRecDate() {
        return recDate;
    }

    public void setRecDate(String recDate) {
        this.recDate = recDate;
    }

    public String getRecNote() {
        return recNote;
    }

    public void setRecNote(String recNote) {
        this.recNote = recNote;
    }

    public String getRecIdClient() {
        return recIdClient;
    }

    public void setRecIdClient(String recIdClient) {
        this.recIdClient = recIdClient;
    }

    public String getRecImage() {
        return recImage;
    }

    public void setRecImage(String recImage) {
        this.recImage = recImage;
    }

    public String getRecUser() {
        return recUser;
    }

    public void setRecUser(String recUser) {
        this.recUser = recUser;
    }
}
