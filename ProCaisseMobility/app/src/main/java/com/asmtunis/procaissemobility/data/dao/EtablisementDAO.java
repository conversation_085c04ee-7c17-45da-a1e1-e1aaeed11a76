package com.asmtunis.procaissemobility.data.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.Etablisement;

import java.util.List;

@Dao
public interface EtablisementDAO {

    @Query("SELECT * FROM Etablisement")
    List<Etablisement> getAll();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(Etablisement item);

    @Query("DELETE FROM Etablisement")
    void deleteAll();
}
