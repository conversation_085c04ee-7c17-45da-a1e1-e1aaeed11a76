package com.asmtunis.procaissemobility.data.network.services;

import com.asmtunis.procaissemobility.data.models.EtatOrdreMission;
import com.asmtunis.procaissemobility.data.models.GenericObject;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

public interface EtatOrdreMissionService {
    @POST("displayetatordremission")
    Call<List<EtatOrdreMission>> getEtatOrdreMission(@Body GenericObject genericObject);
}
