package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

@Entity(primaryKeys =
        {"Parent_CodeBar", "Fils_CodeBar"})
public class ArticleCodeBar extends BaseModel implements Serializable {
    @NonNull
    @ColumnInfo(name = "Parent_CodeBar")
    @SerializedName("Parent_CodeBar")
    @Expose
    public String parentCodeBar;
    @NonNull
    @ColumnInfo(name = "Fils_CodeBar")
    @SerializedName("Fils_CodeBar")
    @Expose
    public String filsCodeBar;
    @ColumnInfo(name = "cod_b_user")
    @SerializedName("cod_b_user")
    @Expose
    @Ignore
    public String codBUser;
    @ColumnInfo(name = "cod_b_station")
    @SerializedName("cod_b_station")
    @Expose
    @Ignore
    public String codBStation;
    @ColumnInfo(name = "cod_b_export")
    @SerializedName("cod_b_export")
    @Expose
    public String codBExport;
    @ColumnInfo(name = "cod_b_DDm")
    @SerializedName("cod_b_DDm")
    @Expose
    @Ignore
    public String codBDDm;
    @ColumnInfo(name = "cod_Qte")
    @SerializedName("cod_Qte")
    @Expose
    public double cod_Qte;

    public ArticleCodeBar() {
    }

    public ArticleCodeBar(String status, boolean isSync, @NonNull String parentCodeBar, @NonNull String filsCodeBar) {
        super(status, isSync);
        this.parentCodeBar = parentCodeBar;
        this.filsCodeBar = filsCodeBar;
    }

    public String getParentCodeBar() {
        return parentCodeBar;
    }

    public void setParentCodeBar(String parentCodeBar) {
        this.parentCodeBar = parentCodeBar;
    }

    public String getFilsCodeBar() {
        return filsCodeBar;
    }

    public void setFilsCodeBar(String filsCodeBar) {
        this.filsCodeBar = filsCodeBar;
    }

    public String getCodBUser() {
        return codBUser;
    }

    public void setCodBUser(String codBUser) {
        this.codBUser = codBUser;
    }

    public String getCodBStation() {
        return codBStation;
    }

    public void setCodBStation(String codBStation) {
        this.codBStation = codBStation;
    }

    public String getCodBExport() {
        return codBExport;
    }

    public void setCodBExport(String codBExport) {
        this.codBExport = codBExport;
    }

    public String getCodBDDm() {
        return codBDDm;
    }

    public void setCodBDDm(String codBDDm) {
        this.codBDDm = codBDDm;
    }

    public double getCod_Qte() {
        return cod_Qte;
    }

    public void setCod_Qte(double cod_Qte) {
        this.cod_Qte = cod_Qte;
    }

    @NonNull
    @Override
    public String toString() {
        return "ArticleCodeBar{" +
                "parentCodeBar='" + parentCodeBar + '\'' +
                ", filsCodeBar='" + filsCodeBar + '\'' +
                ", codBUser=" + codBUser +
                ", codBStation=" + codBStation +
                ", codBExport='" + codBExport + '\'' +
                ", codBDDm=" + codBDDm +
                '}';
    }
}

