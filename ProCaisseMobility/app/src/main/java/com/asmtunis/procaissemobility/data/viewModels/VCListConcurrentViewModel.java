package com.asmtunis.procaissemobility.data.viewModels;

import androidx.fragment.app.Fragment;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelProviders;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.dao.VCListeConcurrentDAO;
import com.asmtunis.procaissemobility.data.dao.VCNewProductDAO;
import com.asmtunis.procaissemobility.data.models.VCListeConcurrent;
import com.asmtunis.procaissemobility.data.models.VCNewProduct;

import java.util.List;

/**
 * Created by Oussama AZIZI on 6/29/22.
 */

public class VCListConcurrentViewModel extends ViewModel {
    public VCListeConcurrentDAO vcListeConcurrentDAO;
    private static VCListConcurrentViewModel instance;

    public static VCListConcurrentViewModel getInstance(Fragment fragment){
        if(instance==null){
            instance = new ViewModelProvider(fragment).get(VCListConcurrentViewModel.class);
            instance.vcListeConcurrentDAO = App.database.vcListeConcurrentDAO();
        }
        return instance;
    }
    public LiveData<List<VCListeConcurrent>> getAllVCListConcurrent(){
        return App.database.vcListeConcurrentDAO().getAll();
    }
    public String getConcurrent(String code){
        return  App.database.vcListeConcurrentDAO().getConcurrent(code);
    }
}
