package com.asmtunis.procaissemobility.data.models;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.annotation.NonNull;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * Created by PC on 10/12/2017.
 */

@Entity(primaryKeys = {"LT_NumTicket","LT_IdCarnet","LT_Exerc","LT_CodArt","LT_Unite"})
public class LigneTicket  extends BaseModel implements Serializable {
    @NonNull
    @ColumnInfo(name = "LT_NumTicket")
    @SerializedName("LT_NumTicket")
    @Expose
    public int lTNumTicket;
    @ColumnInfo(name = "LT_NumTicket_M")
    @SerializedName("LT_NumTicket_M")
    @Expose
    public String lTNumTicketM;
    @NonNull
    @ColumnInfo(name = "LT_IdCarnet")
    @SerializedName("LT_IdCarnet")
    @Expose
    public String lTIdCarnet;
    @NonNull
    @ColumnInfo(name = "LT_Exerc")
    @SerializedName("LT_Exerc")
    @Expose
    public String lTExerc;
    @NonNull
    @ColumnInfo(name = "LT_CodArt")
    @SerializedName("LT_CodArt")
    @Expose
    public String lTCodArt;
    @ColumnInfo(name = "LT_PrixVente")
    @SerializedName("LT_PrixVente")
    @Expose
    public double lTPrixVente;
    @ColumnInfo(name = "LT_Remise")
    @SerializedName("LT_Remise")
    @Expose
    public double lTRemise;
    @ColumnInfo(name = "LT_TVA")
    @SerializedName("LT_TVA")
    @Expose
    public double lTTVA;
    @ColumnInfo(name = "LT_MtHT")
    @SerializedName("LT_MtHT")
    @Expose
    public double lTMtHT;
    @ColumnInfo(name = "LT_MtTTC")
    @SerializedName("LT_MtTTC")
    @Expose
    public double lTMtTTC;
    @NonNull
    @ColumnInfo(name = "LT_Unite")
    @SerializedName("LT_Unite")
    @Expose
    public String lTUnite;
    @ColumnInfo(name = "LT_Qte")
    @SerializedName("LT_Qte")
    @Expose
    public double lTQte;
    @ColumnInfo(name = "LT_Annuler")
    @SerializedName("LT_Annuler")
    @Expose
    public int lTAnnuler;
    @ColumnInfo(name = "LT_NumOrdre")
    @SerializedName("LT_NumOrdre")
    @Expose
    public int lTNumOrdre;
    @ColumnInfo(name = "LT_QtePiece")
    @SerializedName("LT_QtePiece")
    @Expose
    public double lTQtePiece;
    @ColumnInfo(name = "LT_PrixEncaisse")
    @SerializedName("LT_PrixEncaisse")
    @Expose
    public double lTPrixEncaisse;
    @SerializedName("LT_PACHAT")
    @ColumnInfo(name = "LT_PACHAT")
    @Expose
    public double lTPACHAT;
    @ColumnInfo(name = "LT_Taux_Remise")
    @SerializedName("LT_Taux_Remise")
    @Expose
    public double lTTauxRemise;

    @ColumnInfo(name = "LT_Commande")
    @SerializedName("LT_Commande")
    @Expose
    public boolean lTCommande;

    @ColumnInfo(name = "LT_Tarif")
    @SerializedName("LT_Tarif")
    @Expose
    public String ltTarif;

    @ColumnInfo(name = "LT_QteFacturee")
    @SerializedName("LT_QteFacturee")
    @Expose
    public double lTQteFacturee;

    @ColumnInfo(name = "LT_Pachat_Res")
    @SerializedName("LT_Pachat_Res")
    @Expose
    public double lTPachatRes;

    @ColumnInfo(name = "LT_Pachat_PrixFacturee")
    @SerializedName("LT_Pachat_PrixFacturee")
    @Expose
    public double lTPachatprixFacturee;

    @ColumnInfo(name = "LT_NumFacture")
    @SerializedName("LT_NumFacture")
    @Expose
    public String lTnumFacture;

    @ColumnInfo(name = "LT_ExercFacture")
    @SerializedName("LT_ExercFacture")
    @Expose
    public String lTExercFacture;

    @ColumnInfo(name = "CodeBarFils")
    @SerializedName("CodeBarFils")
    @Expose
    public String codeBarFils;

    @Ignore
    public Article article;

    public Boolean authorizedDiscount = true;
    @Ignore
    public LigneTicket(String lTIdCarnet, String lTExerc, int lTAnnuler) {
        this.lTIdCarnet = lTIdCarnet;
        this.lTExerc = lTExerc;
        this.lTAnnuler = lTAnnuler;
    }

    public LigneTicket() {
    }
    @Ignore
    public LigneTicket(int lTNumTicket, String lTIdCarnet, String lTExerc, String lTCodArt, double lTPrixVente, double lTRemise, double lTTVA, double lTMtHT, double lTMtTTC, String lTUnite, double lTQte, int lTAnnuler, int lTNumOrdre, double lTQtePiece, double lTPrixEncaisse, double lTPACHAT, double lTTauxRemise) {
        this.lTNumTicket = lTNumTicket;
        this.lTIdCarnet = lTIdCarnet;
        this.lTExerc = lTExerc;
        this.lTCodArt = lTCodArt;
        this.lTPrixVente = lTPrixVente;
        this.lTRemise = lTRemise;
        this.lTTVA = lTTVA;
        this.lTMtHT = lTMtHT;
        this.lTMtTTC = lTMtTTC;
        this.lTUnite = lTUnite;
        this.lTQte = lTQte;
        this.lTAnnuler = lTAnnuler;
        this.lTNumOrdre = lTNumOrdre;
        this.lTQtePiece = lTQtePiece;
        this.lTPrixEncaisse = lTPrixEncaisse;
        this.lTPACHAT = lTPACHAT;
        this.lTTauxRemise = lTTauxRemise;
    }

    public LigneTicket(Article article) {
        this.article = article;
    }

    public boolean getlTCommande() {
        return lTCommande;
    }

    public void setlTCommande(boolean lTCommande) {
        this.lTCommande = lTCommande;
    }

    public int getlTNumTicket() {
        return lTNumTicket;
    }

    public void setlTNumTicket(int lTNumTicket) {
        this.lTNumTicket = lTNumTicket;
    }

    public String getlTIdCarnet() {
        return lTIdCarnet;
    }

    public void setlTIdCarnet(String lTIdCarnet) {
        this.lTIdCarnet = lTIdCarnet;
    }

    public String getlTExerc() {
        return lTExerc;
    }

    public void setlTExerc(String lTExerc) {
        this.lTExerc = lTExerc;
    }

    public String getlTCodArt() {
        return lTCodArt;
    }

    public void setlTCodArt(String lTCodArt) {
        this.lTCodArt = lTCodArt;
    }

    public double getlTPrixVente() {
        return lTPrixVente;
    }

    public void setlTPrixVente(double lTPrixVente) {
        this.lTPrixVente = lTPrixVente;
    }

    public double getlTRemise() {
        return lTRemise;
    }

    public void setlTRemise(double lTRemise) {
        this.lTRemise = lTRemise;
    }

    public double getlTTVA() {
        return lTTVA;
    }

    public void setlTTVA(double lTTVA) {
        this.lTTVA = lTTVA;
    }

    public double getlTMtHT() {
        return lTMtHT;
    }

    public void setlTMtHT(double lTMtHT) {
        this.lTMtHT = lTMtHT;
    }

    public double getlTMtTTC() {
        return lTMtTTC;
    }

    public void setlTMtTTC(double lTMtTTC) {
        this.lTMtTTC = lTMtTTC;
    }

    public String getlTUnite() {
        return lTUnite;
    }

    public void setlTUnite(String lTUnite) {
        this.lTUnite = lTUnite;
    }

    public double getlTQte() {
        return lTQte;
    }

    public void setlTQte(double lTQte) {
        this.lTQte = lTQte;
    }

    public int getlTAnnuler() {
        return lTAnnuler;
    }

    public void setlTAnnuler(int lTAnnuler) {
        this.lTAnnuler = lTAnnuler;
    }

    public int getlTNumOrdre() {
        return lTNumOrdre;
    }

    public void setlTNumOrdre(int lTNumOrdre) {
        this.lTNumOrdre = lTNumOrdre;
    }

    public double getlTQtePiece() {
        return lTQtePiece;
    }

    public void setlTQtePiece(double lTQtePiece) {
        this.lTQtePiece = lTQtePiece;
    }

    public void setlTQtePiece(int lTQtePiece) {
        this.lTQtePiece = lTQtePiece;
    }

    public double getlTPrixEncaisse() {
        return lTPrixEncaisse;
    }

    public void setlTPrixEncaisse(double lTPrixEncaisse) {
        this.lTPrixEncaisse = lTPrixEncaisse;
    }

    public double getlTPACHAT() {
        return lTPACHAT;
    }

    public void setlTPACHAT(double lTPACHAT) {
        this.lTPACHAT = lTPACHAT;
    }

    public double getlTTauxRemise() {
        return lTTauxRemise;
    }

    public void setlTTauxRemise(double lTTauxRemise) {
        this.lTTauxRemise = lTTauxRemise;
    }

    public Article getArticle() {
        return article;
    }

    public void setArticle(Article article) {
        this.article = article;
    }

    public void setLtTarif(String ltTarif) {
        this.ltTarif = ltTarif;
    }

    public String getLtTarif() {
        return ltTarif;
    }

    @Override
    public String toString() {
        return "LigneTicket{" +
                "lTNumTicket=" + lTNumTicket +
                ", lTIdCarnet='" + lTIdCarnet + '\'' +
                ", lTExerc='" + lTExerc + '\'' +
                ", lTCodArt='" + lTCodArt + '\'' +
                ", lTPrixVente=" + lTPrixVente +
                ", lTRemise=" + lTRemise +
                ", lTTVA=" + lTTVA +
                ", lTMtHT=" + lTMtHT +
                ", lTMtTTC=" + lTMtTTC +
                ", lTUnite='" + lTUnite + '\'' +
                ", lTQte=" + lTQte +
                ", lTAnnuler=" + lTAnnuler +
                ", lTNumOrdre=" + lTNumOrdre +
                ", lTQtePiece=" + lTQtePiece +
                ", lTPrixEncaisse=" + lTPrixEncaisse +
                ", lTPACHAT=" + lTPACHAT +
                ", lTTauxRemise=" + lTTauxRemise +
                ", lTCommande=" + lTCommande +
                ", lTQteFacturee=" + lTQteFacturee +
                ", lTPachatRes=" + lTPachatRes +
                ", lTPachatprixFacturee=" + lTPachatprixFacturee +
                ", lTnumFacture='" + lTnumFacture + '\'' +
                ", lTExercFacture='" + lTExercFacture + '\'' +
                ", article=" + article +
                ", authorizedDiscount=" + authorizedDiscount +
                '}';
    }
}
