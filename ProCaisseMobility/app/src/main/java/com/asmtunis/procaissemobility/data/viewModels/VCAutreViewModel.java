package com.asmtunis.procaissemobility.data.viewModels;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelProviders;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.dao.VCAutreDAO;
import com.asmtunis.procaissemobility.data.models.VCAutre;

import java.util.List;

public class VCAutreViewModel extends ViewModel {
    public VCAutreDAO vcAutreDAO;
    private static VCAutreViewModel instance;

    public static VCAutreViewModel getInstance(Fragment fragment){
        if (instance==null){
            instance = new ViewModelProvider(fragment).get(VCAutreViewModel.class);
            instance.vcAutreDAO= App.database.vcAutreDAO();
        }
        return instance;
    }




    /*  public static DNVisiteViewModel getInstance(Fragment activity) {
        if (instance == null)
            instance = ViewModelProviders.of(activity).get(DNVisiteViewModel.class);
        instance.dnVisiteDAO = App.database.dnVisitesDAO();
        return instance;
    }*/

    public static VCAutreViewModel getInstance(FragmentActivity activity) {
        if (instance == null)
            instance = new ViewModelProvider(activity).get(VCAutreViewModel.class);
        instance.vcAutreDAO = App.database.vcAutreDAO();
        return instance;
    }


    public LiveData<List<VCAutre>> getAll(){
        return vcAutreDAO.getAll();
    }



    public LiveData<Integer> getNoSyncCountMubtale(){
        return App.database.vcAutreDAO().getNoSyncCountMubtale();
    }

    public LiveData<Integer> getNoSyncCountMubtaleToDelete(){
        return App.database.vcAutreDAO().getCountNoSyncedToDeleteMubtale();
    }
}
