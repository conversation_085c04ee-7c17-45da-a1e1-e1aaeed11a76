package com.asmtunis.procaissemobility.data.network.services;


import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Reclamation;

import java.util.List;

import license.model.ServerResponse;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Multipart;
import retrofit2.http.POST;
import retrofit2.http.Part;

/**
 * Created by Achraf on 29/09/2017.
 */

public interface ReclamationService {

    @POST("getReclamations")
    Call<List<Reclamation>> getReclamations(@Body GenericObject genericObject);


    @POST("addBatchReclamation")
    Call<List<Reclamation>> addBatchReclamation(@Body GenericObject genericObject);


    @Multipart
    @POST("uploadReclamationFile")
    Call<ServerResponse> uploadFile(@Part RequestBody file,
                                    @Part("files") MultipartBody.Part name);

}
