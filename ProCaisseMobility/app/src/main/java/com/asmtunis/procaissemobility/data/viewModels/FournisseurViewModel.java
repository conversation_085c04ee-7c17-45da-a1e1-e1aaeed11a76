package com.asmtunis.procaissemobility.data.viewModels;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelProviders;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.dao.ArticleDAO;
import com.asmtunis.procaissemobility.data.dao.FournisseurDAO;
import com.asmtunis.procaissemobility.data.models.Article;
import com.asmtunis.procaissemobility.data.models.Fournisseur;

import java.util.List;




public class FournisseurViewModel extends ViewModel {
    public FournisseurDAO dao;
    private static FournisseurViewModel instance;


    public static FournisseurViewModel getInstance(Fragment activity) {
        if (instance == null)
            instance = new ViewModelProvider(activity).get(FournisseurViewModel.class);
        instance.dao = App.database.fournisseurDAO();

        return instance;
    }

    public static FournisseurViewModel getInstance(FragmentActivity activity) {
        if (instance == null)
            instance = new ViewModelProvider(activity).get(FournisseurViewModel.class);
        instance.dao = App.database.fournisseurDAO();
        return instance;
    }


    public LiveData<List<Fournisseur>> getAll() {
        return App.database.fournisseurDAO().getAll();
    }


}
