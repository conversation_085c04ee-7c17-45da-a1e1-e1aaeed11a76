package com.asmtunis.procaissemobility.data.network.services;

import com.asmtunis.procaissemobility.data.models.BonCommandResponse;
import com.asmtunis.procaissemobility.data.models.BonCommande;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.InvPatBatchResponse;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

public interface BonCommandeService {
    @POST("getCommande")
    Call<List<BonCommande>> getBonCommandes(@Body GenericObject genericObject);

    @POST("addBatchBonCommande")
    Call<InvPatBatchResponse> addBatchBonCommande(@Body GenericObject commandes);


    @POST("addBatchBonCommande")
    Call<InvPatBatchResponse> addBatchinvPat(@Body GenericObject commandes);


    @POST("controleInventaire")
    Call<BonCommandResponse> controlInventaire(@Body GenericObject commandes);
}
