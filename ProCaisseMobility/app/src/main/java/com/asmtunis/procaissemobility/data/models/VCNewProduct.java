package com.asmtunis.procaissemobility.data.models;


import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

@Entity
public class VCNewProduct extends BaseModel implements Serializable {
    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "CodeVCLanP")
    @SerializedName("CodeVCLanP")
    @Expose
    private String codeVCLanP;

    @ColumnInfo(name = "CodeVCLanPM")
    @SerializedName("CodeVCLanPM")
    @Expose
    private String codeVCLanPM;
    @ColumnInfo(name = "ProduitLanP")
    @SerializedName("ProduitLanP")
    @Expose
    private String produitLanP;
    @ColumnInfo(name = "DateOp")
    @SerializedName("DateOp")
    @Expose
    private String dateOp;
    @ColumnInfo(name = "CodeConcur")
    @SerializedName("CodeConcur")
    @Expose
    private String codeConcur;
    @ColumnInfo(name = "NoteOp")
    @SerializedName("NoteOp")
    @Expose
    private String noteOp;
    @ColumnInfo(name = "PrixLanP")
    @SerializedName("PrixLanP")
    @Expose
    private Double prixLanP;
    @ColumnInfo(name = "TauxPromo")
    @SerializedName("TauxPromo")
    @Expose
    private Double tauxPromo;
    @ColumnInfo(name = "CodeUser")
    @SerializedName("CodeUser")
    @Expose
    private Integer codeUser;
    @ColumnInfo(name = "InfoOp1")
    @SerializedName("InfoOp1")
    @Expose
    private String infoOp1;
    @ColumnInfo(name = "CodeTypeCom")
    @SerializedName("CodeTypeCom")
    @Expose
    private String codeTypeCom;

    public String getCodeVCLanP() {
        return codeVCLanP;
    }

    public void setCodeVCLanP(String codeVCLanP) {
        this.codeVCLanP = codeVCLanP;
    }

    public String getCodeVCLanPM() {
        return codeVCLanPM;
    }

    public void setCodeVCLanPM(String codeVCLanPM) {
        this.codeVCLanPM = codeVCLanPM;
    }

    public String getProduitLanP() {
        return produitLanP;
    }

    public void setProduitLanP(String produitLanP) {
        this.produitLanP = produitLanP;
    }

    public String getDateOp() {
        return dateOp;
    }

    public void setDateOp(String dateOp) {
        this.dateOp = dateOp;
    }

    public String getCodeConcur() {
        return codeConcur;
    }

    public void setCodeConcur(String codeConcur) {
        this.codeConcur = codeConcur;
    }

    public String getNoteOp() {
        return noteOp;
    }

    public void setNoteOp(String noteOp) {
        this.noteOp = noteOp;
    }

    public double getPrixLanP() {
        return prixLanP;
    }

    public void setPrixLanP(double prixLanP) {
        this.prixLanP = prixLanP;
    }

    public double getTauxPromo() {
        return tauxPromo;
    }

    public void setTauxPromo(double tauxPromo) {
        this.tauxPromo = tauxPromo;
    }

    public int getCodeUser() {
        return codeUser;
    }

    public void setCodeUser(int codeUser) {
        this.codeUser = codeUser;
    }

    public String getInfoOp1() {
        return infoOp1;
    }

    public void setInfoOp1(String infoOp1) {
        this.infoOp1 = infoOp1;
    }

    public String getCodeTypeCom() {
        return codeTypeCom;
    }

    public void setCodeTypeCom(String codeTypeCom) {
        this.codeTypeCom = codeTypeCom;
    }

    public VCNewProduct(String codeVCLanP,
                        String codeVCLanPM,
                        String produitLanP,
                        String dateOp,
                        String codeConcur,
                        String noteOp,
                        double prixLanP,
                        double tauxPromo,
                        int codeUser,
                        String infoOp1,
                        String codeTypeCom) {
        this.codeVCLanP = codeVCLanP;
        this.codeVCLanPM = codeVCLanPM;
        this.produitLanP = produitLanP;
        this.dateOp = dateOp;
        this.codeConcur = codeConcur;
        this.noteOp = noteOp;
        this.prixLanP = prixLanP;
        this.tauxPromo = tauxPromo;
        this.codeUser = codeUser;
        this.infoOp1 = infoOp1;
        this.codeTypeCom = codeTypeCom;
    }



    public VCNewProduct(){

    }


    public VCNewProduct(String codeVCLanP){
        this.codeVCLanP = codeVCLanP;
    }
}



