package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.ReglementCaisse;
import com.asmtunis.procaissemobility.data.models.SessionCaisse;

import java.util.List;

/**
 * Created by PC on 11/18/2017.
 */
@Dao
public interface SessionCaisseDAO {

    @Query("SELECT * FROM SessionCaisse order by strftime('%Y-%m-%d %H-%M',SC_DateHeureCrea) desc")
    List<SessionCaisse> getAll();

    @Query("SELECT * FROM SessionCaisse order by strftime('%Y-%m-%d %H-%M',SC_DateHeureCrea) desc")
    LiveData<List<SessionCaisse>> getAllMutable();

    @Query("SELECT * FROM SessionCaisse WHERE SC_Caisse = :code ")
    SessionCaisse getOneByCode(String code);

    @Query("SELECT * FROM SessionCaisse LIMIT 1")
    SessionCaisse getOne();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(SessionCaisse item);


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<SessionCaisse> items);

    @Query("DELETE FROM SessionCaisse")
    void deleteAll();
}
