package com.asmtunis.procaissemobility.data.network.datamanager;


import android.util.Log;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.CarteResto;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.CarteRestoService;

import java.util.List;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

/**
 * Created by Achraf on 29/09/2017.
 */

public class CarteRestoDataManager {

    private static CarteRestoDataManager sInstance;

    private final CarteRestoService mCarteRestoService;

    public CarteRestoDataManager() {
        mCarteRestoService = new ServiceFactory<>(CarteRestoService.class,String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"CarteResto")).makeService();

    }

    public static CarteRestoDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new CarteRestoDataManager();
        }
        return sInstance;
    }




    public void getCartesResto(GenericObject genericObject,
                                    RemoteCallback<List<CarteResto>> listener) {
        Log.d("codecode", genericObject.toString());
        mCarteRestoService.getCartesResto(genericObject)
                .enqueue(listener);
    }


    public void getCarteRestoByCode(GenericObject genericObject,
                                    RemoteCallback<CarteResto> listener) {
        Log.d("codecode", genericObject.toString());
        mCarteRestoService.getCarteRestoByCode(genericObject)
                .enqueue(listener);
    }

}


