package com.asmtunis.procaissemobility.data.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * Created by Oussama AZIZI on 3/9/22.
 */

@Entity(primaryKeys = {"ART_CLI_CodeArt", "ART_CLI_CodeCli"})
public class ClientArticlePrix implements Serializable {

    @NonNull
    @SerializedName("ART_CLI_CodeArt")
    @ColumnInfo(name = "ART_CLI_CodeArt")
    @Expose
    private String aRTCLICodeArt;
    @NonNull
    @SerializedName("ART_CLI_CodeCli")
    @ColumnInfo(name = "ART_CLI_CodeCli")
    @Expose
    private String aRTCLICodeCli;
    @SerializedName("ART_TypePrix")
    @ColumnInfo(name = "ART_TypePrix")
    @Expose
    private String aRTTypePrix;
    @SerializedName("ART_PrixClient")
    @ColumnInfo(name = "ART_PrixClient")
    @Expose
    private String aRTPrixClient;
    @SerializedName("ART_export")
    @ColumnInfo(name = "ART_export")
    @Expose
    private String aRTExport;
    @SerializedName("ART_DDm")
    @ColumnInfo(name = "ART_DDm")
    @Expose
    private String aRTDDm;
    @SerializedName("ART_station")
    @ColumnInfo(name = "ART_station")
    @Expose
    private String aRTStation;
    @SerializedName("ART_user")
    @ColumnInfo(name = "ART_user")
    @Expose
    private String aRTUser;
    @SerializedName("ART_unite")
    @ColumnInfo(name = "ART_unite")
    @Expose
    private String aRTUnite;
    @SerializedName("ART_TRemise")
    @ColumnInfo(name = "ART_TRemise")
    @Expose
    private String aRTTRemise;

    public String getARTCLICodeArt() {
        return aRTCLICodeArt;
    }

    public void setARTCLICodeArt(String aRTCLICodeArt) {
        this.aRTCLICodeArt = aRTCLICodeArt;
    }

    public String getARTCLICodeCli() {
        return aRTCLICodeCli;
    }

    public void setARTCLICodeCli(String aRTCLICodeCli) {
        this.aRTCLICodeCli = aRTCLICodeCli;
    }

    public String getARTTypePrix() {
        return aRTTypePrix;
    }

    public void setARTTypePrix(String aRTTypePrix) {
        this.aRTTypePrix = aRTTypePrix;
    }

    public String getARTPrixClient() {
        return aRTPrixClient;
    }

    public void setARTPrixClient(String aRTPrixClient) {
        this.aRTPrixClient = aRTPrixClient;
    }

    public String getARTExport() {
        return aRTExport;
    }

    public void setARTExport(String aRTExport) {
        this.aRTExport = aRTExport;
    }

    public String getARTDDm() {
        return aRTDDm;
    }

    public void setARTDDm(String aRTDDm) {
        this.aRTDDm = aRTDDm;
    }

    public String getARTStation() {
        return aRTStation;
    }

    public void setARTStation(String aRTStation) {
        this.aRTStation = aRTStation;
    }

    public String getARTUser() {
        return aRTUser;
    }

    public void setARTUser(String aRTUser) {
        this.aRTUser = aRTUser;
    }

    public String getARTUnite() {
        return aRTUnite;
    }

    public void setARTUnite(String aRTUnite) {
        this.aRTUnite = aRTUnite;
    }

    public String getARTTRemise() {
        return aRTTRemise;
    }

    public void setARTTRemise(String aRTTRemise) {
        this.aRTTRemise = aRTTRemise;
    }

    }

