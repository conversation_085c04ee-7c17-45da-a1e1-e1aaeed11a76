package com.asmtunis.procaissemobility.data.network.services;

import com.asmtunis.procaissemobility.data.models.BonRetour;
import com.asmtunis.procaissemobility.data.models.GenericObject;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

public interface BonRetourService {

    @POST("getRetour")
    Call<List<BonRetour>> getBonRetours(@Body GenericObject genericObject);

    @POST("addBatchBonRetour")
    Call<List<BonRetour>> addBatchBonRetour(@Body GenericObject genericObject);
}
