package com.asmtunis.procaissemobility.data.viewModels;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelProviders;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.dao.BonCommandeDAO;
import com.asmtunis.procaissemobility.data.models.BonCommande;

import java.util.List;

public class BonComandeViewModel extends ViewModel {

    public BonCommandeDAO dao;
    private static BonComandeViewModel instance;


    public static BonComandeViewModel getInstance(Fragment activity) {
        if (instance == null)
        instance = new ViewModelProvider(activity).get(BonComandeViewModel.class);

        instance.dao = App.database.bonCommandeDAO();
        return instance;
    }

    public static BonComandeViewModel getInstance(FragmentActivity activity) {
        if (instance == null)
            instance = new ViewModelProvider(activity).get(BonComandeViewModel.class);
        instance.dao = App.database.bonCommandeDAO();
        return instance;
    }

    public  LiveData<List<BonCommande>> getAll(){
       return dao.getAllMutable();
    }

    public  LiveData<List<BonCommande>> getByStation(String station, String BCType){
       return dao.getByStationMutable(station, BCType);
    }



    public  LiveData<List<BonCommande>> getByPatStationAndType(String station, String BCType,String info, String patType){
        return dao.getPatByStationAndTypeMutable(station, BCType,info,patType);
    }


    public  LiveData<List<BonCommande>> getByCodeClientandPatEtat(String codeCommande, String devinf3){
        return dao.getByCodeClientandPatEtatLiveData(codeCommande, devinf3);
    }



    public  LiveData<Integer> getNoSyncCount(){
       return dao.getNoSyncCountMubtale();
    }

    public  LiveData<Integer> getAllCountBySessionMutable(String station){
       return dao.getAllCountBySessionMutable(station);
    }

    public  Integer getNoSyncCountNonMutable(){
        return dao.getNoSyncCountNonMubtale();
    }

}
