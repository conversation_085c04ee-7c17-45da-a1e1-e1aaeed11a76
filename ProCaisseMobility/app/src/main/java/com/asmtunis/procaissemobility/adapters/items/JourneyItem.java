package com.asmtunis.procaissemobility.adapters.items;

import android.content.Context;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.recyclerview.widget.RecyclerView;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.adapters.ClientListAdapter;
import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.data.models.EtatOrdreMission;
import com.asmtunis.procaissemobility.data.models.LigneOrdreMission;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.listener.ItemCallback;
import com.blankj.utilcode.util.ObjectUtils;
import com.mikepenz.fastadapter.items.AbstractItem;

import java.util.List;

/**
 * Created by PC on 10/11/2017.
 */


public class JourneyItem extends AbstractItem<JourneyItem, JourneyItem.ViewHolder> {

    private final Context context;
    private Client client;
    private LigneOrdreMission ligneOrdreMission;
    protected ItemCallback itemCallback;

    public JourneyItem(Context context, LigneOrdreMission ligneOrdreMission, Client client) {
        this.client = client;
        this.ligneOrdreMission = ligneOrdreMission;
        this.context = context;
    }

    public void setItemCallback(ItemCallback itemCallback) {
        this.itemCallback = itemCallback;
    }


    public void setCallbacks(ItemCallback itemCallback) {
        this.itemCallback = itemCallback;
    }

    public Client getClient() {
        return client;
    }

    public LigneOrdreMission getLigneOrdreMission() {
        return ligneOrdreMission;
    }

    public void setClient(Client client) {
        this.client = client;
    }

    @Override
    public int getType() {
        return R.id.adapter_client_item_id;
    }

    @Override
    public int getLayoutRes() {
        return R.layout.journey_item;
    }


    void setText(AppCompatTextView editText, String value) {
        if (!StringUtils.isEmptyString(value)) {
            editText.setVisibility(View.VISIBLE);
            editText.setText(value);
        } else {
            editText.setVisibility(View.GONE);
        }
    }

    @Override
    public void bindView(@NonNull final ViewHolder viewHolder, @NonNull List<Object> payloads) {
        super.bindView(viewHolder, payloads);
        if (itemCallback != null) {
            viewHolder.itemView.setOnClickListener(v -> onViewClick(viewHolder));
        }
        setText(viewHolder.customerName, client.cLINomPren);
        setText(viewHolder.customerDistance, String.format("%s (%s)", ligneOrdreMission.getDistanceKm() != null ? ligneOrdreMission.getDistanceKm() : "",
                ligneOrdreMission.getDistanceTime() != null ? ligneOrdreMission.getDistanceTime() : ""));
        EtatOrdreMission etatOrdreMission = App.database.etatOrdreMissionDAO().getOne(ligneOrdreMission.lIGOREtat);
        setTriangleView(viewHolder.itemStatusLabel, etatOrdreMission);
        viewHolder.addressLabel.setVisibility(ObjectUtils.isNotEmpty(client.cLIAdresse) ? View.VISIBLE : View.GONE);
        viewHolder.errorLocation.setVisibility(client.getLongitude() * client.getLatitude() == 0d ? View.VISIBLE : View.GONE);
        viewHolder.customerDistance.setVisibility(client.getLongitude() * client.getLatitude() != 0d ? View.VISIBLE : View.GONE);
        if (ObjectUtils.isNotEmpty(client.cLIAdresse)) {
            viewHolder.customerAddress.setVisibility(View.VISIBLE);
            viewHolder.addressLabel.setVisibility(View.VISIBLE);
            setText(viewHolder.customerAddress, client.cLIAdresse);
        } else {
            viewHolder.customerAddress.setVisibility(View.GONE);
            viewHolder.addressLabel.setVisibility(View.GONE);
        }
        if (ObjectUtils.isNotEmpty(client.cLITel1)) {
            viewHolder.customerPhone.setVisibility(View.VISIBLE);
            viewHolder.phone_label.setVisibility(View.VISIBLE);
            setText(viewHolder.customerPhone, client.cLITel1);
        } else {
            viewHolder.customerPhone.setVisibility(View.GONE);
            viewHolder.phone_label.setVisibility(View.GONE);
        }
    }

    void onViewClick(ViewHolder viewHolder) {
        if (itemCallback != null) {
            itemCallback.onItemClicked(viewHolder, client);
        }
    }

    @Override
    public void unbindView(@NonNull final ViewHolder holder) {
        super.unbindView(holder);
    }

    // Init the viewHolder for this Item
    @NonNull
    @Override
    public ViewHolder getViewHolder(@NonNull View v) {
        return new ViewHolder(v);
    }

    void setTriangleView(jp.shts.android.library.TriangleLabelView labelView, EtatOrdreMission status) {
        labelView.setVisibility(View.VISIBLE);
        if (ObjectUtils.isNotEmpty(status)) {
            if (ObjectUtils.isNotEmpty(status.getCouleur())) {
                labelView.setTriangleBackgroundColor(status.getCouleur());
            }
            labelView.setSecondaryTextColor(labelView.getTriangleBackGroundColor());
            if (ObjectUtils.isNotEmpty(status.getLibelleEtatOrd())) {
                labelView.setPrimaryText(status.getLibelleEtatOrd());
            }
            labelView.setPrimaryTextColorResource(R.color.md_red_100);
        }
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        AppCompatTextView customerName;
        AppCompatTextView customerDistance;
        AppCompatTextView customerPhone;
        AppCompatTextView customerAddress;
        AppCompatTextView addressLabel;
        AppCompatTextView address_label;
        AppCompatTextView phone_label;
        AppCompatImageView errorLocation;
        jp.shts.android.library.TriangleLabelView itemStatusLabel;

        public ViewHolder(View view) {
            super(view);
            customerName = view.findViewById(R.id.customer_name);
            customerDistance = view.findViewById(R.id.customer_distance);
            customerPhone = view.findViewById(R.id.customer_phone);
            customerAddress = view.findViewById(R.id.customer_adress);
            addressLabel = view.findViewById(R.id.address_label);
            itemStatusLabel = view.findViewById(R.id.item_status_label);
            errorLocation = view.findViewById(R.id.error_location);
            phone_label = view.findViewById(R.id.phone_label);
            address_label = view.findViewById(R.id.address_label);
        }
    }
}
