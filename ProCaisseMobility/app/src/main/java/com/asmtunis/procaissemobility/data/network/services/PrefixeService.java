package com.asmtunis.procaissemobility.data.network.services;


import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.Prefixe;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Headers;
import retrofit2.http.POST;

/**
 * Created by Achraf on 28/09/2017.
 */

public interface PrefixeService {

    @Headers("User-Agent: android-api-client")
    @POST("getPrefixes")
    Call<List<Prefixe>> getPrefixes(@Body GenericObject genericObject);



}
