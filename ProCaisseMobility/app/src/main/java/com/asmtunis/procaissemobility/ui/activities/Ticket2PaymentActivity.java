package com.asmtunis.procaissemobility.ui.activities;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.Gravity;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.ScrollView;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.RequiresApi;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.core.app.ActivityCompat;
import androidx.core.content.res.ResourcesCompat;

import com.afollestad.materialdialogs.MaterialDialog;
import com.airbnb.lottie.L;
import com.asmtunis.procaissemobility.BuildConfig;
import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.adapters.tables.LigneChequesTableDataAdapter;
import com.asmtunis.procaissemobility.adapters.tables.LigneTicketRestoTableDataAdapter;
import com.asmtunis.procaissemobility.data.models.Article;
import com.asmtunis.procaissemobility.data.models.ArticleCodeBar;
import com.asmtunis.procaissemobility.data.models.Banque;
import com.asmtunis.procaissemobility.data.models.CarteResto;
import com.asmtunis.procaissemobility.data.models.ChequeCaisse;
import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.data.models.LigneTicket;
import com.asmtunis.procaissemobility.data.models.ReglementCaisse;
import com.asmtunis.procaissemobility.data.models.StationStock;
import com.asmtunis.procaissemobility.data.models.Ticket;
import com.asmtunis.procaissemobility.data.models.TicketWithLines;
import com.asmtunis.procaissemobility.data.models.TicketWithLinesAndPayments;
import com.asmtunis.procaissemobility.data.models.Timbre;
import com.asmtunis.procaissemobility.data.models.TraiteCaisse;
import com.asmtunis.procaissemobility.data.viewModels.LigneTicketViewModel;
import com.asmtunis.procaissemobility.helper.BluetoothService;
import com.asmtunis.procaissemobility.helper.Globals;
import com.asmtunis.procaissemobility.helper.PrinterHelper;
import com.asmtunis.procaissemobility.helper.utils.Calculator;
import com.asmtunis.procaissemobility.helper.utils.DateUtils;
import com.asmtunis.procaissemobility.helper.utils.PrefUtils;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.helper.utils.Utils;
import com.asmtunis.procaissemobility.helper.wifi_print.Comman;
import com.asmtunis.procaissemobility.helper.wifi_print.WifiPrint;
import com.asmtunis.procaissemobility.ui.components.ExtendedMaterialEditText;
import com.blankj.utilcode.util.ObjectUtils;
import com.mikepenz.google_material_typeface_library.GoogleMaterial;
import com.mikepenz.iconics.IconicsDrawable;
import com.mikepenz.iconics.view.IconicsButton;
import com.transitionseverywhere.Slide;
import com.transitionseverywhere.TransitionManager;
import com.wdullaer.materialdatetimepicker.date.DatePickerDialog;

import java.io.File;
import java.io.IOException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import br.com.bloder.blormlib.Blorm;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;
import es.dmoral.toasty.Toasty;

import static br.com.bloder.blormlib.validation.Validations.filled;
import static com.asmtunis.procaissemobility.App.prefUtils;
import static com.asmtunis.procaissemobility.helper.Globals.CLIENT_REG_PARTIEL_KEY;
import static com.asmtunis.procaissemobility.helper.Globals.DEFAULT_ENCODING;
import static com.asmtunis.procaissemobility.helper.Globals.REGLEMENT_PARTIEL_KEY;
import static com.asmtunis.procaissemobility.helper.Globals.TICKET_REG_PARTIEL_KEY;
import static com.asmtunis.procaissemobility.helper.Globals.TICKET_WITH_LINES_AND_PAYEMENT_KEY;
import static com.asmtunis.procaissemobility.helper.Globals.TICKET_WITH_LINES_KEY;
import static com.asmtunis.procaissemobility.helper.utils.StringUtils.parseDoublePayment;

/**
 * Created by PC on 10/24/2017.
 */

public class Ticket2PaymentActivity extends BaseActivity {
    private static final int REQUEST_ENABLE_BT = 2;
    private static final int REQUEST_CONNECT_DEVICE = 1;
    static BluetoothService mService = null;
    boolean notSaved = true;
    BluetoothDevice con_dev = null;
    ReglementCaisse reglementCaisse;
    String prefix = null;
    @BindView(R.id.toolbar)
    Toolbar toolbar;

    @BindView(R.id.cash_button)
    IconicsButton cash_button;

    @BindView(R.id.scrollView)
    ScrollView scrollView;

    @BindView(R.id.cashInputView)
    View cashInputView;

    @BindView(R.id.deleteCashButton)
    IconicsButton deleteCashLayout;

    @BindView(R.id.deleteCheckLayout)
    IconicsButton deleteCheckLayout;

    @BindView(R.id.deleteCarteRestoLayout)
    IconicsButton deleteCarteRestoLayout;

    @BindView(R.id.valBut)
    com.google.android.material.button.MaterialButton validateButton;

    @BindView(R.id.factBut)
    com.google.android.material.button.MaterialButton factureButton;

    @BindView(R.id.bankCheckView)
    View bankCheckView;

    @BindView(R.id.restoTicketView)
    View restoTicketView;

    @BindView(android.R.id.content)
    ViewGroup content;

    @BindView(R.id.ReceivedInputField)
    ExtendedMaterialEditText receivedInputField;

    @BindView(R.id.MadeInputField)
    ExtendedMaterialEditText madeInputField;

    @BindView(R.id.NumberInputField)
    ExtendedMaterialEditText numberInputField;

    @BindView(R.id.DeadlineInputField)
    ExtendedMaterialEditText deadlineInputField;

    @BindView(R.id.AmountInputField)
    ExtendedMaterialEditText amountInputField;

    @BindView(R.id.spinnerBank)
    Spinner spinnerBank;

    @BindView(R.id.spinnerCartResto)
    Spinner spinnerCartResto;


    @BindView(R.id.ticketNumberInputField)
    ExtendedMaterialEditText ticketNumberInputField;

    @BindView(R.id.rateInputField)
    ExtendedMaterialEditText rateInputField;

    //  @BindView(R.id.cardInputField)
    //  AutoCompleteTextView cardInputField;
//    @BindView(R.id.cardInputField)
    //   ExtendedMaterialEditText cardInputField;

    @BindView(R.id.priceInputField)
    ExtendedMaterialEditText priceInputField;

    @BindView(R.id.client_spinner_card_non_editable)
    CardView clientCardView;

    @BindView(R.id.client_spinner_card)
    CardView clientSpinnerCard;

    @BindView(R.id.total)
    Button total;

    @BindView(R.id.rest)
    Button rest;

    @BindView(R.id.client_name)
    TextView clientName;

    @BindView(R.id.addCheckButton)
    com.mikepenz.iconics.view.IconicsButton addCheckButton;
    @BindView(R.id.addTicketButton)
    com.mikepenz.iconics.view.IconicsButton addTicketButton;

    @BindView(R.id.bankDataTableView)
    com.asmtunis.procaissemobility.ui.components.SortableBankCheckLineTableView bankDataTableView;

    @BindView(R.id.restoTicketDataTableView)
    com.asmtunis.procaissemobility.ui.components.SortableRestoTicketTableView restoTicketDatatableView;

    HashMap<String, String> cartResto = new HashMap<String, String>();


    ArrayList<ChequeCaisse> chequeCaisses;

    ArrayList<TraiteCaisse> traiteCaisses;
    LigneChequesTableDataAdapter bankTableDataAdapter;
    LigneTicketRestoTableDataAdapter ticketRestoTableDataAdapter;

    public Client client;
    public Ticket ticket;
    double amount = 0;
    double restAmount = 0;
    double made = 0;

    static TicketWithLines ticketWithLines;
    static boolean isRegPartiel;
    static TicketWithLinesAndPayments ticketWithLinesAndPayments;
    double rEGCMntEspece;
    double rEGCMntChQue;
    double rEGCMntTraite;
    double rEGCMntTotalRecue;
    double rEGCMntEspeceRecue;
    boolean blockRetour = false;
    Activity context = this;

    Unbinder unbinder;
    LigneTicketViewModel ligneTicketViewModel;

    @Override
    protected int setContentView() {
        return R.layout.activity_reglement;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        unbinder = ButterKnife.bind(this);
        clientSpinnerCard.setVisibility(View.GONE);
        clientCardView.setVisibility(View.VISIBLE);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setDisplayShowCustomEnabled(true);
        getSupportActionBar().setDisplayShowTitleEnabled(false);
        getSupportActionBar().setDisplayUseLogoEnabled(false);
        toolbar.setPadding(0, 0, 0, 0);//for tab otherwise give space in tab
        toolbar.setContentInsetsAbsolute(0, 0);
        ticketWithLines = (TicketWithLines) getIntent().getSerializableExtra(TICKET_WITH_LINES_KEY);
        isRegPartiel = (boolean) getIntent().getSerializableExtra(REGLEMENT_PARTIEL_KEY);
        if(isRegPartiel){
            client = (Client) getIntent().getSerializableExtra(CLIENT_REG_PARTIEL_KEY);
            ticket = (Ticket) getIntent().getSerializableExtra(TICKET_REG_PARTIEL_KEY);
        }

        setupClient(ticketWithLines.getTicket().tIKNomClient);
        if (ticketWithLines != null) {
         /*   Timbre timbre = null;
            if (ticketWithLines.getTicket().timbre != 0.0) {
                timbre = App.database.timbreDAO().getByID(String.valueOf((int) ticketWithLines.getTicket().timbre));
            }
            amount = restAmount = timbre == null ? Utils.round(ticketWithLines.getTicket().gettIKMtTTC(), 3)
                    : Utils.round(ticketWithLines.getTicket().gettIKMtTTC() + Double.parseDouble(timbre.tIMBValue), 3);*/
            amount = restAmount = Utils.round(ticketWithLines.getTicket().gettIKMtTTC(), 3);

            setupTotal();
            setupCashInputView();
            setupBankCheckInputView();
            setupRestoTicketInputView();


        }

        validateButton.setOnClickListener(view -> sendData(false,isRegPartiel));

        factureButton.setOnClickListener(view -> sendData(true,isRegPartiel));

        ligneTicketViewModel = LigneTicketViewModel.getInstance(this);

    }

    private void setupClient(String client) {
        clientName.setText(client);
    }

    void setupTotal() {
        total.setText(String.format(getString(R.string.total1_field_title), StringUtils.priceFormat(amount) + "", new PrefUtils(context).getCurrency()));
        rest.setText(String.format(getString(R.string.rest_field_title),
                StringUtils.priceFormat(restAmount), new PrefUtils(context).getCurrency()));
    }

    void setupCashInputView() {
        receivedInputField.addTextChangedListener(new TextWatcher() {
            @Override
            public void onTextChanged(CharSequence cs, int arg1, int arg2, int arg3) {
            }

            @Override
            public void beforeTextChanged(CharSequence s, int arg1, int arg2, int arg3) {
            }

            @Override
            public void afterTextChanged(Editable arg0) {
                setupCalculations();
            }
        });


    }

    void setupCalculations() {
        //  receivedInputField.setText(StringUtils.priceFormat(amount -Calculator.calculateCheckAndCartResto(rEGCMntChQue,rEGCMntTraite)));

        rEGCMntEspece = Utils.round(parseDoublePayment(receivedInputField.getText().toString(), 0),3);
        rEGCMntEspeceRecue = Utils.round(parseDoublePayment(receivedInputField.getText().toString(), 0),3);
        rEGCMntChQue = Calculator.calculateChecks(chequeCaisses);
        rEGCMntTraite = Calculator.calculateTickets(traiteCaisses);
        restAmount = Calculator.calculateRest(amount, rEGCMntEspeceRecue, rEGCMntChQue, rEGCMntTraite, false); //Calculator.calculateRest(amount, parseDoublePayment(receivedInputField.getText().toString(), 0), rEGCMntChQue, rEGCMntTraite, true);
        made = Calculator.calculateMade(amount, Calculator.calculateReceived(parseDoublePayment(receivedInputField.getText().toString(), 0), chequeCaisses, traiteCaisses), false);
        madeInputField.setText(String.valueOf(made));
        amountInputField.setText(StringUtils.priceFormat(Calculator.calculateRest(amount, rEGCMntEspeceRecue, rEGCMntChQue, rEGCMntTraite, false)));


        rest.setText(String.format(getString(R.string.rest_field_title), StringUtils.priceFormat(Calculator.calculateRest(amount, rEGCMntEspeceRecue, rEGCMntChQue, rEGCMntTraite, false)) + "", new PrefUtils(context).getCurrency()));
        // rest.setText(String.format(getString(R.string.rest_field_title), restAmount + "", new PrefUtils(context).getCurrency()));
    }

    void setupBankCheckInputView() {
        bankDataTableView.setSwipeToRefreshEnabled(false);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            bankDataTableView.setElevation(10);
        }
        bankDataTableView.setHeaderBackground(R.color.material_teal500);
        chequeCaisses = new ArrayList<>();
        setSearchableSpinner();
        deadlineInputField.setOnClickListener(v -> showDatePicker());
        //if (DateUtils.getRemainingTime(deadlineInputField.getText().toString()) <= 0)


        new Blorm.Builder()
                .field(deadlineInputField).is(filled)
                .andField(numberInputField).is(filled)
                .andField(amountInputField).is(filled)
                .onSuccess(() -> {
                    boolean echeanceDate = true;

                    try {
                        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
                        Date todayDate = sdf.parse(sdf.format(new Date()));
                        if (todayDate.after(sdf.parse(deadlineInputField.getText().toString()))) {
                            echeanceDate = false;
                        }

                    } catch (ParseException e) {
                        e.printStackTrace();
                    }

                    String titleMsg = "";
                    if (echeanceDate) {
                       if (Calculator.calculateRest(amount, parseDoublePayment(receivedInputField.getText().toString(), 0), chequeCaisses, traiteCaisses, false) > 0 ){


                            if (spinnerBank.getSelectedItemPosition() > 0) {
                                chequeCaisses.add(new ChequeCaisse(numberInputField.getText().toString(),
                                        "dddd", new PrefUtils(context).getCaisseId(),
                                        new PrefUtils(context).getExercice(),
                                        DateUtils.dateToStr(DateUtils.strToDate(deadlineInputField.getText().toString(), "dd/MM/yyyy"),
                                                "MM/dd/yyyy HH:mm:ss")
                                        , spinnerBank.getSelectedItem().toString(),
                                        Utils.round(  parseDoublePayment(amountInputField.getText().toString(), 0),3),
                                        ticketWithLines.getTicket().gettIKUser()
                                ));

                                bankTableDataAdapter = new LigneChequesTableDataAdapter(context, chequeCaisses, bankDataTableView);
                                bankTableDataAdapter.setNotifyOnChange(true);
                                bankDataTableView.setDataAdapter(bankTableDataAdapter);
                                clearBankFields();
                            } else titleMsg = "Sélectionner une banque";

                        }

                    } else titleMsg = "Date échéance chèque Invalide";


                    if(!titleMsg.equals(""))  new MaterialDialog.Builder(this)
                                .title(titleMsg)
                                .backgroundColor(getResources().getColor(R.color.error))
                                //  .content(String.format(StringUtils.priceFormat(amount) + "") + new PrefUtils(context).getCurrency())
                                .positiveColor(Color.BLACK)
                                .positiveText("Exit")

                                // .negativeText("Non")
                                // .onNegative((dialog, which) -> setResult(false))
                                .onPositive((dialog, which) -> {

                                })
                                .show();
                }).onError(() -> {
                }).onSubmit(addCheckButton);
        clearBankFields();

    }

    void showDatePicker() {
        Calendar now = Calendar.getInstance();
        DatePickerDialog dpd = DatePickerDialog.newInstance(
                (view, year, monthOfYear, dayOfMonth) -> deadlineInputField.setText(DateUtils.date(year, monthOfYear, dayOfMonth)),
                now.get(Calendar.YEAR),
                now.get(Calendar.MONTH),
                now.get(Calendar.DAY_OF_MONTH)
        );
        dpd.show(getFragmentManager(), "Datepickerdialog");
    }

    void setSearchableSpinner() {
        List<Banque> banques = App.database.banqueDAO().getAll();
        if (banques != null) {
            List<String> strings = new ArrayList<>();
            strings.add("Aucune sélection...");
            for (Banque banque : banques)
                strings.add(banque.getbANDes());
            spinnerBank.setAdapter(new ArrayAdapter<String>
                    (
                            context,
                            android.R.layout.simple_list_item_1,
                            strings
                    )
            );
        }
    }

    /**
     * load the spinner with carte resto codes
     */
    void setCarteRestoSpinner() {

        List<CarteResto> carteresto = App.database.carteRestoDAO().getAll();


        if (carteresto != null) {
            List<String> strings = new ArrayList<>();
            strings.add("Aucune sélection...");


            for (CarteResto carteResto : carteresto) {
                cartResto.put(carteResto.societe, carteResto.code);
                strings.add(carteResto.societe);

            }

            spinnerCartResto.setAdapter(new ArrayAdapter<String>
                    (
                            context,
                            android.R.layout.simple_list_item_1,
                            strings
                    )
            );
        }
    }


    void setupRestoTicketInputView() {
        restoTicketDatatableView.setSwipeToRefreshEnabled(false);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            restoTicketDatatableView.setElevation(10);
        }
        restoTicketDatatableView.setHeaderBackground(R.color.material_teal500);
        traiteCaisses = new ArrayList<>();
        traiteCaisses = new ArrayList<>();
        setCarteRestoSpinner();

      /*  cardInputField.setOnFocusChangeListener((v, hasFocus) -> {
            if (hasFocus) {
                cardInputField.addTextChangedListener(new TextWatcher() {
                    @Override
                    public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                    }

                    @Override
                    public void onTextChanged(CharSequence s, int start, int before, int count) {
                    }

                    @Override
                    public void afterTextChanged(Editable s) {
                        if (cardInputField.getText().length() >= 8) {
                            priceInputField.setText(StringUtils.priceFormat((StringUtils.parseDoublePayment(cardInputField.getText().toString().substring(4, Math.min(cardInputField.getText().length(), 7)), 0) / 100) * StringUtils.parseDoublePayment(ticketNumberInputField.getText().toString(), 1)));
                        }
                    }
                });
            } else {
             //   cardInputField.clearTextChangedListeners();
                cardInputField.clearComposingText();
            }
        });

        */
        ticketNumberInputField.setOnFocusChangeListener((v, hasFocus) -> {
            if (hasFocus) {
                ticketNumberInputField.addTextChangedListener(new TextWatcher() {
                    @Override
                    public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                    }

                    @Override
                    public void onTextChanged(CharSequence s, int start, int before, int count) {
                    }

                    @Override
                    public void afterTextChanged(Editable s) {
                        if (!spinnerCartResto.getSelectedItem().toString().equals("Aucune sélection...")) {
                            if (cartResto.get(spinnerCartResto.getSelectedItem().toString()).length() >= 8) {
                                priceInputField.setText(StringUtils.priceFormat((StringUtils.parseDoublePayment(
                                        cartResto.get(spinnerCartResto.getSelectedItem().toString())
                                                .substring(3, Math.min(cartResto.get(spinnerCartResto.getSelectedItem().toString()).length(), 7)), 0) / 1000)
                                        * StringUtils.parseDoublePayment(ticketNumberInputField.getText().toString(), 1)));

                            }
                        }


                     /*   if (cardInputField.getText().length() >= 8) {
                            priceInputField.setText(StringUtils.priceFormat((StringUtils.parseDoublePayment(cardInputField.getText().toString().substring(3, Math.min(cardInputField.getText().length(), 7)), 0) / 1000) * StringUtils.parseDoublePayment(ticketNumberInputField.getText().toString(), 1)));
                        }*/
                    }
                });
            } else {
                ticketNumberInputField.clearTextChangedListeners();

            }
        });


        new Blorm.Builder()
                .field(ticketNumberInputField).is(filled)
                .andField(rateInputField).is(filled)
                .andField(priceInputField).is(filled)
                .onSuccess(() -> {
                    if (!spinnerCartResto.getSelectedItem().toString().equals("Aucune sélection...")) {
                        if (StringUtils.parseDoublePayment(amountInputField.getText().toString(), 0) > 0 && cartResto.get(spinnerCartResto.getSelectedItem().toString()).length() > 0) {
                            CarteResto response = App.database.carteRestoDAO().getOneByCode(cartResto.get(spinnerCartResto.getSelectedItem().toString()).substring(0, Math.min(cartResto.get(spinnerCartResto.getSelectedItem().toString()).length(), 3)));


                            traiteCaisses.add(new TraiteCaisse(traiteCaisses.size() + 1,
                                    new PrefUtils(context).getCaisseId(),
                                    new PrefUtils(context).getExercice(),
                                    DateUtils.dateToStr(new Date(), "MM/dd/yyyy HH:mm:ss"),
                                    ticketWithLines.getTicket().gettIKNomClient(),
                                    Utils.round(Calculator.calculateAmountTTCNet(StringUtils.parseDouble(priceInputField.getText().toString(), 0) * StringUtils.parseDouble(ticketNumberInputField.getText().toString(), 0), StringUtils.parseDouble(rateInputField.getText().toString(), 0)),3),
                                    response));


                            ticketRestoTableDataAdapter = new LigneTicketRestoTableDataAdapter(context, traiteCaisses, restoTicketDatatableView);
                            ticketRestoTableDataAdapter.setNotifyOnChange(true);
                            restoTicketDatatableView.setDataAdapter(ticketRestoTableDataAdapter);
                            clearTickerRestoFields();

                            ////   cardInputField.setHint("");
                            //  cardInputField.setBackgroundColor(Color.TRANSPARENT);
                        }

                        //  cardInputField.setHint("code dont exist !");
                        //  cardInputField.setHintTextColor(Color.RED);
                        // cardInputField.setHighlightColor(Color.RED);
                        //  cardInputField.setBackgroundColor(Color.RED);
                    }
                    else new MaterialDialog.Builder(this)
                            .title("Sélectionner type Restaurant")
                            .backgroundColor(getResources().getColor(R.color.error))
                            //  .content(String.format(StringUtils.priceFormat(amount) + "") + new PrefUtils(context).getCurrency())

                            .positiveColor(Color.BLACK)
                            .positiveText("Exit")

                            // .negativeText("Non")
                            // .onNegative((dialog, which) -> setResult(false))
                            .onPositive((dialog, which) -> {

                            })
                            .show();


                }).onError(() -> {

                }).onSubmit(addTicketButton);
        clearTickerRestoFields();
    }


    @OnClick(R.id.deleteChecksButton)
    public void deleteChecks() {
        chequeCaisses = new ArrayList<>();
        bankTableDataAdapter = new LigneChequesTableDataAdapter(this, chequeCaisses, bankDataTableView);
        bankDataTableView.setDataAdapter(bankTableDataAdapter);

        clearBankFields();

    }

    @OnClick(R.id.deleteTicketsButton)
    public void deleteTickets() {
        traiteCaisses = new ArrayList<>();
        ticketRestoTableDataAdapter = new LigneTicketRestoTableDataAdapter(this, traiteCaisses, restoTicketDatatableView);
        restoTicketDatatableView.setDataAdapter(ticketRestoTableDataAdapter);

        clearTickerRestoFields();

    }


    void clearBankFields() {
        numberInputField.setText(null);
        deadlineInputField.setText(null);
        setupCalculations();
    }

    void clearTickerRestoFields() {
        ticketNumberInputField.setText(null);
        rateInputField.setText(null);
        //   cardInputField.setText(null);
        priceInputField.setText(null);
        setupCalculations();
    }

    @OnClick(R.id.cash_button)
    public void cash() {
        if (Calculator.calculateRest(amount, parseDoublePayment(receivedInputField.getText().toString(), 0), chequeCaisses, traiteCaisses, false) > 0) {
            TransitionManager.beginDelayedTransition(scrollView, new Slide(Gravity.BOTTOM));
            cashInputView.setVisibility(View.VISIBLE);
            //receivedInputField.setText(StringUtils.priceFormat(amount))

            receivedInputField.setText(StringUtils.priceFormat(Calculator.calculateRest(amount, parseDoublePayment(receivedInputField.getText().toString(), 0), rEGCMntChQue, rEGCMntTraite, false)));
        }

        if (cashInputView.getVisibility() == View.VISIBLE) {
            cashInputView.getParent().requestChildFocus(cashInputView, cashInputView);
            //cash_button.setEnabled(false);
        }

        if (cashInputView.getVisibility() == View.INVISIBLE) {
            cashInputView.getParent().requestChildFocus(cashInputView, cashInputView);
            //cash_button.setEnabled(true);
        }
    }

    @OnClick(R.id.deleteCashButton)
    public void removeCash() {
        receivedInputField.setText("");
        setupCalculations();
        cashInputView.setVisibility(View.GONE);
        cashInputView.getParent().requestChildFocus(cashInputView, cashInputView);
    }

    @OnClick(R.id.deleteCheckLayout)
    public void removeCheck() {
        //   receivedInputField.setText("");
        //   receivedInputField.setText(String.format(StringUtils.priceFormat(Calculator.calculateRest(amount, 0.0, 0.0, rEGCMntTraite, false))));
        deleteChecks();
        // setupCalculations();
        bankCheckView.setVisibility(View.GONE);
        bankCheckView.getParent().requestChildFocus(bankCheckView, bankCheckView);

        //  receivedInputField.setText(rest.getText());
    }

    @OnClick(R.id.deleteCarteRestoLayout)
    public void removeCarteResto() {
        //   receivedInputField.setText("");
        //   receivedInputField.setText(String.format(StringUtils.priceFormat(Calculator.calculateRest(amount, 0.0, rEGCMntChQue, 0.0, false))));
        deleteTickets();
        //  setupCalculations();
        restoTicketView.setVisibility(View.GONE);
        restoTicketView.getParent().requestChildFocus(restoTicketView, restoTicketView);
        //  receivedInputField.setText(rest.getText());
    }

    @OnClick(R.id.check_button)
    public void check() {
        if (Calculator.calculateRest(amount, parseDoublePayment(receivedInputField.getText().toString(), 0), chequeCaisses, traiteCaisses, false) > 0) {
            // if(!StringUtils.priceFormat(Calculator.calculateRest(amount, rEGCMntEspeceRecue, rEGCMntChQue, rEGCMntTraite, false)).equals("0.000")){
            TransitionManager.beginDelayedTransition(content, new Slide(Gravity.BOTTOM));
            bankCheckView.setVisibility(View.VISIBLE);
        }

        if (bankCheckView.getVisibility() == View.VISIBLE) {
            bankCheckView.getParent().requestChildFocus(bankCheckView, bankCheckView);
        }
    }

    @OnClick(R.id.ticket_button)
    public void ticket() {
        if (Calculator.calculateRest(amount, parseDoublePayment(receivedInputField.getText().toString(), 0), chequeCaisses, traiteCaisses, false) > 0) {
            TransitionManager.beginDelayedTransition(content, new Slide(Gravity.BOTTOM));
            restoTicketView.setVisibility(View.VISIBLE);
        }

        if (restoTicketView.getVisibility() == View.VISIBLE) {
            restoTicketView.getParent().requestChildFocus(restoTicketView, restoTicketView);
        }
    }


    @Override
    public boolean onOptionsItemSelected(MenuItem menuItem) {
        switch (menuItem.getItemId()) {
            case android.R.id.home:
                onBackPressed();
                break;
        }
        return super.onOptionsItemSelected(menuItem);
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.add_ticket_menu, menu);
        MenuItem save = menu.findItem(R.id.save);
        MenuItem scan = menu.findItem(R.id.scan);
        scan.setVisible(false);
        save.setVisible(false);
        save.setIcon(new IconicsDrawable(context).icon(GoogleMaterial.Icon.gmd_send)
                .color(ResourcesCompat.getColor(getResources(), R.color.material_white, null))
                .sizeDp(24));
        save.setOnMenuItemClickListener(item -> {
            sendData(false,isRegPartiel);
            return false;
        });
        return super.onCreateOptionsMenu(menu);
    }


    void sendData(boolean facture, boolean isRegPartiel) {
        String  Title = "";
        String  positvBtnTxt = "";
        String  msg = "";
        if(isRegPartiel){
            if (Utils.round(rEGCMntEspeceRecue + rEGCMntChQue + rEGCMntTraite,3) <  Utils.round(amount,3)  ) {

                Utils.sendPaymentData(
                        true,
                        (long) ticket.gettIKNumTicket(),
                        client,
                        rEGCMntEspece,
                        rEGCMntChQue,
                        rEGCMntTraite,
                        rEGCMntTotalRecue,
                        rEGCMntEspeceRecue,
                        amount,
                        restAmount,
                        made,
                        chequeCaisses,
                        traiteCaisses);

                Utils.insertTicket(
                        true,
                        ticket ,
                        ticketWithLines,
                        client,
                        rEGCMntChQue,
                        rEGCMntTraite,
                        rEGCMntEspeceRecue);



                setResult(false);
            }
            else if (Utils.round(rEGCMntEspeceRecue + rEGCMntChQue + rEGCMntTraite,3) ==  Utils.round(amount,3)  ) {
                new MaterialDialog.Builder(context)
                        .title("Voulez-vous passer un réglement sans crédit?")
                       // .content("Voulez-vous imprimer le B.L.?")
                        .positiveText("Oui")
                        .negativeText("Non")
                        .onNegative((dialog, which) -> {

                        })
                        .onPositive((dialog, which) -> {
                            ticketWithLines.getTicket().settIKEtat(Globals.TICKET_STATE.PAYED.getValue());
                            saveTicketBlReglement(facture);
                        })
                        .show();
            }
            else if (Utils.round(rEGCMntEspeceRecue + rEGCMntChQue + rEGCMntTraite,3) > Utils.round(amount,3)  ) {
                 Title = "Le montant à regler est :" + String.format(StringUtils.priceFormat(amount) + "") + new PrefUtils(context).getCurrency();
                msg ="Montant Saisie : "+String.format(StringUtils.priceFormat(((rEGCMntEspeceRecue + rEGCMntChQue + rEGCMntTraite))) + "")  + new PrefUtils(context).getCurrency();

                new MaterialDialog.Builder(context)
                        .title(Title)
                        .content(msg)
                        .positiveText("ok")
                        .backgroundColor(getResources().getColor(R.color.error))
                        .positiveColor(Color.BLACK)
                        //  .negativeText("ok")
                      //  .onNegative((dialog, which) -> setResult(false))
                        .onPositive((dialog, which) -> {

                        })
                        .show();
            }

        }

        else {
            saveTicketBlReglement(facture);
        }

        }




void saveTicketBlReglement(boolean facture){
    String  Title = "";
    String  positvBtnTxt = "";
    String  msg = "";
    int versionCode = BuildConfig.VERSION_CODE;


    if (prefix == null) {

        prefix = StringUtils.PrefixFormatter(6,
                App.database.prefixeDAO().getOneById("ReglementCaisse").getpREPrefixe(),
                App.database.reglementCaisseDAO().getNewCode(App.database.prefixeDAO().getOneById("ReglementCaisse").getpREPrefixe()));



    }
    reglementCaisse = App.database.reglementCaisseDAO().getByTicket(ticketWithLines.getTicket().tIKNumTicket);
    if (reglementCaisse == null) {

        reglementCaisse = new ReglementCaisse(
                "RG_M_" + prefUtils.getUserStationId() + "_" + prefUtils.getUserId() + "_" + prefUtils.getSessionCaisseId() + "_" +versionCode+"_" + Utils.generateCommonCode(new Date(),String.valueOf((int)Math.random()),String.valueOf((int)Math.random()))+"_" + prefix,//    prefix,
                "RG_M_" + prefUtils.getUserStationId() + "_" + prefUtils.getUserId() + "_" + prefUtils.getSessionCaisseId() + "_" +versionCode+"_" + Utils.generateCommonCode(new Date(),String.valueOf((int)Math.random()),String.valueOf((int)Math.random()))+"_" + prefix,
                ticketWithLines.getTicket().gettIKExerc(),
                ticketWithLines.getTicket().gettIKIdCarnet(),
                (long) ticketWithLines.getTicket().gettIKNumTicket(),
                0L,
                ticketWithLines.getTicket().gettIKIdSCaisse(),
                prefUtils.getCaisseCode(),
                prefUtils.getCaisseStationId(),
                ticketWithLines.getTicket().gettIKCodClt(),
                ticketWithLines.getTicket().gettIKNomClient(),
                Globals.PAYEMENT_MODE.VARIOUS.getValue(),
                ticketWithLines.getTicket().gettIKDateHeureTicket(),
                rEGCMntEspeceRecue - made,
                0,
                //   Utils.round(ticketWithLines.getTicket().gettIKMtTTC() ,3) ,
                Utils.round(rEGCMntChQue ,3),
                rEGCMntTraite,
                Globals.PAYEMENT_REMARK,
                Utils.round(ticketWithLines.getTicket().gettIKMtTTC(),3),
                prefUtils.getCaisseStationId(),
                prefUtils.getUserId(),
                rEGCMntTotalRecue,
                rEGCMntEspeceRecue,
                restAmount,
                made
        );


    }
    else {
        reglementCaisse.setrEGCMntChQue(rEGCMntChQue);
        reglementCaisse.setrEGCMntTraite(rEGCMntTraite);
        reglementCaisse.rEGCMntEspece = rEGCMntEspeceRecue - made;
        reglementCaisse.rEGCMontant =  Utils.round(ticketWithLines.getTicket().gettIKMtTTC(),3);
        reglementCaisse.rEGCMntTotalRecue = rEGCMntTotalRecue;
        reglementCaisse.rEGCMntEspeceRecue = rEGCMntEspeceRecue;
        reglementCaisse.rest = restAmount;
        reglementCaisse.made = made;
    }

    ticketWithLines.getTicket().settIKMtEspece(rEGCMntEspeceRecue - made);
    ticketWithLines.getTicket().settIKMtCheque(String.valueOf(rEGCMntChQue));
    ticketWithLines.getTicket().settIKMtrecue(rEGCMntTraite);

    ticketWithLinesAndPayments = new TicketWithLinesAndPayments(
            ticketWithLines,
            reglementCaisse,
            chequeCaisses,
            traiteCaisses);



    if (ticketWithLines.getTicket().gettIKMtTTC() > 0) {

        if (Utils.round(rEGCMntEspeceRecue + rEGCMntChQue + rEGCMntTraite,3) ==  Utils.round(amount,3)  ) {
            if (notSaved) {
                saveData(facture);
            }

            try {
                if (!facture) {
                    new MaterialDialog.Builder(context)
                            .title("Imprimer ?")
                            .content("Voulez-vous imprimer le B.L.?")
                            .positiveText("Oui")
                            .negativeText("Non")
                            .onNegative((dialog, which) -> setResult(false))
                            .onPositive((dialog, which) -> {
                                printTicket(ticketWithLinesAndPayments, false);
                            })
                            .show();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {

            client = App.database.clientDAO().getOneByCode(ticketWithLines.getTicket().gettIKCodClt());

             /*   if (client.getCliisCredit()== 0){
                    msgTitle = "Le client n'a pas l'autorisation d'voir un credit";
                    positvBtnTxt ="Exit" ;
                }*/


            //  else{
            Title = "Le montant à regler est :" + String.format(StringUtils.priceFormat(amount) + "") + new PrefUtils(context).getCurrency();
            positvBtnTxt ="Exit" ;

            //  }
            rest.setText(String.format(getString(R.string.rest_field_title),
                    StringUtils.priceFormat(Calculator.calculateRest(amount, rEGCMntEspeceRecue, rEGCMntChQue, rEGCMntTraite, false)) + "", new PrefUtils(context).getCurrency()));


            if (Utils.round(rEGCMntEspeceRecue + rEGCMntChQue + rEGCMntTraite,3)>Utils.round(amount,3)) {
                msg ="Montant Saisie : "+String.format(StringUtils.priceFormat(((rEGCMntEspeceRecue + rEGCMntChQue + rEGCMntTraite))) + "")  + new PrefUtils(context).getCurrency();
            }
            else if (Calculator.calculateRest(amount, rEGCMntEspeceRecue, rEGCMntChQue, rEGCMntTraite, false)>0){
                msg = "Montant Manquant : "+ StringUtils.priceFormat(Calculator.calculateRest(amount, rEGCMntEspeceRecue, rEGCMntChQue, rEGCMntTraite, false))+ new PrefUtils(context).getCurrency();
            }

            if(!msg.equals(""))    new MaterialDialog.Builder(context)
                    .title(Title)
                    .content(msg)
                    .backgroundColor(getResources().getColor(R.color.error))
                    //  .content(String.format(StringUtils.priceFormat(amount) + "") + new PrefUtils(context).getCurrency())

                    .positiveColor(Color.BLACK)
                    .positiveText(positvBtnTxt)
                    // .negativeText("Non")
                    // .onNegative((dialog, which) -> setResult(false))
                    .onPositive((dialog, which) -> {

                    })
                    .show();
        }
    }

          /*  client = App.database.clientDAO().getOneByCode(ticketWithLines.getTicket().gettIKCodClt());
            if (client.getCliisCredit()== 0){
                if (Utils.round(rEGCMntEspeceRecue + rEGCMntChQue + rEGCMntTraite,3) ==  Utils.round(amount,3)  ) {
                    save(facture);
                }
                else {
                    String  Title = "";
                    String  positvBtnTxt = "";
                    String  msg = "";
                        Title = "Le client n'a pas l'autorisation d'voir un credit. Le montant à regler est :" + String.format(StringUtils.priceFormat(amount) + "") + new PrefUtils(context).getCurrency();
                        positvBtnTxt ="Exit" ;


                    rest.setText(String.format(getString(R.string.rest_field_title),
                            StringUtils.priceFormat(Calculator.calculateRest(amount, rEGCMntEspeceRecue, rEGCMntChQue, rEGCMntTraite, false)) + "", new PrefUtils(context).getCurrency()));


                    if (Utils.round(rEGCMntEspeceRecue + rEGCMntChQue + rEGCMntTraite,3)>Utils.round(amount,3)) {
                        msg ="Montant Saisie : "+String.format(StringUtils.priceFormat(((rEGCMntEspeceRecue + rEGCMntChQue + rEGCMntTraite))) + "")  + new PrefUtils(context).getCurrency();
                    }
                    else if (Calculator.calculateRest(amount, rEGCMntEspeceRecue, rEGCMntChQue, rEGCMntTraite, false)>0){
                        msg = "Montant Manquant : "+ StringUtils.priceFormat(Calculator.calculateRest(amount, rEGCMntEspeceRecue, rEGCMntChQue, rEGCMntTraite, false))+ new PrefUtils(context).getCurrency();
                    }

                    if(!msg.equals(""))    new MaterialDialog.Builder(context)
                            .title(Title)
                            .content(msg)
                            .backgroundColor(getResources().getColor(R.color.error))
                            //  .content(String.format(StringUtils.priceFormat(amount) + "") + new PrefUtils(context).getCurrency())

                            .positiveColor(Color.BLACK)
                            .positiveText(positvBtnTxt)
                            // .negativeText("Non")
                            // .onNegative((dialog, which) -> setResult(false))
                            .onPositive((dialog, which) -> {

                            })
                            .show();
                }
            }

            else save(facture);*/
}

    private void save(boolean facture){
        if (notSaved) {
            saveData(facture);
        }

        try {
            if (!facture) {
                new MaterialDialog.Builder(context)
                        .title("Imprimer ?")
                        .content("Voulez-vous imprimer le B.L.?")
                        .positiveText("Oui")
                        .negativeText("Non")
                        .onNegative((dialog, which) -> setResult(false))
                        .onPositive((dialog, which) -> {
                            printTicket(ticketWithLinesAndPayments, false);
                        })
                        .show();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    private void printTicket(TicketWithLinesAndPayments ticketWithLinesAndPayments, boolean credit) {

        if(prefUtils.getPrintA4Enabled()){
            new File(Objects.requireNonNull(Comman.Companion.getAppPath(context))).mkdirs();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                WifiPrint.Companion.createTicketPDFFile(context,ticketWithLinesAndPayments,() -> {
                    setResult(Activity.RESULT_OK, new Intent());
                    finish();
                });
            }
        }
        else {
            BluetoothAdapter mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
            if (mBluetoothAdapter == null) {
                // Device does not support Bluetooth
                Toasty.error(getApplication(), "No Bluetooth available in this Device").show();

            }
            else if (!mBluetoothAdapter.isEnabled()) {
                // Bluetooth is not enabled :)

                Toasty.error(getApplication(), "Activer le Bluetooth").show();


                Intent enableIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
                if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                    // TODO: Consider calling
                    //    ActivityCompat#requestPermissions
                    // here to request the missing permissions, and then overriding
                    //   public void onRequestPermissionsResult(int requestCode, String[] permissions,
                    //                                          int[] grantResults)
                    // to handle the case where the user grants the permission. See the documentation
                    // for ActivityCompat#requestPermissions for more details.
                    return;
                }

                startActivityForResult(enableIntent, REQUEST_ENABLE_BT);
            } else {
                // Bluetooth is enabled

                mService = new BluetoothService(context, new Handler() {
                    @Override
                    public void handleMessage(Message msg2) {
                        switch (msg2.what) {
                            case BluetoothService.MESSAGE_STATE_CHANGE:
                                switch (msg2.arg1) {
                                    case BluetoothService.STATE_CONNECTED:
                                        Toast.makeText(context, "Connect successful",
                                                Toast.LENGTH_SHORT).show();
                                        if (ticketWithLinesAndPayments != null)
                                            try {
                                                new PrinterHelper(mService, DEFAULT_ENCODING).printTicket(context,
                                                        ticketWithLinesAndPayments);
                                                //   finish();
                                                goToBlFragment();
                                                try {
                                                    TicketActivity.getInstance().finish();
                                                } catch (Exception e) {
                                                    Log.d("dddd", e.getMessage());
                                                }
                                            } catch (IOException e) {
                                                e.printStackTrace();
                                            }
                                        break;
                                    case BluetoothService.STATE_CONNECTING:
                                        break;
                                    case BluetoothService.STATE_LISTEN:
                                    case BluetoothService.STATE_NONE:
                                        Toasty.info(getApplication(), "Unable to connect device").show();

                                        break;
                                }
                                break;
                            case BluetoothService.MESSAGE_CONNECTION_LOST:
                                Toast.makeText(context, "Device connection was lost",
                                        Toast.LENGTH_SHORT).show();
                                break;
                            case BluetoothService.MESSAGE_UNABLE_CONNECT:
                                //    Toast.makeText(context, "Unable to connect device ", Toast.LENGTH_SHORT).show();
                                break;
                            case Activity.RESULT_CANCELED:
                                // finish();
                                Toasty.info(getApplication(), "RESULT_CANCELED Bluetooth").show();

                                goToBlFragment();
                                break;
                        }
                    }

                });
                if (!mService.isBTopen()) {
                    Intent enableIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
                    if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                        // TODO: Consider calling
                        //    ActivityCompat#requestPermissions
                        // here to request the missing permissions, and then overriding
                        //   public void onRequestPermissionsResult(int requestCode, String[] permissions,
                        //                                          int[] grantResults)
                        // to handle the case where the user grants the permission. See the documentation
                        // for ActivityCompat#requestPermissions for more details.
                        return;
                    }

                    startActivityForResult(enableIntent, REQUEST_ENABLE_BT);
                }
                if (!mService.isAvailable()) {
                    Toast.makeText(context, "Bluetooth is not available", Toast.LENGTH_LONG).show();
                }
                else {
                    Intent serverIntent = new Intent(context, DeviceListActivity.class);
                    startActivityForResult(serverIntent, REQUEST_CONNECT_DEVICE);
                }
            }
        }




    }

    String cutendfix(String string, String numTikM) {
        String toaadd = string.substring(0, org.apache.commons.lang3.StringUtils.ordinalIndexOf(string, "_", 5) + 1);


        return toaadd + App.database.prefixeDAO().getOneById("ReglementCaisse").getpREPrefixe()
                + numTikM.substring(org.apache.commons.lang3.StringUtils.ordinalIndexOf(numTikM, "_", 5) + 1, numTikM.length());

    }

    void saveData(boolean facture) {


        notSaved = false;
        ticketWithLinesAndPayments.getTicket().settIKDateHeureTicket(DateUtils.dateToStr(DateUtils.strToDate(ticketWithLinesAndPayments.getTicket().gettIKDateHeureTicket(), "MM/dd/yyyy HH:mm:ss"), "yyyy-MM-dd HH:mm"));
        ticketWithLinesAndPayments.getTicket().setSync(facture);
        ticketWithLinesAndPayments.getTicket().setStatus(Globals.ITEM_STATUS.INSERTED.getStatus());

        App.database.ticketDAO().deleteByCode(ticketWithLinesAndPayments.getTicket().tIKNumTicket, prefUtils.getExercice());
        App.database.ticketDAO().insert(ticketWithLinesAndPayments.getTicket());
        //System.out.println("numticket " + ticketWithLines.getTicket().tIKNumTicket);
        ligneTicketViewModel.deleteByTicket(ticketWithLines.getTicket().gettIKNumTicket(),
                true,
                ticketWithLines.getTicket().gettIKIdCarnet(),
                ticketWithLines.getTicket().gettIKExerc());


        App.database.ligneTicketDAO().insertAll(ticketWithLines.getLigneTicket());

        /*App.database.ligneTicketDAO().deleteByTicket(ticketWithLines.getTicket().tIKNumTicket,
                ticketWithLines.getTicket().tIKIdCarnet,
                ticketWithLines.getTicket().tIKExerc
        );

         */
        /*for (LigneTicket ligneTicket : ticketWithLinesAndPayments.getLigneTicket()) {
            //Article article = ligneTicket.getArticle();
            Article article = App.database.articleDAO().getOneByCodeAndStation(ligneTicket.getlTCodArt(), App.prefUtils.getUserStationId());
            double qte = article.getsARTQte();
            article.setsARTQte(qte - ligneTicket.getlTQte());
            double newQte = qte - ligneTicket.getlTQte();
            article.setsARTQte(newQte);
            App.database.articleDAO().insert(article);
        }
         */

        ticketWithLinesAndPayments.getReglement().setrEGCDateReg(DateUtils.dateToStr(DateUtils.strToDate(ticketWithLinesAndPayments.getReglement().getrEGCDateReg(), "MM/dd/yyyy HH:mm:ss"), "yyyy-MM-dd HH:mm"));
        ticketWithLinesAndPayments.getReglement().setSync(false);
        ticketWithLinesAndPayments.getReglement().setStatus(Globals.ITEM_STATUS.INSERTED.getStatus());

/**
 * the tow lines blOW are a temp mesure  to fix regMobile numb pb when new session created
 */
     //  ticketWithLinesAndPayments.getReglement().setrEGCCode_M(cutendfix(ticketWithLinesAndPayments.getReglement().rEGCCode_M, ticketWithLinesAndPayments.getTicket().tikNumTicketM));
      //  ticketWithLinesAndPayments.getReglement().setrEGCCode(cutendfix(ticketWithLinesAndPayments.getReglement().rEGCCode_M, ticketWithLinesAndPayments.getTicket().tikNumTicketM));

       /*ticketWithLinesAndPayments.getReglement().setrEGCCode(
                App.database.prefixeDAO().getOneById("ReglementCaisse").
                        getpREPrefixe() + ticketWithLinesAndPayments.getTicket().tikNumTicketM.substring(
                        org.apache.commons.lang3.StringUtils.ordinalIndexOf(ticketWithLinesAndPayments.getTicket().tikNumTicketM, "_", 5) + 1, ticketWithLinesAndPayments.getTicket().tikNumTicketM.length()));
*/

        App.database.reglementCaisseDAO().insert(ticketWithLinesAndPayments.getReglement());


        // List<TraiteCaisse> traiteCaisses = new ArrayList<>();

        //  App.database.traiteCaisseDAO().insertAll(traiteCaisses);
      /*  for (ChequeCaisse chequeCaisse :
                ticketWithLinesAndPayments.getCheques()) {
            chequeCaisse.setEcheanceCheque(DateUtils.dateToStr(DateUtils.strToDate(chequeCaisse.getEcheanceCheque(), "MM/dd/yyyy hh:mm:ss"), "yyyy-MM-dd HH:mm"));
            chequeCaisse.setSync(false);
            chequeCaisse.setStatus(Globals.ITEM_STATUS.INSERTED.getStatus());
        }*/


        for (ChequeCaisse chequeCaisse : ticketWithLinesAndPayments.getCheques()) {
            chequeCaisse.setEcheanceCheque(DateUtils.dateToStr(DateUtils.strToDate(chequeCaisse.getEcheanceCheque(), "MM/dd/yyyy hh:mm:ss"), "yyyy-MM-dd HH:mm"));
            chequeCaisse.setSync(false);
            chequeCaisse.setStatus(Globals.ITEM_STATUS.INSERTED.getStatus());
            chequeCaisse.setReglement(ticketWithLinesAndPayments.getReglement().rEGCCode_M);
            chequeCaisse.setReglement_m(ticketWithLinesAndPayments.getReglement().rEGCCode_M);

        }
        App.database.chequeCaisseDAO().insertAll(ticketWithLinesAndPayments.getCheques());


        for (TraiteCaisse traiteCaisses : ticketWithLinesAndPayments.getTraites()) {
            traiteCaisses.settRAITEcheance(DateUtils.dateToStr(DateUtils.strToDate(traiteCaisses.gettRAITEcheance(), "MM/dd/yyyy hh:mm:ss"), "yyyy-MM-dd HH:mm"));
            traiteCaisses.setSync(false);
            traiteCaisses.setStatus(Globals.ITEM_STATUS.INSERTED.getStatus());

            traiteCaisses.settRAITNUM_M(ticketWithLinesAndPayments.getReglement().rEGCCode_M);
            traiteCaisses.settRAITNum(ticketWithLinesAndPayments.getReglement().rEGCCode_M);
            App.database.traiteCaisseDAO().insert(traiteCaisses);

        }

    }




    void setResult(boolean doPrint) {
        new Handler().postDelayed(() -> {
            for (LigneTicket ligneTicket : ticketWithLines.getLigneTicket()) {
                try {
                    Article article = App.database.articleDAO().getOneByCodeAndStation(ligneTicket.getlTCodArt(), prefUtils.getUserStationId());
                    StationStock stationStock = App.database.stationStockDAO().getOneByCode(article.getaRTCode(), prefUtils.getUserStationId());
                    double SART_Qte = stationStock.getSARTQte() - ligneTicket.getlTQte();
                    stationStock.setSARTQte(SART_Qte);
                    ArticleCodeBar articleCodeBar = App.database.articleCodeBarDAO().getParent(article.getaRTCodeBar());
                    if (ObjectUtils.isNotEmpty(articleCodeBar)) {
                        stationStock.setSARTQte(SART_Qte);
                    }
                    article.setaRTQteStock(SART_Qte);

                    App.database.stationStockDAO().insertOne(stationStock);
                    App.database.articleDAO().insert(article);
                } catch (Exception e) {
                    Log.d("save stock error", "sendData: " + e);
                }

            }
            if (doPrint) {
                printTicket(ticketWithLinesAndPayments, false);
            } else {
                setResult(Activity.RESULT_OK, getIntent().putExtra(TICKET_WITH_LINES_AND_PAYEMENT_KEY, ticketWithLinesAndPayments));
               // finish();
                goToBlFragment();
                try {
                    TicketActivity.getInstance().finish();
                } catch (Exception e) {
                    Log.d("dddd", e.getMessage());
                }
            }

        }, 10);
    }

    @Override
    public void onStart() {
        super.onStart();
    }

    @Override
    public void onBackPressed() {
        if (!blockRetour) {
            super.onBackPressed();
            try {
                notSaved = false;
                ticketWithLines = null;
                TicketActivity.getInstance().ticket = null;
                TicketActivity.getInstance().save.setEnabled(true);
            } catch (Exception e) {
                Log.d("dddd", e.getMessage());
            }
        } else {
            Toasty.info(context, R.string.cannot_retour).show();
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        switch (requestCode) {
            case REQUEST_ENABLE_BT:
                if (resultCode == Activity.RESULT_OK) {
                    Toast.makeText(context, "Bluetooth open successful", Toast.LENGTH_LONG).show();
                } else {
                    Toast.makeText(context, "Bluetooth failed to connect", Toast.LENGTH_LONG).show();

                }
                break;
            case REQUEST_CONNECT_DEVICE:
                if (resultCode == Activity.RESULT_OK) {
                    String address = data.getExtras()
                            .getString(DeviceListActivity.EXTRA_DEVICE_ADDRESS);
                    try {
                        con_dev = mService.getDevByMac(address);
                        mService.connect(con_dev);
                    } catch (Exception e) {
                        Log.d("erro", e.getMessage());
                    }
                }
            case Activity.RESULT_CANCELED:
                blockRetour = true;
//                finish();
//                try {
//                    TicketActivity.getInstance().finish();
//                } catch (Exception e) {
//                    Log.d("dddd", e.getMessage());
//                }
                break;
        }
    }

    public void calcuteTotalAndRemisePrices(Ticket ticket, List<LigneTicket> ligneTickets) {
        double mntRemise = 0.0;
        double total = 0.0;
        for (LigneTicket ligneTicket : ligneTickets
        ) {
            try {
                mntRemise += Utils.round(ligneTicket.lTQte * ligneTicket.getArticle().pvttc * ligneTicket.lTTauxRemise / 100,3);
                total += Utils.round(ligneTicket.lTQte * ligneTicket.getArticle().pvttc,3);
            } catch (Exception e) {
                Log.d("errcoammabdefragment", e.getMessage());
            }
        }
        ticket.settIKMtRemise(mntRemise);
        ticket.settIKMtTTC(total);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        unbinder.unbind();

        if (mService != null)
            mService.stop();
        mService = null;

    }


    void goToBlFragment(){
      /*  Intent i = new Intent(this, MainActivity.class);
        i.putExtra("frgmntToLoad", "BL");
        // Now start your activity
        startActivity(i);*/
        finish();
    }
}