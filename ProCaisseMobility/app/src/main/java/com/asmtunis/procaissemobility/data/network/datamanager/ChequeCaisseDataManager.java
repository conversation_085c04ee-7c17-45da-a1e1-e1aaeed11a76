package com.asmtunis.procaissemobility.data.network.datamanager;


import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.ChequeCaisse;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.ChequeCaisseService;

import java.util.List;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

/**
 * Created by PC on 10/4/2017.
 */

public class ChequeCaisseDataManager  {
    private static ChequeCaisseDataManager sInstance;

    private final ChequeCaisseService mChequeCaisseService;

    public ChequeCaisseDataManager() {
        mChequeCaisseService = new ServiceFactory<>(ChequeCaisseService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"ChequeCaisse")).makeService();

    }

    public static ChequeCaisseDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new ChequeCaisseDataManager();
        }
        return sInstance;
    }
/*
    public void getChequeCaisse(GenericObject genericObject,
                                                 RemoteCallback<List<LigneTicket>> listener) {
        mChequeCaisseService.getChequeCaisse(genericObject)
                .enqueue(listener);
    }
*/

    public void getChequeCaisseByReglement(GenericObject genericObject,
                                RemoteCallback<List<ChequeCaisse>> listener) {
        mChequeCaisseService.getChequeCaisseByReglement(genericObject)
                .enqueue(listener);
    }
    public void getChequeCaisseByReglements(GenericObject genericObject,
                                       RemoteCallback<List<List<ChequeCaisse>>> listener) {
        mChequeCaisseService.getChequeCaisseByReglements(genericObject)
                .enqueue(listener);
    }
}

