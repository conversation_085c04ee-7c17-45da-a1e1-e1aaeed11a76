package com.asmtunis.procaissemobility.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.Client;

import java.util.List;

/**
 * Created by PC on 11/18/2017.
 */
@Dao
public interface ClientDAO {

    @Query("SELECT * FROM Client WHERE CLI_Etat LIKE 'Active' order by CLI_NomPren asc")
    List<Client> getAll();

   // @Query("SELECT * FROM Client order by CLI_NomPren asc")
    @Query("SELECT * FROM Client WHERE CLI_Etat LIKE 'Active' order by strftime('%Y-%m-%d %H-%M-%S',CLI_Date_Cre) desc")
    LiveData<List<Client>> getAllMutable();

    @Query("SELECT * FROM Client WHERE CLI_Type != :CLIType and CLI_Etat LIKE 'Active' order by strftime('%Y-%m-%d %H-%M-%S',CLI_Date_Cre) desc ")
    LiveData<List<Client>> getAllByexepProspectMutable(String CLIType);

    @Query("SELECT * FROM Client WHERE CLI_Type = :CLIType and CLI_Etat LIKE 'Active' order by strftime('%Y-%m-%d %H-%M-%S',CLI_Date_Cre) desc ")
    LiveData<List<Client>> getAllProspectMutable(String CLIType);

    @Query("SELECT COUNT(*) FROM Client where CLI_Etat LIKE 'Active'")
    int count();

    @Query("SELECT count(*) FROM Client where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    int getNoSyncCount();

    @Query("SELECT count(*) FROM Client where isSync=0 and  Status='TOUPDATE'")
    int getNoSyncCountToUpdate();

    @Query("SELECT count(*) FROM Client where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    LiveData<Integer> getNoSyncCountMutable();

    @Query("SELECT count(*) FROM Client where CLI_Etat LIKE 'Active'")
        //@Query("SELECT count(*) FROM Client WHERE CLI_Station = :station")
    Integer getCount();


    @Query("SELECT * FROM Client WHERE isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    List<Client> getByQuery();

    @Query("SELECT * FROM Client WHERE isSync=0 and  Status='TOUPDATE'")
    Client getClientToUpdateByQuery();

    @Query("SELECT count(*) FROM Client where isSync=0 and  Status='TOUPDATE'")
    LiveData<Integer> getNoSyncToUpdateCountMutable();

    @Query("SELECT ifnull(MAX(cast(substr(CLI_Code,length(:prefix) + 1 ,length(CLI_Code)) as integer)),0)+1 FROM Client WHERE substr(CLI_Code, 0 ,length(:prefix)+1) = :prefix")
    Object getNewCode(String prefix);


    @Query("SELECT * FROM Client WHERE CLI_Code = :code and CLI_Etat LIKE 'Active'")
    Client getOneByCode(String code);

    @Query("SELECT * FROM Client WHERE CLI_Code_M = :code and CLI_Etat LIKE 'Active'")
    Client getOneByCodeM(String code);


    @Query("UPDATE Client SET Solde = :Solde where CLI_Code = :CLICode")
    void updateSoldClient(Double Solde, String CLICode);

    @Query("UPDATE Client SET Cli_Credit = :CLICredit where CLI_Code = :CLICode")
    void updateSoldCredit(Double CLICredit, String CLICode);

    @Query("UPDATE Client SET Debit = :CLIDebit where CLI_Code = :CLICode")
    void updateSoldDebit(Double CLIDebit, String CLICode);

    //@Query("SELECT * FROM Client WHERE CLI_Station = :station ")
    @Query("SELECT * FROM Client where CLI_Etat LIKE 'Active'")
    List<Client> getByStation();

    //@Query("SELECT * FROM Client WHERE CLI_Station = :station order by CAST(CLI_Date_Cre AS DATE) desc ,CLI_Date_Cre desc")
    @Query("SELECT * FROM Client where CLI_Etat LIKE 'Active' order by strftime('%Y-%m-%d %H-%M',CLI_Date_Cre) desc ")
    LiveData<List<Client>> getByStationMutable();

    @Query("SELECT * FROM Client where CLI_Etat LIKE 'Active' and CLI_Station = :station or CLI_Station is null order by CLI_NomPren asc ")
    LiveData<List<Client>> getAllByStationMutable(String station);

    @Query("SELECT * FROM Client where CLI_Etat LIKE 'Active' LIMIT 1")
    Client getOne();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(Client item);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<Client> items);

   // @Query("DELETE FROM Client where Status='SELECTED'")
 //   void deleteAll();

    @Query("DELETE FROM Client")
    void deleteAll();

    @Query("DELETE FROM Client where CLI_Code= :code_client")
    void deleteById(String code_client);

    @Query("DELETE FROM Client where CLI_Code_M= :code_client")
    void deleteBycodeM(String code_client);

    @Query("SELECT strftime('%Y-%m-%d',CLI_DDm) FROM Client order by strftime('%Y-%m-%d %H-%M',CLI_DDm) desc limit 1")
    String getDDM();

    @Query("SELECT count(*) FROM Client where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    Integer getNoSyncCountNonMutable();

    @Query("SELECT count(*) FROM Client where CLI_Station=:station and CLI_Etat LIKE 'Active'")
    LiveData<Integer> getAllCountBySessionMutable(String station);
}
