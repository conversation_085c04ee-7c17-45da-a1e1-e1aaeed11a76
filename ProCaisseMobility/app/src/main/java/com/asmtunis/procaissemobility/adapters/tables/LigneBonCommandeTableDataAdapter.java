package com.asmtunis.procaissemobility.adapters.tables;

import android.content.Context;
import android.graphics.Typeface;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.data.models.LigneBonCommande;
import com.asmtunis.procaissemobility.data.models.LigneTicket;
import com.asmtunis.procaissemobility.helper.utils.Calculator;
import com.asmtunis.procaissemobility.helper.utils.StringUtils;
import com.asmtunis.procaissemobility.ui.components.SortableLigneCommandeTableView;

import java.text.NumberFormat;
import java.util.List;

import de.codecrafters.tableview.toolkit.LongPressAwareTableDataAdapter;

/**
 * ²
 */

public class LigneBonCommandeTableDataAdapter extends LongPressAwareTableDataAdapter<LigneBonCommande> {

    private static final int TEXT_SIZE = 16;
    private static final NumberFormat PRICE_FORMATTER = NumberFormat.getNumberInstance();

    public LigneBonCommandeTableDataAdapter(final Context context, final List<LigneBonCommande> data, final SortableLigneCommandeTableView tableView) {
        super(context, data, tableView);
    }

    @Override
    public View getDefaultCellView(int rowIndex, int columnIndex, ViewGroup parentView) {
        final LigneBonCommande ligneTicket = getRowData(rowIndex);
        View renderedView = null;

        switch (columnIndex) {
            case 0:
                renderedView = renderQuantity(ligneTicket);
                break;

            case 1:
                renderedView = renderLigneTicketName(ligneTicket);
                break;

            case 2:
                renderedView = renderUnitPrice(ligneTicket);
                break;

            case 3:

                renderedView = renderDiscount(ligneTicket);
                break;
            case 4:
                renderedView = renderPriceWithDiscount(ligneTicket);
                break;
        }

        return renderedView;
    }

    @Override
    public View getLongPressCellView(int rowIndex, int columnIndex, ViewGroup parentView) {

        return getDefaultCellView(rowIndex, columnIndex, parentView);
    }


    private View renderLigneTicketName(final LigneBonCommande ligneTicket) {
        final TextView textView = new TextView(getContext());
          String ligneTicketNameString = "";

         if(ligneTicket.getArticle() != null){
             ligneTicketNameString =   StringUtils.isEmptyString(ligneTicket.getArticle().getaRTDesignation()) ? ligneTicket.getArticle().getmARDesignation() : ligneTicket.getArticle().getaRTDesignation();
         }else  ligneTicketNameString = ligneTicket.getLGDEVCodeArt(); // l'article introuvable

        textView.setText(ligneTicketNameString);
        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        textView.setHeight(140);
        textView.setGravity(Gravity.CENTER_VERTICAL);
        return textView;
    }

    private View renderQuantity(final LigneBonCommande ligneTicket) {
        final TextView textView = new TextView(getContext());

        final String quantityString = String.valueOf(StringUtils.decimalFormat(Double.parseDouble(ligneTicket.getLGDEVQte())));

        textView.setText(quantityString);
        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        textView.setHeight(140);
        textView.setGravity(Gravity.CENTER_VERTICAL);
        return textView;
    }


    private View renderUnitPrice(final LigneBonCommande ligneTicket) {
        final TextView textView = new TextView(getContext());

        //  final String priceString = StringUtils.priceFormat(ligneTicket.getArticle().getPvttc());
       final String priceString = StringUtils.priceFormat(Double.parseDouble(ligneTicket.getLGDEVPUTTC()));

        textView.setText(priceString);
        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        textView.setHeight(140);
        textView.setGravity(Gravity.CENTER_VERTICAL);
        double amountTTC = Double.parseDouble(ligneTicket.getLGDEVMntTTC());
        if (amountTTC > 50000) {
            textView.setTextColor(ContextCompat.getColor(getContext(), R.color.material_green700));
        } else if (amountTTC > 100000) {
            textView.setTextColor(ContextCompat.getColor(getContext(), R.color.material_red600));
        }

        return textView;
    }


    private View renderPriceWithDiscount(final LigneBonCommande ligneTicket) {
        final TextView textView = new TextView(getContext());
        //  ligneTicket.setLGDEVMntTTC((Double.parseDouble(ligneTicket.getLGDEVQte()) * Calculator.calculateAmountTTCNet(ligneTicket.getArticle().getPvttc(), Double.parseDouble(ligneTicket.getLGDEVRemise()))) + "");
        //  ligneTicket.setLGDEVMntTTC((Calculator.calculateAmountTTCNet(Double.parseDouble(ligneTicket.getLGDEVMntTTC()), Double.parseDouble(ligneTicket.getLGDEVRemise())) )+ "");
        ligneTicket.setLGDEVMntTTC((Double.parseDouble(ligneTicket.getLGDEVQte()) * Calculator.calculateAmountTTCNet(Double.parseDouble(ligneTicket.getLGDEVPUTTC()), Double.parseDouble(ligneTicket.getLGDEVRemise()))) + "");


        final String priceString = StringUtils.priceFormat(Double.parseDouble(ligneTicket.getLGDEVMntTTC()));
        textView.setText(priceString);
        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        textView.setTypeface(textView.getTypeface(), Typeface.BOLD);
        textView.setHeight(140);
        textView.setGravity(Gravity.CENTER_VERTICAL);
        if (Double.parseDouble(ligneTicket.getLGDEVRemise()) > 0) {
            textView.setTextColor(ContextCompat.getColor(getContext(), R.color.md_blue_grey_800));

        }

        return textView;
    }


    private View renderDiscount(final LigneBonCommande ligneTicket) {
        final TextView textView = new TextView(getContext());
        String priceString =0+"";
        try {
            priceString = PRICE_FORMATTER.format(Double.parseDouble(ligneTicket.getLGDEVRemise())) + " %";

        } catch (Exception e) {
            priceString = PRICE_FORMATTER.format(0) + " %";

        }

        textView.setText(priceString);
        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        textView.setHeight(140);
        textView.setGravity(Gravity.CENTER_VERTICAL);
        if (Double.parseDouble(ligneTicket.getLGDEVMntTTC()) > 0) {
            textView.setTextColor(ContextCompat.getColor(getContext(), R.color.material_green600));
        }

        return textView;
    }


    private View renderString(final String value) {
        final TextView textView = new TextView(getContext());
        textView.setText(value);
        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        textView.setHeight(140);
        textView.setGravity(Gravity.CENTER_VERTICAL);
        return textView;
    }

    private static class LigneTicketNameUpdater implements TextWatcher {

        private LigneTicket ligneTicketToUpdate;

        public LigneTicketNameUpdater(LigneTicket ligneTicketToUpdate) {
            this.ligneTicketToUpdate = ligneTicketToUpdate;
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            // no used
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            // not used
        }

        @Override
        public void afterTextChanged(Editable s) {
            //    ligneTicketToUpdate.setName(s.toString());
        }
    }


}
