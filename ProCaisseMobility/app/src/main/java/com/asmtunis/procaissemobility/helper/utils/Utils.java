package com.asmtunis.procaissemobility.helper.utils;

import static android.content.ContentValues.TAG;
import static com.asmtunis.procaissemobility.App.prefUtils;
import static com.asmtunis.procaissemobility.helper.utils.StringUtils.decimalFormat;

import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.ColorMatrix;
import android.graphics.ColorMatrixColorFilter;
import android.graphics.Paint;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.LayerDrawable;
import android.icu.text.SimpleDateFormat;
import android.os.Build;
import android.util.Log;
import android.util.Patterns;

import androidx.annotation.RequiresApi;

import com.asmtunis.procaissemobility.BuildConfig;
import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Article;
import com.asmtunis.procaissemobility.data.models.ChequeCaisse;
import com.asmtunis.procaissemobility.data.models.Client;
import com.asmtunis.procaissemobility.data.models.LigneTicket;
import com.asmtunis.procaissemobility.data.models.ReglementCaisse;
import com.asmtunis.procaissemobility.data.models.StationStock;
import com.asmtunis.procaissemobility.data.models.Ticket;
import com.asmtunis.procaissemobility.data.models.TicketWithLines;
import com.asmtunis.procaissemobility.data.models.TicketWithLinesAndPayments;
import com.asmtunis.procaissemobility.data.models.TraiteCaisse;
import com.asmtunis.procaissemobility.helper.Globals;
import com.asmtunis.procaissemobility.helper.Hashids;
import com.blankj.utilcode.util.DeviceUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <NAME_EMAIL>
 */

public class Utils {
    static String versionCode = BuildConfig.VERSION_NAME;

    public static String generateCode(Date date) {
        return versionCode + "_" + new Hashids(DeviceUtils.getAndroidID()).encode(date.getTime());
    }

    public static String generatevcAutreCode() {
        return "AU_M_" + prefUtils.getUserId() + "_" + Utils.generateCode(new Date());
    }


    public static String generateCommonCode(Date date, String num, String numLign) {
        return versionCode + "_" + new Hashids(DeviceUtils.getAndroidID()).encode(
                date.getTime() +
                        Long.parseLong(num) +
                        Long.parseLong(numLign));
    }

    public static String generateMobileTicketCode(Date date, String ticketStation, String ticketNumber) {
        return "BL_M_" + ticketStation + "_" + prefUtils.getUserId() + "_" + ticketNumber
                + "_" + Utils.generateCommonCode(date, ticketNumber, String.valueOf((int) Math.random()));
    }

    public static String generateMobileLigneTicketCode(String ticketStation, String ticketNumber, String tiknumOrdre) {
        return "L_BL_M_" + ticketStation + "_" + prefUtils.getUserId() + "_" + ticketNumber
                + "_" + Utils.generateCommonCode(new Date(), ticketNumber, tiknumOrdre);
    }

    public static String generateMobileDevisCode(String ticketStation, String devCode) {
        return "DEV_M_" + ticketStation + "_" + prefUtils.getUserId() + "_" /*+ devCode
                + "_" */ + Utils.generateCommonCode(new Date(), String.valueOf((int) Math.random()), String.valueOf((int) Math.random()));
    }

    public static String generateMobileLigneDevisCode(String ticketStation, String devCode, String num, String numLign) {
        return "L_DEV_M_" + ticketStation + "_" + prefUtils.getUserId() + "_" + devCode
                + "_" + Utils.generateCommonCode(new Date(), String.valueOf(num), String.valueOf(numLign));
    }

    public static String generateClientCode(String ticketStation, String cliCode) {
        return "CL_M_" + ticketStation + "_" + prefUtils.getUserId() /*+ "_" + cliCode*/
                + "_" + Utils.generateCommonCode(new Date(), String.valueOf((int) Math.random()), String.valueOf((int) Math.random()));
    }

    // UNICODE 0x23 = #
    public static final byte[] UNICODE_TEXT = new byte[]{0x23, 0x23, 0x23,
            0x23, 0x23, 0x23, 0x23, 0x23, 0x23, 0x23, 0x23, 0x23, 0x23, 0x23, 0x23,
            0x23, 0x23, 0x23, 0x23, 0x23, 0x23, 0x23, 0x23, 0x23, 0x23, 0x23, 0x23,
            0x23, 0x23, 0x23};

    private static String hexStr = "0123456789ABCDEF";
    private static String[] binaryArray = {"0000", "0001", "0010", "0011",
            "0100", "0101", "0110", "0111", "1000", "1001", "1010", "1011",
            "1100", "1101", "1110", "1111"};

    public static byte[] decodeBitmap(Bitmap bmp) {
        int bmpWidth = bmp.getWidth();
        int bmpHeight = bmp.getHeight();

        List<String> list = new ArrayList<String>(); //binaryString list
        StringBuffer sb;


        int bitLen = bmpWidth / 8;
        int zeroCount = bmpWidth % 8;

        String zeroStr = "";
        if (zeroCount > 0) {
            bitLen = bmpWidth / 8 + 1;
            for (int i = 0; i < (8 - zeroCount); i++) {
                zeroStr = zeroStr + "0";
            }
        }

        for (int i = 0; i < bmpHeight; i++) {
            sb = new StringBuffer();
            for (int j = 0; j < bmpWidth; j++) {
                int color = bmp.getPixel(j, i);

                int r = (color >> 16) & 0xff;
                int g = (color >> 8) & 0xff;
                int b = color & 0xff;

                // if color close to white，bit='0', else bit='1'
                if (r > 160 && g > 160 && b > 160)
                    sb.append("0");
                else
                    sb.append("1");
            }
            if (zeroCount > 0) {
                sb.append(zeroStr);
            }
            list.add(sb.toString());
        }

        List<String> bmpHexList = binaryListToHexStringList(list);
        String commandHexString = "1D763000";
        String widthHexString = Integer
                .toHexString(bmpWidth % 8 == 0 ? bmpWidth / 8
                        : (bmpWidth / 8 + 1));
        if (widthHexString.length() > 2) {
            Log.e("decodeBitmap error", " width is too large");
            return null;
        } else if (widthHexString.length() == 1) {
            widthHexString = "0" + widthHexString;
        }
        widthHexString = widthHexString + "00";

        String heightHexString = Integer.toHexString(bmpHeight);
        if (heightHexString.length() > 2) {
            Log.e("decodeBitmap error", " height is too large");
            return null;
        } else if (heightHexString.length() == 1) {
            heightHexString = "0" + heightHexString;
        }
        heightHexString = heightHexString + "00";

        List<String> commandList = new ArrayList<String>();
        commandList.add(commandHexString + widthHexString + heightHexString);
        commandList.addAll(bmpHexList);

        return hexList2Byte(commandList);
    }

    public static int indexOfClient(ArrayList<Client> clients, Client client) {
        int index = -1;
        for (int i = 0; i < clients.size(); i++) {
            if (client != null)
                if (clients.get(i).cLICode.equals(client.cLICode)) {
                    index = i;
                }
        }
        return index;
    }

    public static List<String> binaryListToHexStringList(List<String> list) {
        List<String> hexList = new ArrayList<String>();
        for (String binaryStr : list) {
            StringBuffer sb = new StringBuffer();
            for (int i = 0; i < binaryStr.length(); i += 8) {
                String str = binaryStr.substring(i, i + 8);

                String hexString = myBinaryStrToHexString(str);
                sb.append(hexString);
            }
            hexList.add(sb.toString());
        }
        return hexList;

    }

    public static String myBinaryStrToHexString(String binaryStr) {
        String hex = "";
        String f4 = binaryStr.substring(0, 4);
        String b4 = binaryStr.substring(4, 8);
        for (int i = 0; i < binaryArray.length; i++) {
            if (f4.equals(binaryArray[i]))
                hex += hexStr.substring(i, i + 1);
        }
        for (int i = 0; i < binaryArray.length; i++) {
            if (b4.equals(binaryArray[i]))
                hex += hexStr.substring(i, i + 1);
        }

        return hex;
    }

    public static byte[] hexList2Byte(List<String> list) {
        List<byte[]> commandList = new ArrayList<byte[]>();

        for (String hexStr : list) {
            commandList.add(hexStringToBytes(hexStr));
        }
        byte[] bytes = sysCopy(commandList);
        return bytes;
    }

    public static byte[] hexStringToBytes(String hexString) {
        if (hexString == null || hexString.equals("")) {
            return null;
        }
        hexString = hexString.toUpperCase();
        int length = hexString.length() / 2;
        char[] hexChars = hexString.toCharArray();
        byte[] d = new byte[length];
        for (int i = 0; i < length; i++) {
            int pos = i * 2;
            d[i] = (byte) (charToByte(hexChars[pos]) << 4 | charToByte(hexChars[pos + 1]));
        }
        return d;
    }

    public static byte[] sysCopy(List<byte[]> srcArrays) {
        int len = 0;
        for (byte[] srcArray : srcArrays) {
            len += srcArray.length;
        }
        byte[] destArray = new byte[len];
        int destLen = 0;
        for (byte[] srcArray : srcArrays) {
            System.arraycopy(srcArray, 0, destArray, destLen, srcArray.length);
            destLen += srcArray.length;
        }
        return destArray;
    }

    private static byte charToByte(char c) {
        return (byte) "0123456789ABCDEF".indexOf(c);
    }

    public static double round(double value, int places) {
        if (places < 0) throw new IllegalArgumentException();

        long factor = (long) Math.pow(10, places);
        value = value * factor;
        long tmp = Math.round(value);
        return (double) tmp / factor;
    }

    public static Bitmap toGrayscale(Bitmap bmpOriginal) {
        int width, height;
        height = bmpOriginal.getHeight();
        width = bmpOriginal.getWidth();

        Bitmap bmpGrayscale = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        Canvas c = new Canvas(bmpGrayscale);
        Paint paint = new Paint();
        ColorMatrix cm = new ColorMatrix();
        cm.setSaturation(0);
        ColorMatrixColorFilter f = new ColorMatrixColorFilter(cm);
        paint.setColorFilter(f);
        c.drawBitmap(bmpOriginal, 0, 0, paint);
        return bmpGrayscale;
    }

    public static boolean hasCamera(Context context) {
        PackageManager pm = context.getPackageManager();

        return (pm.hasSystemFeature(PackageManager.FEATURE_CAMERA));
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    public static int isSessionCaisseExpired(String dateOuv) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Date now = new Date(System.currentTimeMillis());
        return DateUtils.strToDate(dateOuv, "yyyy-MM-dd").compareTo(DateUtils.strToDate(formatter.format(now), "yyyy-MM-dd"));
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    public static boolean isNotSessionCaisseExpired(String dateOuv) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        //  SimpleDateFormat fmt = new SimpleDateFormat("yyyyMMdd");
        return formatter.format(DateUtils.strToDate(dateOuv, "yyyy-MM-dd")).equals(returnCurrentDate());
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    public static String returnCurrentDate() {

        Date date = Calendar.getInstance().getTime();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return dateFormat.format(date);
    }


    public static LayerDrawable generateBottomBorder() {
        Integer bgColor = Color.WHITE;
        Integer borderColor = Color.GRAY;
        Integer widthInPixels = 4;

        List<Drawable> drawableList = Arrays.asList(new ColorDrawable(borderColor), new ColorDrawable(bgColor));
        Drawable[] drawables = new Drawable[drawableList.size()];
        drawableList.toArray(drawables);
        LayerDrawable layerDrawable = new LayerDrawable(drawables);

        layerDrawable.setLayerInset(
                1, // index of drawable, background
                0, // left border in pixels
                0, // top border in pixels
                0, // right border in pixels
                widthInPixels // bottom border in pixels
        );

        return layerDrawable;

    }

    public static String validateBaseUrl() {
        if (Patterns.IP_ADDRESS.matcher(PrefUtils.getServerIPAddress()).matches()) {
            Globals.BASE_URL = "http://%s:%s/%s/";
        } else {
            Globals.BASE_URL = "https://%s:%s/%s/";
        }
        return Globals.BASE_URL;
    }


    public static List<LigneTicket> AddAllLigneTiket(Context context, List<Ticket> tickets) {
        List<Ticket> sumtickets;
        List<LigneTicket> ligneTickets;
        List<LigneTicket> Lignart;
        List<LigneTicket> duplicates;
        List<LigneTicket> Notduplicates;
        //  Remove ticket if tIKNumTicket == 0
        for (Iterator<Ticket> iterator = tickets.iterator(); iterator.hasNext(); ) {
            Ticket ticket = iterator.next();
            if (ticket.tIKNumTicket == 0) {
                iterator.remove();
            }
        }
       /* for (int i = finaltickets.size() - 1; i >= 0; i--) {
            if (finaltickets.get(i).tIKNumTicket == 0) {
                finaltickets.remove(i);
            }
        }*/
        // private  void AddAllTiketMoney(List<Ticket> tickets) {

        sumtickets = new ArrayList<Ticket>();
        ligneTickets = new ArrayList<LigneTicket>();
        sumtickets.add(tickets.get(0));


        /*
        Map<String, String> codebarAndPrice;
        codebarAndPrice = new LinkedHashMap<>();
        for (LigneTicket dt: ligneTickets) {
                codebarAndPrice.put(dt.codeBarFils, String.valueOf(dt.getlTMtTTC()));

        }
         */
// create hash map to put articles by codeBarFils
      /*  Map<String, List<LigneTicket>> Articldata;
        Articldata = new LinkedHashMap<>();
        for (LigneTicket dt: ligneTickets) {
            List<LigneTicket> list = Articldata.get(dt.codeBarFils);
            if (list == null) {
                list = new ArrayList<>();
                Articldata.put(dt.codeBarFils, list);
            }
            list.add(dt);
        }*/

        Lignart = new ArrayList<>();
        duplicates = new ArrayList<>();



/*
        for (int j = 0; j < Articldata.keySet().size(); j++) {

            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                Map.Entry<String, List<LigneTicket>> entry = Articldata.entrySet().stream().skip(j).findFirst().get();

                String key = Articldata.keySet().stream().skip(j).findFirst().get();
                List<LigneTicket> value = Articldata.values().stream().skip(j).findFirst().get();

                //Lignart.addAll(value);

                Lignart.add(Articldata.get(key).get(0));
               if(Objects.requireNonNull(Articldata.get(key)).size()>0)
                for (int d = 1; d < Objects.requireNonNull(Articldata.get(key)).size(); d++) {
                    Lignart.get(j).setlTQte(Lignart.get(j).getlTQte()+ Objects.requireNonNull(Articldata.get(key)).get(d).getlTQte());
                   // Lignart.get(j).setlTMtTTC(Lignart.get(j).getlTQte()+Articldata.get(key).get(d).getlTQte());
                }

            }
        }
*/


        List<LigneTicket> allDuplicatesCodeBare = new ArrayList<>();
        List<LigneTicket> duplicatesbutPriceDiffer = new ArrayList<>();
        List<LigneTicket> duplicatesSamePrice = new ArrayList<>();
        List<LigneTicket> duplicatesSamePriceWithSameRemise = new ArrayList<>();
        List<LigneTicket> duplicatesSamePriceWithDiffrentRemise = new ArrayList<>();

        Notduplicates = new ArrayList<>(ligneTickets);


        //get duplicated item list
        Map<String, List<Integer>> allDuplicatesindexes = new HashMap<>();
        for (int i = 0; i < ligneTickets.size(); i++) {

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                allDuplicatesindexes.computeIfAbsent(ligneTickets.get(i).codeBarFils, c -> new ArrayList<>()).add(i);
            }
        }
        for (int j = 0; j < allDuplicatesindexes.keySet().size(); j++) {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                Map.Entry<String, List<Integer>> entry = allDuplicatesindexes.entrySet().stream().skip(j).findFirst().get();
                String key = allDuplicatesindexes.keySet().stream().skip(j).findFirst().get();
                List<Integer> value = allDuplicatesindexes.values().stream().skip(j).findFirst().get();

                for (int s = 0; s < value.size(); s++) {
                    //     Log.d("hhds","value size "+ String.valueOf(value.size()));
                    if (value.size() > 1) {
                        // Log.d("hhds","c "+ String.valueOf(value.get(s)));
                        allDuplicatesCodeBare.add(ligneTickets.get(value.get(s)));
                    }

                }


            }
        }

        //get duplicated item list with same price
        Map<Double, List<Integer>> duplicatesSamePriceindexes = new HashMap<>();
        for (int i = 0; i < allDuplicatesCodeBare.size(); i++) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                duplicatesSamePriceindexes.computeIfAbsent(allDuplicatesCodeBare.get(i).getlTPrixVente(), c -> new ArrayList<>()).add(i);
            }
        }

        for (int j = 0; j < duplicatesSamePriceindexes.keySet().size(); j++) {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                Map.Entry<Double, List<Integer>> entry = duplicatesSamePriceindexes.entrySet().stream().skip(j).findFirst().get();
                Double key = duplicatesSamePriceindexes.keySet().stream().skip(j).findFirst().get();
                List<Integer> value = duplicatesSamePriceindexes.values().stream().skip(j).findFirst().get();

                double mtttc = 0.0;
                double qt = 0.0;

                if (value.size() > 1) {
                    duplicatesSamePrice.add(allDuplicatesCodeBare.get(value.get(0)));
                    for (Integer index : value) {
                        //  System.out.println(temp);
                        // Log.d("hhds","valNue "+ String.valueOf(value));

                        mtttc = mtttc + allDuplicatesCodeBare.get(index).getlTMtTTC();
                        qt = qt + allDuplicatesCodeBare.get(index).getlTQte();

                    }

                    duplicatesSamePrice.get(duplicatesSamePrice.size() - 1).setlTQte(qt);
                    duplicatesSamePrice.get(duplicatesSamePrice.size() - 1).setlTMtTTC(mtttc);


                }
                //get duplicated item list with PriceDiffer
                else duplicatesbutPriceDiffer.add(allDuplicatesCodeBare.get(value.get(0)));
            }
        }


        /**
         * get duplicated same price and remise
         */
        //get duplicated item list with same price
        Map<Double, List<Integer>> duplicatesSamePriceAndRemiseindexes = new HashMap<>();
        for (int i = 0; i < duplicatesSamePrice.size(); i++) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                duplicatesSamePriceAndRemiseindexes.computeIfAbsent(duplicatesSamePrice.get(i).getlTRemise(), c -> new ArrayList<>()).add(i);
            }
        }

        for (int j = 0; j < duplicatesSamePriceAndRemiseindexes.keySet().size(); j++) {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                Map.Entry<Double, List<Integer>> entry = duplicatesSamePriceindexes.entrySet().stream().skip(j).findFirst().get();
                Double key = duplicatesSamePriceindexes.keySet().stream().skip(j).findFirst().get();
                List<Integer> value = duplicatesSamePriceindexes.values().stream().skip(j).findFirst().get();

                double mtttc = 0.0;
                double qt = 0.0;

                if (value.size() > 1) {
                    duplicatesSamePriceWithSameRemise.add(duplicatesSamePrice.get(value.get(0)));
                    for (Integer index : value) {
                        //  System.out.println(temp);
                        // Log.d("hhds","valNue "+ String.valueOf(value));

                        mtttc = mtttc + duplicatesSamePrice.get(index).getlTMtTTC();
                        qt = qt + duplicatesSamePrice.get(index).getlTQte();

                    }

                    duplicatesSamePriceWithSameRemise.get(duplicatesSamePriceWithSameRemise.size() - 1).setlTQte(qt);
                    duplicatesSamePriceWithSameRemise.get(duplicatesSamePriceWithSameRemise.size() - 1).setlTMtTTC(mtttc);


                }
                //get duplicated item  list with same price and remise differ
                else
                    duplicatesSamePriceWithDiffrentRemise.add(duplicatesSamePrice.get(value.get(0)));
            }
        }


        /**
         * END
         */
        //get not duplicated item list
        for (int i = Notduplicates.size() - 1; i >= 0; i--) {
            for (int j = 0; j < allDuplicatesCodeBare.size(); j++) {
                if (Notduplicates.get(i).codeBarFils.equals(allDuplicatesCodeBare.get(j).codeBarFils)) {
                    Notduplicates.remove(i);
                    break;
                }
            }


        }


        Lignart.addAll(Notduplicates);
        Lignart.addAll(duplicatesSamePrice);
        Lignart.addAll(duplicatesbutPriceDiffer);
        Lignart.addAll(duplicatesSamePriceWithDiffrentRemise);
        Lignart.addAll(duplicatesSamePriceWithSameRemise);

        putArticleInLignes(Lignart);


        //sumtickets.get(0).settIKNumeroBL("ééé");
        sumtickets.get(0).settIK_NumeroBL(context.getString(R.string.Nombre_des_tickets) + " " + String.valueOf(tickets.size()));
        // sumtickets.get(0).settIKNomClient(String.valueOf(Lignart.size()));
        sumtickets.get(0).settIKNomClient(String.valueOf(allDuplicatesCodeBare.size()));


        return Lignart;
    }

    public static List<Ticket> AddAllTiket(Context context, List<Ticket> tickets) {
        List<Ticket> sumtickets;
        List<LigneTicket> ligneTickets;
        List<LigneTicket> Lignart;
        List<LigneTicket> duplicates;
        List<LigneTicket> Notduplicates;
        //  Remove ticket if tIKNumTicket == 0
        for (Iterator<Ticket> iterator = tickets.iterator(); iterator.hasNext(); ) {
            Ticket ticket = iterator.next();
            if (ticket.tIKNumTicket == 0) {
                iterator.remove();
            }
        }
       /* for (int i = finaltickets.size() - 1; i >= 0; i--) {
            if (finaltickets.get(i).tIKNumTicket == 0) {
                finaltickets.remove(i);
            }
        }*/
        // private  void AddAllTiketMoney(List<Ticket> tickets) {
        double tIKMtTTC = 0;
        double tIKMtRemise = 0;
        double tIKMtHT = 0;
        double tIKMtTVA = 0;
        double tIKMtEspece = 0;
        double tIKMtCheque = 0;
        double tIKMtCarteBanq = 0;
        double tIKMtrecue = 0;
        double tIKMtrendue = 0;
        sumtickets = new ArrayList<Ticket>();
        ligneTickets = new ArrayList<LigneTicket>();
        sumtickets.add(tickets.get(0));

        for (int i = 0; i < tickets.size(); i++) {
            if (tickets.get(i).tIKNumTicket != 0) {
                tIKMtTTC = tIKMtTTC + tickets.get(i).gettIKMtTTC();
                tIKMtRemise = tIKMtRemise + tickets.get(i).gettIKMtRemise();
                tIKMtHT = tIKMtHT + tickets.get(i).gettIKMtHT();
                tIKMtTVA = tIKMtTVA + tickets.get(i).gettIKMtTVA();
                tIKMtEspece = tIKMtEspece + tickets.get(i).gettIKMtEspece();

                if (tickets.get(i).gettIKMtCheque() != null)
                    tIKMtCheque = tIKMtCheque + Double.parseDouble(tickets.get(i).gettIKMtCheque());

                tIKMtCarteBanq = tIKMtCarteBanq + tickets.get(i).gettIKMtCarteBanq();
                tIKMtrecue = tIKMtrecue + tickets.get(i).gettIKMtrecue();
                tIKMtrendue = tIKMtrendue + tickets.get(i).gettIKMtrendue();


                ligneTickets.addAll(App.database.ligneTicketDAO().getByTicket(
                        tickets.get(i).gettIKNumTicket(),
                        tickets.get(i).tIKIdCarnet,
                        tickets.get(i).tIKExerc
                ));


            } else Log.d("kkklld", "bhhhh");

        }

        sumtickets.get(0).settIKMtTTC(tIKMtTTC);
        sumtickets.get(0).settIKMtRemise(tIKMtRemise);
        sumtickets.get(0).settIKMtHT(tIKMtHT);
        sumtickets.get(0).settIKMtTVA(tIKMtTVA);
        sumtickets.get(0).settIKMtEspece(tIKMtEspece);
        sumtickets.get(0).settIKMtCheque(String.valueOf(tIKMtCheque));
        sumtickets.get(0).settIKMtCarteBanq(tIKMtCarteBanq);
        sumtickets.get(0).settIKMtrecue(tIKMtrecue);
        sumtickets.get(0).settIKMtrendue(tIKMtrendue);

        //sumtickets.get(0).settIKNumeroBL("ééé");
        sumtickets.get(0).settIK_NumeroBL(context.getString(R.string.Nombre_des_tickets) + " " + String.valueOf(tickets.size()));
        // sumtickets.get(0).settIKNomClient(String.valueOf(Lignart.size()));
        sumtickets.get(0).settIKNomClient(String.valueOf(tickets.size()));


        return sumtickets;
    }

    private static void putArticleInLignes(List<LigneTicket> ligneTickets) {
        for (int i = 0; i < ligneTickets.size(); i++) {
            ligneTickets.get(i).setArticle(App.database.articleDAO().getOneByCodeAndStation(ligneTickets.get(i).getlTCodArt(), App.prefUtils.getUserStationId()));
        }
    }


    public static void insertTicket(boolean isRegPartiel,
                                    Ticket ticket,
                                    TicketWithLines ticketWithLines,
                                    Client client,
                                    double rEGCMntChQue,
                                    double rEGCMntTraite,
                                    double rEGCMntEspeceRecue) {


        ticket.settIKEtat(Globals.TICKET_STATE.CREDIT.getValue());
        ticket.settIKCodClt(client.getcLICode());


       /*  ticketWithLines.getTicket().settIKDateHeureTicket(ticketWithLines.getTicket().gettIKDateHeureTicket());
        ticketWithLines.getTicket().setSync(false);
        ticketWithLines.getTicket().setStatus(Globals.ITEM_STATUS.INSERTED.getStatus());
*/

        ticket.setSync(false);
        ticket.setStatus(Globals.ITEM_STATUS.INSERTED.getStatus());
        App.database.ticketDAO().delete(ticket);
        App.database.ticketDAO().insert(ticket);


        for (LigneTicket ligneTicket : ticketWithLines.getLigneTicket()) {
            try {
                Article article = App.database.articleDAO().getOneByCodeAndStation(ligneTicket.getlTCodArt(), App.prefUtils.getUserStationId());
                StationStock stationStock = App.database.stationStockDAO().getOneByCode(article.getaRTCode(), prefUtils.getUserStationId());
                stationStock.setSARTQte(((stationStock.getSARTQte()) - ligneTicket.getlTQte()));
                article.setaRTQteStock((stationStock.getSARTQte()));
                App.database.stationStockDAO().insertOne(stationStock);
                App.database.articleDAO().insert(article);
            } catch (Exception e) {
                Log.d(TAG, "sendData: " + e);
            }

        }


        App.database.ligneTicketDAO().insertAll(ticketWithLines.getLigneTicket());
      /*  if(ticket.getTimbre()==0.0)   client.setSolde( Utils.round((client.getcLISolde() - Math.abs(amount)),3));
        else  {
            Timbre timbre = App.database.timbreDAO().getActif();
            if (timbre != null) {
                client.setSolde( Utils.round((client.getcLISolde() - Math.abs(amount+Double.parseDouble( timbre.tIMBValue))) ,3));
            }
        }*/
        setClientSoldCredit(
                isRegPartiel,
                ticket,
                client,
                rEGCMntChQue,
         rEGCMntTraite,
         rEGCMntEspeceRecue);

        client.setCLIStation(App.prefUtils.getUserStationId());
        client.setExercice(App.prefUtils.getExercice());
        App.database.clientDAO().insert(client);

    }


    static void setClientSoldCredit(boolean isRegPartiel,
                                    Ticket ticket,
                                    Client client,
                                    double rEGCMntChQue,
                                    double rEGCMntTraite,
                                    double rEGCMntEspeceRecue) {

        double soldClient;
        if(!isRegPartiel){
            soldClient = client.getSolde() - ticket.gettIKMtTTC();
        } else {
            soldClient = client.getSolde() - (ticket.gettIKMtTTC() - (rEGCMntChQue + rEGCMntTraite + rEGCMntEspeceRecue));

        }
        client.setSolde(soldClient);


    }
    public static void setClientSoldBnRetour(
            Client client,
            double mnt) {


        client.setSolde(client.getSolde() + mnt);

        App.database.clientDAO().insert(client);
    }

    /**
     * prepare the data and send it to save in local function
     */
    public static void sendPaymentData(boolean isRegPartiel,
                                       Long tickNum,
                                       Client client,
                                       double rEGCMntEspece,
                                       double rEGCMntChQue,
                                       double rEGCMntTraite,
                                       double rEGCMntTotalRecue,
                                       double rEGCMntEspeceRecue,
                                       double amount,
                                       double restAmount,
                                       double made,
                                       ArrayList<ChequeCaisse> chequeCaisses,
                                       ArrayList<TraiteCaisse> traiteCaisses) {
         /*   String   regcCode = StringUtils.PrefixFormatter(6,
                    App.database.prefixeDAO().getOneById("ReglementCaisse").getpREPrefixe(),
                    App.database.reglementCaisseDAO().getNewCode(App.database.prefixeDAO().getOneById("ReglementCaisse").getpREPrefixe()));
*/
        Date date = new Date();

        String regcCode = Utils.generateCommonCode(date, String.valueOf((int) Math.random()), String.valueOf((int) Math.random()));
        // ticketWithLinesAndPayments.getReglement().setrEGCCode_M(cutendfix(ticketWithLinesAndPayments.getReglement().rEGCCode_M,randomCode));
        // ticketWithLinesAndPayments.getReglement().setrEGCCode(cutendfix(ticketWithLinesAndPayments.getReglement().rEGCCode_M,randomCode));
        //    regcCode,
        /*"yyyy/MM/dd HH:mm:ss"*/
        //rEGCMntEspeceRecue - made,
        //  rEGCMntTotalRecue,

        String regPART = "";
        if(isRegPartiel) regPART = "_isRegPart";
        Long regTickNum = 0L;
        if(tickNum!=0L) regTickNum = tickNum;
        TicketWithLinesAndPayments ticketWithLinesAndPayments = new TicketWithLinesAndPayments(
                new ReglementCaisse(
                        "RG_M_" + App.prefUtils.getUserStationId() + "_" + App.prefUtils.getUserId() + "_" + App.prefUtils.getSessionCaisseId() + "_" + regcCode + regPART,
                        //    regcCode,
                        "RG_M_" + App.prefUtils.getUserStationId() + "_" + App.prefUtils.getUserId() + "_" + App.prefUtils.getSessionCaisseId() + "_" + regcCode + regPART,
                        App.prefUtils.getExercice(),
                        App.prefUtils.getCarnetId(),
                        0L,
                        regTickNum,
                        App.prefUtils.getCaisseId(),
                        App.prefUtils.getCaisseCode(),
                        App.prefUtils.getCaisseStationId(),
                        client.getcLICode(),
                        client.getcLINomPren(),
                        Globals.PAYEMENT_MODE.VARIOUS.getValue(),
                        DateUtils.dateToStr(new Date(), /*"yyyy/MM/dd HH:mm:ss"*/"yyyy-MM-dd HH:mm:ss"),
                        //rEGCMntEspeceRecue - made,
                        rEGCMntEspeceRecue,
                        0,
                        Utils.round(rEGCMntChQue, 3),
                        rEGCMntTraite,
                        Globals.CREDIT_PAYEMENT_REMARK,
                        (rEGCMntEspeceRecue + rEGCMntChQue + rEGCMntTraite),
                        App.prefUtils.getCaisseStationId(),
                        App.prefUtils.getUserId(),
                        (rEGCMntEspeceRecue + rEGCMntChQue + rEGCMntTraite),//  rEGCMntTotalRecue,
                        rEGCMntEspeceRecue,
                        restAmount,
                        made),
                chequeCaisses,
                traiteCaisses
        );

        if ((rEGCMntEspeceRecue + rEGCMntChQue + rEGCMntTraite) > 0
        ) {
            saveData(
                    isRegPartiel,
                    ticketWithLinesAndPayments,
                     client,
             rEGCMntEspece,
             rEGCMntChQue,
             rEGCMntTraite,
             rEGCMntEspeceRecue);
         //   setResult(false);
        }


    }
    /**
     * save the created data in local database
     */
    static void saveData(boolean isRegPartiel,
            TicketWithLinesAndPayments ticketWithLinesAndPayments,
                         Client client,
                         double rEGCMntEspece,
                         double rEGCMntChQue,
                         double rEGCMntTraite,
                         double rEGCMntEspeceRecue) {
        ticketWithLinesAndPayments.getReglement().setSync(false);
        ticketWithLinesAndPayments.getReglement().setStatus(Globals.ITEM_STATUS.INSERTED_REG_FROM_REGLEMENT.getStatus());






        App.database.reglementCaisseDAO().insert(ticketWithLinesAndPayments.getReglement());


   if(!isRegPartiel)
       setClientSold(
         ticketWithLinesAndPayments.getReglement(),
         client, rEGCMntEspece,
         rEGCMntChQue,
         rEGCMntTraite,
         rEGCMntEspeceRecue
       );
        //    client.setSolde(client.getSolde() + (rEGCMntEspeceRecue + rEGCMntChQue + rEGCMntTraite));
        client.setCLIStation(App.prefUtils.getUserStationId());
        client.setExercice(App.prefUtils.getExercice());
        App.database.clientDAO().insert(client);

        for (TraiteCaisse traiteCaisses : ticketWithLinesAndPayments.getTraites()) {
            traiteCaisses.settRAITEcheance(DateUtils.dateToStr(DateUtils.strToDate(traiteCaisses.gettRAITEcheance(), "MM/dd/yyyy hh:mm:ss"), "yyyy-MM-dd HH:mm"));
            traiteCaisses.setSync(false);
            traiteCaisses.setStatus(Globals.ITEM_STATUS.INSERTED.getStatus());

            traiteCaisses.settRAITNUM_M(ticketWithLinesAndPayments.getReglement().rEGCCode_M);
            traiteCaisses.settRAITNum(ticketWithLinesAndPayments.getReglement().rEGCCode_M);
            //  App.database.traiteCaisseDAO().insert(traiteCaisses);

        }
        App.database.traiteCaisseDAO().insertAll(ticketWithLinesAndPayments.getTraites());


        List<ChequeCaisse> chequeCaisses = new ArrayList<>();

        for (ChequeCaisse chequeCaisse :
                ticketWithLinesAndPayments.getCheques()) {
            chequeCaisse.setEcheanceCheque(DateUtils.dateToStr(DateUtils.strToDate(chequeCaisse.getEcheanceCheque(), "MM/dd/yyyy hh:mm:ss"), "yyyy-MM-dd HH:mm"));
            chequeCaisse.setSync(false);
            chequeCaisse.setStatus(Globals.ITEM_STATUS.INSERTED.getStatus());
            chequeCaisse.setReglement(ticketWithLinesAndPayments.getReglement().rEGCCode_M);

            chequeCaisse.setReglement_m(ticketWithLinesAndPayments.getReglement().rEGCCode_M);

        }
        App.database.chequeCaisseDAO().insertAll(ticketWithLinesAndPayments.getCheques());

        //  List<TraiteCaisse> traiteCaisses = new ArrayList<>();



    }


    static void setClientSold(ReglementCaisse reglementCaisse,
                              Client client,
                              double rEGCMntEspece,
                              double rEGCMntChQue,
                              double rEGCMntTraite,
                              double rEGCMntEspeceRecue){
        //  Client clt = App.database.clientDAO().getOneByCode(reglementCaisse.getrEGCCodeClient());

        //  Double soldClient = clt.getcLISolde() +reglementCaisse.getrEGCMntTotalRecue();
        //   App.database.clientDAO().updateSoldClient(soldClient, reglementCaisse.getrEGCCodeClient());


        // Double soldClient = client.getcLISolde() +reglementCaisse.getrEGCMntTotalRecue();
        Double soldClient = client.getSolde() + rEGCMntEspeceRecue + rEGCMntChQue + rEGCMntTraite;


        //  client.setcLISolde(client.getSolde() + (rEGCMntEspeceRecue + rEGCMntChQue + rEGCMntTraite));
        client.setSolde(soldClient);


    }




}