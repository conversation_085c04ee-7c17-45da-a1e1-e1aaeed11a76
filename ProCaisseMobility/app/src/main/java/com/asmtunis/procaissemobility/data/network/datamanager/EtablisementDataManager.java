package com.asmtunis.procaissemobility.data.network.datamanager;

import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Etablisement;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.EtablisementService;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

public class EtablisementDataManager {
    private static EtablisementDataManager sInstance;
    private final EtablisementService mEtablisementService;

    public EtablisementDataManager() {

        mEtablisementService = new ServiceFactory<>(EtablisementService.class, String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Etablisement")).makeService();
    }

    public static EtablisementDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new EtablisementDataManager();
        }
        return sInstance;
    }

    public void getEtablisements(GenericObject genericObject,
                               RemoteCallback<Etablisement> listener) {
        mEtablisementService.getEtablisement(genericObject)
                .enqueue(listener);
    }
}
