package com.asmtunis.procaissemobility.data.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.asmtunis.procaissemobility.data.models.Banque;

import java.util.List;

/**
 * Created by PC on 11/18/2017.
 */
@Dao
public interface BanqueDAO {

    @Query("SELECT * FROM Banque")
    List<Banque> getAll();

    @Query("SELECT * FROM Banque WHERE BAN_Code = :code ")
    Banque getOneByCode(String code);

    @Query("SELECT * FROM Banque LIMIT 1")
    Banque getOne();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(Banque item);


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<Banque> items);

    @Query("DELETE FROM Banque")
    void deleteAll();


}
