package com.asmtunis.procaissemobility.data.models;


import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * Created by Achraf on 26/09/2017.
 */
@Entity
public class Client extends BaseModel implements Serializable {
    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "CLI_Code")
    @SerializedName("CLI_Code")
    @Expose
    public String cLICode;

    @ColumnInfo(name = "CLI_NomPren")
    @SerializedName("CLI_NomPren")
    @Expose
    public String cLINomPren;

    public String getCliEtat() {
        return cliEtat;
    }

    public void setCliEtat(String cliEtat) {
        this.cliEtat = cliEtat;
    }

    @ColumnInfo(name = "CLI_Etat")
    @SerializedName("CLI_Etat")
    @Expose
    public String cliEtat;

    @ColumnInfo(name = "CLI_Date_Cre")
    @SerializedName("CLI_Date_Cre")
    @Expose
    public String cLIDateCre;

    @ColumnInfo(name = "CLI_DDm")
    @SerializedName("CLI_DDm")
    @Expose
    public String CliDDm;

    @ColumnInfo(name = "CLI_Industrielle")
    @SerializedName("CLI_Industrielle")
    @Expose
    public int cLIIndustrielle;

    public void setcLIAdresse(String cLIAdresse) {
        this.cLIAdresse = cLIAdresse;
    }

    @ColumnInfo(name = "CLI_Adresse")
    @SerializedName("CLI_Adresse")
    @Expose
    public String cLIAdresse;

    @ColumnInfo(name = "CLI_Fodec")
    @SerializedName("CLI_Fodec")
    @Expose
    public String cLIFodec;

    @ColumnInfo(name = "CLI_DC")
    @SerializedName("CLI_DC")
    @Expose
    public int cLIDC;
    @ColumnInfo(name = "CLI_Station")
    @SerializedName("CLI_Station")
    @Expose
    private String cLIStation;

    @ColumnInfo(name = "CLI_Timbre")
    @SerializedName("CLI_Timbre")
    @Expose
    public int cLITimbre = 1;


    public Double getCltMntRevImp() {
        return cltMntRevImp;
    }

    public void setCltMntRevImp(Double cltMntRevImp) {
        this.cltMntRevImp = cltMntRevImp;
    }

    @ColumnInfo(name = "Clt_MntRevImp")
    @SerializedName("Clt_MntRevImp")
    @Expose
    public Double cltMntRevImp;



    public void setcLINomPren(String cLINomPren) {
        this.cLINomPren = cLINomPren;
    }

    public void setcLIDateCre(String cLIDateCre) {
        this.cLIDateCre = cLIDateCre;
    }

    public int getcLIIndustrielle() {
        return cLIIndustrielle;
    }

    public void setcLIIndustrielle(int cLIIndustrielle) {
        this.cLIIndustrielle = cLIIndustrielle;
    }

    public String getcLIFodec() {
        return cLIFodec;
    }

    public void setcLIFodec(String cLIFodec) {
        this.cLIFodec = cLIFodec;
    }

    public int getcLIDC() {
        return cLIDC;
    }

    public void setcLIDC(int cLIDC) {
        this.cLIDC = cLIDC;
    }

    public void setcLISolde(Double cLISolde) {
        this.cLISolde = cLISolde;
    }

    public int getcLITauxRemGlob() {
        return cLITauxRemGlob;
    }

    public void setcLITauxRemGlob(int cLITauxRemGlob) {
        this.cLITauxRemGlob = cLITauxRemGlob;
    }

    public String getcLIExoNum() {
        return cLIExoNum;
    }

    public void setcLIExoNum(String cLIExoNum) {
        this.cLIExoNum = cLIExoNum;
    }

    public String getcLIExoValable() {
        return cLIExoValable;
    }

    public int getcLI_Exonoration() {
        return cLI_Exonoration;
    }

    public void setcLI_Exonoration(int cLI_Exonoration) {
        this.cLI_Exonoration = cLI_Exonoration;
    }

    public int getcLIExport() {
        return cLIExport;
    }

    public void setcLIExport(int cLIExport) {
        this.cLIExport = cLIExport;
    }

    public int getcLIForfetaire() {
        return cLIForfetaire;
    }

    public void setcLIForfetaire(int cLIForfetaire) {
        this.cLIForfetaire = cLIForfetaire;
    }

    public String getcLIObs() {
        return cLIObs;
    }

    public void setcLIObs(String cLIObs) {
        this.cLIObs = cLIObs;
    }

    @ColumnInfo(name = "CLI_Solde")
    @SerializedName("CLI_Solde")
    @Expose
    public Double cLISolde = 0.0;

    @ColumnInfo(name = "CLI_Type")
    @SerializedName("CLI_Type")
    @Expose
    public String cLIType;

    public String getDebit() {
        return debit;
    }

    public void setDebit(String debit) {
        this.debit = debit;
    }

    @ColumnInfo(name = "Debit")
    @SerializedName("Debit")
    @Expose
    public String debit;

    public String getcLILocalite() {
        return cLILocalite;
    }

    public void setcLILocalite(String cLILocalite) {
        this.cLILocalite = cLILocalite;
    }

    public String getcLIVille() {
        return cLIVille;
    }

    public void setcLIVille(String cLIVille) {
        this.cLIVille = cLIVille;
    }

    @ColumnInfo(name = "CLI_Localite")
    @SerializedName("CLI_Localite")
    @Expose
    public String cLILocalite;

    @ColumnInfo(name = "CLI_Ville")
    @SerializedName("CLI_Ville")
    @Expose
    public String cLIVille;



    @ColumnInfo(name = "CLI_TauxRemGlob")
    @SerializedName("CLI_TauxRemGlob")
    @Expose
    public int cLITauxRemGlob;

    @ColumnInfo(name = "CLI_Tel1")
    @SerializedName("CLI_Tel1")
    @Expose
    public String cLITel1;

    @ColumnInfo(name = "CLI_Tel2")
    @SerializedName("CLI_Tel2")
    @Expose
    public String cLITel2;

    @ColumnInfo(name = "CLI_mail")
    @SerializedName("CLI_mail")
    @Expose
    public String cLIMail;

    @ColumnInfo(name = "CLI_Exo_Num")
    @SerializedName("CLI_Exo_Num")
    @Expose
    public String cLIExoNum;

    @ColumnInfo(name = "CLI_Exo_Valable")
    @SerializedName("CLI_Exo_Valable")
    @Expose
    public String cLIExoValable;

    @ColumnInfo(name = "CLI_Exonoration")
    @SerializedName("CLI_Exonoration")
    @Expose
    public int cLI_Exonoration;

    @ColumnInfo(name = "CLI_export")
    @SerializedName("CLI_export")
    @Expose
    public int cLIExport;

    @ColumnInfo(name = "exercice")
    @SerializedName("exercice")
    public String exercice = "";


    public String getCltnomGearant() {
        return cltnomGearant;
    }

    public void setCltnomGearant(String cltnomGearant) {
        this.cltnomGearant = cltnomGearant;
    }

    public String getCltVille() {
        return cltVille;
    }

    public void setCltVille(String cltVille) {
        this.cltVille = cltVille;
    }

    public String getCltGouvernorat() {
        return cltGouvernorat;
    }

    public void setCltGouvernorat(String cltGouvernorat) {
        this.cltGouvernorat = cltGouvernorat;
    }

    public String getcLtNomMagasin() {
        return cLtNomMagasin;
    }

    public void setcLtNomMagasin(String cLtNomMagasin) {
        this.cLtNomMagasin = cLtNomMagasin;
    }

    public String getcLtProfession() {
        return cLtProfession;
    }

    public void setcLtProfession(String cLtProfession) {
        this.cLtProfession = cLtProfession;
    }

    @ColumnInfo(name = "Clt_Info6")
    @SerializedName("Clt_Info6")
    public String cltnomGearant = "";

    @ColumnInfo(name = "Clt_Info7")
    @SerializedName("Clt_Info7")
    public String cltVille = "";

    @ColumnInfo(name = "Clt_Info8")
    @SerializedName("Clt_Info8")
    public String cltGouvernorat = "";


    @ColumnInfo(name = "Clt_Info9")
    @SerializedName("Clt_Info9")
    public String cLtNomMagasin = "";

    @ColumnInfo(name = "Clt_Info10")
    @SerializedName("Clt_Info10")
    public String cLtProfession = "";


    @ColumnInfo(name = "CLI_Forfetaire")
    @SerializedName("CLI_Forfetaire")
    @Expose
    public int cLIForfetaire;

    @ColumnInfo(name = "Clt_Epouse")
    @SerializedName("Clt_Epouse")
    @Expose
    public String cLISociete;

    @Ignore
    @SerializedName("CA")
    @Expose
    private String cA;

    @ColumnInfo(name = "Clt_Longitude")
    @SerializedName("Clt_Longitude")
    @Expose
    private Double longitude  = 0.0;

    @ColumnInfo(name = "Clt_Latitude")
    @SerializedName("Clt_Latitude")
    @Expose
    private Double latitude  = 0.0;

    @ColumnInfo(name = "Solde")
    @SerializedName("Solde")
    @Expose
    private Double solde = 0.0;

    public Integer getCliisCredit() {
        return cliisCredit;
    }

    public void setCliisCredit(Integer cliisCredit) {
        this.cliisCredit = cliisCredit;
    }

    @ColumnInfo(name = "CLI_isCredit")
    @SerializedName("CLI_isCredit")
    @Expose
    private Integer cliisCredit;


    public Double getCliCredit() {
        return cliCredit;
    }

    public void setCliCredit(Double cliCredit) {
        this.cliCredit = cliCredit;
    }

    @ColumnInfo(name = "Cli_Credit")
    @SerializedName("Cli_Credit")
    @Expose
    private Double cliCredit;


    public Double getCliMaxCredit() {
        return cliMaxCredit;
    }

    public void setCliMaxCredit(Double cliMaxCredit) {
        this.cliMaxCredit = cliMaxCredit;
    }

    @ColumnInfo(name = "CLI_MaxCredit")
    @SerializedName("CLI_MaxCredit")
    @Expose
    private Double cliMaxCredit;




    @ColumnInfo(name = "CLI_Exeno")
    @SerializedName("CLI_Exeno")
    @Expose
    private String CliExeno;
    @ColumnInfo(name = "CLI_MatFisc")
    @SerializedName("CLI_MatFisc")
    @Expose
    public String CLI_MatFisc;

    @ColumnInfo(name = "Clt_Epoux")
    @SerializedName("Clt_Epoux")
    @Expose
    public String CltEpoux;

    public String getcLICodeM() {
        return cLICodeM;
    }

    public void setcLICodeM(String cLICodeM) {
        this.cLICodeM = cLICodeM;
    }

    @ColumnInfo(name = "CLI_Code_M")
    @SerializedName("CLI_Code_M")
    @Expose
    public String cLICodeM;

    @ColumnInfo(name = "CLI_Obs")
    @SerializedName("CLI_Obs")
    @Expose
    public String cLIObs;

    @ColumnInfo(name = "Clt_Circuit")
    @SerializedName("Clt_Circuit")
    @Expose
    private String cltCircuit;

    @ColumnInfo(name = "datePA")
    @SerializedName("datePA")
    @Expose
    private String datePA;


    public String getCltInfo1() {
        return cltInfo1;
    }

    public void setCltInfo1(String cltInfo1) {
        this.cltInfo1 = cltInfo1;
    }

    @ColumnInfo(name = "Clt_Info1")
    @SerializedName("Clt_Info1")
    @Expose
    private String cltInfo1;


    public String getCliUser() {
        return cliUser;
    }

    public void setCliUser(String cliUser) {
        this.cliUser = cliUser;
    }

    @ColumnInfo(name = "CLI_User")
    @SerializedName("CLI_User")
    @Expose
    private String cliUser;

    public Client() {
    }

    @Ignore
    public Client(@NonNull String cLICode, String cLINomPren, String cLIDateCre, int cLIIndustrielle, String cLIAdresse,
                  int cLIFodec, int cLIDC, int cLITimbre, Double cLISolde, String cLIType, int cLITauxRemGlob,
                  String cLITel1, String cLITel2, String cLIMail, String cLIExoNum, String cLIExoValable,
                  int cLI_Exonoration, int cLIExport, int cLIForfetaire, String cLISociete, Double longitude, Double latitude, String CLI_MatFisc,
                  String cltInfo1, Double Solde, String cliUser, String cLICodeM, String CltEpoux, String exercice) {
        this.cLICode = cLICode;
        this.cLINomPren = cLINomPren;
        this.cLIDateCre = cLIDateCre;
        this.cLIIndustrielle = cLIIndustrielle;
        this.cLIAdresse = cLIAdresse;
        this.cLIFodec = "" + cLIFodec;
        this.cLIDC = cLIDC;
        this.cLITimbre = cLITimbre;
        this.cLISolde = cLISolde;
        this.solde = Solde;
        this.cLIType = cLIType;
        this.cLITauxRemGlob = cLITauxRemGlob;
        this.cLITel1 = cLITel1;
        this.cLITel2 = cLITel2;
        this.cLIMail = cLIMail;
        this.cLIExoNum = cLIExoNum;
        this.cLIExoValable = cLIExoValable;
        this.cLI_Exonoration = cLI_Exonoration;
        this.cLIExport = cLIExport;
        this.cLIForfetaire = cLIForfetaire;
        this.cLISociete = cLISociete;
        this.longitude = longitude;
        this.latitude = latitude;
        this.CLI_MatFisc = CLI_MatFisc;
        this.cltInfo1 = cltInfo1;
        this.cliUser = cliUser;
        this.cLICodeM = cLICodeM;
        this.CltEpoux = CltEpoux;
        this.exercice = exercice;

    }

    @Ignore
    public Client(@NonNull String cLICode, String cLINomPren, String CLI_MatFisc, String cLIDateCre, int cLIIndustrielle, String cLIAdresse, int cLIFodec, int cLIDC, int cLITimbre, Double cLISolde, String cLIType, int cLITauxRemGlob, String cLITel1, String cLITel2, String cLIMail, String cLIExoNum, String cLIExoValable, int cLI_Exonoration, int cLIExport, int cLIForfetaire, String cLISociete, String cltInfo1, Double Solde) {
        this.cLICode = cLICode;
        this.cLINomPren = cLINomPren;
        this.CLI_MatFisc = CLI_MatFisc;
        this.cLIDateCre = cLIDateCre;
        this.cLIIndustrielle = cLIIndustrielle;
        this.cLIAdresse = cLIAdresse;
        this.cLIFodec = cLIFodec + "";
        this.cLIDC = cLIDC;
        this.cLITimbre = cLITimbre;
        this.cLISolde = cLISolde;
        this.solde = Solde;
        this.cLIType = cLIType;
        this.cLITauxRemGlob = cLITauxRemGlob;
        this.cLITel1 = cLITel1;
        this.cLITel2 = cLITel2;
        this.cLIMail = cLIMail;
        this.cLIExoNum = cLIExoNum;
        this.cLIExoValable = cLIExoValable;
        this.cLI_Exonoration = cLI_Exonoration;
        this.cLIExport = cLIExport;
        this.cLIForfetaire = cLIForfetaire;
        this.cLISociete = cLISociete;
        this.cltInfo1= cltInfo1;
    }

    public String getDatePA() {
        return datePA;
    }



    public void setDatePA(String datePA) {
        this.datePA = datePA;
    }

    public String getCltCircuit() {
        return cltCircuit;
    }

    public void setCltCircuit(String cltCircuit) {
        this.cltCircuit = cltCircuit;
    }

    public String getCltEpoux() {
        return CltEpoux;
    }

    public void setCltEpoux(String cltEpoux) {
        CltEpoux = cltEpoux;
    }

    public String getCliDDm() {
        return CliDDm;
    }

    public void setCliDDm(String cliDDm) {
        CliDDm = cliDDm;
    }



    public String getcLIStation() {
        return cLIStation;
    }

    public void setcLIStation(String cLIStation) {
        this.cLIStation = cLIStation;
    }

    public String getCliExeno() {
        return CliExeno;
    }

    public void setCliExeno(String cliExeno) {
        CliExeno = cliExeno;
    }

    public String getcA() {
        return cA;
    }

    public void setcA(String cA) {
        this.cA = cA;
    }

    public Double getSolde() {
        return solde;
    }

    public void setSolde(Double solde) {
        this.solde = solde;

    }

    @NonNull
    public String getcLICode() {
        return cLICode;
    }


    public void setcLICode(@NonNull String cLICode) {
        this.cLICode = cLICode;
    }

    public String getCLIStation() {
        return cLIStation;
    }

    public void setCLIStation(String cLIStation) {
        this.cLIStation = cLIStation;
    }

    public String getcLINomPren() {
        return cLINomPren;
    }


    public String getcLIDateCre() {
        return cLIDateCre;
    }


    public String getcLIAdresse() {
        return cLIAdresse;
    }


    public int getcLITimbre() {
        return cLITimbre;
    }

    public void setcLITimbre(int cLITimbre) {
        this.cLITimbre = cLITimbre;
    }

    public Double getcLISolde() {
        return cLISolde;
    }

    public String getcLIType() {
        return String.format("%s", cLIType);
    }

    public void setcLIType(String cLIType) {
        this.cLIType = cLIType;
    }

    public String getcLITel1() {
        return cLITel1;
    }

    public void setcLITel1(String cLITel1) {
        this.cLITel1 = cLITel1;
    }

    public String getcLIMail() {
        return cLIMail;
    }

    public void setcLIMail(String cLIMail) {
        this.cLIMail = cLIMail;
    }

    public String getCLI_MatFisc() {
        return String.format("%s", CLI_MatFisc);
    }

    public void setCLI_MatFisc(String CLI_MatFisc) {
        this.CLI_MatFisc = CLI_MatFisc;
    }


    public void setcLIExoValable(String cLIExoValable) {
        this.cLIExoValable = cLIExoValable;
    }

    public String getcLITel2() {
        return cLITel2;
    }

    public void setcLITel2(String cLITel2) {
        this.cLITel2 = cLITel2;
    }


    public String getcLISociete() {
        return cLISociete;
    }

    public void setcLISociete(String cLISociete) {
        this.cLISociete = cLISociete;
    }


    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public String getExercice() {
        return exercice;
    }

    public void setExercice(String exercice) {
        this.exercice = exercice;
    }

    @NonNull
    @Override
    public String toString() {
        return "Client{" +
                "cLICode='" + cLICode + '\'' +
                ", cLINomPren='" + cLINomPren + '\'' +
                ", cLIDateCre='" + cLIDateCre + '\'' +
                ", cLIIndustrielle=" + cLIIndustrielle +
                ", cLIAdresse='" + cLIAdresse + '\'' +
                ", cLIFodec=" + cLIFodec +
                ", cLIDC=" + cLIDC +
                ", cLITimbre=" + cLITimbre +
                ", cLISolde=" + cLISolde +
                ", cLIType='" + cLIType + '\'' +
                ", cLITauxRemGlob=" + cLITauxRemGlob +
                ", cLITel1='" + cLITel1 + '\'' +
                ", cLITel2='" + cLITel2 + '\'' +
                ", cLIMail='" + cLIMail + '\'' +
                ", cLIExoNum=" + cLIExoNum +
                ", cLIExoValable='" + cLIExoValable + '\'' +
                ", cLI_Exonoration=" + cLI_Exonoration +
                ", cLIExport=" + cLIExport +
                ", cLIForfetaire=" + cLIForfetaire +
                ", cLISociete='" + cLISociete + '\'' +
                ", cA='" + cA + '\'' +
                ", longitude=" + longitude +
                ", latitude=" + latitude +
                ", CLI_MatFisc='" + CLI_MatFisc + '\'' +
                ", exercice='" + exercice + '\'' +
                ", cltInfo1='" + cltInfo1 + '\'' +
                ", cliUser='" + cliUser + '\'' +

                '}';
    }
}
