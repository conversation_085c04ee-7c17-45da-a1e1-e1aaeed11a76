package com.asmtunis.procaissemobility.data.network.services;

import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.models.StationStock;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

public interface StationStockService {
    @POST("getstockArticleByStation")
    Call<List<StationStock>> getStockByStation(@Body GenericObject genericObject);
    @POST("getstockArticle")
    Call<List<StationStock>> getstockArticle(@Body GenericObject genericObject);
}
