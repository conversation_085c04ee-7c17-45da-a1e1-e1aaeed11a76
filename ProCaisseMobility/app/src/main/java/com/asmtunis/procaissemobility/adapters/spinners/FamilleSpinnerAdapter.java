package com.asmtunis.procaissemobility.adapters.spinners;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.TextView;

import com.asmtunis.procaissemobility.data.models.Famille;
import com.asmtunis.procaissemobility.R;

import java.util.List;

/**
 * Created by PC on 10/6/2017.
 */

public class FamilleSpinnerAdapter extends ArrayAdapter<Famille> {
    private List<Famille> arrayList;

    public FamilleSpinnerAdapter(Context context, int resource, List<Famille> objects) {
        super(context, resource, objects);
        arrayList = objects;
    }

    @Override
    public int getCount() {
        if (arrayList != null)
            return arrayList.size();
        return 0;
    }

    @Override
    public Famille getItem(int position) {
        return arrayList.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }


    public View getDropDownView(int position, View convertView, ViewGroup parent) {

        View v = convertView;
        if (v == null) {
            LayoutInflater inflater = (LayoutInflater) getContext().getSystemService(Context
                    .LAYOUT_INFLATER_SERVICE);
            v = inflater.inflate(R.layout.spinner_dropdown_item, null);
        }
        Famille action = arrayList.get(position);
        if (action != null) {
            TextView action_txt = (TextView) v.findViewById(R.id.text1);
            action_txt.setText(action.getFAM_Lib());
        }
        return v;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        View v = convertView;
        if (v == null) {
            LayoutInflater inflater = (LayoutInflater) getContext().getSystemService(Context
                    .LAYOUT_INFLATER_SERVICE);
            v = inflater.inflate(R.layout.spinner_dropdown_item, null);
        }
        Famille action = arrayList.get(position);
        if (action != null) {
            TextView action_txt = (TextView) v.findViewById(R.id.text1);
            action_txt.setText(action.getFAM_Lib());
        }
        return v;
    }
}
