package com.asmtunis.procaissemobility.data.network.datamanager;


import com.asmtunis.procaissemobility.App;
import com.asmtunis.procaissemobility.data.models.Carnet;
import com.asmtunis.procaissemobility.data.models.GenericObject;
import com.asmtunis.procaissemobility.data.network.base.RemoteCallback;
import com.asmtunis.procaissemobility.data.network.base.ServiceFactory;
import com.asmtunis.procaissemobility.data.network.services.CarnetService;

import static com.asmtunis.procaissemobility.helper.Globals.BASE_URL;

/**
 * Created by Achraf on 29/09/2017.
 */

public class CarnetDataManager {

    private static CarnetDataManager sInstance;

    private final CarnetService mCarnetService;

    public CarnetDataManager() {

        mCarnetService = new ServiceFactory<>(CarnetService.class,String.format(BASE_URL, App.prefUtils.getServerIPAddress(),App.prefUtils.getServerPort(),"Carnet")).makeService();

    }

    public static CarnetDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new CarnetDataManager();
        }
        return sInstance;
    }

    public void getCarnetById(GenericObject genericObject,
                              RemoteCallback<Carnet> listener) {
        mCarnetService.getCarnetById(genericObject)
                .enqueue(listener);
    }
}


