package com.asmtunis.procaissemobility.recivers;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.asmtunis.procaissemobility.helper.utils.NetworkUtils;

/**
 * Created by me on 12/19/2017.
 */

public class ConnectivityReceiver extends BroadcastReceiver {

    private OnConnectivityChanged onConnectivityChanged;

    public interface OnConnectivityChanged {
        void onConnected();
        void onNotConnected();
    }

    public void setOnConnectivityChanged(OnConnectivityChanged onConnectivityChanged) {
        this.onConnectivityChanged = onConnectivityChanged;
    }

    @Override
    public void onReceive(final Context context, final Intent intent) {
        if (onConnectivityChanged == null) {
            Log.w("ConnectivityReceiver", "OnConnectivityChanged listener is not set.");
            return; // Exit gracefully.
        }
        int status = NetworkUtils.getConnectivityStatusString(context);
        if ("android.net.conn.CONNECTIVITY_CHANGE".equals(intent.getAction())) {
            if (status == NetworkUtils.NETWORK_STATUS_NOT_CONNECTED) {
                onConnectivityChanged.onNotConnected();
            } else {
                onConnectivityChanged.onConnected();
            }
        }
    }
}