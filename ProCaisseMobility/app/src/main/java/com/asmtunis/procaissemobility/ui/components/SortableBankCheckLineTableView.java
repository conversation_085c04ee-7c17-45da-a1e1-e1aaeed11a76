package com.asmtunis.procaissemobility.ui.components;

import android.content.Context;
import android.graphics.Typeface;
import androidx.core.content.ContextCompat;
import android.util.AttributeSet;

import com.asmtunis.procaissemobility.R;
import com.asmtunis.procaissemobility.data.models.ChequeCaisse;

import de.codecrafters.tableview.SortableTableView;
import de.codecrafters.tableview.model.TableColumnWeightModel;
import de.codecrafters.tableview.toolkit.SimpleTableHeaderAdapter;
import de.codecrafters.tableview.toolkit.SortStateViewProviders;
import de.codecrafters.tableview.toolkit.TableDataRowBackgroundProviders;

/**
 * Created by PC on 10/3/2017.
 */

public class SortableBankCheckLineTableView extends SortableTableView<ChequeCaisse> {

    public SortableBankCheckLineTableView(final Context context) {
        this(context, null);
    }

    public SortableBankCheckLineTableView(final Context context, final AttributeSet attributes) {
        this(context, attributes, android.R.attr.listViewStyle);
    }

    public SortableBankCheckLineTableView(final Context context, final AttributeSet attributes, final int styleAttributes) {
        super(context, attributes, styleAttributes);
        final SimpleTableHeaderAdapter simpleTableHeaderAdapter = new SimpleTableHeaderAdapter(context,
                context.getResources().getString(R.string.check_number_field_title),
                context.getResources().getString(R.string.deadline_field_title),
                context.getResources().getString(R.string.bank_field_title),
                context.getResources().getString(R.string.amount_field_title)
        );

        simpleTableHeaderAdapter.setTextColor(ContextCompat.getColor(context, R.color.material_grey100));

        setHeaderAdapter(simpleTableHeaderAdapter);

        final int rowColorEven = ContextCompat.getColor(context, R.color.md_white_1000);
        final int rowColorOdd = ContextCompat.getColor(context, R.color.material_grey100);
        setDataRowBackgroundProvider(TableDataRowBackgroundProviders.alternatingRowColors(rowColorEven, rowColorOdd));
        setHeaderSortStateViewProvider(SortStateViewProviders.brightArrows());

        final TableColumnWeightModel tableColumnWeightModel = new TableColumnWeightModel(4);
        tableColumnWeightModel.setColumnWeight(0, 3);
        tableColumnWeightModel.setColumnWeight(1, 4);
        tableColumnWeightModel.setColumnWeight(2, 3);
        tableColumnWeightModel.setColumnWeight(3, 3);
        setColumnModel(tableColumnWeightModel);
    //    simpleTableHeaderAdapter.setTypeface(Typeface.createFromAsset(getContext().getAssets(), "fonts/Exo2-Medium.otf").getStyle());

    }


}