{"v": "4.7.0", "fr": 29.9700012207031, "ip": 0, "op": 60.0000024438501, "w": 500, "h": 500, "nm": "np_shrug_622363_FFFFFF", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "left arm", "parent": 2, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 7, "s": [0], "e": [0]}, {"t": 53.0000021587343}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_inOutBack(t, b, c, d) {\n    if ((t /= d / 2) < 1)\n        return sum(mul(div(c, 2), mul(mul(t, t), sub(mul(sum(s *= 1.525, 1), t), s))), b);\n    return sum(mul(div(c, 2), sum(mul(mul(t -= 2, t), sum(mul(sum(s *= 1.525, 1), t), s)), 2)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_inOutBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_inOutBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 7, "s": [-2.535, -10.496, 0], "e": [-2.535, -10.496, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 53.0000021587343}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_inOutBack(t, b, c, d) {\n    if ((t /= d / 2) < 1)\n        return sum(mul(div(c, 2), mul(mul(t, t), sub(mul(sum(s *= 1.525, 1), t), s))), b);\n    return sum(mul(div(c, 2), sum(mul(mul(t -= 2, t), sum(mul(sum(s *= 1.525, 1), t), s)), 2)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_inOutBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_inOutBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}, "a": {"a": 0, "k": [3.383, 9.215, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.543, 8.965], [-3.133, -8.965], [-1.539, -8.965], [3.133, 8.965]], "c": true}}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group"}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill"}, {"ty": "tr", "p": {"a": 0, "k": [3.383, 9.215], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group"}], "ip": 0, "op": 90.0000036657751, "st": 0, "bm": 0, "sr": 1}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "left_shoulder", "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 7, "s": [0], "e": [4]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 22.334, "s": [4], "e": [4]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 37.666, "s": [4], "e": [0]}, {"t": 53.0000021587343}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_inOutBack(t, b, c, d) {\n    if ((t /= d / 2) < 1)\n        return sum(mul(div(c, 2), mul(mul(t, t), sub(mul(sum(s *= 1.525, 1), t), s))), b);\n    return sum(mul(div(c, 2), sum(mul(mul(t -= 2, t), sum(mul(sum(s *= 1.525, 1), t), s)), 2)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_inOutBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_inOutBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 7, "s": [167.621, 288.235, 0], "e": [167.621, 283.235, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 22.334, "s": [167.621, 283.235, 0], "e": [167.621, 283.235, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 37.666, "s": [167.621, 283.235, 0], "e": [167.621, 288.235, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 53.0000021587343}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_inOutBack(t, b, c, d) {\n    if ((t /= d / 2) < 1)\n        return sum(mul(div(c, 2), mul(mul(t, t), sub(mul(sum(s *= 1.525, 1), t), s))), b);\n    return sum(mul(div(c, 2), sum(mul(mul(t -= 2, t), sum(mul(sum(s *= 1.525, 1), t), s)), 2)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_inOutBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_inOutBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}, "a": {"a": 0, "k": [6.812, 0.965, 0]}, "s": {"a": 0, "k": [333, 333, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.562, -0.715], [6.562, -0.715], [6.562, 0.715], [-6.562, 0.715]], "c": true}}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group"}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill"}, {"ty": "tr", "p": {"a": 0, "k": [6.812, 0.965], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group"}], "ip": 0, "op": 90.0000036657751, "st": 0, "bm": 0, "sr": 1}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "left face", "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 7, "s": [0], "e": [0]}, {"t": 53.0000021587343}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_inOutBack(t, b, c, d) {\n    if ((t /= d / 2) < 1)\n        return sum(mul(div(c, 2), mul(mul(t, t), sub(mul(sum(s *= 1.525, 1), t), s))), b);\n    return sum(mul(div(c, 2), sum(mul(mul(t -= 2, t), sum(mul(sum(s *= 1.525, 1), t), s)), 2)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_inOutBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_inOutBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 7, "s": [201.417, 255.423, 0], "e": [201.417, 255.423, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 53.0000021587343}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_inOutBack(t, b, c, d) {\n    if ((t /= d / 2) < 1)\n        return sum(mul(div(c, 2), mul(mul(t, t), sub(mul(sum(s *= 1.525, 1), t), s))), b);\n    return sum(mul(div(c, 2), sum(mul(mul(t -= 2, t), sum(mul(sum(s *= 1.525, 1), t), s)), 2)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_inOutBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_inOutBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}, "a": {"a": 0, "k": [3.097, 10.819, 0]}, "s": {"a": 0, "k": [333, 333, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.757, 1.848], [-1.218, 3.5], [-1.379, 1.84], [0, 0], [0.289, -0.652], [0, 0], [0.254, -1.086], [0, -1.395], [-2.191, -3.52]], "o": [[-1.094, -1.379], [-1.403, -3.429], [0.661, -1.855], [0, 0], [-0.887, 1.528], [0, 0], [-0.449, 1.019], [-0.313, 1.355], [0, 3.527], [0, 0]], "v": [[1.43, 10.568], [-1.344, 5.729], [-1.63, -5.025], [1.43, -10.568], [2.848, -10.568], [1.085, -7.303], [1.09, -7.303], [0.031, -4.139], [-0.441, -0.001], [2.848, 10.568]], "c": true}}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group"}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill"}, {"ty": "tr", "p": {"a": 0, "k": [3.097, 10.819], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group"}], "ip": 0, "op": 90.0000036657751, "st": 0, "bm": 0, "sr": 1}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "left eye", "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 7, "s": [0], "e": [0]}, {"t": 53.0000021587343}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_inOutBack(t, b, c, d) {\n    if ((t /= d / 2) < 1)\n        return sum(mul(div(c, 2), mul(mul(t, t), sub(mul(sum(s *= 1.525, 1), t), s))), b);\n    return sum(mul(div(c, 2), sum(mul(mul(t -= 2, t), sum(mul(sum(s *= 1.525, 1), t), s)), 2)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_inOutBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_inOutBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 7, "s": [226.592, 233.494, 0], "e": [232.592, 233.494, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 22.334, "s": [232.592, 233.494, 0], "e": [232.592, 233.494, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 37.666, "s": [232.592, 233.494, 0], "e": [226.592, 233.494, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 53.0000021587343}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_inOutBack(t, b, c, d) {\n    if ((t /= d / 2) < 1)\n        return sum(mul(div(c, 2), mul(mul(t, t), sub(mul(sum(s *= 1.525, 1), t), s))), b);\n    return sum(mul(div(c, 2), sum(mul(mul(t -= 2, t), sum(mul(sum(s *= 1.525, 1), t), s)), 2)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_inOutBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_inOutBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}, "a": {"a": 0, "k": [2.064, 3.494, 0]}, "s": {"a": 0, "k": [333, 333, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.312, -1.621], [0, 0], [0.86, 1.757]], "o": [[0.855, 1.672], [0, 0], [-0.312, -1.578], [0, 0]], "v": [[-0.346, -3.244], [1.814, 2.748], [0.307, 3.244], [-1.814, -2.771]], "c": true}}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group"}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill"}, {"ty": "tr", "p": {"a": 0, "k": [2.064, 3.494], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group"}], "ip": 0, "op": 90.0000036657751, "st": 0, "bm": 0, "sr": 1}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "right eye", "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 7, "s": [0], "e": [0]}, {"t": 53.0000021587343}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_inOutBack(t, b, c, d) {\n    if ((t /= d / 2) < 1)\n        return sum(mul(div(c, 2), mul(mul(t, t), sub(mul(sum(s *= 1.525, 1), t), s))), b);\n    return sum(mul(div(c, 2), sum(mul(mul(t -= 2, t), sum(mul(sum(s *= 1.525, 1), t), s)), 2)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_inOutBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_inOutBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 7, "s": [248.588, 229.48, 0], "e": [254.588, 229.48, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 22.334, "s": [254.588, 229.48, 0], "e": [254.588, 229.48, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 37.666, "s": [254.588, 229.48, 0], "e": [248.588, 229.48, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 53.0000021587343}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_inOutBack(t, b, c, d) {\n    if ((t /= d / 2) < 1)\n        return sum(mul(div(c, 2), mul(mul(t, t), sub(mul(sum(s *= 1.525, 1), t), s))), b);\n    return sum(mul(div(c, 2), sum(mul(mul(t -= 2, t), sum(mul(sum(s *= 1.525, 1), t), s)), 2)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_inOutBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_inOutBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}, "a": {"a": 0, "k": [1.634, 3.574, 0]}, "s": {"a": 0, "k": [333, 333, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.113, -1.734], [0, 0], [0.539, 1.891]], "o": [[0.52, 1.871], [0, 0], [-0.141, -1.785], [0, 0]], "v": [[0.189, -3.324], [1.385, 3.074], [-0.212, 3.324], [-1.385, -3.051]], "c": true}}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group"}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill"}, {"ty": "tr", "p": {"a": 0, "k": [1.634, 3.574], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group"}], "ip": 0, "op": 90.0000036657751, "st": 0, "bm": 0, "sr": 1}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "right face", "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 7, "s": [0], "e": [0]}, {"t": 53.0000021587343}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_inOutBack(t, b, c, d) {\n    if ((t /= d / 2) < 1)\n        return sum(mul(div(c, 2), mul(mul(t, t), sub(mul(sum(s *= 1.525, 1), t), s))), b);\n    return sum(mul(div(c, 2), sum(mul(mul(t -= 2, t), sum(mul(sum(s *= 1.525, 1), t), s)), 2)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_inOutBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_inOutBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 7, "s": [300.411, 255.418, 0], "e": [300.411, 255.418, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 53.0000021587343}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_inOutBack(t, b, c, d) {\n    if ((t /= d / 2) < 1)\n        return sum(mul(div(c, 2), mul(mul(t, t), sub(mul(sum(s *= 1.525, 1), t), s))), b);\n    return sum(mul(div(c, 2), sum(mul(mul(t -= 2, t), sum(mul(sum(s *= 1.525, 1), t), s)), 2)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_inOutBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_inOutBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}, "a": {"a": 0, "k": [3.1, 10.82, 0]}, "s": {"a": 0, "k": [333, 333, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.086, -1.379], [0, 0], [0, 3.527], [0.312, 1.347], [0.449, 1.019], [0.895, 1.543], [0, 0], [-0.66, -1.856], [0, 0], [1.41, -3.426]], "o": [[0, 0], [2.192, -3.52], [0, -1.383], [-0.247, -1.086], [-0.286, -0.657], [0, 0], [1.379, 1.839], [0, 0], [1.218, 3.5], [-0.758, 1.851]], "v": [[-1.427, 10.57], [-2.85, 10.57], [0.439, 0], [-0.033, -4.105], [-1.079, -7.273], [-2.85, -10.57], [-1.432, -10.57], [1.628, -5.027], [1.632, -5.027], [1.339, 5.727]], "c": true}}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group"}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill"}, {"ty": "tr", "p": {"a": 0, "k": [3.099, 10.82], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group"}], "ip": 0, "op": 90.0000036657751, "st": 0, "bm": 0, "sr": 1}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "right shoulder", "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 7, "s": [0], "e": [-4]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 22.334, "s": [-4], "e": [-4]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 37.666, "s": [-4], "e": [0]}, {"t": 53.0000021587343}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_inOutBack(t, b, c, d) {\n    if ((t /= d / 2) < 1)\n        return sum(mul(div(c, 2), mul(mul(t, t), sub(mul(sum(s *= 1.525, 1), t), s))), b);\n    return sum(mul(div(c, 2), sum(mul(mul(t -= 2, t), sum(mul(sum(s *= 1.525, 1), t), s)), 2)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_inOutBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_inOutBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 7, "s": [332.086, 288.235, 0], "e": [332.086, 283.235, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 22.334, "s": [332.086, 283.235, 0], "e": [332.086, 283.235, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 37.666, "s": [332.086, 283.235, 0], "e": [332.086, 288.235, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 53.0000021587343}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_inOutBack(t, b, c, d) {\n    if ((t /= d / 2) < 1)\n        return sum(mul(div(c, 2), mul(mul(t, t), sub(mul(sum(s *= 1.525, 1), t), s))), b);\n    return sum(mul(div(c, 2), sum(mul(mul(t -= 2, t), sum(mul(sum(s *= 1.525, 1), t), s)), 2)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_inOutBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_inOutBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}, "a": {"a": 0, "k": [6.811, 0.965, 0]}, "s": {"a": 0, "k": [333, 333, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.561, -0.715], [6.561, -0.715], [6.561, 0.715], [-6.561, 0.715]], "c": true}}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group"}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill"}, {"ty": "tr", "p": {"a": 0, "k": [6.811, 0.965], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group"}], "ip": 0, "op": 90.0000036657751, "st": 0, "bm": 0, "sr": 1}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "right arm", "parent": 7, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 7, "s": [0], "e": [0]}, {"t": 53.0000021587343}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_inOutBack(t, b, c, d) {\n    if ((t /= d / 2) < 1)\n        return sum(mul(div(c, 2), mul(mul(t, t), sub(mul(sum(s *= 1.525, 1), t), s))), b);\n    return sum(mul(div(c, 2), sum(mul(mul(t -= 2, t), sum(mul(sum(s *= 1.525, 1), t), s)), 2)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_inOutBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_inOutBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 7, "s": [16.244, -10.496, 0], "e": [16.244, -10.496, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 53.0000021587343}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_inOutBack(t, b, c, d) {\n    if ((t /= d / 2) < 1)\n        return sum(mul(div(c, 2), mul(mul(t, t), sub(mul(sum(s *= 1.525, 1), t), s))), b);\n    return sum(mul(div(c, 2), sum(mul(mul(t -= 2, t), sum(mul(sum(s *= 1.525, 1), t), s)), 2)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_inOutBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_inOutBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}, "a": {"a": 0, "k": [3.381, 9.215, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.131, 8.965], [1.549, -8.965], [3.131, -8.965], [-1.529, 8.965]], "c": true}}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group"}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill"}, {"ty": "tr", "p": {"a": 0, "k": [3.381, 9.215], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group"}], "ip": 0, "op": 90.0000036657751, "st": 0, "bm": 0, "sr": 1}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "right hand", "parent": 8, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 7, "s": [0], "e": [4]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 22.334, "s": [4], "e": [4]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 37.666, "s": [4], "e": [0]}, {"t": 53.0000021587343}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_inOutBack(t, b, c, d) {\n    if ((t /= d / 2) < 1)\n        return sum(mul(div(c, 2), mul(mul(t, t), sub(mul(sum(s *= 1.525, 1), t), s))), b);\n    return sum(mul(div(c, 2), sum(mul(mul(t -= 2, t), sum(mul(sum(s *= 1.525, 1), t), s)), 2)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_inOutBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_inOutBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 7, "s": [12.734, -2.29, 0], "e": [12.734, -2.29, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 53.0000021587343}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_inOutBack(t, b, c, d) {\n    if ((t /= d / 2) < 1)\n        return sum(mul(div(c, 2), mul(mul(t, t), sub(mul(sum(s *= 1.525, 1), t), s))), b);\n    return sum(mul(div(c, 2), sum(mul(mul(t -= 2, t), sum(mul(sum(s *= 1.525, 1), t), s)), 2)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_inOutBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_inOutBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}, "a": {"a": 0, "k": [6.812, 0.959, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.562, 0.709], [6.562, 0.709], [6.562, -0.709], [-6.562, -0.709]], "c": true}}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group"}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill"}, {"ty": "tr", "p": {"a": 0, "k": [6.812, 0.959], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group"}], "ip": 0, "op": 90.0000036657751, "st": 0, "bm": 0, "sr": 1}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "mouth", "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 7, "s": [0], "e": [14]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 22.334, "s": [14], "e": [14]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 37.666, "s": [14], "e": [0]}, {"t": 53.0000021587343}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_inOutBack(t, b, c, d) {\n    if ((t /= d / 2) < 1)\n        return sum(mul(div(c, 2), mul(mul(t, t), sub(mul(sum(s *= 1.525, 1), t), s))), b);\n    return sum(mul(div(c, 2), sum(mul(mul(t -= 2, t), sum(mul(sum(s *= 1.525, 1), t), s)), 2)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_inOutBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_inOutBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 7, "s": [256.427, 249.93, 0], "e": [256.427, 256.93, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 22.334, "s": [256.427, 256.93, 0], "e": [256.427, 256.93, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 37.666, "s": [256.427, 256.93, 0], "e": [256.427, 249.93, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 53.0000021587343}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_inOutBack(t, b, c, d) {\n    if ((t /= d / 2) < 1)\n        return sum(mul(div(c, 2), mul(mul(t, t), sub(mul(sum(s *= 1.525, 1), t), s))), b);\n    return sum(mul(div(c, 2), sum(mul(mul(t -= 2, t), sum(mul(sum(s *= 1.525, 1), t), s)), 2)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_inOutBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_inOutBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}, "a": {"a": 0, "k": [7.203, 8.742, 0]}, "s": {"a": 0, "k": [333, 333, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.101, -0.035], [0, 0], [0, 0], [0, 0], [-2.621, 2.277], [-1.723, 6.367], [0, 0], [2.613, -2.254]], "o": [[0, 0], [0, 0], [0, 0], [0.023, -0.008], [2.41, -2.094], [0, 0], [-1.855, 6.832], [-2.864, 2.465]], "v": [[-6.414, 8.492], [-6.953, 6.871], [-6.68, 7.68], [-6.953, 6.871], [-2.066, 3.805], [5.305, -8.492], [6.953, -8.047], [-1.023, 5.16]], "c": true}}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group"}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill"}, {"ty": "tr", "p": {"a": 0, "k": [7.203, 8.742], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group"}], "ip": 0, "op": 90.0000036657751, "st": 0, "bm": 0, "sr": 1}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "left hand", "parent": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 7, "s": [0], "e": [-4]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 22.334, "s": [-4], "e": [-4]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 37.666, "s": [-4], "e": [0]}, {"t": 53.0000021587343}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_inOutBack(t, b, c, d) {\n    if ((t /= d / 2) < 1)\n        return sum(mul(div(c, 2), mul(mul(t, t), sub(mul(sum(s *= 1.525, 1), t), s))), b);\n    return sum(mul(div(c, 2), sum(mul(mul(t -= 2, t), sum(mul(sum(s *= 1.525, 1), t), s)), 2)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_inOutBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_inOutBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 7, "s": [-5.969, -2.29, 0], "e": [-5.969, -2.29, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 53.0000021587343}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_inOutBack(t, b, c, d) {\n    if ((t /= d / 2) < 1)\n        return sum(mul(div(c, 2), mul(mul(t, t), sub(mul(sum(s *= 1.525, 1), t), s))), b);\n    return sum(mul(div(c, 2), sum(mul(mul(t -= 2, t), sum(mul(sum(s *= 1.525, 1), t), s)), 2)), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key(sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = sub(time, key1.time);\n    d = sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_inOutBack(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_inOutBack(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_inOutBack(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}, "a": {"a": 0, "k": [6.812, 0.959, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.562, 0.709], [6.562, 0.709], [6.562, -0.709], [-6.562, -0.709]], "c": true}}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group"}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill"}, {"ty": "tr", "p": {"a": 0, "k": [6.812, 0.959], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group"}], "ip": 0, "op": 90.0000036657751, "st": 0, "bm": 0, "sr": 1}]}