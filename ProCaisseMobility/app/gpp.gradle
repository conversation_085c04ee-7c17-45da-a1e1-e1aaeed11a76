def playStoreProperties = new Properties()

ext.BASE_CONFIG_DIR = rootDir.getCanonicalPath() + '/config/'
ext.keystorePropertiesFile = file(BASE_CONFIG_DIR + "keystore.properties")
ext.versionPropertiesFile = file(BASE_CONFIG_DIR + "version.properties")
ext.playStorePropertiesFile = file(BASE_CONFIG_DIR + 'google-play-publisher.properties')

//if (playStorePropertiesFile.exists()) {
//    apply plugin: 'com.github.triplet.play'
//    playStoreProperties.load(new FileInputStream(playStorePropertiesFile))
//    play {
//        serviceAccountCredentials = file(BASE_CONFIG_DIR + playStoreProperties['apiKey'])
//
//        track = playStoreProperties['track']
//    }
//} else {
//    playStoreProperties['pk12FilePath'] = 'not-found'
//    playStoreProperties['track'] = 'internal'
//}
