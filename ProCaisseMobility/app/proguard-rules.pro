# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in C:\Users\<USER>\AppData\Local\Android\Sdk/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html
# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

#-dontwarn java.lang.FunctionalInterface
#-dontwarn java.util.**
#-dontwarn java.time.**
#-dontwarn javax.annotation.**
#-dontwarn javax.cache.**
#-dontwarn javax.naming.**
#-dontwarn javax.transaction.**
#-dontwarn java.sql.**
#-dontwarn javax.sql.**
#-dontwarn android.support.**
#-dontwarn io.requery.cache.**
#-dontwarn io.requery.rx.**
#-dontwarn io.requery.reactivex.**
#-dontwarn io.requery.reactor.**
#-dontwarn io.requery.query.**
#-dontwarn io.requery.android.sqlcipher.**
#-dontwarn io.requery.android.sqlitex.**
#-keepclassmembers enum io.requery.** {
#    public static **[] values();
#    public static ** valueOf(java.lang.String);
#}