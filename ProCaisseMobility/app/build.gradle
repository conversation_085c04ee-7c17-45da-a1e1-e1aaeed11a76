apply plugin: 'com.android.application'
apply from: 'gpp.gradle'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: 'com.google.gms.google-services'


def id = "com.asmtunis.procaissemobility"
def versionMajor = 3
def versionMinor = 3
def versionPatch = 7
def minSdk = 19
def sdk = 34
def code = versionMajor * 10000 + versionMinor * 100 + versionPatch + (minSdk * sdk)
def name = "${versionMajor}.${versionMinor}.${versionPatch}(${new Date().format('yyyy')})"

static def setOutputAPKName(applicationId, applicationVariants) {
    applicationVariants.all { variant ->
        variant.outputs.each { output ->
            def SEPARATOR = "_"
            def project = applicationId
            def buildType = variant.buildType.name
            def versionName = "v" + variant.versionName
            def versionCode = "c" + variant.versionCode
            def date = new Date()
            def formattedDate = date.format('dd.MM.yyyy_HH.mm')
            output.outputFileName = project + SEPARATOR + buildType + SEPARATOR + versionCode +
                    SEPARATOR + versionName +
                    SEPARATOR + formattedDate + ".apk"
        }
    }
}

android {
    buildFeatures {
        viewBinding = true
    }
    compileSdkVersion sdk
    defaultConfig {
        applicationId id
        minSdkVersion minSdk
        targetSdkVersion sdk
        versionCode code
        versionName name
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
        multiDexEnabled true
    }
    packagingOptions {
        resources {
            excludes += ['META-INF/DEPENDENCIES.txt', 'META-INF/LICENSE.txt', 'META-INF/NOTICE.txt', 'META-INF/NOTICE', 'META-INF/LICENSE', 'META-INF/DEPENDENCIES', 'META-INF/notice.txt', 'META-INF/license.txt', 'META-INF/dependencies.txt', 'META-INF/LGPL2.1']
        }
    }

    if (keystorePropertiesFile.canRead()) {
        Properties keystoreProperties = new Properties()
        keystoreProperties.load(new FileInputStream(keystorePropertiesFile))

        signingConfigs {
            debug {
                storeFile = file(rootDir.getCanonicalPath() + '/config/' + keystoreProperties['storeFile'])
                storePassword = keystoreProperties['storePassword']
                keyAlias = keystoreProperties['keyAlias']
                keyPassword = keystoreProperties['keyPassword']
            }

            release {
                storeFile = file(rootDir.getCanonicalPath() + '/config/' + keystoreProperties['storeFile'])
                storePassword = keystoreProperties['storePassword']
                keyAlias = keystoreProperties['keyAlias']
                keyPassword = keystoreProperties['keyPassword']
                v1SigningEnabled true
                v2SigningEnabled true
            }
        }

    } else {
        print("Could not read keystore.properties!")
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
            setOutputAPKName(defaultConfig.applicationId, applicationVariants)
        }
        debug {
            signingConfig signingConfigs.debug
            setOutputAPKName(defaultConfig.applicationId, applicationVariants)
        }
    }
    compileOptions {
        targetCompatibility 11
        sourceCompatibility 11
    }

    dexOptions {
        javaMaxHeapSize "4g"
    }

    lint {
        checkReleaseBuilds false
    }
    namespace 'com.asmtunis.procaissemobility'
    kotlinOptions {
        jvmTarget = '11'
    }

}

dependencies {
    implementation fileTree(include: ['*.jar', '*.aar'], dir: 'libs')
    implementation project(path: ':expandablelayout')
    implementation project(path: ':flowinggradient')
    implementation project(path: ':textdrawable')
    implementation project(path: ':statusbarnotifier')

    implementation files('libs/tscsdk.jar')
    implementation "androidx.lifecycle:lifecycle-extensions:2.2.0"
    implementation project(':asm')
    //implementation project(path: ':BareCodeReader')
    implementation project(':BareCodeReader')
    implementation 'org.apache.commons:commons-lang3:3.13.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'

    implementation 'com.github.mcginty:material-colors:1.1.0'
    implementation("com.github.bumptech.glide:glide:4.14.2@aar") {
        transitive = true
    }
    implementation 'com.google.android.material:material:1.7.0'
    implementation 'androidx.navigation:navigation-fragment-ktx:2.5.3'
    implementation 'androidx.navigation:navigation-ui-ktx:2.5.3'
    implementation 'com.google.firebase:protolite-well-known-types:18.0.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.13.2'
    implementation 'com.github.garretyoder:Colorful:1.1'

    implementation 'com.mobsandgeeks:android-saripaar:2.0.3'
    implementation 'com.jakewharton.timber:timber:5.0.1'
    implementation 'com.github.nekocode:Badge:2.1'


    implementation 'com.jakewharton:butterknife:10.2.3'
    annotationProcessor 'com.jakewharton:butterknife-compiler:10.2.3'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.appcompat:appcompat:1.5.1'
    implementation 'com.github.vlonjatg:progress-activity:2.0.5'
    implementation 'com.github.bloder:blormlib:2.4'
    implementation 'org.codepond:wizardroid:1.3.1'
    implementation "com.andkulikov:transitionseverywhere:1.8.0"
    implementation 'com.jaredrummler:android-device-names:2.1.0'
    implementation 'io.reactivex.rxjava2:rxandroid:2.1.1'
    implementation 'io.reactivex.rxjava2:rxjava:2.2.21'
    implementation 'com.wdullaer:materialdatetimepicker:3.5.1'
    implementation 'com.github.shts:TriangleLabelView:1.1.2'
    implementation 'com.github.pwittchen:reactivenetwork-rx2:3.0.8'
    implementation group: 'com.alibaba', name: 'fastjson', version: '1.2.79'
    implementation 'com.arasthel:asyncjob-library:1.0.3'
    implementation 'com.airbnb.android:lottie:5.2.0'

    implementation('androidx.room:room-runtime:2.4.3') {
        exclude group: 'com.android.support'
    }
    annotationProcessor 'androidx.room:room-compiler:2.4.3'
    implementation 'com.github.PhilJay:MPAndroidChart:v3.0.2'
    implementation 'com.github.Q42:AndroidScrollingImageView:1.3.2'
    implementation 'com.github.hackware1993:MagicIndicator:1.5.0'

    implementation 'com.sothree.slidinguppanel:library:3.4.0'
    implementation 'com.google.android.gms:play-services-places:17.0.0'
    implementation 'com.github.thunder413:DateTimeUtils:3.0'
    implementation 'io.github.pilgr:paperdb:2.7.2'
    implementation 'com.vipulasri:ticketview:1.1.2'

    implementation 'com.squareup:otto:1.3.8'
    implementation 'com.mikepenz:pixeden-7-stroke-typeface:*******'
    implementation "com.mikepenz:crossfadedrawerlayout:1.1.0"
    implementation 'com.mikepenz:itemanimators:1.1.0'
    implementation 'com.mikepenz:fastadapter-extensions-expandable:3.3.1'
    implementation 'com.mikepenz:material-design-iconic-typeface:*******'

    implementation 'com.github.Kunzisoft:Android-SwitchDateTimePicker:1.9'




    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'com.github.GrenderG:Toasty:1.5.2'
    implementation 'pl.droidsonroids.gif:android-gif-drawable:1.2.24'



  //  implementation 'com.google.android.play:core:1.10.3'
    implementation 'com.github.AndroidDeveloperLB:AutoFitTextView:+'
  //  implementation 'io.sentry:sentry-android:6.1.0'
    implementation project(':filter')

    implementation 'com.basgeekball:awesome-validation:4.3'
    implementation "androidx.startup:startup-runtime:1.1.1"
    implementation "com.github.BirjuVachhani:locus-android:+"
    implementation 'org.jetbrains.kotlin:kotlin-stdlib:1.7.21'
    implementation group: 'com.google.code.gson', name: 'gson', version: '2.10'
    implementation 'com.google.android.gms:play-services-location:21.0.1'

    implementation 'org.maplibre.gl:android-sdk:9.5.2'
    implementation 'org.maplibre.gl:android-plugin-annotation-v9:1.0.0'
    implementation 'com.google.android.gms:play-services-maps:18.1.0'
    implementation 'com.github.amoskorir:avatarimagegenerator:1.5.0'

    implementation 'androidx.work:work-runtime-ktx:2.7.1'
    implementation 'com.google.android.material:material:1.7.0'
    implementation 'androidx.viewpager2:viewpager2:1.0.0'
    implementation 'com.makeramen:roundedimageview:2.3.0'
    implementation 'com.intellij:annotations:+@jar'
    implementation 'com.akexorcist:round-corner-progress-bar:2.1.2'

    implementation 'com.ramotion.fluidslider:fluid-slider:0.3.1'

    implementation 'com.google.android.play:app-update:2.1.0'


    // Import the BoM for the Firebase platform
    implementation platform('com.google.firebase:firebase-bom:31.0.2')

    // Add the dependencies for the Crashlytics and Analytics libraries
    // When using the BoM, you don't specify versions in Firebase library dependencies
    implementation 'com.google.firebase:firebase-crashlytics'
    implementation 'com.google.firebase:firebase-analytics'



    implementation 'com.itextpdf:io:7.0.2'
    implementation 'com.itextpdf:kernel:7.0.2'
    implementation 'com.itextpdf:layout:7.0.2'
    implementation 'com.karumi:dexter:6.2.3'

    def camerax_version = "1.0.2"
    implementation "androidx.camera:camera-core:${camerax_version}"
    implementation "androidx.camera:camera-lifecycle:${camerax_version}"
    implementation "androidx.camera:camera-camera2:${camerax_version}"

    implementation "androidx.camera:camera-view:1.0.0-alpha32"
    implementation "com.google.mlkit:barcode-scanning:17.0.2"
    //  implementation 'me.dm7.barcodescanner:zxing:1.9.13'

  //  implementation("com.mikepenz:materialdrawer:6.1.3")
        implementation("com.mikepenz:materialdrawer:6.1.2@aar") {
        transitive = true
    }
//        implementation("com.mikepenz:materialdrawer:6.1.3@aar") {
//        transitive = true
//    }
    // implementation("de.codecrafters.tableview:tableview:2.8.1")
    implementation 'com.github.ISchwarz23:SortableTableView:2.8.1'

 //   implementation("net.cachapa.expandablelayout:expandablelayout:2.9.2")
 //   implementation group: 'net.cachapa.expandablelayout', name: 'expandablelayout', version: '2.9.2'
    //implementation 'com.amulyakhare:com.amulyakhare.textdrawable:1.0.1'
    //implementation 'com.dynamitechetan.flowinggradient:flowinggradient:1.1'


  // implementation 'com.ui.optimize:dimens:1.0.0'
//    implementation 'co.chiragm.sbn:library:0.0.1'
    // implementation 'com.github.devlight.navigationtabstrip:navigationtabstrip:1.0.4'
    //implementation 'com.dmitrymalkovich.android:material-design-dimens:1.4'
 //   implementation 'com.symbol:emdk:7.4.24'

}

task deleteConfig {
    doLast {
        delete(files("$rootProject.projectDir/app/src/main/assets/config"))
    }
}

task copyConfig {
    doLast {
        project.mkdir "$rootProject.projectDir/app/src/main/assets/config"
        copy {
            from("$rootProject.projectDir/config") {
                include '*.json'
            }
            into("$rootProject.projectDir/app/src/main/assets/config")
        }
    }
}

tasks.whenTaskAdded { task ->
    if (task.name == 'assembleRelease') {
        task.dependsOn deleteConfig
    }
}

afterEvaluate {
    assembleRelease.dependsOn copyConfig
}
configurations {
    implementation.exclude group: 'org.jetbrains', module: 'annotations'

}