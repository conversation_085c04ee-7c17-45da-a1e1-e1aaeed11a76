apply plugin: 'com.android.library'

android {
    compileSdkVersion 34

    defaultConfig {
        minSdkVersion 19
        targetSdkVersion 34

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    namespace 'asm.asmtunis'

}

dependencies {
    api project(':library')
    api "com.mikepenz:iconics-core:3.2.5@aar"
    api "com.mikepenz:iconics-views:3.2.5@aar"
    api 'com.afollestad.material-dialogs:core:0.9.6.0'
    api 'com.afollestad.material-dialogs:commons:0.9.6.0'
    api group: 'com.squareup.retrofit2', name: 'retrofit', version: '2.9.0'
    api 'com.squareup.okhttp3:logging-interceptor:5.0.0-alpha.8'
    api 'com.squareup.okhttp3:okhttp:5.0.0-alpha.8'
    api group: 'com.squareup.retrofit2', name: 'converter-gson', version: '2.9.0'
    api group: 'com.squareup.retrofit2', name: 'adapter-rxjava', version: '2.9.0'
    api 'com.rengwuxian.materialedittext:library:2.1.4'
    api 'com.mikepenz:fontawesome-typeface:5.3.1.1@aar'
    api 'com.mikepenz:google-material-typeface:3.0.1.2.original@aar'
    api 'com.mikepenz:material-design-iconic-typeface:2.2.0.4@aar'
    api 'com.blankj:utilcodex:1.31.0'
    implementation 'com.hbb20:ccp:2.6.0'
}
