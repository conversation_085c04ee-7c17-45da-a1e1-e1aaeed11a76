<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/animation_list"
    android:orientation="vertical">

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:contentDescription=""
        android:padding="25dp"
        android:src="@drawable/ic_asm" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/deivceID"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="#72E4E4E4"
        android:clickable="true"
        android:editable="false"
        android:gravity="center"
        android:selectAllOnFocus="true"
        android:text="4df654gf6d5"
        android:textAlignment="center"
        android:textIsSelectable="true"
        android:textColor="@color/black"
        android:textSize="14dp"
        android:textStyle="bold" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:scrollbarStyle="outsideOverlay">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingLeft="8dp"
            android:paddingRight="8dp">
            <!--                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"-->

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/userField"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp">

                <com.google.android.material.textfield.TextInputEditText
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/name_entreprise_hint"
                    android:textColor="@color/white"
                    android:inputType="textPersonName"
                    tools:text="@tools:sample/lorem" />
            </com.google.android.material.textfield.TextInputLayout>

<!--            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"-->

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/establishmentField"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp">

                <com.google.android.material.textfield.TextInputEditText
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/establishment_hint"
                    android:textColor="@color/white"
                    android:inputType="textPersonName"
                    tools:text="@tools:sample/lorem" />
            </com.google.android.material.textfield.TextInputLayout>
<!--            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"-->

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/eMailField"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp">

                <com.google.android.material.textfield.TextInputEditText
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/email_hint"
                    android:textColor="@color/white"
                    android:inputType="textEmailAddress"
                    tools:text="@tools:sample/lorem" />
            </com.google.android.material.textfield.TextInputLayout>

<!--            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"-->

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.hbb20.CountryCodePicker
                    android:id="@+id/ccp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingTop="20dp"
                    android:theme="@style/AlertDialog.AppCompat.Light"
                    app:ccpDialog_allowSearch="true"
                    app:ccpDialog_backgroundColor="@color/white"
                    app:ccp_arrowColor="@color/white"
                    app:ccp_contentColor="@color/white"
                    app:ccp_defaultLanguage="FRENCH"
                    app:ccp_defaultNameCode="TN" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/phoneNumberField"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="8dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/telephone_hint_1"
                        android:inputType="phone"
                        android:textColor="@color/white"
                        tools:text="@tools:sample/lorem" />
                </com.google.android.material.textfield.TextInputLayout>

            </androidx.appcompat.widget.LinearLayoutCompat>
<!--            style="@style/Widget.AppCompat.Spinner.Underlined"-->

            <androidx.appcompat.widget.AppCompatSpinner
                android:id="@+id/spinner"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="12dp"
                android:entries="@array/license_label"
                android:spinnerMode="dialog"
                />
<!--            android:theme="@style/ThemeOverlay.AppCompat.Dark"-->

        </androidx.appcompat.widget.LinearLayoutCompat>


    </ScrollView>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/signup"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/button"
        android:gravity="center"
        android:padding="13dp"
        android:text="Envoyer"
        android:textColor="#000"
        android:textSize="20dp" />


</androidx.appcompat.widget.LinearLayoutCompat>