package license.model;

import android.content.Context;
import android.provider.Settings;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * Created by ASM Tunis on 20/09/2016.
 */
public class Activation {
    public static int ST_MAC_AVAILABLE = 0;
    static int ST_MAC_NOT_AVAILABLE = 1;
    static Context ctx;
    String mac;
    @SerializedName("android_id")
    @Expose
    private String android_id;
    private int status;


    public Activation(Context context) {
        android_id = Settings.Secure.getString(context.getContentResolver(),
                Settings.Secure.ANDROID_ID);
    }


    public static byte[] hexStringToByteArray(String s) {
        int j = s.length();
        byte abyte0[] = new byte[j / 2];
        int i = 0;
        do {
            if (i >= j) {
                return abyte0;
            }
            abyte0[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4) + Character.digit(s.charAt(i + 1), 16));
            i += 2;
        } while (true);
    }


    public int getMacStatus() {
        return status;
    }

    public String getIdDevice() {
        return android_id;
    }
}
