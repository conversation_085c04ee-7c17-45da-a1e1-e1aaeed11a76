package license.model;


import android.content.Context;

public class Demande {
    private String idDevice;
    private String nomPrenom;
    private String etablissement;
    private String numTel;
    private String email;
    private long duree;
    private String produit;
    private String pays = "Tunisie";
    private String device = "promobile";

    public Demande() {

    }

    public Demande(Context context) {
        produit = String.valueOf(context.getApplicationInfo().loadLabel(context.getPackageManager()));
    }


    public Demande(String idDevice, String nomPrenom, String etablissement, String numTel, String email, long duree) {
        this.idDevice = idDevice;
        this.nomPrenom = nomPrenom;
        this.etablissement = etablissement;
        this.numTel = numTel;
        this.email = email;
        this.duree = duree;
    }




    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device;
    }

    public String getProduit() {
        return produit;
    }

    public void setProduit(String produit) {
        this.produit = produit;
    }

    public String getPays() {
        return pays;
    }

    public void setPays(String pays) {
        this.pays = pays;
    }

    public String getIdDevice() {
        return idDevice;
    }

    public void setIdDevice(String idDevice) {
        this.idDevice = idDevice;
    }

    public String getNomPrenom() {
        return nomPrenom;
    }

    public void setNomPrenom(String nomPrenom) {
        this.nomPrenom = nomPrenom;
    }

    public String getNumTel() {
        return numTel;
    }

    public void setNumTel(String numTel) {
        this.numTel = numTel;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public long getDuree() {
        return duree;
    }

    public void setDuree(long duree) {
        this.duree = duree;
    }

    public String getEtablissement() {
        return etablissement;
    }

    public void setEtablissement(String etablissement) {
        this.etablissement = etablissement;
    }
}

