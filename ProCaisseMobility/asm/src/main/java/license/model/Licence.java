package license.model;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * Created by asm-ala on 02/03/2017.
 */

public class Licence {
    @SerializedName("id")
    @Expose
    private long id;
    @SerializedName("id_client")
    @Expose
    private String idClient;
    @SerializedName("nom_prenom")
    @Expose
    private String nomPrenom;
    @SerializedName("email")
    @Expose
    private String email;
    @SerializedName("telephone")
    @Expose
    private String telephone;
    @SerializedName("datef")
    @Expose
    private String datef;
    @SerializedName("id_device")
    @Expose
    private String idDevice;
    @SerializedName("dater")
    @Expose
    private double nbjr;
    @SerializedName("activat")
    @Expose
    private String isActivated;
    @SerializedName("type")
    @Expose
    private String type;
    @SerializedName("etablissement")
    @Expose
    private String etablissement;
    @SerializedName("demo")
    @Expose
    private String demo;
    @SerializedName("pays")
    @Expose
    private String pays;
    @SerializedName("produit")
    @Expose
    private String produit;
    @SerializedName("version")
    @Expose
    private String version;
    @SerializedName("id_integrateur")
    @Expose
    private String idIntegrateur;
    @SerializedName("id_CPU")
    @Expose
    private String idCPU;
    @SerializedName("prix")
    @Expose
    private String prix;
    @SerializedName("code_activation")
    @Expose
    private String codeActivation;
    @SerializedName("date_activation")
    @Expose
    private String dateActivation;
    @SerializedName("heure_activation")
    @Expose
    private String heureActivation;
    @SerializedName("device")
    @Expose
    private String device;
    @SerializedName("date_conx")
    @Expose
    private String dateConx;


    public Licence() {
    }


    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getIdClient() {
        return idClient;
    }

    public void setIdClient(String idClient) {
        this.idClient = idClient;
    }

    public String getNomPrenom() {
        return nomPrenom;
    }

    public void setNomPrenom(String nomPrenom) {
        this.nomPrenom = nomPrenom;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getDatef() {
        return datef;
    }

    public void setDatef(String datef) {
        this.datef = datef;
    }

    public String getIdDevice() {
        return idDevice;
    }

    public void setIdDevice(String idDevice) {
        this.idDevice = idDevice;
    }

    public double getNbjr() {
        return nbjr;
    }

    public void setNbjr(double nbjr) {
        this.nbjr = nbjr;
    }

    public String getActivated() {
        return isActivated;
    }

    public void setActivated(String activated) {
        isActivated = activated;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getEtablissement() {
        return etablissement;
    }

    public void setEtablissement(String etablissement) {
        this.etablissement = etablissement;
    }

    public String getDemo() {
        return demo;
    }

    public void setDemo(String demo) {
        this.demo = demo;
    }

    public String getPays() {
        return pays;
    }

    public void setPays(String pays) {
        this.pays = pays;
    }

    public String getProduit() {
        return produit;
    }

    public void setProduit(String produit) {
        this.produit = produit;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getIdIntegrateur() {
        return idIntegrateur;
    }

    public void setIdIntegrateur(String idIntegrateur) {
        this.idIntegrateur = idIntegrateur;
    }

    public String getIdCPU() {
        return idCPU;
    }

    public void setIdCPU(String idCPU) {
        this.idCPU = idCPU;
    }

    public String getPrix() {
        return prix;
    }

    public void setPrix(String prix) {
        this.prix = prix;
    }

    public String getCodeActivation() {
        return codeActivation;
    }

    public void setCodeActivation(String codeActivation) {
        this.codeActivation = codeActivation;
    }

    public String getDateActivation() {
        return dateActivation;
    }

    public void setDateActivation(String dateActivation) {
        this.dateActivation = dateActivation;
    }

    public String getHeureActivation() {
        return heureActivation;
    }

    public void setHeureActivation(String heureActivation) {
        this.heureActivation = heureActivation;
    }

    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device;
    }

    public String getDateConx() {
        return dateConx;
    }

    public void setDateConx(String dateConx) {
        this.dateConx = dateConx;
    }

    @Override
    public String toString() {
        return "Licence{" +
                "id=" + id +
                ", idClient='" + idClient + '\'' +
                ", nomPrenom='" + nomPrenom + '\'' +
                ", email='" + email + '\'' +
                ", telephone='" + telephone + '\'' +
                ", datef='" + datef + '\'' +
                ", idDevice='" + idDevice + '\'' +
                ", nbjr=" + nbjr +
                ", isActivated=" + isActivated +
                ", type='" + type + '\'' +
                ", etablissement='" + etablissement + '\'' +
                ", demo=" + demo +
                ", pays='" + pays + '\'' +
                ", produit='" + produit + '\'' +
                ", version='" + version + '\'' +
                ", idIntegrateur='" + idIntegrateur + '\'' +
                ", idCPU='" + idCPU + '\'' +
                ", prix='" + prix + '\'' +
                ", codeActivation='" + codeActivation + '\'' +
                ", dateActivation='" + dateActivation + '\'' +
                ", heureActivation='" + heureActivation + '\'' +
                ", device='" + device + '\'' +
                ", dateConx='" + dateConx + '\'' +
                '}';
    }
}


