package license;

import android.app.Activity;
import android.app.ProgressDialog;
import android.content.pm.ActivityInfo;
import android.graphics.drawable.AnimationDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatSpinner;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.appcompat.widget.LinearLayoutCompat;

import com.blankj.utilcode.util.DeviceUtils;
import com.blankj.utilcode.util.ObjectUtils;
import com.google.android.material.snackbar.Snackbar;
import com.google.android.material.textfield.TextInputLayout;
import com.hbb20.CountryCodePicker;

import asm.asmtunis.R;
import license.asyncTask.SubscriptionTask;
import license.model.Demande;

/**
 * Created by me on 4/17/2017.
 */

public abstract class DemandeLicenceActivity extends AppCompatActivity {

    Activity context = this;
    TextInputLayout userField;
    TextInputLayout establishmentField;
    TextInputLayout phoneNumberField;
    TextInputLayout eMailField;
    AppCompatSpinner duree;
    CountryCodePicker countryCodePicker;
    // AppCompatSpinner licenseSpinner;
    AppCompatButton signUp;
    Demande demande;
    boolean doubleBackToExitPressedOnce = false;
    //    Disposable disposable;
    AppCompatTextView deviceId;
    int[] licenseValues;
    AnimationDrawable anim;
    LinearLayoutCompat layout;
    private ProgressDialog dialog;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);
        setContentView(R.layout.activity_demande_licence);
        // if (isOnline()) {
        setupView();

    }

    public void hideKeyboard(Activity activity) {
        InputMethodManager imm = (InputMethodManager) activity.getSystemService(Activity.INPUT_METHOD_SERVICE);
        View view = activity.getCurrentFocus();
        if (view == null) {
            view = new View(activity);
        }
        imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
    }


    void signUp(View view) {
        hideKeyboard(context);
        if (validate()) {
            demande = new Demande();
            demande.setNomPrenom(String.format("%s", userField.getEditText().getText()));
            demande.setDuree(licenseValues[duree.getSelectedItemPosition()]);
            demande.setEmail(String.format("%s", eMailField.getEditText().getText()));
            demande.setEtablissement(String.format("%s", establishmentField.getEditText().getText()));
            demande.setNumTel(String.format("%s", phoneNumberField.getEditText().getText()));
            demande.setIdDevice(String.format("%s", DeviceUtils.getAndroidID()));
            demande.setProduit(String.format("%s", getApplicationName()));
            new SubscriptionTask(context, demande, output -> {
                if (output != null) {
                    Snackbar.make(view, output.getMessage(), Snackbar.LENGTH_LONG).show();
                    runOnUiThread(() -> {
                        final Handler handler = new Handler();
                        handler.postDelayed(this::finish, 1555);
                    });
                } else {
                    Snackbar.make(view, "Erreur de connexion sur le serveur.", Snackbar.LENGTH_LONG)
                            .show();
                }

            }).execute();
        }
    }


    void setupView() {

        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);
        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE | WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN);
        setContentView(R.layout.activity_demande_licence);
        licenseValues = getResources().getIntArray(R.array.license_value);
        deviceId = findViewById(R.id.deivceID);
        userField = findViewById(R.id.userField);
        establishmentField = findViewById(R.id.establishmentField);
        phoneNumberField = findViewById(R.id.phoneNumberField);
        eMailField = findViewById(R.id.eMailField);
        countryCodePicker = findViewById(R.id.ccp);
        duree = findViewById(R.id.spinner);
        signUp = findViewById(R.id.signup);
        layout = findViewById(R.id.layout);
        signUp.setOnClickListener(this::signUp);
        dialog = new ProgressDialog(this);
        dialog.setCancelable(false);
        dialog.setCanceledOnTouchOutside(false);
        anim = (AnimationDrawable) layout.getBackground();
        anim.setEnterFadeDuration(100);
        anim.setExitFadeDuration(1000);
        deviceId.setText(DeviceUtils.getAndroidID());
    }


    public boolean validate() {
        boolean valid = true;
        String email = eMailField.getEditText().getText().toString();
        String establishment = establishmentField.getEditText().getText().toString();
        String user = userField.getEditText().getText().toString();
        String phoneNumber = phoneNumberField.getEditText().getText().toString();

        if (ObjectUtils.isEmpty(email) || !email.contains("@")) {
            eMailField.setError(getResources().getString(R.string.error_invalid_email));
            valid = false;
        } else {
            eMailField.setError(null);
        }

        if (ObjectUtils.isEmpty(establishment)) {
            establishmentField.setError(getResources().getString(R.string.error_field_required));
            valid = false;
        } else {
            establishmentField.setError(null);
        }
        if (ObjectUtils.isEmpty(user)) {
            userField.setError(getResources().getString(R.string.error_field_required));
            valid = false;
        } else {
            userField.setError(null);
        }

        if (ObjectUtils.isEmpty(phoneNumber)) {
            phoneNumberField.setError(getResources().getString(R.string.error_field_required));
            valid = false;
        } else {
            phoneNumberField.setError(null);
        }

        return valid;
    }

    private boolean checkField(EditText value) {
        if (value.getText().toString().isEmpty() || value.getText().toString().length() == 0) {
            value.setError("required field");
            return false;
        } else {
            value.setError(null);
            return true;
        }

    }


    public abstract String getApplicationName();

}



