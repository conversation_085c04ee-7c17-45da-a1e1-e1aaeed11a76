package license.asyncTask;

import android.app.Activity;
import android.os.AsyncTask;

import com.afollestad.materialdialogs.MaterialDialog;

import java.util.HashMap;


/**
 * Created by me on 3/20/2017.
 */

public abstract class BaseAsyncTask<P, Q, T> extends AsyncTask<P, Q, T> {

    protected final String TAG = "PostFetcher";
    public AsyncResponse<T> delegate = null;
    MaterialDialog.Builder builder;
    MaterialDialog progressDialog;
    Activity context;
    String url, id, parameter;
    HashMap<String, String> parameters = new HashMap<>();
    Q object;


    public BaseAsyncTask(Activity context, Q object, AsyncResponse<T> delegate) {
        this.delegate = delegate;
        this.context = context;
        this.object = object;
        builder = new MaterialDialog.Builder(context);

    }


    public BaseAsyncTask(Activity context, String url, HashMap<String, String> parameters, Q object, AsyncResponse<T> delegate) {
        this.delegate = delegate;
        this.context = context;
        this.url = url;
        this.parameters = parameters;
        builder = new MaterialDialog.Builder(context);
        this.object = object;

    }


    public BaseAsyncTask(Activity context, String url, Q object, AsyncResponse<T> delegate) {
        this.delegate = delegate;
        this.context = context;
        this.url = url;
        builder = new MaterialDialog.Builder(context);
        this.object = object;
    }


    public BaseAsyncTask(Activity context, String url, String id, Q object, AsyncResponse<T> delegate) {
        this.delegate = delegate;
        this.context = context;
        this.url = url;
        this.id = id;
        this.object = object;
        builder = new MaterialDialog.Builder(context);
    }


    public BaseAsyncTask(Activity context, String url, String parameter, String id, Q object, AsyncResponse<T> delegate) {
        this.delegate = delegate;
        this.context = context;
        this.url = url;
        this.id = id;
        this.parameter = parameter;
        this.object = object;
        builder = new MaterialDialog.Builder(context);
    }


    public BaseAsyncTask(Activity context, AsyncResponse<T> delegate) {
        this.delegate = delegate;
        this.context = context;
        builder = new MaterialDialog.Builder(context);

    }

    public BaseAsyncTask(Activity context, String url, HashMap<String, String> parameters, AsyncResponse<T> delegate) {
        this.delegate = delegate;
        this.context = context;
        this.url = url;
        this.parameters = parameters;
        builder = new MaterialDialog.Builder(context);
    }


    public BaseAsyncTask(Activity context, String url, AsyncResponse<T> delegate) {
        this.delegate = delegate;
        this.context = context;
        this.url = url;
        builder = new MaterialDialog.Builder(context);
    }

    public BaseAsyncTask(Activity context, String url, String id, AsyncResponse<T> delegate) {
        this.delegate = delegate;
        this.context = context;
        this.url = url;
        this.id = id;
        builder = new MaterialDialog.Builder(context);
    }

    public BaseAsyncTask(Activity context, String url, String parameter, String id, AsyncResponse<T> delegate) {
        this.delegate = delegate;
        this.context = context;
        this.url = url;
        this.id = id;
        this.parameter = parameter;
        builder = new MaterialDialog.Builder(context);
    }
}
