package license.asyncTask;

import android.app.Activity;

import com.google.gson.GsonBuilder;

import license.model.Demande;
import license.model.ServerResponse;
import license.utils.WebServiceConsumer;


/**
 * Created by me on 3/17/2017.
 */

public class SubscriptionTask extends BaseAsyncTask<Object, Demande, ServerResponse> {


    public SubscriptionTask(Activity context, Demande input, AsyncResponse<ServerResponse> delegate) {
        super(context, input, delegate);
    }


    @Override
    protected ServerResponse doInBackground(Object... params) {
        parameters.put("demande", new GsonBuilder().create().toJson(object));
        return new WebServiceConsumer<>(ServerResponse.class).post("http://as.asmhost.net/proresto/WS/Add_demande_licence.php", parameters);

    }

    @Override
    protected void onPreExecute() {
        progressDialog = builder.progress(true, 0).cancelable(false)
                .show();
    }

    @Override
    protected void onPostExecute(ServerResponse result) {
        super.onPostExecute(result);
        if (progressDialog.isShowing()) {
            progressDialog.dismiss();
        }

        delegate.processFinish(result);
    }
}
