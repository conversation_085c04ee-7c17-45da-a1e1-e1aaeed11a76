package license.asyncTask;

import android.app.Activity;

import license.model.Activation;
import license.model.Licence;
import license.utils.WebServiceConsumer;


/**
 * Created by me on 3/17/2017.
 */

public class CheckActivationTask extends BaseAsyncTask<Object, Activation, Licence> {

    public CheckActivationTask(Activity context, Activation object, AsyncResponse<Licence> delegate) {
        super(context, object, delegate);
    }

    @Override
    protected Licence doInBackground(Object... params) {
        parameters.put("iddevice", object.getIdDevice());
        parameters.put("produit", String.valueOf(context.getApplicationInfo().loadLabel(context.getPackageManager())));
        return new WebServiceConsumer<>(Licence.class).post("http://as.asmhost.net/proresto/Service_Activation_Mobile.php", parameters);
    }

    @Override
    protected void onPreExecute() {
    /*    progressDialog = builder.progress(true, 0).cancelable(false)
                .show();*/
    }

    @Override
    protected void onPostExecute(Licence result) {
        super.onPostExecute(result);
      /*  if (progressDialog.isShowing()) {
            progressDialog.dismiss();
        }*/

//      setIsActivated(result.getActivate().equals("true") ? true : false);
        //setIsDemo(result.getDemo());

        delegate.processFinish(result);

    }
}
