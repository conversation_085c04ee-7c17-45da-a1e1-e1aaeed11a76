package license.customview;

/**
 * Created by me on 4/16/2017.
 */
//
//public class MyFloatingActionButton  extends com.github.clans.fab.FloatingActionButton{
//
//
//    public MyFloatingActionButton(Context context) {
//        super(context);
//    }
//
//    public MyFloatingActionButton(Context context, AttributeSet attrs) {
//        super(context, attrs);
//    }
//
//    public MyFloatingActionButton(Context context, AttributeSet attrs, int defStyleAttr) {
//        super(context, attrs, defStyleAttr);
//    }
//
//    public MyFloatingActionButton(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
//        super(context, attrs, defStyleAttr, defStyleRes);
//    }
//
//
//    private void init() {
//
//    }
//}
