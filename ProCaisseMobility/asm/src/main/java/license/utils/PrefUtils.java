package license.utils;

import android.content.Context;
import android.content.SharedPreferences;

/**
 * Created by me on 3/19/2017.
 */

public abstract class PrefUtils {

        protected Context context;
        protected String preferencesName;
        public static SharedPreferences sharedpreferences;
        public SharedPreferences.Editor editor;

        public PrefUtils(Context context, String preferencesName) {
            this.context = context;
            this.preferencesName = preferencesName;
            sharedpreferences = context.getSharedPreferences(preferencesName,
                    Context.MODE_PRIVATE);
            editor = sharedpreferences.edit();
        }

        public PrefUtils(Context context) {
            preferencesName = getPreferencesName();
            this.context = context;
            sharedpreferences = context.getSharedPreferences(preferencesName,
                    Context.MODE_PRIVATE);
            editor = sharedpreferences.edit();
        }

        protected abstract String getPreferencesName();

        public void clearSharedPreferences() {
            editor.clear();
            editor.apply();
        }

    public String getIsActivated() {
        return sharedpreferences.getString(Globals.IS_ACTIVATED_KEY, "0");
    }

    public void setIsActivated(String isActivated) {
        editor.putString(Globals.IS_ACTIVATED_KEY, isActivated);
        editor.apply();
    }

    public String getEntrepriseAdresse() {
        return sharedpreferences.getString(Globals.ENTREPRISE_ADRESSE, " ");
    }

    public void setEntrepriseAdresse(String adresse) {
        editor.putString(Globals.ENTREPRISE_ADRESSE, adresse);
        editor.apply();
    }

    public String getEntrepriseName() {
        return sharedpreferences.getString(Globals.ENTREPRISE_NAME, " ");
    }

    public void setEntrepriseName(String name) {
        editor.putString(Globals.ENTREPRISE_NAME, name);
        editor.apply();
    }

    public String getEntrepriseTel() {
        return sharedpreferences.getString(Globals.ENTREPRISE_TEL, " ");
    }

    public void setEntrepriseTel(String numTel) {
        editor.putString(Globals.ENTREPRISE_TEL, numTel);
        editor.apply();
    }

    public String getEntrepriseMat() {
        return sharedpreferences.getString(Globals.ENTREPRISE_MAT, " ");
    }

    public void setEntrepriseMat(String numMat) {
        editor.putString(Globals.ENTREPRISE_MAT, numMat);
        editor.apply();
    }


    public boolean getIsDemo() {
        return sharedpreferences.getBoolean(Globals.IS_DEMO_KEY, false);
    }

    public void setIsDemo(boolean isDemo) {
        editor.putBoolean(Globals.IS_DEMO_KEY, isDemo);
        editor.apply();
    }

    public void setExpirationDte(String date) {
        editor.putString(Globals.EXPIRATION_DATE_KEY, date);
        editor.apply();
    }

    public String getExpirationDate() {
        return sharedpreferences.getString(Globals.EXPIRATION_DATE_KEY, null);
    }

    public void setDigitNumber(int digits) {
        editor.putInt(Globals.DIGITS_NUMBER_KEY, digits);
        editor.apply();
    }

    public int getDigitNumber() {
        return sharedpreferences.getInt(Globals.DIGITS_NUMBER_KEY, 3);
    }


}
