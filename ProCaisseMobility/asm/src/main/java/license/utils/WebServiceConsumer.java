package license.utils;

import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.Reader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;


/**
 * Created by me on 4/16/2017.
 */

public class WebServiceConsumer<R> {
    public  final String TAG = "WebServiceConsumer";


    private final Class<R> clazz;


    public WebServiceConsumer(Class<R> clazz) {

        this.clazz = clazz;
    }

    String setupParameters(HashMap<String, String> parameters)

    {
        StringBuilder stringBuilder = new StringBuilder();
        if (!parameters.isEmpty() || parameters != null) {
            for (int i = 0; i < parameters.size(); i++) {
                stringBuilder.append(String.format("%s=%s", new ArrayList(parameters.keySet()).get(i), parameters.get(new ArrayList(parameters.keySet()).get(i))));
                if (parameters.size() > 1 && i != parameters.size() - 1) {
                    stringBuilder.append("&");
                }
            }
        }
        Log.d("parameters123", stringBuilder.toString());
        return String.valueOf(stringBuilder);

    }

    HttpURLConnection setupConnection(String url1, String requestMethod) throws IOException

    {
        URL url = new URL(url1);
        HttpURLConnection urlConn = (HttpURLConnection) url.openConnection();
        urlConn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
        urlConn.setRequestProperty("Accept", "application/json");
        urlConn.setRequestMethod(requestMethod);
        urlConn.setDoInput(true);
        urlConn.setDoOutput(true);
        urlConn.setUseCaches(false);
        return urlConn;
    }

    public R post(String url1, HashMap<String, String> parameters)

    {
        Log.d("url123", url1);
        try {
            HttpURLConnection urlConnection = setupConnection(url1, "POST");

            //String jsontosend = new GsonBuilder().create().toJson(object);
            String urlParameters = setupParameters(parameters);

            if (urlParameters != null) {
                OutputStreamWriter wr = new OutputStreamWriter(urlConnection.getOutputStream());
                wr.write(urlParameters);
                wr.flush();
            }
            int res = urlConnection.getResponseCode();
            if (res == HttpURLConnection.HTTP_OK) {
                try {
                    Reader reader = new BufferedReader(new InputStreamReader(urlConnection.getInputStream()));
                    GsonBuilder gsonBuilder = new GsonBuilder();
                    gsonBuilder.disableHtmlEscaping();
                    Gson gsonbuilder = gsonBuilder.create();


                    return gsonbuilder.fromJson(reader, clazz);
                } catch (Exception ex) {
                    Log.e(TAG, "Failed to parse JSON due to: " + ex);
                    return null;
                }
            } else {
                Log.e(TAG, "Server responded with status code: " + res);
                return null;
            }
        } catch (Exception ex) {
            Log.e(TAG, "Failed to send HTTP POST request due to: " + ex);
            return null;
        }

    }


}
